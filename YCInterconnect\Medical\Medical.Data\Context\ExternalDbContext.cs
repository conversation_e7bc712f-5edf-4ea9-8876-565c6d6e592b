﻿using Medical.Models;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public partial class ExternalDbContext : DbContext
    {
        public ExternalDbContext(DbContextOptions<ExternalDbContext> options)
           : base(options)
        { }
        protected override void OnModelCreating(ModelBuilder builder)
        {

            base.OnModelCreating(builder);
        }
        public DbSet<StatisticsScheduleOracleInfo> StatisticsScheduleOracleInfos { get; set; }
        public DbSet<StatisticsProcessOracleInfo> StatisticsProcessOracleInfos { get; set; }
        public DbSet<StatisticsRiskOracleInfo> StatisticsRiskOracleInfos { get; set; }
    }
}