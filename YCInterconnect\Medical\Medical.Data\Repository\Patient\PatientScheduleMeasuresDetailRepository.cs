﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientScheduleMeasuresDetailRepository : IPatientScheduleMeasuresDetailRepository
    {
        private MedicalDbContext _dbContext = null;

        public PatientScheduleMeasuresDetailRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }
        /// <summary>
        /// 根据病人住院序号获取明细数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMeasuresDetailInfo>> GetObserveDetailByInpatientID(string inpatientID)
        {
            return await _dbContext.PatientScheduleMeasuresDetailInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据病人主记录ID获取明细数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="recordsCode"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMeasuresDetailInfo>> GetObserveDetailByRecordID(string recordID, string recordsCode)
        {
            return await _dbContext.PatientScheduleMeasuresDetailInfos.Where(m => m.PatientScheduleMeasuresID == recordID && m.RecordsCode == recordsCode && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据主记录ID与细项组ID获取评估细项值
        /// </summary>
        /// <param name="recordID">主记录ID</param>
        /// <param name="assessListGroupID">组ID</param>
        /// <returns>细项值，AssessValue</returns>
        public async Task<List<string>> GetObserveDetailValueByRecordIDAndGroupID(string recordID, int assessListGroupID)
        {
            return await _dbContext.PatientScheduleMeasuresDetailInfos.Where(m => m.PatientScheduleMeasuresID == recordID && m.AssessListGroupID == assessListGroupID && m.DeleteFlag != "*")
                .Select(m => m.AssessValue).ToListAsync();
        }
        /// <summary>
        /// 根据病人主记录ID获取明细数据
        /// </summary>
        /// <param name="sourceID"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMeasuresDetailInfo>> GetObserveDetailBySourceID(string sourceID)
        {
            return await _dbContext.PatientScheduleMeasuresDetailInfos.Where(m => m.PatientScheduleMeasuresID == sourceID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<string>> GetScheduleMainIDsByDetailIDs(List<string> detailIDs)
        {
            return await (from measure in _dbContext.PatientScheduleMeasuresDetailInfos.AsNoTracking().Where(m => detailIDs.Contains(m.PatientScheduleMeasuresDetailID) && m.DeleteFlag != "*")
                          join schedule in _dbContext.PatientScheduleMain.AsNoTracking()
                          on measure.PatientScheduleMeasuresID equals schedule.PatientInterventionID
                          select schedule.PatientScheduleMainID).ToListAsync();
        }
    }
}