﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Interconnect.Data.Context;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Interconnect.ViewModels;
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ViewModel;

namespace Interconnect.Services
{
    public class OperateListService : IOperateListService
    {


        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        //private readonly IOperateListRepository _operateListRepository;

        private readonly IPatientOperationRepository _patientOperationRepository;

        private readonly IInpatientDataRepository _IInpatientDataRepository;
        private readonly ILogInfoServices _ILogInfoServices;
        private readonly ICommonHelper _commonHelper;
        private readonly ISettingDescRepository _interconnectSDRepository;
        private readonly IInpatientLogRepository _inpatientLogRepository;     
        private readonly IAssessMainRepository _assessMainRepository;
        private readonly IOptions<SystemConfig> _config;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly ISyncAPIConfigRepository _syncAPIConfigRepository;
        private readonly IUnitOfWork<DataOutConnection> _unitOfWorkOut;
        public OperateListService(IUnitOfWork<MedicalDbContext> unitOfWork
         //, IOperateListRepository operateListRepository

         , IPatientOperationRepository patientOperationRepository

         , IInpatientDataRepository inPatientRepository
         , ILogInfoServices logInfoServices
         , ICommonHelper commonHelper
         , ISettingDescRepository settingDescRepository
         , IInpatientLogRepository inpatientLogRepository        
         , IOptions<SystemConfig> options
         , IAppConfigSettingRepository  appConfigSettingRepository     
         , IAssessMainRepository assessMainRepository
         , ISyncAPIConfigRepository syncAPIConfigRepository
         , IUnitOfWork<DataOutConnection> unitOfWorkOut
         )
        {
            _unitOfWork = unitOfWork;
            _patientOperationRepository = patientOperationRepository;
            _IInpatientDataRepository = inPatientRepository;
            _ILogInfoServices = logInfoServices;
            _commonHelper = commonHelper;
            _interconnectSDRepository = settingDescRepository;
            _inpatientLogRepository = inpatientLogRepository;
            _config = options;
            _appConfigSettingRepository = appConfigSettingRepository;
            _assessMainRepository = assessMainRepository;
            _syncAPIConfigRepository = syncAPIConfigRepository;
            _unitOfWorkOut = unitOfWorkOut;
        }


        /// <summary>
        /// 同步手术记录,同步一段时间内的手术数据
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncOperateDataByDateTime()
        {
            //获取最后的同步时间
            var hospitalID = _config.Value.HospitalID;
            var nowDateTime = DateTime.Now;
            var lastDateTime = nowDateTime;
            var intervalminutes = 30;
            //获取手术数据API
            var syncAPIConfigInfo = _syncAPIConfigRepository.GetSyncAPIConfigByApiID(16);
            if (syncAPIConfigInfo == null)
            {
                _logger.Error("没有找到ApiID为16(手术API)的数据");
                return false;
            }
            lastDateTime = syncAPIConfigInfo.LastSyncDateTime;
            intervalminutes = syncAPIConfigInfo.IntervalMinutes;
            if (intervalminutes == 0)
            {
                intervalminutes = 30;
            }
            //获取时间重叠分钟，没有值默认60分钟
            var overlapMinutes = syncAPIConfigInfo.OverlapMinutes;
            if (overlapMinutes == null)
            {
                overlapMinutes = 60;
            }
            //如果间隔时间小于5分钟，不启用同步，避免接口服务器阻塞
            if (lastDateTime.AddMinutes(5) > nowDateTime)
            {
                return false;
            }

            return await SyncOperateDataDetail(syncAPIConfigInfo, lastDateTime, nowDateTime, overlapMinutes ?? 60, intervalminutes, hospitalID);

        }

        /// <summary>
        /// 同步手术数据
        /// </summary>
        /// <param name="syncAPIConfigInfo">API地址</param>
        /// <param name="lastDateTime">最后同步时间</param>
        /// <param name="nowDateTime">当前时间</param>
        /// <param name="overlapMinutes">同步的重叠时间</param>
        /// <param name="intervalminutes">每次获取得时间间隔</param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<bool> SyncOperateDataDetail(SyncAPIConfigInfo syncAPIConfigInfo, DateTime lastDateTime, DateTime nowDateTime, int overlapMinutes, int intervalminutes, string hospitalID)
        {
            //术前需要调用的API
            var preOperationAPI = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "PreOperationAPI");
            //术后需要调用的API
            var postOperationAPI = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "PostOperationAPI");
            if (string.IsNullOrEmpty(preOperationAPI))
            {
                _logger.Error("appConfigSetting没有配置PreOperationAPI,无法同步手术");
                return false;
            }

            if (string.IsNullOrEmpty(postOperationAPI))
            {
                _logger.Error("appConfigSetting没有配置postOperationAPI,无法同步手术");
                return false;
            }

            //设置数据同步，时间重叠分钟，避免获取的数据不完整
            var startDateTime = lastDateTime.AddMinutes(-overlapMinutes);
            var endDateTime = startDateTime;
            //每次获取30分钟的数据同步频率
            while (lastDateTime < nowDateTime)
            {
                if (lastDateTime.AddMinutes(intervalminutes) > nowDateTime)
                {
                    endDateTime = nowDateTime;
                    lastDateTime = endDateTime;
                }
                else
                {
                    //获取30分钟内的数据
                    lastDateTime = lastDateTime.AddMinutes(intervalminutes);
                    endDateTime = lastDateTime;
                }
                var dictionary = new Dictionary<string, string>
                 {
                    { "StartDateTime", startDateTime.ToString()},
                    { "EndDateTime", endDateTime.ToString()},
                 };
                //获取API接口数据
                var hisData = GetOperateApiData(syncAPIConfigInfo.APIAddress, dictionary, hospitalID);
                //所以在时间段内如果没有数据，则继续循环，直到获取数据。
                if (hisData == null || hisData.Count <= 0)
                {
                    _logger.Info("时间范围:" + startDateTime.ToString() + "-" + endDateTime.ToString() + "没有数据");
                    continue;
                }
                //数据同步
                if (! SynchronizationDetail(hisData, preOperationAPI, postOperationAPI))
                {
                    return false;
                }
                //同步成功，更新最后同步时间
                syncAPIConfigInfo.LastSyncDateTime = lastDateTime;
                try
                {
                   _unitOfWorkOut.SaveChanges();
                }
                catch (Exception ex)
                {
                    _logger.Error("更新最后同步时间失败" + ex.ToString());
                    return false;
                }
            }
            return true;
        }



        /// <summary>
        /// 根据该时间段的数据的CaseNumber进行分组,并获取对应的手术数据进行同步
        /// </summary>
        /// <param name="hisData"></param>
        /// <param name="preOperationAPI"></param>
        /// <param name="postOperationAPI"></param>
        /// <returns></returns>
        private  bool SynchronizationDetail(List<HISSurgeryHistoryView> hisData,string preOperationAPI, string postOperationAPI)
        {
            _logger.Info(" 手术 开始进行数据同步，数据条数：" + hisData.Count +"接口数据:"+ ListToJson.ToJson<List<HISSurgeryHistoryView>>(hisData));
            var caseNumberList= hisData.Select(m => m.CaseNumber).ToList();
            var inpatientDataList =  _IInpatientDataRepository.GetByCaseNumbersAll(caseNumberList);
            if (inpatientDataList.Count == 0)
            {
                _logger.Error("手术病人信息无法获取 CaseNumberArr : " + ListToJson.ToJson<List<string>>(caseNumberList));
                return false;
            }
            var postOperation = new List<PatientOperation>();
            //遍历所有没有抽档的数据
            foreach (var item in hisData)
            {
                if (item.OperateName == null)
                {
                    continue;
                }
                if (item.ScheduledDatetime == null)
                {
                    _logger.Info("ChartNO:[" + item.ChartNO + "] CaseNumber:[" + item.CaseNumber + "] 不同步, 预约时间为空");
                    continue;
                }
                var inpatient = inpatientDataList.Find(m => m.CaseNumber == item.CaseNumber.Trim());
                var oprationItem = GetNewOprationInfo(item, inpatient);
                //获取存在的手术数据
                var patientOperation = _patientOperationRepository.GetPatientOperateByOperationNo(item.CaseNumber.ToString(), item.OperationNo);    
                if (patientOperation == null)
                {
                    oprationItem.AddDate = DateTime.Now;
                    patientOperation = oprationItem;
                    _unitOfWork.GetRepository<PatientOperationInfo>().Insert(patientOperation);
                }
                else
                {
                    UpPatientOperateInfo(patientOperation, oprationItem);
                }
                try
                {
                    _unitOfWork.SaveChanges();
                }
                catch (Exception ex)
                {
                    _logger.Info( "手术同步失败||" + ex.ToString());
                    return false;
                }
                if (patientOperation.OperationEndDateTime != null)
                {
                    postOperation.Add(CreatePatientOperation(patientOperation));
                };
            }
            if (postOperation.Count == 0)
            {
                _logger.Info("无术后手术需要同步");
                return true;
            }
            //呼叫MedicalAPI，写Proflie,及病人事件
            CallAPI(postOperation, postOperationAPI);
            _logger.Info ("手术同步结束");
            return true;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="hisOprationView"></param>
        /// <param name="inpatient"></param>
        /// <returns></returns>
        private PatientOperationInfo GetNewOprationInfo(HISSurgeryHistoryView hisOprationView,InpatientDataInfo inpatient)
        {

            var datetimes = TransformationDateTimes(hisOprationView);
            var patientOperationInfo = new PatientOperationInfo
            {
                PatientOperationID = Guid.NewGuid().ToString("N"),
                CaseNumber = inpatient.CaseNumber,
                ChartNo = inpatient.ChartNo,
                InpatientID = inpatient.ID,
                HISOperationNo = hisOprationView.OperationNo,
                OperationCode = hisOprationView.OperateCode,
                OperationName = hisOprationView.OperateName ?? "",
                ICDPCS = hisOprationView.ICDPCS,
                AnesthesiaMethod = hisOprationView.AnesthesiaMethod,
                StatusCode = hisOprationView.StatusCode,
                ModifyDate = DateTime.Now,
                ModifyPersonID = "system",
                OperationDate = datetimes.OperateDate,
                EntryRoomDateTime = datetimes.EntryRoomDateTime,
                EntryRecoveryRoomDateTime = datetimes.EntryRecoveryRoomDateTime,
                OperationStartDateTime = datetimes.OperationStartDateTime,
                OperationEndDateTime = datetimes.OperationEndDateTime,
                ScheduledDatetime = datetimes.ScheduledDatetime,
                ExitRecoveryRoomDateTime = datetimes.ExitRecoveryRoomDateTime,
                ExitRoomDateTime = datetimes.ExitRoomDateTime,
                OperationGroupID = "",
                OperationType = "",
                DeleteFlag = "",
            };
            if (int.TryParse(hisOprationView.OperateNum, out Int32 parStr))
            {
                patientOperationInfo.OperationNum = parStr;
            }
            return patientOperationInfo;
        }

        /// <summary>
        /// 呼叫API
        /// </summary>
        /// <param name="operateListInfo"></param>
        /// <param name="preOperationAPI">术前</param>
        /// <param name="postOperationAPI">术后</param>
        /// <returns></returns>
        private bool CallAPIMain(PatientOperationInfo patientOperationInfo, string preOperationAPI, string postOperationAPI)
        {
            //说明，为了统一，手术结束结束时间按照出手术室时间

            var patientOperationList = new List<PatientOperation>();
            //组装呼叫API需要的数据
            var patientOperation = CreatePatientOperation(patientOperationInfo);
            //术前（预约时间不为空，结束时间为空）
            //if (patientOperationInfo.ScheduledDatetime != null && patientOperationInfo.OperationEndDateTime == null)
            //{
            //    patientOperationList.Add(patientOperation);
            //return CallAPI(patientOperationList, preOperationAPI);
            //}
            //术后，有手术结束时间           
            if (patientOperationInfo.OperationEndDateTime != null)
            {
                patientOperationList.Add(patientOperation);
            }
            return true;
        }

        /// <summary>
        /// 组装PatientOperation数据(呼叫API使用)
        /// </summary>
        /// <param name="item"></param>
        /// <returns></returns>
        private PatientOperation CreatePatientOperation(PatientOperationInfo item)
        {
            var patientOperation = new PatientOperation
            {
                CaseNumber = item.CaseNumber.ToString(),
                OperID = 1,
                ChartNo = item.ChartNo,
                Name = item.OperationName,
                AnesthesiaMethod = item.AnesthesiaMethod,
                ScheduledDateTime = item.ScheduledDatetime,
                InRoomDateTime = item.EntryRoomDateTime,
                StartDateTime = item.OperationStartDateTime,
                EndDateTime = item.OperationEndDateTime,
                OutRoomDateTime = item.ExitRoomDateTime
            };
            return patientOperation;
        }
       

        /// <summary>
        /// 更新手术数据
        /// </summary>
        /// <param name="operateList"></param>
        /// <param name="item"></param>
        private PatientOperationInfo UpPatientOperateInfo(PatientOperationInfo patientOperation, PatientOperationInfo item)
        {
            var result = new Tuple<bool, PatientOperationInfo>(false, null);
            var updataFlag = false;

            if (patientOperation.OperationCode != item.OperationCode)
            {
                patientOperation.OperationCode = item.OperationCode;
                updataFlag = true;
            }
            if (patientOperation.OperationName != item.OperationName)
            {
                patientOperation.OperationName = item.OperationName;
                updataFlag = true;
            }
            if (patientOperation.ICDPCS != item.ICDPCS)
            {
                patientOperation.ICDPCS = item.ICDPCS;
                updataFlag = true;
            }
            if (patientOperation.AnesthesiaMethod != item.AnesthesiaMethod)
            {
                patientOperation.AnesthesiaMethod = item.AnesthesiaMethod;
                updataFlag = true;
            }
            if (patientOperation.StatusCode != item.StatusCode)
            {
                patientOperation.StatusCode = item.StatusCode;
                updataFlag = true;
            }
            if (patientOperation.OperationDate != item.OperationDate)
            {
                patientOperation.OperationDate = item.OperationDate;
                updataFlag = true;
            }
            if (patientOperation.EntryRoomDateTime != item.EntryRoomDateTime)
            {
                patientOperation.EntryRoomDateTime = item.EntryRoomDateTime;
                updataFlag = true;
            }
            if (patientOperation.EntryRecoveryRoomDateTime != item.EntryRecoveryRoomDateTime)
            {
                patientOperation.EntryRecoveryRoomDateTime = item.EntryRecoveryRoomDateTime;
                updataFlag = true;
            }
            if (patientOperation.OperationStartDateTime != item.OperationStartDateTime)
            {
                patientOperation.OperationStartDateTime = item.OperationStartDateTime;
                updataFlag = true;
            }
            if (patientOperation.OperationEndDateTime != item.OperationEndDateTime)
            {
                patientOperation.OperationEndDateTime = item.OperationEndDateTime;
                updataFlag = true;
            }
            if (patientOperation.ScheduledDatetime != item.ScheduledDatetime)
            {
                patientOperation.ScheduledDatetime = item.ScheduledDatetime;
                updataFlag = true;
            }
            if (patientOperation.ExitRecoveryRoomDateTime != item.ExitRecoveryRoomDateTime)
            {
                patientOperation.ExitRecoveryRoomDateTime = item.ExitRecoveryRoomDateTime;
                updataFlag = true;
            }
            if (patientOperation.ExitRoomDateTime != item.ExitRoomDateTime)
            {
                patientOperation.ExitRoomDateTime = item.ExitRoomDateTime;
                updataFlag = true;
            }

            if (patientOperation.OperationGroupID != "")
            {
                patientOperation.OperationGroupID = "";
                updataFlag = true;
            }
            if (patientOperation.OperationType != "")
            {
                patientOperation.OperationType = "";
                updataFlag = true;
            }

            if (updataFlag)
            {
                patientOperation.ModifyDate = DateTime.Now;
                patientOperation.ModifyPersonID = "system";
                _logger.Info("手术信息发生变化，进行更新,HISOperateNo" + patientOperation.HISOperationNo);
            }
            return patientOperation;
        }

        /// <summary>
        /// 转换手术时间
        /// </summary>
        /// <param name="item"></param>
        /// <returns></returns>
        private OperateListView TransformationDateTimes(HISSurgeryHistoryView item)
        {
            var operateListView = new OperateListView();
            //DateTime dt = DateTime.ParseExact(item.OperateDate, "yyyy-MM-dd hh:mm ", System.Globalization.CultureInfo.CurrentCulture);
            if (!string.IsNullOrEmpty(item.OperateDate))
            {
                operateListView.OperateDate = Convert.ToDateTime(item.OperateDate);
            }
            if (!string.IsNullOrEmpty(item.ArrivedStationDateTime))
            {
                operateListView.ArrivedStationDateTime = Convert.ToDateTime(item.ArrivedStationDateTime);
            }
            if (!string.IsNullOrEmpty(item.InRoomDateTime))
            {
                operateListView.EntryRoomDateTime = Convert.ToDateTime(item.InRoomDateTime);
            }
            if (!string.IsNullOrEmpty(item.InRecoveryRoomDateTime))
            {
                operateListView.EntryRecoveryRoomDateTime = Convert.ToDateTime(item.InRecoveryRoomDateTime);
            }
            if (!string.IsNullOrEmpty(item.OperationStartDateTime))
            {
                operateListView.OperationStartDateTime = Convert.ToDateTime(item.OperationStartDateTime);
            }
            if (!string.IsNullOrEmpty(item.OperationEndDateTime))
            {
                operateListView.OperationEndDateTime = Convert.ToDateTime(item.OperationEndDateTime);
            }
            if (!string.IsNullOrEmpty(item.ScheduledDatetime))
            {
                operateListView.ScheduledDatetime = Convert.ToDateTime(item.ScheduledDatetime);
            }
            if (!string.IsNullOrEmpty(item.OutRecoveryRoomDateTime))
            {
                operateListView.ExitRecoveryRoomDateTime = Convert.ToDateTime(item.OutRecoveryRoomDateTime);
            }
            if (!string.IsNullOrEmpty(item.OutRoomDateTime))
            {
                operateListView.ExitRoomDateTime = Convert.ToDateTime(item.OutRoomDateTime);
            }
            return operateListView;
        }

       
      


        /// <summary>
        /// 呼叫medicalAPI写入profile，problem等一系列手术相关数据
        /// </summary>
        /// <param name="proOperation"></param>
        /// <param name="preOperationAPI"></param>
        private bool CallAPI(List<PatientOperation> sourceList, string apiUrl)
        {
            try
            {
                WebRequestSugar wrs = new WebRequestSugar();
                wrs.SendObjectAsJsonInBody(apiUrl, sourceList);
            }
            catch (Exception ex)
            {
                _logger.Error("手术同步调用API：" + apiUrl + "失败：" + ex.Message);
                return false;
            }
            return true;
        }


        #region 获取银川api数据并将json转model,

        /// <summary>
        /// 获取api数据后json转model
        /// </summary>
        /// <param name="api"></param>
        /// <param name="dictionary"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private List<HISSurgeryHistoryView> GetOperateApiData(string api, Dictionary<string, string> dictionary, string hospitalID)
        {
            _logger.Info("呼叫API获取检验数据");
            var hisData = GetApiData(api, dictionary, hospitalID);
            if (string.IsNullOrEmpty(hisData))
            {
                return null;
            }
            var interconnect_Data = new List<HISSurgeryHistoryView>();
            try
            {
                interconnect_Data = JsonConvert.DeserializeObject<List<HISSurgeryHistoryView>>(hisData);
            }
            catch (Exception ex)
            {
                _logger.Error("Json格式化失败:" + ex.ToString());
                return null;
            }
            _logger.Info("转换Json数据完成，获得手术数据" + interconnect_Data.Count() + "条！");
            return interconnect_Data;
        }

        #endregion 获取银川api数据并将json转model,

        #region 获取银川api数据

        /// <summary>
        /// 获取api数据
        /// </summary>
        /// <param name="api"></param>
        /// <param name="dictionarys"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private string GetApiData(string api, Dictionary<string, string> dictionarys, string hospitalID)
        {
            if (string.IsNullOrEmpty(api))
            {
                return "";
            }
            //获取环境 ,1 开发环境
            var systemOperatingEnvironment = _appConfigSettingRepository.GetConfigSettingValue("Configs", "SystemOperatingEnvironment").Result;
            var resultData = "";
            if (systemOperatingEnvironment != "1")
            {
                //呼叫API获得数据
                resultData = _commonHelper.GetInterconnectData(api, dictionarys);
            }
            else
            {
                resultData = _ILogInfoServices.GetLog("17")[0].Logs;
            }

            var printInterfaceData = 0;
            //获取打印接口配置
            var resultPrintDate = _appConfigSettingRepository.GetConfigSettingValue("Configs", "PrintInterfaceData").Result;
            if (StringCheck.IsNumeric(resultPrintDate))
            {
                printInterfaceData = int.Parse(resultPrintDate);
            }

            if (printInterfaceData == 1)
            {
                _logger.Info("Api:" + api + "获取的手术数据" + ListToJson.ToJson(dictionarys) + "手术数据：" + resultData);
            }

            try
            {
                var result = JsonConvert.DeserializeObject<ResponseResult>(resultData);
                var resultDataStr = result.Data.ToString();
                if (resultDataStr == "" || resultDataStr == "[]" || resultDataStr == "[{}]" || resultDataStr == "{}")
                {
                    return "";
                }
                return resultDataStr;
            }
            catch (Exception ex)
            {
                _logger.Error("Api: " + api + "获取的手术数据ResponseResult" + ex.ToString());
                return "";
            }
        }

        #endregion 获取银川api数据
        /*
        #region 旧的手术同步逻辑

        

        /// <summary>
        /// 手术数据同步
        /// </summary>
        /// <param name="OriginalList">手术记录</param>
        /// <param name="inpatientData">病人基本信息</param>
        /// <returns></returns>
        private async Task<bool> SyncOperateDetail(InpatientDataInfo inpatientData)
        {
            #region "初始宣告"

            //1821:手术开始,1822:手术结束,1813:術前(预约手术)
            var assessListIDs = new List<int> { 1821, 1822, 1813 };

            //判断是否为新数据
            bool newData = false;

            #endregion "初始宣告"

            //由API获取需要同步的数据
            var originalList = GetApiData(inpatientData.CaseNumber);
            if (originalList == null || originalList.Count <= 0)
            {
                return false;
            }

            #region "数据加载"

            //取得现有的手术记录
            var operationList = await _operateListRepository.GetPatientOperateList(inpatientData.ID);

            //取得病人日志
            var inpatientLogs = await _inpatientLogRepository.GetByInpatientIDAsync(inpatientData.ID);

            //取得病人事件
            var patientEvents = await _patientEventService.GetEventByAssessListIDs(inpatientData.ID, assessListIDs);

            #endregion "数据加载"

            foreach (var item in originalList)
            {
                newData = false;

                if (string.IsNullOrEmpty(item.OperateDate))
                {
                    continue;
                }

                if (string.IsNullOrEmpty(item.OperateNum))
                {
                    item.OperateNum = "1";
                }

                //写入手术数据 true表示新增,false表示修改
                newData = await WriteOperateData(operationList, inpatientData, item, inpatientLogs, patientEvents);
                if (newData)
                {
                    await AddLog(item, inpatientData);
                }
            }

            var keys = originalList.Select(m => m.OperationNo).ToList();
            //反比,避免有删除的手术记录
            var needDeleLog = inpatientLogs.Where(m => !keys.Contains(m.OPCode)).ToList();
            var needDeleEvent = patientEvents.Where(m => !keys.Contains(m.SourceID)).ToList();
            if (needDeleLog.Count > 0)
            {
                _unitOfWork.GetRepository<InpatientLogInfo>().Delete(needDeleLog);
                foreach (var item in needDeleLog)
                {
                    await DelClusterOrder(item.LogCode, inpatientData.ID);
                }
            }

            if (needDeleEvent.Count > 0)
            {
                _unitOfWork.GetRepository<PatientEventInfo>().Delete(needDeleEvent);
            }

            return _unitOfWork.SaveChanges() >= 0;
        }

        //写入手术数据
        private async Task<bool> WriteOperateData(List<OperateListInfo> oldDatas, InpatientDataInfo inpatientData, HISSurgeryHistoryView view, List<InpatientLogInfo> logs, List<PatientEventInfo> patientEvents)
        {
            //病人手术数据
            var operatetInfo = new OperateListInfo();

            //透过手术次数及手术名称判断是否为同一次手术
            var data = oldDatas.Find(m => m.OperateNum == int.Parse(view.OperateNum) && m.OperateName == view.OperateName);

            if (data == null)
            {
                operatetInfo = CreateOperateInfo(inpatientData, view);
                _unitOfWork.GetRepository<OperateListInfo>().Insert(operatetInfo);
                return true;
            }
            else
            {
                await CheckScheduledDatetime(logs, patientEvents, data, view, inpatientData);
                await CheckOperationStartDateTime(logs, patientEvents, data, view, inpatientData);
                await CheckOutRoomDateTime(logs, patientEvents, data, view, inpatientData);
                return false;
            }
        }

        //写入病人手术表
        private OperateListInfo CreateOperateInfo(InpatientDataInfo inpatientData, HISSurgeryHistoryView item)
        {
            var t = new OperateListInfo()
            {
                InpatientID = inpatientData.ID,
                CaseNumber = inpatientData.CaseNumber,
                ChartNo = inpatientData.ChartNo,
                OperateNum = int.Parse(item.OperateNum),
                ICDPCS = item.ICDPCS,
                OperateCode = item.OperateCode,
                OperateName = item.OperateName,
                ScheduledDatetime = _commonHelper.GetDateTime(item.ScheduledDatetime),
                EntryRoomDateTime = _commonHelper.GetDateTime(item.InRoomDateTime),
                ArrivedStationDateTime = _commonHelper.GetDateTime(item.ArrivedStationDateTime),
                OperationStartDateTime = _commonHelper.GetDateTime(item.OperationStartDateTime),
                OperationEndDateTime = _commonHelper.GetDateTime(item.OperationEndDateTime),
                ExitRoomDateTime = _commonHelper.GetDateTime(item.OutRoomDateTime),
                AnesthesiaMethod = item.AnesthesiaMethod,
                EntryRecoveryRoomDateTime = _commonHelper.GetDateTime(item.InRecoveryRoomDateTime),
                ExitRecoveryRoomDateTime = _commonHelper.GetDateTime(item.OutRecoveryRoomDateTime),
                StatusCode = item.StatusCode,
                ModifyPersonID = "system",
                ModifyDate = DateTime.Now
            };
            if (!string.IsNullOrEmpty(item.OperateDate))
            {
                t.OperateDate = Convert.ToDateTime(item.OperateDate);
            }
            return t;
        }

        private async Task CheckScheduledDatetime(List<InpatientLogInfo> logs, List<PatientEventInfo> events, OperateListInfo oldData, HISSurgeryHistoryView view, InpatientDataInfo inpatientData)
        {
            if (!string.IsNullOrEmpty(view.ScheduledDatetime))
            {
                if (oldData.ScheduledDatetime == null)
                {
                    await InsertInpatientLogInfo(inpatientData, "PreOP", view.OperateName, view.OperationNo, Convert.ToDateTime(view.ScheduledDatetime), 1813);

                    return;
                }
                else
                {
                    if (Convert.ToDateTime(view.ScheduledDatetime) != oldData.ScheduledDatetime)
                    {
                        var log = logs.Find(m => m.LogCode == "PreOP" && m.OPCode == view.OperationNo);

                        if (log == null)
                        {
                            await InsertInpatientLogInfo(inpatientData, "PreOP", view.OperateName, view.OperationNo, Convert.ToDateTime(view.ScheduledDatetime), 1813);
                        }
                        else
                        {
                            log.LogDateTime = Convert.ToDateTime(view.ScheduledDatetime);
                        }

                        var patientEvent = events.Find(m => m.AssessListID == 1813 && m.SourceID == view.OperationNo);

                        if (patientEvent != null)
                        {
                            patientEvent.OccurDate = Convert.ToDateTime(view.ScheduledDatetime).Date;
                            patientEvent.OccurTime = Convert.ToDateTime(view.ScheduledDatetime).TimeOfDay;
                        }
                        else
                        {
                            _logger.Error("查无病人手术事件,AssessListID:1813,唯一:" + view.OperationNo);
                        }
                    }
                }
            }
            else
            {
                if (oldData.ScheduledDatetime == null)
                {
                    return;
                }
                else
                {
                    var log = logs.Find(m => m.LogCode == "PreOP" && m.OPCode == view.OperationNo);

                    if (log != null)
                    {
                        _unitOfWork.GetRepository<InpatientLogInfo>().Delete(log);
                    }
                    var patientEvent = events.Find(m => m.AssessListID == 1813 && m.SourceID == view.OperationNo);

                    if (patientEvent != null)
                    {
                        _unitOfWork.GetRepository<PatientEventInfo>().Delete(patientEvent);
                    }
                }
            }
        }

        private async Task CheckOperationStartDateTime(List<InpatientLogInfo> logs, List<PatientEventInfo> events, OperateListInfo oldData, HISSurgeryHistoryView view, InpatientDataInfo inpatientData)
        {
            if (!string.IsNullOrEmpty(view.OperationStartDateTime))
            {
                if (oldData.OperationStartDateTime == null)
                {
                    await InsertInpatientLogInfo(inpatientData, "StartOP", view.OperateName, view.OperationNo, Convert.ToDateTime(view.OperationStartDateTime), 1821);

                    return;
                }
                else
                {
                    if (Convert.ToDateTime(view.OperationStartDateTime) != oldData.OperationStartDateTime)
                    {
                        var log = logs.Find(m => m.LogCode == "StartOP" && m.OPCode == view.OperationNo);

                        if (log == null)
                        {
                            await InsertInpatientLogInfo(inpatientData, "StartOP", view.OperateName, view.OperationNo, Convert.ToDateTime(view.OperationStartDateTime), 1821);
                        }
                        else
                        {
                            log.LogDateTime = Convert.ToDateTime(view.OperationStartDateTime);
                        }

                        var patientEvent = events.Find(m => m.AssessListID == 1821 && m.SourceID == view.OperationNo);

                        if (patientEvent != null)
                        {
                            patientEvent.OccurDate = Convert.ToDateTime(view.OperationStartDateTime).Date;
                            patientEvent.OccurTime = Convert.ToDateTime(view.OperationStartDateTime).TimeOfDay;
                        }
                        else
                        {
                            _logger.Error("查无病人手术事件,AssessListID:1821,唯一:" + view.OperationNo);
                        }
                    }
                }
            }
            else
            {
                if (oldData.ScheduledDatetime == null)
                {
                    return;
                }
                else
                {
                    var log = logs.Find(m => m.LogCode == "StartOP" && m.OPCode == view.OperationNo);

                    if (log != null)
                    {
                        _unitOfWork.GetRepository<InpatientLogInfo>().Delete(log);
                    }
                    var patientEvent = events.Find(m => m.AssessListID == 1821 && m.SourceID == view.OperationNo);

                    if (patientEvent != null)
                    {
                        _unitOfWork.GetRepository<PatientEventInfo>().Delete(patientEvent);
                    }
                }
            }
        }

        private async Task CheckOutRoomDateTime(List<InpatientLogInfo> logs, List<PatientEventInfo> events, OperateListInfo oldData, HISSurgeryHistoryView view, InpatientDataInfo inpatientData)
        {
            if (!string.IsNullOrEmpty(view.OutRoomDateTime))
            {
                if (oldData.ExitRoomDateTime == null)
                {
                    await InsertInpatientLogInfo(inpatientData, "PostOP", view.OperateName, view.OperationNo, Convert.ToDateTime(view.OutRoomDateTime), 1822);

                    return;
                }
                else
                {
                    if (Convert.ToDateTime(view.OutRoomDateTime) != oldData.ExitRoomDateTime)
                    {
                        var log = logs.Find(m => m.LogCode == "PostOP" && m.OPCode == view.OperationNo);

                        if (log == null)
                        {
                            await InsertInpatientLogInfo(inpatientData, "PostOP", view.OperateName, view.OperationNo, Convert.ToDateTime(view.OutRoomDateTime), 1822);
                        }
                        else
                        {
                            log.LogDateTime = Convert.ToDateTime(view.OutRoomDateTime);
                        }

                        var patientEvent = events.Find(m => m.AssessListID == 1822 && m.SourceID == view.OperationNo);

                        if (patientEvent != null)
                        {
                            patientEvent.OccurDate = Convert.ToDateTime(view.OutRoomDateTime).Date;
                            patientEvent.OccurTime = Convert.ToDateTime(view.OutRoomDateTime).TimeOfDay;
                        }
                        else
                        {
                            _logger.Error("查无病人手术事件,AssessListID:1822,唯一:" + view.OperationNo);
                        }
                    }
                }
            }
            else
            {
                if (oldData.ExitRoomDateTime == null)
                {
                    return;
                }
                else
                {
                    var log = logs.Find(m => m.LogCode == "PostOP" && m.OPCode == view.OperationNo);

                    if (log != null)
                    {
                        _unitOfWork.GetRepository<InpatientLogInfo>().Delete(log);
                    }
                    var patientEvent = events.Find(m => m.AssessListID == 1822 && m.SourceID == view.OperationNo);

                    if (patientEvent != null)
                    {
                        _unitOfWork.GetRepository<PatientEventInfo>().Delete(patientEvent);
                    }
                }
            }
        }

        //更新病人手术表
        private OperateListInfo UpdateOperateInfo(InpatientDataInfo inpatientData, HISSurgeryHistoryView item, OperateListInfo operateListInfo)
        {
            operateListInfo.OperateDate = Convert.ToDateTime(item.OperateDate);
            operateListInfo.ICDPCS = item.ICDPCS;
            operateListInfo.OperateCode = item.OperateCode;
            operateListInfo.OperateName = item.OperateName;
            operateListInfo.ScheduledDatetime = _commonHelper.GetDateTime(item.ScheduledDatetime);
            operateListInfo.EntryRoomDateTime = _commonHelper.GetDateTime(item.InRoomDateTime);
            operateListInfo.ArrivedStationDateTime = _commonHelper.GetDateTime(item.ArrivedStationDateTime);
            operateListInfo.OperationStartDateTime = _commonHelper.GetDateTime(item.OperationStartDateTime);
            operateListInfo.OperationEndDateTime = _commonHelper.GetDateTime(item.OperationEndDateTime);
            operateListInfo.ExitRoomDateTime = _commonHelper.GetDateTime(item.OutRoomDateTime);
            operateListInfo.AnesthesiaMethod = item.AnesthesiaMethod;
            operateListInfo.EntryRecoveryRoomDateTime = _commonHelper.GetDateTime(item.InRecoveryRoomDateTime);
            operateListInfo.ExitRecoveryRoomDateTime = _commonHelper.GetDateTime(item.OutRecoveryRoomDateTime);
            operateListInfo.StatusCode = item.StatusCode;
            return operateListInfo;
        }

        //写入日志
        private async Task AddLog(HISSurgeryHistoryView view, InpatientDataInfo inpatientData)
        {
            //手术预约时间=>写入术前1813
            if (!string.IsNullOrEmpty(view.ScheduledDatetime))
            {
                await InsertInpatientLogInfo(inpatientData, "PreOP", view.OperateName, view.OperationNo, Convert.ToDateTime(view.ScheduledDatetime), 1813);
            }
            //手术开始时间=>写入手术开始1821
            if (!string.IsNullOrEmpty(view.OperationStartDateTime))
            {
                await InsertInpatientLogInfo(inpatientData, "StartOP", view.OperateName, view.OperationNo, Convert.ToDateTime(view.OperationStartDateTime), 1821);
            }
            //出手术室时间=>写入手术结束1822
            if (!string.IsNullOrEmpty(view.OutRoomDateTime))
            {
                await InsertInpatientLogInfo(inpatientData, "PostOP", view.OperateName, view.OperationNo, Convert.ToDateTime(view.OutRoomDateTime), 1822);
            }
        }

        private async Task InsertInpatientLogInfo(InpatientDataInfo inpatientData, string logCode, string opName, string key, DateTime logDateTime, int assessListID)
        {
            string assessMainID = "";

            var inpatientLog = new InpatientLogInfo
            {
                InpatientID = inpatientData.ID,
                PatientID = inpatientData.PatientID,
                CaseNumber = inpatientData.CaseNumber,
                ChartNo = inpatientData.ChartNo,
                StationID = inpatientData.StationID,
                BedID = inpatientData.BedID,
                BedNumber = inpatientData.BedNumber,
                LogCode = logCode,
                LogContent = "",
                LogDateTime = logDateTime,
                OPCode = key,
                OPName = "",
                ModifyPersonID = "System",
                ModifyDate = DateTime.Now,
                DeleteFlag = "",
                AnesthesiaMethod = ""
            };

            _unitOfWork.GetRepository<InpatientLogInfo>().Insert(inpatientLog);

            //await _patientEventService.AddEvent(inpatientData, logDateTime, assessListID, "System", key);

            var clusterOrder = await _nursingClusterOrderService.GetBySourceTable(logCode);

            if (clusterOrder.Count == 0)
            {
                return;
            }

            var assess = await _assessMainRepository.GetLastAsync(inpatientData.ID);

            if (assess == null)
            {
                _logger.Error("查无评估记录,病人住院序号:" + inpatientData.ID);
            }
            else
            {
                assessMainID = assess.ID;
            }
            var order = new int[] { clusterOrder[0] };
            await _patientProblemService.AddClusterProblem(inpatientData.ID, assessMainID, order, "System", 1, inpatientData.HospitalID, "O", key, logDateTime);

            if (logCode == "PostOP")
            {
                var preOP = await _nursingClusterOrderService.GetBySourceTable("PreOP");

                if (preOP.Count == 0)
                {
                    return;
                }

                var patientProblem = await _patientProblemService.Get(new List<string> { inpatientData.ID }.ToArray(), new List<int> { preOP[0] }.ToArray());

                if (patientProblem == null || patientProblem.Count <= 0)
                {
                    return;
                }
                await _patientProblemService.DoStopPatientProblem(patientProblem, "System", logDateTime.Date, logDateTime.TimeOfDay);
            }

        }

        private async Task DelClusterOrder(string logCode, string inpatientID)
        {
            var op = await _nursingClusterOrderService.GetBySourceTable(logCode);

            if (op == null)
            {
                return;
            }

            var patientProblem = await _patientProblemService.Get(new List<string> { inpatientID }.ToArray(), new List<int> { op[0] }.ToArray());

            if (patientProblem.Count > 0)
            {
                await _patientProblemService.DeletePatientProblem(patientProblem[0].ID, "System");
            }
        }

        #endregion 旧的手术同步逻辑
        */
        #region 由API获取需要同步的数据     

        #endregion 取得APIUrl
    }
}