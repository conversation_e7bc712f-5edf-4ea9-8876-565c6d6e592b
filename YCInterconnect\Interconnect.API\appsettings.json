﻿{
  "ConnectionStrings": {
    "DataInConnection": "server=www.honLivit.com;database=Medical_Dev;uid=zhongYunCCC;pwd=**`1q;TrustServerCertificate=true",
    "HangFireConnection": "server=www.honlivit.com;database=HangFire;uid=Medical;pwd=************;TrustServerCertificate=true",
    "DataOutConnection": "server=www.honLivit.com;database=Interconnect;uid=zhongYunCCC;pwd=**`1q;TrustServerCertificate=true",
    "FilesConnection": "server=www.honlivit.com;database=Interconnect;uid=medical;pwd=************;TrustServerCertificate=true",
    "StatisticsConnection": "server=www.honlivit.com;database=Medical_Release;uid=Medical;pwd=************;TrustServerCertificate=true",
    "CDAConnection": "server=www.honlivit.com;database=CDA;uid=medical;pwd=************;TrustServerCertificate=true",
    "CAConnection": "server=www.honlivit.com;database=EMR_CA;uid=medical;pwd=************;TrustServerCertificate=true",
    "RedisConnection": "127.0.0.1:6379,password=ZhongYun`1q20230716,syncTimeout =20000,connectTimeout=3000,connectRetry=3,DefaultDatabase=6",
    "Cache": "127.0.0.1:6379"
  },
  "Cache": {
    "Dictionary": 3000
  },
  "Configs": {
    "ModifyPersonID": 1,
    "AgeAssessListID": 23,
    "DrugAllergyID": 182, //药物过敏
    "FoodAllergyID": 186, //食物过敏
    "HospitalID": "3",
    "Language": 1,
    "UseCacheType": "Redis", //使用缓存类型：Redis、Memory
    "PreDate": 3,
    "TakeRows": 200, //同步时，每次取出的记录数量，避免一次取出的数据量过大
    "LogSaveDays": 30, //同步日志保存天数
    "StartupJob": 0, //1启动定时作业，0或其它 不启动定时作业
    "TongbuCount": 2, //一条数据的最多同步次数
    "SyncDays": 1, //同步多少天内的数据
    "SyncHour": 1, //同步多少小时内的数据，住院病人同步使用
    "JobRunTime": 20, //作业的执行时间长度，超过这个时间，重新开启一个作业,单位分钟，最小10分钟
    "Stations": "*", //导入哪些病区的住院信息,*，所有的病区。如果有多个病区用“,”号分割(英文标点)如"2133,2937,2141"
    "CheckEmployeeFlag": 0, //同步医嘱的时候，是否判断人员信息，0 不判断，1 判断
    "AllLogSet": 1, //是否记录全部没有同步成功的日志信息，1 是，0否
    "MinuteInterval": 5, //自动同步所有数据的频次,单位:分钟
    "InpatientDataMinuteInterval": 5, //自动同步住院病人数据的频次,单位:分钟
    "GetDataMinuteInterval": 5, //数据抽档并回写的频次,单位:分钟
    "InPatientDay": -1, //住院病人提取的时间范围 （-1，当前时间-1），（-2，当前时间-2）,如果>=0,则默认-1
    "PrintInterfaceData": 0, //打印接口数据，1打印接口数据,其他不打印
    "EmrServerAllowNum": "2", //电子病历服务允许启动线程数量，系统内部默认10
    //"SentFromUser": "xml", //发送者昵称"xml"
    //"SentFromAddress": "<EMAIL>", //发送者邮箱 "<EMAIL>"
    //"SentFromAuthorizationCode": "oaqqgatslinvbjad", //邮箱SMTP授权码"oaqqgatslinvbjad"
    //"MailHost": "smtp.qq.com", //发送方邮箱服务器"smtp.qq.com"
    //"MailHostPost": 587, //发送方邮件服务器端口587
    "PatientProfileAPI": "http://localhost:56194/api/PatientProfile/Add",
    "StopPatientProblemAPI": "http://localhost:56194/api/External/StopPatientProblem", //停止护理问题
    "CheckSchedulePatientInfoAPI": "http://localhost:56194/api/PatientSchedule/CheckSchedulePatientInfo", //验证排程中病人信息
    "PreOperationAPI": "http://localhost:56194/api/operation/PreOperation", //术前集束护理
    "PostOperationAPI": "http://localhost:56194/api/operation/PostOperation", //术后集束护理
    "StopSchduleByOrderAPI": "http://localhost:56194/api/PatientSchedule/StopByOrder", // 根据医嘱ID停止时间点后排程
    "CacheUpdateAPI": "http://***********:56194/api/cache/update", //更新缓存
    "SentFromUser": "ccc", //发送者昵称"xml"
    "SentFromAddress": "<EMAIL>", //发送者邮箱 "<EMAIL>"
    "SmtpUserName": "info.ccc", //Smtp连接用户
    "SmtpAuthorizationCode": "C3@zsyy2018", //邮箱SMTP授权码"oaqqgatslinvbjad"
    "MailHost": "smtp.zs-hospital.sh.cn", //发送方邮箱服务器"smtp.qq.com"
    "MailHostPost": 25, //发送方邮件服务器端口587
    "EmployeeDefaultRole": 2, // 人员信息同步时，默认的较色
    "InpatientProfileMarkAPI": "http://localhost:56194/api/ProfileMark/Inpatient", //新入患者ProfileMark
    "TransferProfileMarkAPI": "http://localhost:56194/api/ProfileMark/Transfer", //转科ProfileMark
    "DischargeProfileMarkAPI": "http://localhost:56194/api/ProfileMark/Discharge", //出院ProfileMark
    "GetPatientRiskDataListAPI": "http://localhost:56194/api/ProfileMark/GetRiskDataList", //获取在院病人风险数据
    "Status": "2", //1:正式环境,2:测试环境
    "SyncMedicalApi": "http://localhost:56194",
    "DataInterfaceAPI": "http://localhost:5000"
  },
  "Logging": {
    "IncludeScopes": false,
    "Debug": {
      "LogLevel": {
        "Default": "Warning"
      }
    },
    "Console": {
      "LogLevel": {
        "Default": "Warning"
      }
    }
  },
  "MongoDB": {
    "MongoDbService": "mongodb://**********:27017",
    "DataBase": "medicaldb",
    "AutoCreateDb": "false",
    "AutoCreateCollection": "false"
  }
}
