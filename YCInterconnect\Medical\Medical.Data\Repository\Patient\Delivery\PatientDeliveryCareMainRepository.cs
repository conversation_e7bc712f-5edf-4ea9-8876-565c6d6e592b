﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientDeliveryCareMainRepository : IPatientDeliveryCareMainRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientDeliveryCareMainRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }
        /// <summary>
        /// 根据D获取对应数据
        /// </summary>
        /// <param name="id">分娩照护主记录ID</param>
        /// <returns></returns>
        public async Task<PatientDeliveryCareMainInfo> GetByID(string id)
        {
            return await _medicalDbContext.PatientDeliveryCareMainInfos.Where(t => t.PatientDeliveryCareMainID == id && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据分娩记录获取照护主记录
        /// </summary>
        /// <param name="recordID">分娩记录ID</param>
        /// <returns></returns>
        public async Task<List<PatientDeliveryCareMainInfo>> GetByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientDeliveryCareMainInfos.Where(t => t.PatientDeliveryRecordID == recordID && t.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取记录最后一次照护记录
        /// </summary>
        /// <param name="recordID">分娩记录ID</param>
        /// <returns></returns>
        public async Task<PatientDeliveryCareMainInfo> GetLastCare(string recordID)
        {
            var list = await _medicalDbContext.PatientDeliveryCareMainInfos.Where(t => t.PatientDeliveryRecordID == recordID && t.DeleteFlag != "*")
                .OrderBy(m => m.NumberOfAssessment).ToListAsync();
            if (list.Count == 0)
            {
                return null;
            }
            return list.Last();
        }

        public async Task<PatientDeliveryCareMainInfo> GetByRecordCode(string recordID, string recordsCode)
        {
            return await _medicalDbContext.PatientDeliveryCareMainInfos.Where(t => t.PatientDeliveryRecordID == recordID && t.RecordsCode == recordsCode && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据recordID获取新生儿数量
        /// </summary>
        /// <param name="recordID">分娩照护主记录ID</param>
        /// <returns></returns>
        public async Task<List<byte?>> GetNewBornNumByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientDeliveryCareMainInfos.Where(t => t.PatientDeliveryRecordID == recordID && t.DeleteFlag != "*"
            && t.NewBornNum.HasValue).Select(m => m.NewBornNum).ToListAsync();
        }
        /// <summary>
        /// 获取新生儿信息
        /// </summary>
        /// <param name="recordID">分娩照护记录ID</param>
        /// <returns></returns>
        public async Task<List<PatientDeliveryCareMainInfo>> GetNewBornInfo(string recordID)
        {
            return await _medicalDbContext.PatientDeliveryCareMainInfos.Where(t => t.PatientDeliveryRecordID == recordID
            && t.DeleteFlag != "*" && t.NewBornNum.HasValue).ToListAsync();
        }
        /// <summary>
        /// 判断该病人是否存在产时结束的记录（产后护理记录单使用判断是否需要产生产后护理记录单）
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns>true：存在产时结束</returns>
        public async Task<bool> GetDeleveryEndBoolAsync(string inpatientID)
        {
            var count = await _medicalDbContext.PatientDeliveryCareMainInfos.Where(t => t.InpatientID == inpatientID
                && t.RecordsCode == "DeliveryEnd" && t.DeleteFlag != "*").CountAsync();

            return count > 0;
        }

        /// <summary>
        /// 根据主表ID获取RecordsCode
        /// </summary>
        /// <param name="careMainID">主表ID</param>
        /// <returns></returns>
        public async Task<string> GetRecordsCodeByCareMainID(string careMainID)
        {
            return await _medicalDbContext.PatientDeliveryCareMainInfos.Where(t => t.PatientDeliveryCareMainID == careMainID)
                .Select(t => t.RecordsCode).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 只取胎方位和产时用药两个字段
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<List<PatientDeliveryCareMainInfo>> GetFetalPositionAndOxytocinByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientDeliveryCareMainInfos.Where(t => t.PatientDeliveryRecordID == recordID && t.DeleteFlag != "*").
                Select(m => new PatientDeliveryCareMainInfo
                {
                    PatientDeliveryCareMainID = m.PatientDeliveryCareMainID,
                    FetalHeartRatePosition = m.FetalHeartRatePosition,
                    Oxytocin = m.Oxytocin,
                }).
                ToListAsync();
        }

        public async Task<List<string>> GetCareMainIDs(string recordID)
        {
            return await _medicalDbContext.PatientDeliveryCareMainInfos.Where(t => t.PatientDeliveryRecordID == recordID && t.DeleteFlag != "*")
                .Select(m => m.PatientDeliveryCareMainID).ToListAsync();
        }

        /// <summary>
        /// 获取班内产程主表指定记录码最新的维护记录
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="recordsCode">记录码</param>
        /// <param name="shift">班别</param>
        /// <param name="shiftDate">班别日期</param>
        /// <returns></returns>
        public async Task<DeliveryMainAssessDateView> GetShiftLastAssessDateView(string inpatientID, string recordsCode, string shift, DateTime shiftDate)
        {
            return await _medicalDbContext.PatientDeliveryCareMainInfos.Where(m => m.InpatientID == inpatientID && m.Shift == shift
            && m.ShiftDate == shiftDate && m.RecordsCode == recordsCode && m.DeleteFlag != "*")
                .OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime)
                .Select(m => new DeliveryMainAssessDateView
                {
                    PatientDeliveryCareMainID = m.PatientDeliveryCareMainID,
                    AssessDate = m.AssessDate,
                    AssessTime = m.AssessTime
                })
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取病人最新的结束评估ID与评估时间
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <returns></returns>
        public async Task<Tuple<string, DateTime?>> GetLatestEndCareIDAndDate(string inpatientID)
        {
            var data = await _medicalDbContext.PatientDeliveryCareMainInfos.Where(m => m.InpatientID == inpatientID && m.RecordsCode.EndsWith("End") && m.DeleteFlag != "*")
                .OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime)
                .Select(m => Tuple.Create<string, DateTime?>(m.PatientDeliveryCareMainID, m.AssessDate.Add(m.AssessTime))).FirstOrDefaultAsync();
            if (data == null)
            {
                return Tuple.Create<string, DateTime?>(null, null);
            }
            return data;
        }
        /// <summary>
        /// 获取最后有录入某项的评估时间
        /// </summary>
        /// <param name="careMain">当前异动的维护记录</param>
        /// <param name="assessListIDs">评估ID集合</param>
        /// <returns></returns>
        public async Task<DateTime?> GetLastestAssessDateTimeByIDs(PatientDeliveryCareMainInfo careMain, params int[] assessListIDs)
        {
            var currentDateTime = careMain.AssessDate.Add(careMain.AssessTime);
            var tuple = await (from a in _medicalDbContext.PatientDeliveryCareMainInfos
                               .Where(m => m.InpatientID == careMain.InpatientID && m.RecordsCode == careMain.RecordsCode && m.DeleteFlag != "*")
                               from b in _medicalDbContext.PatientDeliveryCareDetailInfos
                               .Where(m => m.InpatientID == careMain.InpatientID && m.DeleteFlag != "*")
                               where a.PatientDeliveryCareMainID == b.PatientDeliveryCareMainID && a.PatientDeliveryCareMainID != careMain.PatientDeliveryCareMainID
                               && assessListIDs.Contains(b.AssessListID)
                               orderby a.AssessDate, a.AssessTime, a.NumberOfAssessment
                               select Tuple.Create(a.AssessDate, a.AssessTime)).LastOrDefaultAsync();

            if (tuple == null && careMain.DeleteFlag == "*")
            {
                return null;
            }
            if (tuple == null && careMain.DeleteFlag != "*")
            {
                return currentDateTime;
            }
            if (tuple != null && careMain.DeleteFlag != "*")
            {
                return tuple.Item1.Add(tuple.Item2) < currentDateTime ? currentDateTime : tuple.Item1.Add(tuple.Item2);
            }
            if (tuple != null && careMain.DeleteFlag == "*")
            {
                return tuple.Item1.Add(tuple.Item2);
            }
            return null;
        }
        /// <summary>
        /// 根据记录码获取不同维护记录的列表
        /// </summary>
        /// <param name="recordID">分娩记录ID</param>
        /// <param name="recordsCode">记录码</param>
        /// <returns></returns>
        public async Task<List<PatientDeliveryCareMainInfo>> GetCareMainInfoByRecordIDAndCode(string recordID, string recordsCode)
        {
            return await _medicalDbContext.PatientDeliveryCareMainInfos.Where(m =>
            m.PatientDeliveryRecordID == recordID && m.RecordsCode == recordsCode && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据RecordsCode获取一条维护记录（纯模板）
        /// </summary>
        /// <param name="recordID">分娩记录ID</param>
        /// <param name="recordsCode">评估Code</param>
        /// <returns></returns>
        public async Task<BaseDeliveryCareMainView> GetCareMainViewByRecordIDAndCode(string recordID, string recordsCode)
        {
            return await _medicalDbContext.PatientDeliveryCareMainInfos.Where(m => m.PatientDeliveryRecordID == recordID
            && m.RecordsCode == recordsCode && m.DeleteFlag != "*").Select(m => new BaseDeliveryCareMainView
            {
                PatientDeliveryCareMainID = m.PatientDeliveryCareMainID,
                StationID = m.StationID,
                DepartmentListID = m.DepartmentListID,
                AssessDate = m.AssessDate,
                AssessTime = m.AssessTime,
                UserID = m.AddEmployeeID,
                BringToShift = m.BringToShift,
                InformPhysician = m.InformPhysician,
                BringToNursingRecord = m.BringToNursingRecord,
            }).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取此编码类别的维护记录评估次数
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="recordsCode">编码</param>
        /// <returns></returns>
        public async Task<int> GetNumberOfAssessment(string inpatientID, string recordsCode)
        {
            return await _medicalDbContext.PatientDeliveryCareMainInfos
                .Where(m => m.InpatientID == inpatientID && m.RecordsCode == recordsCode).CountAsync();
        }
        /// <summary>
        /// 获取维护记录是否存在
        /// </summary>
        /// <param name="recordID">主记录ID</param>
        /// <param name="recordsCode">编码</param>
        /// <returns></returns>
        public async Task<bool> CheckCareMainExistence(string recordID, string recordsCode)
        {
            return await _medicalDbContext.PatientDeliveryCareMainInfos.CountAsync(m => recordID == m.PatientDeliveryRecordID && m.DeleteFlag != "*" &&
            m.RecordsCode == recordsCode) > 0;
        }
        /// <summary>
        /// 根据维护记录主表IDs获取数据列表
        /// </summary>
        /// <param name="careMainIDs">维护记录主表ID集合</param>
        /// <returns></returns>
        public async Task<List<PatientDeliveryCareMainInfo>> GetInfosByCareMainIDs(IEnumerable<string> careMainIDs)
        {
            return await _medicalDbContext.PatientDeliveryCareMainInfos.Where(m =>
            careMainIDs.Contains(m.PatientDeliveryCareMainID)).ToListAsync();
        }
        /// <summary>
        /// 获取录入最早有录入胎次的评估时间
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="recordID">主记录</param>
        /// <param name="excludeCareMainID">排除的维护记录ID</param>
        /// <param name="assessListID">明細項目：分娩/胎次</param>
        /// <returns></returns>
        public async Task<DateTime?> GetEarliestParityAssessDateTime(string inpatientID, string recordID, string excludeCareMainID = "", int assessListID = 0)
        {
            var tuple = await (from a in _medicalDbContext.PatientDeliveryCareMainInfos
                               .Where(m => m.PatientDeliveryRecordID == recordID && m.InpatientID == inpatientID && m.RecordsCode == "DeliveryMaintain" &&
                               m.PatientDeliveryCareMainID != excludeCareMainID && m.DeleteFlag != "*")
                               from b in _medicalDbContext.PatientDeliveryCareDetailInfos
                               .Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*")
                               where a.PatientDeliveryCareMainID == b.PatientDeliveryCareMainID && b.AssessListID == assessListID
                               orderby a.AssessDate, a.AssessTime, a.NumberOfAssessment
                               select Tuple.Create(a.AssessDate, a.AssessTime)).FirstOrDefaultAsync();
            if (tuple == null)
            {
                return null;
            }
            return tuple.Item1.Add(tuple.Item2);
        }
        /// <summary>
        /// 获取维护记录View
        /// </summary>
        /// <param name="recordID">主记录</param>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="recordsCode">表单编号</param>
        /// <returns>Key：维护记录，Value：对应的明细记录</returns>
        public async Task<Dictionary<DeliveryMainAssessDateView, List<Detail>>> GetMainDetailsDict(string recordID, string inpatientID, string recordsCode)
        {
            // 获取 Mains
            var mainList = await _medicalDbContext.PatientDeliveryCareMainInfos
                .Where(a => a.PatientDeliveryRecordID == recordID && a.InpatientID == inpatientID
                && a.DeleteFlag != "*" && a.RecordsCode == recordsCode).Select(a => new
                {
                    a.PatientDeliveryCareMainID,
                    a.AssessDate,
                    a.AssessTime,
                    a.NumberOfAssessment,
                }).ToListAsync();
            // 获取 Details
            var detailList = await _medicalDbContext.PatientDeliveryCareDetailInfos
                .Where(b => b.InpatientID == inpatientID && b.DeleteFlag != "*").Select(b => new
                {
                    b.PatientDeliveryCareMainID,
                    b.AssessListID,
                    b.AssessValue
                }).ToListAsync();
            // 组合为字典
            var dict = (from a in mainList
                        join b in detailList
                        on a.PatientDeliveryCareMainID equals b.PatientDeliveryCareMainID
                        into DetailGroup
                        select new
                        {
                            Key = new DeliveryMainAssessDateView
                            {
                                PatientDeliveryCareMainID = a.PatientDeliveryCareMainID,
                                AssessDate = a.AssessDate,
                                AssessTime = a.AssessTime,
                                NumberOfAssessment = a.NumberOfAssessment,
                            },
                            Value = DetailGroup.Select(m => new Detail
                            {
                                AssessListID = m.AssessListID,
                                AssessValue = m.AssessValue
                            }).ToList()
                        }).ToDictionary(x => x.Key, x => x.Value);
            return dict;
        }
        /// <summary>
        /// 获取最近有录入指定评估流水号的值
        /// </summary>
        /// <param name="careMain">当前异动的维护记录</param>
        /// <param name="details">明细项集合</param>
        /// <param name="assessListID">评估流水号</param>
        /// <returns></returns>
        public async Task<string> GetLatestCareMainDetailByAssessListID(PatientDeliveryCareMainInfo careMain, List<Detail> details, int assessListID)
        {
            var currentDateTime = careMain.AssessDate.Add(careMain.AssessTime);
            var tuple = await (from a in _medicalDbContext.PatientDeliveryCareMainInfos
                               .Where(m => m.PatientDeliveryRecordID == careMain.PatientDeliveryRecordID && m.InpatientID == careMain.InpatientID && m.RecordsCode == careMain.RecordsCode && m.DeleteFlag != "*")
                               from b in _medicalDbContext.PatientDeliveryCareDetailInfos
                               .Where(m => m.InpatientID == careMain.InpatientID && m.DeleteFlag != "*")
                               where
                               a.PatientDeliveryCareMainID == b.PatientDeliveryCareMainID &&
                               a.PatientDeliveryCareMainID != careMain.PatientDeliveryCareMainID &&
                               assessListID == b.AssessListID
                               orderby a.AssessDate, a.AssessTime, a.NumberOfAssessment
                               select Tuple.Create(a, b.AssessValue)).FirstOrDefaultAsync();

            // 都有，取最近的
            if (tuple != null && careMain.DeleteFlag != "*")
            {
                return tuple.Item1.AssessDate.Add(tuple.Item1.AssessTime) > currentDateTime
                    ? tuple.Item2
                    : details.Find(m => m.AssessListID == assessListID)?.AssessValue;
            }
            // 数据库没有，取当前的
            if (tuple == null && careMain.DeleteFlag != "*")
            {
                return details.Find(m => m.AssessListID == assessListID)?.AssessValue;
            }
            else
            {
                return "";
            }
        }
        /// <summary>
        /// 根据分娩记录获取照护主记录
        /// </summary>
        /// <param name="recordID">分娩记录ID</param>
        /// <returns></returns>
        public async Task<List<PatientDeliveryCareMainInfo>> GetNoTrackingByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientDeliveryCareMainInfos.AsNoTracking().Where(t => t.PatientDeliveryRecordID == recordID && t.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据Code获取结束评估的主键
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="recordsCode"></param>
        /// <returns></returns>
        public async Task<List<string>> GetAssessTimeOfAssessment(string inpatientID, string recordsCode)
        {
            return await _medicalDbContext.PatientDeliveryCareMainInfos
                .Where(m => m.InpatientID == inpatientID && m.RecordsCode == recordsCode && m.DeleteFlag != "*")
                .OrderBy(m => m.AddDate)
                .Select(m => m.PatientDeliveryCareMainID)
                .ToListAsync();
        }
        /// <summary>
        /// 根据主记录ID和评估主键获取是否包含特定数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="assesslistID"></param>
        /// <returns></returns>
        public async Task<string> GetLastMidOdinopoeiaMainID(string recordID, int assesslistID)
        {
            var query = await (from a in _medicalDbContext.PatientDeliveryCareMainInfos
                               join b in _medicalDbContext.PatientDeliveryCareDetailInfos
                                on a.PatientDeliveryCareMainID equals b.PatientDeliveryCareMainID
                               where a.PatientDeliveryRecordID == recordID
                                  && a.DeleteFlag != "*"
                                  && b.DeleteFlag != "*"
                                  && b.AssessListID == assesslistID
                               select new PatientDeliveryCareMainInfo
                               {
                                   PatientDeliveryCareMainID = a.PatientDeliveryCareMainID,
                                   AssessDate = a.AssessDate,
                                   AssessTime = a.AssessTime
                               }).ToListAsync();
            return query.Count == 0 ? "" : query.OrderBy(m => m.AssessDate.Add(m.AssessTime)).FirstOrDefault().PatientDeliveryCareMainID;
        }
        /// <summary>
        /// 获取时间范围内最后一次产时维护记录
        /// </summary>
        /// <param name="inpatientID">病人本次住院唯一ID</param>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束事件</param>
        /// <returns></returns>
        public Task<PatientDeliveryCareMainInfo> GetLastCareMainInDateTimeRange(string inpatientID, DateTime startDateTime, DateTime endDateTime)
        {
            return _medicalDbContext.PatientDeliveryCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*"
            //大于等于开始时间
            && ((m.AssessDate == startDateTime.Date && m.AssessTime >= startDateTime.TimeOfDay) || m.AssessDate > startDateTime.Date)
            // 小于等于结束时间
            && ((m.AssessDate == endDateTime.Date && m.AssessTime <= endDateTime.TimeOfDay) || m.AssessDate < endDateTime.Date)
            ).OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).LastOrDefaultAsync();
        }

        public async Task<List<PatientDeliveryCareMainInfo>> GetCareMainIDByRecordIDAndRecordsCode(string recordID, List<string> recordsCode)
        {
            return await _medicalDbContext.PatientDeliveryCareMainInfos.Where(m => m.PatientDeliveryRecordID == recordID && recordsCode.Contains(m.RecordsCode)
            && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientDeliveryCareMainInfo>> GetHandoverViews(string inpatientID, DateTime startDateTime, DateTime endDateTime)
        {
            var date = await _medicalDbContext.PatientDeliveryCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*"
                && m.AssessDate >= startDateTime.Date && m.AssessDate <= endDateTime.Date).ToListAsync();

            return date.Where(m => m.AssessDate.Add(m.AssessTime) >= startDateTime && m.AssessDate.Add(m.AssessTime) <= endDateTime).OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).ToList();
        }
    }
}