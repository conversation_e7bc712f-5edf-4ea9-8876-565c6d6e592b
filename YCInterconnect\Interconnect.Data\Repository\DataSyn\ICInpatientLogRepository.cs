﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;


namespace Interconnect.Data.Repository
{
   public class ICInpatientLogRepository:IICInpatientLogRepository
    {
        private DataOutConnection _DataOutConnection = null;

        public ICInpatientLogRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }
        /// <summary>
        /// 获取所有没有抽取的数据
        /// </summary>
        /// <param name="tongbuCount"></param>
        /// <param name="takeRows"></param>
        /// <returns></returns>
        public async Task<List<ICInpatientLogInfo>> GetAsync(int tongbuCount,int takeRows)
        {    
            return await _DataOutConnection.ICInpatientLogInfos.Where(m => m.DataPumpFlag != "*"
            && ((m.Counts ?? 0) < tongbuCount)).OrderBy(m=>m.InpatientLogID).Take(takeRows).ToListAsync();

        }
    }
}
