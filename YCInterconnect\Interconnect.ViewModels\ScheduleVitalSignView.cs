﻿using System.Collections.Generic;

namespace Interconnect.ViewModels
{
    public class ScheduleVitalSignView
    {
        /// <summary>
        /// 来源类型
        /// </summary>
        public string SourceType { get; set; }
        /// <summary>
        /// 一条数据的唯一键
        /// </summary>
        public string RecordID { get; set; }
        /// <summary>
        /// 详细数据
        /// </summary>
        public List<VitalSignData> Data { get; set; }
    }
    public class VitalSignData
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 患者编号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 住院号
        /// </summary>
        public string ChartNo { get; set; }
        /// <summary>
        /// 病区编码
        /// </summary>
        public string StationCode { get; set; }
        /// <summary>
        /// 病区名
        /// </summary>
        public string StationName { get; set; }
        /// <summary>
        /// 数据编码
        /// </summary>
        public string MonitorCode { get; set; }
        /// <summary>
        /// 数据名称
        /// </summary>
        public string MonitorName { get; set; }
        /// <summary>
        /// 数据值
        /// </summary>
        public string DataValue { get; set; }
        /// <summary>
        /// 数据单位
        /// </summary>
        public string Unit { get; set; }
        /// <summary>
        /// 修改人工号
        /// </summary>
        public string ModifyPersonID { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>
        public string ModifyDate { get; set; }
        /// <summary>
        /// 删除标记
        /// </summary>
        public string DeleteFlag { get; set; }
    }
}
