﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    //<summary>
    //风险表，返回HIS数据
    //</summary>
    [Serializable]
    [Table("PatientScoreMain")]
    public   class InterconnectPatientScoreMainInfo
    {
        /// <summary>
        /// 风险表主键
        /// </summary>
        [Key]
        public string PatientScoreMainID { get; set; }
        /// <summary>
        /// 姓名
        /// </summary>
        public string PatientName{ get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public string Sex{ get; set; }
        /// <summary>
        /// 风险说明
        /// </summary>
        public string RangeContent { get; set; }
        /// <summary>
        /// 风险表ID
        /// </summary>
        public int? RecordListID { get; set; }
        /// <summary>
        /// 风险名称
        /// </summary>
        public string RecordName { get; set; }
        /// <summary>
        /// 风险评估时间
        /// </summary>
        public DateTime AssessDateTime { get; set; }
        /// <summary>
        /// 是否需要提醒 0 不提醒 1提醒    
        /// </summary>
        public int? RemindFlag{ get; set; }         
       
        /// <summary>
        /// 新增时间
        /// </summary>
        public DateTime AddDateTime { get; set; }         
      /// <summary>
      /// 科室Code
      /// </summary>
        public string DepartmentCode { get; set; }    
        /// <summary>
        /// 住院唯一号
        /// </summary>
        public string CaseNumber { get; set; }  
        /// <summary>
        /// 修改人员
        /// </summary>
        public string ModifyPersonID { get; set; }   
       
        /// <summary>
        /// 病区Code
        /// </summary>
        public string StationCode { get; set; }  
     
        /// <summary>
        /// 科室名称
        /// </summary>
        public string DepartmentName { get; set; }  
        /// <summary>
        /// 新增人员
        /// </summary>
        public string AddPersonID { get; set; } 
        /// <summary>
        /// 床位号
        /// </summary>
        public string BedNumber { get; set; }   
        /// <summary>
        /// 病区名称
        /// </summary>
        public string StationName { get; set; }  
        /// <summary>
        /// 主治医生ID
        /// </summary>
        public string AttendingPhysicianID { get; set; }   
        /// <summary>
        /// 同步标记
        /// </summary>
        public string DataPumpFlag { get; set; }
        /// <summary>
        /// 同步时间
        /// </summary>
        public DateTime? DataPumpDate { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyDateTime { get; set; } 
        /// <summary>
        /// 主治医生
        /// </summary>
        public string AttendingPhysicianName { get; set; }
        /// <summary>
        /// 分数
        /// </summary>
        public decimal ScorePoint { get; set; }
    }
}

