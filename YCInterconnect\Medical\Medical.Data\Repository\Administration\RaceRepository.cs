﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class RaceRepository : IRaceRepository
    {
        private readonly MedicalDbContext _medicalDbContext;

        public RaceRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<RaceInfo> GetByRaceIDAsync(string race)
        {
            return await _medicalDbContext.RaceInfos.Where(m => m.DeleteFlag != "*"
            && m.RaceID == race).SingleOrDefaultAsync();
        }

        public async Task<List<RaceInfo>> GetListAsync()
        {
            return await _medicalDbContext.RaceInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
        }
    }
}