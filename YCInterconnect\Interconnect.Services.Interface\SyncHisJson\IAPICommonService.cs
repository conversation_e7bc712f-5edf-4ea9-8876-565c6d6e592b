﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Models;

namespace Interconnect.Services.Interface
{
    public interface IAPICommonService
    {

        /// <summary>
        /// 异步GET方式获取平台数据
        /// </summary>
        /// <param name="api"></param>
        /// <param name="dictionarys"></param>
        /// <returns></returns>
        Task<string> GetApiDataHttpGet(string url, string contentType, Dictionary<string, string> headers);
    }
}