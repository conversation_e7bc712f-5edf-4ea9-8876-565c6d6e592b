﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientWoundCareDetailRepository : IPatientWoundCareDetailRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientWoundCareDetailRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 根据伤口评估主表获取明细列表
        /// </summary>
        /// <param name="woundCareMainID"></param>
        /// <param name=""></param>
        /// <returns></returns>
        public async Task<List<PatientWoundCareDetailInfo>> GetByMainIDAsync(string woundCareMainID)
        {
            return await _medicalDbContext.PatientWoundCareDetailInfos.Where(t => t.PatientWoundCareMainID == woundCareMainID && t.DeleteFlag != "*").ToListAsync();
        }
        public async Task<List<PatientWoundCareDetailInfo>> GetStartDetailsByRecordIDAsync(string woundRecordMainID)
        {
            return await (from careMain in _medicalDbContext.PatientWoundCareMainInfos
                          join careDetail in _medicalDbContext.PatientWoundCareDetailInfos
                          on careMain.PatientWoundCareMainID equals careDetail.PatientWoundCareMainID
                          where careMain.PatientWoundRecordID == woundRecordMainID && careMain.DeleteFlag != "*" && careDetail.DeleteFlag != "*"
                          && careMain.RecordsCode.EndsWith("Start")
                          select careDetail).ToListAsync();
        }
        public async Task<List<PatientCareDetailView>> GetDetailByCareMainIDs(List<string> careMainIDs, List<int?> detailGroupIDs)
        {
            return await _medicalDbContext.PatientWoundCareDetailInfos.Where(t => careMainIDs.Contains(t.PatientWoundCareMainID)
            && detailGroupIDs.Contains(t.AssessListGroupID) && t.DeleteFlag != "*")
                .Select(m => new PatientCareDetailView
                {
                    CareMainID = m.PatientWoundCareMainID,
                    AssessListID = m.AssessListID,
                    AssessValue = m.AssessValue,
                    AssessListGroupID = m.AssessListGroupID,
                }).ToListAsync();
        }
        /// <summary>
        /// 根据主表记录ID集合获取明细数据
        /// </summary>
        /// <param name="careMainIDs">主表ID集合</param>
        /// <returns></returns>
        public async Task<List<SpecialListDetailView>> GetWoundDetailViewsByCareMainIDs(List<string> careMainIDs)
        {
            return await _medicalDbContext.PatientWoundCareDetailInfos.Where(t => careMainIDs.Contains(t.PatientWoundCareMainID)
            && t.DeleteFlag != "*")
                .Select(m => new SpecialListDetailView
                {
                    MainID = m.PatientWoundCareMainID,
                    AssessListID = m.AssessListID,
                    AssessValue = m.AssessValue,
                    AssessListGroupID = m.AssessListGroupID,
                }).ToListAsync();
        }
        public async Task<List<PatientWoundCareDetailInfo>> GetDetailsByRecordIDAsync(string woundRecordID)
        {
            return await (from careMain in _medicalDbContext.PatientWoundCareMainInfos
                          join careDetail in _medicalDbContext.PatientWoundCareDetailInfos
                          on careMain.PatientWoundCareMainID equals careDetail.PatientWoundCareMainID
                          where careMain.PatientWoundRecordID == woundRecordID && careMain.DeleteFlag != "*" && careDetail.DeleteFlag != "*"
                          select careDetail).ToListAsync();
        }
        public async Task<int> GetDetailListByRecordAndAssessListIDs(string woundRecordID, List<int> woundList)
        {
            return await (from careMain in _medicalDbContext.PatientWoundCareMainInfos
                          join careDetail in _medicalDbContext.PatientWoundCareDetailInfos
                          on careMain.PatientWoundCareMainID equals careDetail.PatientWoundCareMainID
                          where careMain.PatientWoundRecordID == woundRecordID && woundList.Contains(careDetail.AssessListID) && careMain.DeleteFlag != "*" && careDetail.DeleteFlag != "*"
                          select careDetail.AssessListID).FirstOrDefaultAsync();
        }
    }
}