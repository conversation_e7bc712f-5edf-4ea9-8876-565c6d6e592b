using Interconnect.ViewModels;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Interconnect.Services.Interface
{
    /// <summary>
    /// 输血数据同步到NursingRecordDetail服务接口
    /// </summary>
    public interface IBloodTransfusionSyncService
    {
        /// <summary>
        /// 同步输血数据到NursingRecordDetail
        /// </summary>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <returns>同步结果</returns>
        Task<BloodTransfusionSyncResponse> SyncBloodTransfusionToNursingRecordDetail(DateTime startDateTime, DateTime endDateTime);
        /// <summary>
        /// 同步Wiley补录单病人输血数据到NursingRecordDetail
        /// </summary>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <param name="caseNumber">病案号</param>
        /// <returns>同步结果</returns>
        Task<BloodTransfusionSyncResponse> SyncWileyBloodTransfusionToNursingRecordDetail(DateTime startDateTime, DateTime endDateTime, string caseNumber);
        /// <summary>
        /// 从API获取输血数据
        /// </summary>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <returns>输血数据列表</returns>
        Task<List<BloodTransfusionSyncView>> GetBloodTransfusionDataFromApi(DateTime startDateTime, DateTime endDateTime);
        /// <summary>
        /// 从API获取Wiley补录单病人输血数据
        /// </summary>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <param name="caseNumber">病案号</param>
        /// <returns>输血数据列表</returns>
        Task<List<BloodTransfusionSyncView>> GetWileyBloodTransfusionDataFromApi(DateTime startDateTime, DateTime endDateTime, string caseNumber);
        /// <summary>
        /// 处理输血数据同步到NursingRecordDetail（新增和修改）
        /// </summary>
        /// <param name="bloodTransfusionData">输血数据列表</param>
        /// <returns>处理结果</returns>
        Task<BloodTransfusionSyncResponse> ProcessBloodTransfusionToNursingRecordDetail(List<BloodTransfusionSyncView> bloodTransfusionData);
        /// <summary>
        /// 添加输血数据到NursingRecordDetail
        /// </summary>
        /// <param name="bloodTransfusionData">输血数据</param>
        /// <returns>添加结果</returns>
        Task<bool> AddBloodTransfusionToNursingRecordDetail(BloodTransfusionSyncView bloodTransfusionData);
        /// <summary>
        /// 更新输血数据到NursingRecordDetail
        /// </summary>
        /// <param name="bloodTransfusionData">输血数据</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateBloodTransfusionToNursingRecordDetail(BloodTransfusionSyncView bloodTransfusionData);
        /// <summary>
        /// 检查输血数据在NursingRecordDetail中是否存在
        /// </summary>
        /// <param name="transfusionID">输血单号</param>
        /// <param name="bloodDonorBagsCode">血袋码</param>
        /// <returns>是否存在</returns>
        Task<bool> CheckBloodTransfusionExistsInNursingRecordDetail(string transfusionID, string bloodDonorBagsCode);
    }
}
