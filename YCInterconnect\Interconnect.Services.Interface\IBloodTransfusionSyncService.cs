using System;
using System.Threading.Tasks;

namespace Interconnect.Services.Interface
{
    /// <summary>
    /// 输血数据同步到NursingRecordDetail服务接口
    /// </summary>
    public interface IBloodTransfusionSyncService
    {
        /// <summary>
        /// 同步输血数据到NursingRecordDetail
        /// </summary>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <returns>同步结果</returns>
        Task<bool> SyncTransfusionToRecordDetail(DateTime startDateTime, DateTime endDateTime);
        /// <summary>
        /// 同步单个病人输血数据到NursingRecordDetail
        /// </summary>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <param name="caseNumber">病案号</param>
        /// <returns>同步结果</returns>
        Task<bool> SyncSinglePatientTransfusionToRecordDetail(DateTime startDateTime, DateTime endDateTime, string caseNumber);
    }
}
