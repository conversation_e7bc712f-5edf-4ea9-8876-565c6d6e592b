﻿using Medical.Models;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public class MessageDBContext : DbContext
    {
        #region -- 构造函数
        public MessageDBContext(DbContextOptions<MessageDBContext> options)
           : base(options)
        { }
        #endregion

        #region -- 创建数据库上下文对象时
        protected override void OnModelCreating(ModelBuilder builder)
        {

            base.OnModelCreating(builder);
        }
        #endregion

        #region -- DbSet
        /// <summary>
        /// 推送消息明细
        /// </summary>
        public DbSet<SendMessageDetailInfo> SendMessageDetailInfos { get; set; }
        /// <summary>
        /// 已经推送的消息
        /// </summary>
        public DbSet<SendMessageInfo> SendMessageInfos { get; set; }
        #endregion
    }
}

