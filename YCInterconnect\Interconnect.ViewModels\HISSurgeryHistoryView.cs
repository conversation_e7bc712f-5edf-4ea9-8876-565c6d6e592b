﻿using System.ComponentModel.DataAnnotations.Schema;

namespace ViewModel
{
  public  class HISSurgeryHistoryView
    {
        /// <summary>
        /// 手术唯一Key
        /// </summary>

        public string OperationNo { get; set; }
        /// <summary>
        /// 住院号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 病历号
        /// </summary>
        public string ChartNO { get; set; }
        /// <summary>
        /// 手术日期
        /// </summary>
        public string OperateDate { get; set; }
        /// <summary>
        /// PCSCode
        /// </summary>
        public string ICDPCS { get; set; }
        /// <summary>
        /// 手术次数
        /// </summary>
        public string OperateNum { get; set; }

        /// <summary>
        /// 手术编码
        /// </summary>
        public string OperateCode { get; set; }
        /// <summary>
        /// 手术名称
        /// </summary>
        public string OperateName { get; set; }
        /// <summary>
        /// 手术预约时间
        /// </summary>
        public string ScheduledDatetime { get; set; }
        /// <summary>
        /// 入手术室时间
        /// </summary>
        public string InRoomDateTime { get; set; }
        /// <summary>
        /// 手术开始时间
        /// </summary>
        public string OperationStartDateTime { get; set; }
        /// <summary>
        /// 手术结束时间
        /// </summary>
        public string OperationEndDateTime { get; set; }
        /// <summary>
        /// 出手术室时间
        /// </summary>
        public string OutRoomDateTime { get; set; }
        /// <summary>
        /// 麻醉方式
        /// </summary>
        public string AnesthesiaMethod { get; set; }
        /// <summary>
        /// 入恢复室时间
        /// </summary>
        public string InRecoveryRoomDateTime { get; set; }
        /// <summary>
        /// 出恢复室时间
        /// </summary>
        public string OutRecoveryRoomDateTime { get; set; }
        /// <summary>
        /// 到达手术室时间
        /// </summary>
        public string ArrivedStationDateTime { get; set; }
        /// <summary>
        /// 手术进行状态
        /// </summary>
        public string StatusCode { get; set; }

    }
}
