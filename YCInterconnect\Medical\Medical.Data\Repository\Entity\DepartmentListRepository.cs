﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class DepartmentListRepository : IDepartmentListRepository
    {
        private MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly IOptions<SystemConfig> _systemConfig;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public DepartmentListRepository(
            MedicalDbContext medicalDbContext
            , IMemoryCache memoryCache
            , IOptions<SystemConfig> options
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService
            )
        {
            _medicalDbContext = medicalDbContext;
            _memoryCache = memoryCache;
            _systemConfig = options;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        /// <summary>
        /// 获取科别清单
        /// </summary>
        /// <returns></returns>
        public async Task<List<DepartmentListInfo>> GetAsync()
        {
            return await this.GetAllAsync<DepartmentListInfo>();
        }

        /// <summary>
        /// 获取科别清单,同步使用
        /// </summary>
        /// <returns></returns>
        public List<DepartmentListInfo> GetDepartmentList()
        {
            return (List<DepartmentListInfo>)GetCacheAsync().Result;
        }

        public async Task<List<DepartmentListInfo>> GetDepartmentListAsync()
        {
            return (List<DepartmentListInfo>)await GetCacheAsync();

        }

        /// <summary>
        /// 获取所有科别清单,同步科别使用
        /// </summary>
        /// <returns></returns>
        public List<DepartmentListInfo> GetAllDepartmentList()
        {
            return (List<DepartmentListInfo>)GetCacheAsync().Result;
        }

        /// <summary>
        /// 获取科室明细,同步使用
        /// </summary>
        /// <returns></returns>
        public DepartmentListInfo GetDepartmentInfo(string departmentCode)
        {
            var datas = (List<DepartmentListInfo>)GetCacheAsync().Result;
            return datas.Where(m => m.DepartmentCode == departmentCode).FirstOrDefault();
        }

        /// <summary>
        /// 根据科别代号获取科室（External用）
        /// </summary>
        /// <param name="departCode">科室代号</param>
        /// <returns></returns>
        public async Task<DepartmentListInfo> GetAsync(string departCode)
        {
            var datas = (List<DepartmentListInfo>)await GetCacheAsync();
            return datas.Where(m => m.DepartmentCode.Trim() == departCode).FirstOrDefault();
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<DepartmentListInfo>>(key, GetDataBaseListData);
        }
        /// <summary>
        /// 获取数据库数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            var data = await _medicalDbContext.DepartmentListInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.DeleteFlag != "*").ToListAsync();
            return data;
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.Department.GetKey(_sessionCommonServer);
        }

        public async Task<DepartmentListInfo> GetAsync(int id)
        {
            var data = await this.GetAllAsync<DepartmentListInfo>();
            return data.Find(t => t.ID == id);
        }
        /// <summary>
        /// 获取科室集合对应点的code集合
        /// </summary>
        /// <param name="ids">多个科室ID</param>
        /// <returns>会出现空值</returns>
        public async Task<List<string>> GetAsync(List<int> ids)
        {
            var data = await this.GetAllAsync<DepartmentListInfo>();
            return data.Where(t => ids.Contains(t.ID)).Select(m => m.DepartmentCode).ToList();
        }
        public async Task<int> GetMaxID()
        {
            var data = await this.GetAllAsync<DepartmentListInfo>();
            return data.Max(m => m.ID);
        }

        public async Task<int> GetMaxIDAsync()
        {
            var maxID = await _medicalDbContext.departmentListInfos.MaxAsync(m => (int?)m.ID);
            return maxID ?? 0;
        }

        public async Task<List<SimpleInfo>> GetSimpleListAsync()
        {
            var data = await this.GetAllAsync<DepartmentListInfo>();

            return data.Select(m => new SimpleInfo
            {
                ID = m.ID,
                Name = m.Department,
                Code = m.DepartmentCode
            }).ToList();
        }
        /// <summary>
        /// 根据科室码获取科室ID
        /// </summary>
        /// <param name="departmentCode"></param>
        /// <returns></returns>
        public async Task<int?> GetDeparmentListIDByCodeAsync(string departmentCode)
        {
            var data = await this.GetAllAsync<DepartmentListInfo>();
            return data.Find(m => m.DepartmentCode == departmentCode)?.ID;
        }
        /// <summary>
        /// 获取ID集合获取科室集合
        /// </summary>
        /// <param name="ids">多个科室ID</param>
        /// <returns>会出现空值</returns>
        public async Task<List<KeyValue>> GetDepartmentListDataByIDs(List<int> ids)
        {
            var data = await this.GetAllAsync<DepartmentListInfo>();
            return data.
                Where(t => ids.Contains(t.ID)).
                Select(m => new KeyValue
                {
                    ID = m.ID,
                    Value = m.Department
                }).ToList();
        }
        public async Task<List<DepartmentListInfo>> GetAllDeptCodeAsync()
        {
            var data = await this.GetAllAsync<DepartmentListInfo>();
            return data.Select(s => new DepartmentListInfo
            {
                ID = s.ID,
                DepartmentCode = s.DepartmentCode
            }).ToList();
        }
        /// <summary>
        /// 同步使用 禁止medical使用
        /// </summary>
        /// <returns></returns>
        public List<DepartmentListInfo> GetAllDeparment()
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            return _medicalDbContext.DepartmentListInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").ToList();
        }

        public async Task<List<DepartmentListInfo>> GetDeparmentListNoCache()
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            return await _medicalDbContext.DepartmentListInfos.Where(m => m.HospitalID == hospitalID).ToListAsync();
        }
        /// <summary>
        /// 根据科室Code集合获取数据
        /// </summary>
        /// <param name="departmentCodes"></param>
        /// <returns></returns>
        public async Task<List<DepartmentListInfo>> GetDepartmentByCodeList(List<string> departmentCodes)
        {
            var datas = (List<DepartmentListInfo>)await GetCacheAsync();
            return datas.Where(m => departmentCodes.Any(code => code == m.DepartmentCode)).ToList();
        }
    }
}