﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class EmployeeJobPersonSetRepository : IEmployeeJobPersonSetRepository
    {
        private readonly MedicalDbContext _medicalDbContext;
        private readonly GetCacheService _getCacheService;
        private readonly IMemoryCache _memoryCache;
        public EmployeeJobPersonSetRepository(MedicalDbContext db, IMemoryCache memoryCache, GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _getCacheService = getCacheService;
        }

        public async Task<List<EmployeeJobPersonSet>> GetAsync(int deptmentJobID)
        {
            var data = (List<EmployeeJobPersonSet>)await GetCacheAsync();
            if (data == null)
            {
                return new List<EmployeeJobPersonSet>();
            }
            return data.Where(t => t.DepartmentJobID == deptmentJobID).ToList();
        }

        public async Task<List<EmployeeJobPersonSet>> GetList()
        {
            return await _medicalDbContext.EmployeeJobPersonSets.Where(
               m => m.DeleteFlag != "*").ToListAsync();
        }
        public async Task<EmployeeJobPersonSet> GetList(int ID)
        {
            return await _medicalDbContext.EmployeeJobPersonSets.Where(
                m => m.DeleteFlag != "*" && m.EmployeeJobPersonSetID == ID).SingleAsync();
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<EmployeeJobPersonSet>>(key, GetDataBaseListData);
        }
        /// <summary>
        /// 获取数据库数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            var data = await _medicalDbContext.EmployeeJobPersonSets.Where(m => m.DeleteFlag != "*").ToListAsync();
            return data;
        }


        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.DepartmentJobConfig.ToString();
        }
    }
}