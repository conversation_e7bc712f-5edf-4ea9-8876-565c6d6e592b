﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Interconnect.Data.Repository
{
    public class PatientOrderMainRepository : IPatientOrderMainRepository
    {
        private DataOutConnection _DataOutConnection = null;

        public PatientOrderMainRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }
        /// <summary>
        /// 获取所有没有抽取的病区数据
        /// </summary>
        /// <param name="tongbuCount"></param>
        /// <param name="takeRows"></param>
        /// <returns></returns>
        public List<PatientOrderMainInfo> GetAsync(int tongbuCount, int takeRows)
        {
            return  _DataOutConnection.PatientOrderMains.Where(m => m.DataPumpFlag != "*"
           && ((m.Counts ?? 0) < tongbuCount)).Take(takeRows).ToList();
        }
    }
}