﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class EducationDegreeRepository : IEducationDegreeRepository
    {
        private readonly MedicalDbContext _medicalDbContext;

        public EducationDegreeRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<EducationDegreeInfo> GetByEmployeeBasicIDAndEducationCategoryAsync(int employeeDataID, string educationCategory)
        {
            return await _medicalDbContext.EducationDegreeInfos.Where(
                m => m.DeleteFlag != "*"
                && m.EmployeeDataID == employeeDataID
                && m.EducationCategory == educationCategory).SingleOrDefaultAsync();
        }

        public async Task<List<EducationDegreeInfo>> GetList()
        {
            return await _medicalDbContext.EducationDegreeInfos.Where(
                m => m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<EducationDegreeInfo> GetEducationDegreeInfoByID(int ID)
        {
            return await _medicalDbContext.EducationDegreeInfos.Where(
                m => m.DeleteFlag != "*" && m.EducationDegreeID == ID).SingleAsync();
        }

        public async Task<List<EducationDegreeInfo>> GetListByEmployeeBasicIDAsync(int employeeDataID)
        {
            return await _medicalDbContext.EducationDegreeInfos.Where(
                m => m.DeleteFlag != "*" && m.EmployeeDataID == employeeDataID).ToListAsync();
        }
    }
}