﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("NISOrderDetail")]
    public class NISOrderDetailInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///医嘱key
        ///</summary>
        public string OrderID { get; set; }
        /// <summary>
        ///NISKey
        ///</summary>
        public string NISID { get; set; }
        /// <summary>
        ///医嘱已执行标记
        ///</summary>
        public string PerformedFlag { get; set; }
        /// <summary>
        ///医嘱已执行人员工号 
        ///</summary>
        public string PerformedPersonID { get; set; }
        /// <summary>
        ///医嘱已执行日期时间
        ///</summary>
        public DateTime PerformedDate { get; set; }
    }
}