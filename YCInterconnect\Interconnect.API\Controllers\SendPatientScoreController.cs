﻿using Interconnect.Services.Interface;
using Interconnect.ViewModels;
using Medical.Common;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using System;
using System.Threading.Tasks;


namespace Interconnect.API.Controllers
{
    /// <summary>
    /// 信息同步
    /// </summary>
    [Produces("application/json")]
    [Route("api/PaientScore")]
    [EnableCors("any")]
    public class SendPatientScoreController : Controller
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IInterconnectPatientScoreMainService _interconnectPatientScoreMainService;
        private readonly IJobLogService _jobLogService;
        public SendPatientScoreController(IInterconnectPatientScoreMainService interconnectPatientScoreMainService,
            IJobLogService jobLogService)
        {
            _interconnectPatientScoreMainService = interconnectPatientScoreMainService;
            _jobLogService = jobLogService;
        }
        /// <summary>
        ///  同步风险数据给HIS
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SendPatientScoreToHIS")]
        public async Task<IActionResult> SendPatientScoreToHIS()
        {
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.SendPatientScore).ToString();
            var jobName = "风险数据";
            var jobStatus = false;
            var logMsg = "作业编号:" + guid + "启动同步任务:";
            jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            var resultFlag = false;
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);

                resultFlag = await _interconnectPatientScoreMainService.SendPatientScoreToHIS();
                _jobLogService.RemoveJob(jobId, jobName, "");
            }
            var result = new ResponseResult
            {
                Data = resultFlag,
                Code = 1
            };
            return result.ToJson();
        }
    }
}
