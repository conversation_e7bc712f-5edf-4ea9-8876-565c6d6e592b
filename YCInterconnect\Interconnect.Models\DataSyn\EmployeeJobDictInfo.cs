﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("EmployeeJobDict")]
    public class EmployeeJobDictInfo : ModifyReturnInfo
    {
        /// <summary>
        ///	自增ID	
        ///</summary>
        [Key]
        public int EmployeeJobID { get; set; }
        /// <summary>
        ///医院数据的唯一编码，用来做数据
        ///</summary>
        public string SN { get; set; }
        /// <summary>
        ///	病区编码	
        ///</summary>
        public string WardCode { get; set; }
        /// <summary>
        ///	病区岗位编号(没有不写)	
        ///</summary>
        public string SattionJobID { get; set; }
        /// <summary>
        ///
        ///</summary>
        public string StationJobName { get; set; }
        /// <summary>
        ///	员工基本信息序号	
        ///</summary>
        public string EmployeeID { get; set; }
        /// <summary>
        ///	到职原因（可以为空）	
        ///</summary>
        public string AssumeReason { get; set; }
        /// <summary>
        ///	离职原因（可以为空）	
        ///</summary>
        public string DimissionReason { get; set; }
        /// <summary>
        ///	主要联系标记（可以为空）	
        ///</summary>
        public string PrimaryMark { get; set; }
        /// <summary>
        ///	开始日期（可以为空）	
        ///</summary>
        public DateTime StartDate { get; set; }
        /// <summary>
        ///	结束日期（可以为空）	
        ///</summary>
        public DateTime? EndDate { get; set; }
        /// <summary>
        ///	异动人员	
        ///</summary>
        public string ModifyPersonID { get; set; }
        /// <summary>
        ///	异动日期	
        ///</summary>
        public DateTime? ModifyDate { get; set; }
        /// <summary>
        ///	删除注记	
        ///</summary>
        public string DeleteFlag { get; set; }   
        /// <summary>
        ///	同步次数	
        ///</summary>
        public int? Counts { get; set; }
    }
}