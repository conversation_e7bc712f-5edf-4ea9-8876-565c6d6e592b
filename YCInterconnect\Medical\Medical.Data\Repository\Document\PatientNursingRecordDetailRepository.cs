﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientNursingRecordDetailRepository : IPatientNursingRecordDetailRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientNursingRecordDetailRepository(
            MedicalDbContext medicalDb)
        {
            _medicalDbContext = medicalDb;
        }

        public async Task<List<PatientNursingRecordDetailInfo>> GetNursingRecordDetailByLogID(int logID)
        {
            return await _medicalDbContext.PatientNursingRecordDetailInfos.Where(m => m.LogID == logID).ToListAsync();
        }

        public async Task<PatientNursingRecordDetailInfo> GetNursingRecordDetailBySourceID(string sourceID, int emrFilesID)
        {
            return await _medicalDbContext.PatientNursingRecordDetailInfos.Where(m => m.SourceID == sourceID && m.EmrFilesID == emrFilesID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<List<PatientNursingRecordDetailInfo>> GetNursingRecordDetailBySourceIDs(List<string> sourceIDs, List<int> emrfieldIDs)
        {
            return await _medicalDbContext.PatientNursingRecordDetailInfos.Where(m => sourceIDs.Contains(m.SourceID) && emrfieldIDs.Contains(m.EmrFilesID) && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据患者信息获取需要带入护理记录单的数据
        /// </summary>
        /// <param name="inpatientID">病人ID</param>
        /// <param name="emrfieldIDs">列名ID</param>
        /// <returns></returns>
        public async Task<List<NursingRecordDetailView>> GetNursingRecordDetailByInpatientData(string inpatientID, List<int> emrfieldIDs)
        {
            return await _medicalDbContext.PatientNursingRecordDetailInfos
                 .Where(m => m.InpatientID == inpatientID && emrfieldIDs.Contains(m.EmrFilesID) && m.DeleteFlag != "*" && m.NursingRecordFlag.Value)
                 .Select(m => new NursingRecordDetailView
                 {
                     StationID = m.StationID,
                     EmrFieldID = m.EmrFilesID,
                     DataValue = m.DataValue,
                     PerformPersonID = m.PerformPersonID,
                     SourceID = m.SourceID,
                     PerformDateTime = m.PerformDateTime,
                     EMRSourceListSourceID = m.EMRSourceListSourceID,
                 }).ToListAsync();
        }
        /// <summary>
        /// 根据患者信息和时间获取护理记录的数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="departmentID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public async Task<List<PatientNursingRecordDetailInfo>> GetInfosByDateRange(string inpatientID, int departmentID, DateTime startDate, DateTime endDate)
        {
            return await _medicalDbContext.PatientNursingRecordDetailInfos
                 .Where(m => m.InpatientID == inpatientID
                 && m.DepartmentListID == departmentID
                 && m.PerformDateTime >= startDate
                 && m.PerformDateTime < endDate
                 && m.DeleteFlag != "*").OrderBy(m => m.PerformDateTime).ToListAsync();
        }

        public async Task<List<NursingRecordDetailView>> GetByFieldIDs(string inpatientID, List<int> emrfieldIDs)
        {
            return await _medicalDbContext.PatientNursingRecordDetailInfos
                 .Where(m => m.InpatientID == inpatientID && emrfieldIDs.Contains(m.EmrFilesID) && !string.IsNullOrEmpty(m.DataValue) && m.DeleteFlag != "*").AsNoTracking()
                 .Select(m => new NursingRecordDetailView
                 {
                     ID = m.NursingRecordDetailID,
                     EmrFieldID = m.EmrFilesID,
                     DataValue = m.DataValue,
                     PerformPersonID = m.PerformPersonID,
                     DepartmentListID = m.DepartmentListID,
                     SourceID = m.SourceID,
                     PerformDateTime = m.PerformDateTime,
                     TPRScheduleID = m.TPRScheduleID,
                     //2022-10-17 使用DataType字段存储排程对应的InterventionID，用来处理体温复测相关问题
                     InterventionID = m.ConditionValue,
                     SourceMainID = m.SourceMainID,
                     EMRSourceListSourceID = m.EMRSourceListSourceID,
                     RefillFlag = m.RefillFlag,
                     ConditionType = m.ConditionType,
                     ConditionValue = m.ConditionValue,
                     StationID = m.StationID,
                     BedNumber = m.BedNumber
                 }).ToListAsync();
        }

        public async Task<List<PatientNursingRecordDetailInfo>> GetByDateTimeAndFieldID(string inpatientID, DateTime startDateTime, DateTime endDateTime)
        {
            return await _medicalDbContext.PatientNursingRecordDetailInfos.Where(m => m.InpatientID == inpatientID && m.PerformDateTime > startDateTime && m.PerformDateTime <= endDateTime
                    && m.NursingRecordFlag.HasValue && m.NursingRecordFlag.Value && !string.IsNullOrEmpty(m.DataValue) && m.DeleteFlag != "*").AsNoTracking().ToListAsync();
        }
        /// <summary>
        /// 获取该病人此次住院有记录的所有科室
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<int>> GetHaveRecordAllDepartmentID(string inpatientID)
        {
            return await _medicalDbContext.PatientNursingRecordDetailInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").Select(m => m.DepartmentListID).Distinct().ToListAsync();
        }
        /// <summary>
        /// 获取复测数据
        /// </summary>
        /// <param name="tPRScheduleID"></param>
        /// <param name="emrFilesID"></param>
        /// <returns></returns>
        public async Task<PatientNursingRecordDetailInfo> GetRecordByTprScheduleIDAndEmrFilesID(int tPRScheduleID, int emrFilesID)
        {
            return await _medicalDbContext.PatientNursingRecordDetailInfos.Where(m => m.TPRScheduleID == tPRScheduleID && m.EmrFilesID == emrFilesID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据tPRScheduleID集合获取数据
        /// </summary>
        /// <param name="tPRScheduleIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientNursingRecordDetailInfo>> GetRecordByTprScheduleIDs(List<int> tPRScheduleIDs)
        {
            return await _medicalDbContext.PatientNursingRecordDetailInfos.Where(m => m.TPRScheduleID.HasValue && tPRScheduleIDs.Contains(m.TPRScheduleID.Value) && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 批量获取一个患者的所有Detail数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="departmentListID"></param>
        /// <returns></returns>
        public async Task<List<PatientNursingRecordDetailInfo>> GetNursingRecordDetailByInpatientID(string inpatientID, int departmentListID)
        {
            return await _medicalDbContext.PatientNursingRecordDetailInfos
               .Where(m => m.InpatientID == inpatientID && m.DepartmentListID == departmentListID && m.NursingRecordFlag == true && m.DeleteFlag != "*")
               .ToListAsync();
        }
        /// <summary>
        /// 根据inpatientID获取患者都在哪些病区待过
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<int>> GetDepartmentListIDByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientNursingRecordDetailInfos
                .Where(m => m.InpatientID == inpatientID && m.NursingRecordFlag == true && m.DeleteFlag != "*")
                .Select(m => m.DepartmentListID).Distinct().ToListAsync();
        }

        public async Task<List<PatientNursingRecordDetailInfo>> GetDetails(string inpatientID)
        {
            return await _medicalDbContext.PatientNursingRecordDetailInfos
                 .Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" && m.NursingRecordFlag.Value)
                 .AsNoTracking()
                 .Select(m => new PatientNursingRecordDetailInfo
                 {
                     DepartmentListID = m.DepartmentListID,
                     StationID = m.StationID,
                     BedNumber = m.BedNumber,
                     PerformDateTime = m.PerformDateTime,
                     PerformPersonID = m.PerformPersonID,
                     EmrFilesID = m.EmrFilesID,
                     ConditionValue = m.ConditionValue,
                     SourceID = m.SourceID,
                     DataValue = m.DataValue,
                     ConditionType = m.ConditionType,
                     SourceMainID = m.SourceMainID
                 }).ToListAsync();
        }
        public async Task<List<PatientNursingRecordDetailInfo>> GetDataValueByInpatientID(List<string> inpatientID, List<int> emrfieldIDs
            , DateTime startTime, DateTime endTime)
        {
            return await _medicalDbContext.PatientNursingRecordDetailInfos
                 .Where(m => inpatientID.Contains(m.InpatientID) && emrfieldIDs.Contains(m.EmrFilesID) && m.PerformDateTime >= startTime && m.PerformDateTime <= endTime && m.DeleteFlag != "*")
                 .Select(m => new PatientNursingRecordDetailInfo
                 {
                     InpatientID = m.InpatientID,
                     EmrFilesID = m.EmrFilesID,
                     DataValue = m.DataValue,
                     PerformDateTime = m.PerformDateTime,
                     StationID = m.StationID
                 }).ToListAsync();
        }
        public async Task<List<PatientNursingRecordDetailInfo>> GetViewByParam(string inpatientID, List<int> emrfieldIDs
        , DateTime startTime, DateTime endTime)
        {
            return await _medicalDbContext.PatientNursingRecordDetailInfos
                 .Where(m => m.InpatientID== inpatientID && emrfieldIDs.Contains(m.EmrFilesID) && m.PerformDateTime >= startTime && m.PerformDateTime <= endTime && m.DeleteFlag != "*")
                 .Select(m => new PatientNursingRecordDetailInfo
                 {
                     InpatientID = m.InpatientID,
                     EmrFilesID = m.EmrFilesID,
                     DataValue = m.DataValue,
                     PerformDateTime = m.PerformDateTime,
                     StationID = m.StationID,
                     DepartmentListID = m.DepartmentListID,
                     BedID = m.BedID,
                     BedNumber = m.BedNumber,
                     PerformPersonID = m.PerformPersonID,
                     NursingRecordDetailID = m.NursingRecordDetailID
                 }).ToListAsync();
        }
        public async Task<List<PatientNursingRecordDetailView>> GetSinglePersonDataValue(string inpatientID, List<int> emrfieldIDs
           , DateTime startTime, DateTime endTime)
        {
            return await _medicalDbContext.PatientNursingRecordDetailInfos
                 .Where(m => m.InpatientID == inpatientID && emrfieldIDs.Contains(m.EmrFilesID) && m.PerformDateTime >= startTime && m.PerformDateTime <= endTime && m.DeleteFlag != "*")
                 .Select(m => new PatientNursingRecordDetailView
                 {
                     EmrFilesID = m.EmrFilesID,
                     DataValue = m.DataValue,
                     PerformDateTime = m.PerformDateTime
                 }).ToListAsync();
        }

        public async Task<List<PatientNursingRecordDetailInfo>> GetByParam(string inpatientID, List<string> sourceIDs)
        {
            return await _medicalDbContext.PatientNursingRecordDetailInfos.Where(m => m.InpatientID == inpatientID && sourceIDs.Contains(m.SourceID) && m.DeleteFlag != "*").ToListAsync();
        }
        public async Task<List<PatientNursingRecordDetailInfo>> GetDetailsTrace(string inpatientID)
        {
            return await _medicalDbContext.PatientNursingRecordDetailInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" && m.NursingRecordFlag.Value).ToListAsync();
        }
        /// <summary>
        /// 根据来源ID获取单病人数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="sourceID"></param>
        /// <returns></returns>
        public async Task<PatientNursingRecordDetailInfo> GetSinglePersonBysourceID(string inpatientID, string sourceID)
        {
            return await _medicalDbContext.PatientNursingRecordDetailInfos.Where(m => m.InpatientID == inpatientID && m.SourceID == sourceID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据数据来源类别获取明细
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="conditionType">数据来源类别：IntakeOutputsetting、TubeList、ObserveTemplate...</param>
        /// <param name="conditionValues">数据类型，对特殊数据进行处理，如出入量数据，不能进行合并处理</param>
        /// <returns></returns>
        public async Task<List<NursingRecordDetailView>> GetViewsByCondition(string inpatientID, string conditionType, params int[] conditionValues)
        {
            var conditions = conditionValues.Select(m => m.ToString()).ToArray();
            return await _medicalDbContext.PatientNursingRecordDetailInfos.Where(m =>
            m.InpatientID == inpatientID && m.ConditionType == conditionType && conditions.Contains(m.ConditionValue) &&
            m.DeleteFlag != "*").Select(m => new NursingRecordDetailView
            {
                PerformDateTime = m.PerformDateTime,
                EmrFieldID = m.EmrFilesID,
                DataValue = m.DataValue,
                PerformPersonID = m.PerformPersonID,
            }).ToListAsync();
        }
        /// <summary>
        /// 根据来源主表ID获取明细数据
        /// </summary>
        /// <param name="inpatientID">病人住院序号</param>
        /// <param name="sourceMainIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientNursingRecordDetailInfo>> GetNursingRecordDetailBySourceMainIDs(string inpatientID, List<string> sourceMainIDs)
        {
            return await _medicalDbContext.PatientNursingRecordDetailInfos.Where(m =>
            m.InpatientID == inpatientID && sourceMainIDs.Contains(m.SourceMainID) && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据EmrFilesID获取数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="emrfieldIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientNursingRecordDetailInfo>> GetDataByEmrFilesID(string inpatientID, List<int> emrfieldIDs)
        {
            return await _medicalDbContext.PatientNursingRecordDetailInfos
                 .Where(m => m.InpatientID == inpatientID && emrfieldIDs.Contains(m.EmrFilesID) && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<PatientNursingRecordDetailInfo> GetDataByID(int id)
        {
            return await _medicalDbContext.PatientNursingRecordDetailInfos
                 .Where(m => m.NursingRecordDetailID == id  && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
    }
}