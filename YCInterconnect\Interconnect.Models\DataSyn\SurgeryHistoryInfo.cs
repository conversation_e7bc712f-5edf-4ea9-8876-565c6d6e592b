﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("SurgeryHistory")]
    public class SurgeryHistoryInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///病案号
        ///</summary>
        public string ChartNo { get; set; }
        /// <summary>
        ///PCSCode
        ///</summary>
        public string ICDPCS { get; set; }
        /// <summary>
        ///日期
        ///</summary>
        public DateTime SurgeryDate { get; set; }
        /// <summary>
        ///手术名称
        ///</summary>
        public string OperativeName { get; set; }
    }
}