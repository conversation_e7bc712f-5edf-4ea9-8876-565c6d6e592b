﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Interconnect.Data.Interface;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using NLog;
using System.Linq;
using Microsoft.Extensions.Options;
using Medical.Data.Context;
using Interconnect.Data.Context;
using Medical.ViewModels.Interface;
using Medical.Common;
using MedicalExternalCommon.Service;
using Arch.EntityFrameworkCore.UnitOfWork;

namespace Interconnect.Services
{
    public class PatientOrderDetailService : IPatientOrderDetailService
    {
        //Mdeical
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly Medical.Data.Interface.IPatientOrderDetailRepository _IMedicalOrderDetailRepository;
        private readonly IPatientOrderRepository _IMedicalOrderMainRepository;
        private readonly IPhysicianOrderRepository _physicianOrderRepository;
        private readonly IOrderToAssessListRepository _orderToAssessListRepository;
        private readonly IPhysicianToInterventionRepository _physicianToIntervention;
        private readonly IAssessListRepository _assessListRepository;
        //Interconnect
        private readonly IUnitOfWork<DataOutConnection> _unitOfWorkOut;
        private readonly IOptions<SystemConfig> _config;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly Data.Interface.IPatientOrderDetailRepository _IShzsyyOrderDetailRepository;
        private readonly ILogInfoServices _ILogInfoServices;
        List<PatientProfile> patientProfilesList;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly ICommonHelper _commonHelper;
        private readonly ExternalProfileCommonService _externalProfileCommonService;

        /// <summary>
        /// 医疗院代码
        /// </summary>
        private string HOSPITALID = "";
        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";
        /// <summary>
        /// 系统默认操作人
        /// </summary>
        private string MODIFYPERSONID = "";

        public PatientOrderDetailService(
              IUnitOfWork<MedicalDbContext> UnitOfWork
            , IUnitOfWork<DataOutConnection> UnitOfWorkOut
            , IOptions<SystemConfig> config
            , Medical.Data.Interface.IPatientOrderDetailRepository MedicalOrderDetailRepository
            , Data.Interface.IPatientOrderDetailRepository ShzsyyOrderDetailRepository
            , IPatientOrderRepository MedicalOrderMainRepository
            , IPhysicianOrderRepository PhysicianOrderRepository
            , ILogInfoServices LogInfoServices
            , IOrderToAssessListRepository orderToAssessListRepository
            , ICommonHelper commonHelper
            , IPhysicianToInterventionRepository physicianToInterventionRepository
            , IAppConfigSettingRepository  appConfigSettingRepository
            , IAssessListRepository assessListRepository
            , ExternalProfileCommonService externalProfileCommonService
            )
        {
            _unitOfWork = UnitOfWork;
            _unitOfWorkOut = UnitOfWorkOut;
            _config = config;
            _IMedicalOrderDetailRepository = MedicalOrderDetailRepository;
            _IShzsyyOrderDetailRepository = ShzsyyOrderDetailRepository;
            _IMedicalOrderMainRepository = MedicalOrderMainRepository;
            _physicianOrderRepository = PhysicianOrderRepository;
            _ILogInfoServices = LogInfoServices;
            _orderToAssessListRepository = orderToAssessListRepository;
            _commonHelper = commonHelper;
            _physicianToIntervention = physicianToInterventionRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
            _assessListRepository = assessListRepository;
            _externalProfileCommonService = externalProfileCommonService;        
        }
        
        /// <summary>
        /// 获取没有同步的所有医嘱Detail
        /// </summary>
        /// <returns></returns>
        public List<Models.PatientOrderDetailInfo> GetAllAsync()
        {
            //从配置档中获取数据 梁宝华 2020-04-29
            var resultTongbuCount = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "TongbuCount").Result;
            var tongbuCount = 0;
            if (StringCheck.IsNumeric(resultTongbuCount))
            {
                tongbuCount = int.Parse(resultTongbuCount);
            }
            var resultTakeRows = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "TakeRows").Result;
            var takeRows = 0;
            if (StringCheck.IsNumeric(resultTakeRows))
            {
                takeRows = int.Parse(resultTakeRows);
            }

            List<Models.PatientOrderDetailInfo> ResultList = _IShzsyyOrderDetailRepository.GetAsync(tongbuCount, takeRows);
            return ResultList;
        }

        /// <summary>
        /// 如果发现有没有同步的数据，则一直获取，直至完全同步
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SynchronizationMain()
        {
            var OriginalList = GetAllAsync();
            var orderToAssessList = _orderToAssessListRepository.GetAsync().Result;
            var physicianToIntervention =await _physicianToIntervention.GetAllData();
            while (OriginalList.Count > 0) //如果没有同步完成，则继续同步
            {
                if (!SynchronizationDetail(OriginalList, orderToAssessList, physicianToIntervention))
                {
                    return false;
                }
                OriginalList = GetAllAsync();
            }
            return true;
        }

        private bool SynchronizationDetail(List<Models.PatientOrderDetailInfo> OriginalList
            , List<OrderToAssessListInfo> orderToAssessList, List<PhysicianToInterventionInfo> PhysicianToIntervention)
        {
            #region "数据准备"
            string Tablename = "PatientOrderDetail";
            List<LogInfo> LogList = new List<LogInfo>();
            LogInfo TempLog = null;
            int Failcount = 0;

            //从配置当中获取数据 梁宝华 2020-04-29
            int AlllogSetFlag = 0;
            var resultAllLogSet = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "AllLogSet").Result;
            if (StringCheck.IsNumeric(resultAllLogSet))
            {
                AlllogSetFlag = int.Parse(resultAllLogSet);
            }

            TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, " 开始进行数据同步，数据条数：" + OriginalList.Count);
            LogList.Add(TempLog);

            List<Medical.Models.PatientOrderDetailInfo> Insertlist = new List<Medical.Models.PatientOrderDetailInfo>();
            Medical.Models.PatientOrderDetailInfo t = null;

            #endregion
            #region "数据同步"
            foreach (var item in OriginalList)
            {

                item.Counts = item.Counts ?? 0;
                item.Counts = item.Counts + 1;

                try
                {
                    #region "单条数据准备"
                    //获取Medical OrderMain中的医嘱PatientOrderMainID，没有医嘱主记录，不同步，并且不写日志
                    //获取Medical表中的医嘱主记录
                    List<Medical.Models.PatientOrderMainInfo> TempOrderMain = _IMedicalOrderMainRepository.GetPatientByOneOrder(item.OrderID);
                    if (TempOrderMain.Count != 1)
                    {
                        if (AlllogSetFlag == 1)
                        {
                            TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, "表: PatientOrderMain[" + item.OrderID + "]查询错误!");
                            LogList.Add(TempLog);
                        }
                        Failcount++;
                        continue;
                    }

                    //获得医嘱类别对照字典PhysicianOrder
                    var TempOrderList = PhysicianToIntervention.Where(m => m.OrderCode == item.OrderCode).ToList();
                    if (TempOrderList.Count != 1)
                    {
                        TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, "OrderID [" + item.OrderID + "] 表: PhysicianOrder[" + item.OrderCode + "]查询信息错误!");
                        LogList.Add(TempLog);
                        Failcount++;
                        continue;
                    }

                    //获取Medical PatientOrderDetail 中的医嘱明细
                    List<Medical.Models.PatientOrderDetailInfo> TempOrderDetail = _IMedicalOrderDetailRepository.GetOneOrderDetail
                        (TempOrderMain[0].PatientOrderMainID, item.OrderID, TempOrderList[0].OrderCode);

                    if (TempOrderDetail.Count > 1)
                    {
                        TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, "表: PatientOrderDetail" + item.OrderID + " 查询错误!");
                        LogList.Add(TempLog);
                        Failcount++;
                        continue;
                    }

                    //获取插入的数据,避免数据重复插入
                    var TempInsertlist = Insertlist.Where(m => m.PatientOrderMainID == TempOrderMain[0].PatientOrderMainID
                    && m.OrderCode == TempOrderList[0].OrderCode && m.OrderID == item.OrderID).ToList();


                    #endregion

                    if (TempOrderDetail.Count < 1 && TempInsertlist.Count < 1)
                    {
                        t = new Medical.Models.PatientOrderDetailInfo();
                        {
                            t.PatientOrderDetailID = t.GetId();  //医嘱明细档序号
                            t.PatientOrderMainID = TempOrderMain[0].PatientOrderMainID;  //医嘱主序号
                            t.OrderID = item.OrderID; //序号
                            t.OrderCode = TempOrderList[0].OrderCode;   //医嘱编码
                            t.OrderPattern = item.OrderPattern;    //医嘱类别
                            t.OrderContent = item.OrderContent;    //医嘱内容
                            t.OrderDose = item.OrderDose;//每次執行量/剂量
                            t.Frequency = item.Frequency;  //频次
                            t.Unit = item.Unit;  //单位
                            t.TotalVolume = item.TotalVolume;//总剂量
                            t.OrderRule = item.OrderRule;  //服法/途径/姿势
                            t.Location = item.Location;//部位
                            t.SpecimenCategory = item.MethodCategory;   //检验类别
                            t.OrderType = TempOrderMain[0].OrderType;
                            if (item.NumberOfExecution != null)
                            {
                                t.NumberOfExecution = (byte)item.NumberOfExecution;//执行次数
                            }

                            t.StartDate = item.StartDate; //开始日期
                            t.StartTime = item.StartTime; //开始时间
                            t.EndDate = item.EndDate; //结束日期
                            t.EndTime = item.EndTime; //结束时间
                            t.ModifyPersonID = MODIFYPERSONID;
                            t.ModifyDate = DateTime.Now;
                            t.DeleteFlag = "";
                        };
                        Insertlist.Add(t);
                        //查找是否存在AssessListID
                        var assessListToOrderCode = orderToAssessList.Where(m => m.OrderCode == t.OrderCode).ToList();
                        if (assessListToOrderCode.Count > 0)
                        {
                            foreach (var code in assessListToOrderCode)
                            {
                                AddProfile(TempOrderMain[0], t, code.AssessListID);
                            }
                        }
                    }
                    if (TempOrderDetail.Count == 1) //如果查询到这条记录，则更新信息      
                    {

                        TempOrderDetail[0].OrderCode = item.OrderCode; //医嘱编码
                        TempOrderDetail[0].OrderPattern = item.OrderPattern; //医嘱类别
                        TempOrderDetail[0].OrderContent = item.OrderContent; //医嘱内容
                        TempOrderDetail[0].OrderDose = item.OrderDose; //每次執行量/ 剂量
                        TempOrderDetail[0].Frequency = item.Frequency; //频次
                        TempOrderDetail[0].Unit = item.Unit; //单位
                        TempOrderDetail[0].TotalVolume = item.TotalVolume; //总剂量
                        TempOrderDetail[0].OrderRule = item.OrderRule; //服法/ 途径 / 姿势
                        TempOrderDetail[0].Location = item.Location; //部位
                        TempOrderDetail[0].SpecimenCategory = item.MethodCategory; //检验类别
                        TempOrderDetail[0].OrderType = TempOrderMain[0].OrderType;
                        // TempOrderDetail[0].NumberOfExecution = (byte)item.NumberOfExecution; //执行次数
                        if (item.NumberOfExecution != null)
                        {
                            TempOrderDetail[0].NumberOfExecution = (byte)item.NumberOfExecution;//执行次数
                        }
                        TempOrderDetail[0].StartDate = item.StartDate; //开始日期
                        TempOrderDetail[0].StartTime = item.StartTime; //开始时间
                        TempOrderDetail[0].EndDate = item.EndDate; //结束日期
                        TempOrderDetail[0].EndTime = item.EndTime; //结束时间
                        TempOrderDetail[0].ModifyPersonID = MODIFYPERSONID; //异动人员
                        TempOrderDetail[0].ModifyDate = DateTime.Now; //异动时间
                        TempOrderMain[0].DeleteFlag = "";
                        _unitOfWork.GetRepository<Medical.Models.PatientOrderDetailInfo>().Update(TempOrderDetail);
                    };
                    item.DataPumpFlag = "*";
                    item.DataPumpDate = DateTime.Now;

                }
                catch (Exception ex)
                {
                    _logger.Error("表 PatientOrderDetail 同步错误：OrderID [" + item.OrderID + "]|| OrderCode[" + item.OrderCode + "]" + ex.ToString());
                    return false;
                }

            }
            #endregion 
            #region "数据更新"
            if (OriginalList.Count >= 1)
            {
                try
                {
                    _unitOfWork.GetRepository<Medical.Models.PatientOrderDetailInfo>().Insert(Insertlist);
                    // _unitOfWorkOut.GetRepository<Models.PatientOrderDetailInfo>().Update(OriginalList); 
                    _unitOfWork.SaveChanges();
                    // _unitOfWorkOut.SaveChanges();
                }
                catch (Exception ex)
                {
                    _logger.Error(Tablename + "||同步失败||" + ex.ToString());
                    return false;
                }

            }

            TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, " 同步结束 成功：" + (OriginalList.Count - Failcount).ToString() + "条！ 失败：" + Failcount.ToString() + "条！");

            LogList.Add(TempLog);
            int ItemNo = 0;
            string Guid = "";
            Guid = System.Guid.NewGuid().ToString("N");
            // item.Guid = Guid;
            foreach (var item in LogList)
            {
                item.Guid = Guid;
                item.ItemNo = ItemNo;
                ItemNo++;
            }
            try
            {
                _unitOfWorkOut.GetRepository<LogInfo>().Insert(LogList);
                // await _unitOfWorkOut.SaveChangesAsync();
                _unitOfWorkOut.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error(Tablename + "同步成功，但写同步日志失败||" + ex.ToString());
            }
            _logger.Info(Tablename + "    同步完成!");
            if (patientProfilesList != null)
            {
                var deitSettings = _assessListRepository.GetBySystemCode("601", 1).Result;

                var inpatients = patientProfilesList.Select(m => m.InpatientID).ToList();

                foreach (var ids in inpatients)
                {
                    var temp = patientProfilesList.Where(m => m.InpatientID == ids).Select(m => m.AssessListID).ToList();

                    _externalProfileCommonService.DelDietProfile(ids, temp, deitSettings);
                }

                _commonHelper.AddProfile(patientProfilesList);
            }

            return true;
            #endregion
        }

        #region --添加Profile
        private void AddProfile(Medical.Models.PatientOrderMainInfo orderMainInfo,
            Medical.Models.PatientOrderDetailInfo orderDetailInfo, int assessListID)
        {
            var tmp = patientProfilesList.Find(m => m.SerialNumber == orderDetailInfo.PatientOrderDetailID);
            if (tmp != null)
            {
                return;
            }

            var patientProfile = new PatientProfile
            {
                HospitalID = HOSPITALID,
                InpatientID = orderMainInfo.InpatientID,
                CaseNumber = orderMainInfo.CaseNumber,
                PatientID = orderMainInfo.PatientID,

                ModelName = "HIS.Order",
                Source = "I",
                ProfileDate = orderMainInfo.StartDate,
                ProfileTime = orderMainInfo.StartTime,

                AutoAddFlag = "",
                Note = "",
                ModifyPersonID = MODIFYPERSONID,
                ModifyDate = DateTime.Now
            };

            patientProfile.SerialNumber = orderDetailInfo.PatientOrderDetailID;
            patientProfile.AssessListID = assessListID;

            patientProfilesList.Add(patientProfile);
        }
        #endregion
    }
}
