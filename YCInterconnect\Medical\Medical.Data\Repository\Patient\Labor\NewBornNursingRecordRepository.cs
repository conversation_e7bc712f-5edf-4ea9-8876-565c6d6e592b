﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.Models.Patient;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    /// <summary>
    /// 维护记录副表,新生儿记录仓储层
    /// </summary>
    public class NewBornNursingRecordRepository : INewBornNursingRecordRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;
        private readonly IInpatientDataRepository _inpatientDataRepository;

        public NewBornNursingRecordRepository(MedicalDbContext db, IInpatientDataRepository inpatientDataRepository)
        {
            _medicalDbContext = db;
            _inpatientDataRepository = inpatientDataRepository;
        }

        /// <summary>
        /// 根据维护记录ID集合获取新生儿护理记录数据
        /// </summary>
        /// <param name="careMainInfos">维护记录主表集合</param>
        /// <returns></returns>
        public async Task<List<dynamic>> GetViewsByCareMainIDs(List<PatientDeliveryCareMainInfo> careMainInfos)
        {
            var careMainIDs = careMainInfos.Select(m => m.PatientDeliveryCareMainID);
            var newBornNursingRecordCareMainInfos = await _medicalDbContext.NewBornNursingRecordCareMainInfos.Where(m => careMainIDs.Contains(m.PatientDeliveryCareMainID)).ToListAsync();
            var newbornInpatients = await _inpatientDataRepository.GetInpatientDataByCaseNumbers(careMainInfos.Select(m => m.CaseNumber));
            var newbornNursingRecordCareMainViews = (from m in newBornNursingRecordCareMainInfos
                                                     from n in careMainInfos
                                                     from o in newbornInpatients
                                                     where m.PatientDeliveryCareMainID == n.PatientDeliveryCareMainID &&
                                                     n.CaseNumber == o.CaseNumber
                                                     select (dynamic)new NewBornNursingRecordCareMainView
                                                     {
                                                         PatientDeliveryCareMainID = m.PatientDeliveryCareMainID,
                                                         InpatientID = n.InpatientID,
                                                         PatientID = o.PatientID,
                                                         ChartNo = o.ChartNo,
                                                         CaseNumber = o.CaseNumber,
                                                         Age = o.Age,
                                                         DateOfBirth = o.DateOfBirth,
                                                         NusingLevel = o.NursingLevel,
                                                         BedID = o.BedID,
                                                         BedNumber = o.BedNumber,
                                                         NewBornID = m.NewBornID,
                                                         StationID = n.StationID,
                                                         DepartmentListID = n.DepartmentListID,
                                                         AssessDate = n.AssessDate,
                                                         AssessTime = n.AssessTime,
                                                         Temperature = m.Temperature,
                                                         Complexion = m.Complexion,
                                                         Cry = m.Cry,
                                                         Umbilicus = m.Umbilicus,
                                                         TcBForehead = m.TcBForehead,
                                                         TcBCheek = m.TcBCheek,
                                                         TcBSternum = m.TcBSternum,
                                                         FeedingMethod = m.FeedingMethod,
                                                         NumberOfBowel = m.NumberOfBowel,
                                                         NumberOfUrinations = m.NumberOfUrinations,
                                                         BodyWeight = m.BodyWeight,
                                                         BloodGlucose = m.BloodGlucose,
                                                         Notes = m.Notes,
                                                         UserID = n.AddEmployeeID,
                                                         BringToShift = n.BringToShift,
                                                         InformPhysician = n.InformPhysician,
                                                         BringToNursingRecord = n.BringToNursingRecord
                                                     }).ToList();
            return newbornNursingRecordCareMainViews;
        }
        /// <summary>
        /// 根据维护记录ID获取副表数据
        /// </summary>
        /// <param name="careMainID">维护记录ID</param>
        /// <returns></returns>
        public async Task<dynamic> GetByCareMainID(string careMainID)
        {
            return await _medicalDbContext.NewBornNursingRecordCareMainInfos.Where(m => careMainID == m.PatientDeliveryCareMainID)
                .Select(m => (dynamic)m).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据新生儿ID查询出对应的新生儿护理记录数据
        /// </summary>
        /// <param name="newBornID">新生儿ID</param>
        /// <returns></returns>
        public async Task<List<string>> GetDeliveryCareMainIDsByNewBornID(string newBornID)
        {
            return await _medicalDbContext.NewBornNursingRecordCareMainInfos.Where(m =>
            newBornID == m.NewBornID).Select(m => m.PatientDeliveryCareMainID).ToListAsync();
        }
        /// <summary>
        /// 根据新生儿NewBornID获取新生儿护理记录
        /// </summary>
        /// <param name="newBornID"></param>
        /// <returns></returns>
        public async Task<List<NewBornNursingRecordCareMainInfo>> GetByNewBornID(string newBornID)
        {
            return await (from a in _medicalDbContext.NewBornNursingRecordCareMainInfos
                          join b in _medicalDbContext.PatientDeliveryCareMainInfos
                          on a.PatientDeliveryCareMainID equals b.PatientDeliveryCareMainID
                          where a.NewBornID == newBornID && b.DeleteFlag != "*"
                          select a).ToListAsync();
        }
    }
}
