﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Interconnect.Data.Context;
using Interconnect.Data.Interface;
using Interconnect.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Interconnect.Data
{
    public class InterconnectNurseShiftRepository : IInterconnectNurseShiftRepository
    {
        private DataOutConnection _DataOutConnection = null;
        public InterconnectNurseShiftRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }

        /// <summary>
        /// 获取所有科室信息
        /// </summary>
        /// <returns></returns>
        public  List<NurseShiftInfo> GetAsync()
        {
            return  _DataOutConnection.nurseShiftInfos.ToList();
        }

        /// <summary>
        /// 获取未同步的科室基本信息
        /// </summary>
        /// <param name="tongbuCount"></param>
        /// <returns></returns>
        public List<NurseShiftInfo> GetDataPump(int tongbuCount)
        {
            try
            {
                return _DataOutConnection.nurseShiftInfos.Where(m => m.DataPumpFlag != "*"
                  && ((m.Counts ?? 0) < tongbuCount)).ToList();
            }
            catch (System.Exception ex)
            {

                throw ex;
            }

        }
    }
}
