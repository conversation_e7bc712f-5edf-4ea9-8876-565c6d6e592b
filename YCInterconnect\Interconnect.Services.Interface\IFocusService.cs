﻿using System;
using System.Threading.Tasks;

namespace Interconnect.Services.Interface
{
    public interface IFocusService
    {
        /// <summary>
        /// 获取医院字典数据
        /// </summary>
        /// <returns></returns>
        bool SyncInpatientBaseDict();

        /// <summary>
        /// 获取新入院病人数据
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncNewInPatient();

        /// <summary>
        /// 同步所有在院病人数据
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncInPatientByStationGroup(int StationGroup);

        /// <summary>
        /// / 根据病区code 同步在院病人
        /// </summary>
        /// <param name="stationCode"></param>
        /// <returns></returns>
        Task<bool> SyncInPatientByStationCode(string stationCode);

        /// <summary>
        /// 同步历史 医嘱数据
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncHistoryPatientOrder();

        /// <summary>
        ///  同步历史 医嘱数据手动
        /// </summary>
        /// <param name="startDateTime"></param>
        /// <param name="endDateTime"></param>
        /// <param name="apiAddress"></param>
        /// <param name="hospitalID"></param>
        /// <param name="syncStopFlag">是否只同步停止医嘱</param>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        Task<bool> SyncHisOrderByManual(DateTime startDateTime, DateTime endDateTime, string apiAddress, string hospitalID, bool syncStopFlag, string caseNumber);

        /// <summary>
        /// 获取 检验数据
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncTestReportByCaseNumber(string CaseNumber);

        Task<bool> SyncTestReportByDateTime();

        /// <summary>
        /// 获取员工基本信息
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncEmployee();

        /// <summary>
        /// 获取床位
        /// </summary>
        bool SyncBed();

        /// <summary>
        /// 获取病区
        /// </summary>
        bool SyncStation();

        /// <summary>
        /// 获取科室
        /// </summary>
        bool SyncDepartmen();

        /// <summary>
        /// 病人基本信息
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncPatientBasic(string chartNO);

        /// <summary>
        /// 员工与病区对照信息
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncEmployeeStationSwitch();

        /// <summary>
        /// 科室与病区对照信息
        /// </summary>
        /// <returns></returns>
        bool SyncWardDept();

        /// <summary>
        /// 同步医嘱字典
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncOrderDict();

        /// <summary>
        /// 同步病人主诉
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncPatientChiefComplaint(int? stationID);

        /// <summary>
        /// 同步医嘱数据
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncPatientOrderByDateTime();

        /// <summary>
        /// 出院病人同步,同步一段时间内的出院病人数据
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncDischargedPatientsByDateTime();

        /// <summary>
        /// 同步一段时间内的给药记录数据
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncMedicineRecordByDateTimeAsync();

        /// <summary>
        ///病人出院病人数据补救
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncDisChargeInPatientByDatetimeSupplement();

        /// <summary>
        /// 同步手术记录,同步一段时间内的手术数据
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncOperateDataByDateTime();

        /// <summary>
        /// 根据时间段同步出院患者（刷新用）
        /// </summary>
        /// <param name="startDateTime"></param>
        /// <param name="endDateTime"></param>
        /// <returns></returns>
        Task<bool> SyncDischargedByDateTime(DateTime? startDateTime, DateTime? endDateTime);

        /// <summary>
        /// 同步母婴关系
        /// </summary>
        /// <param name="stationCode">有母婴关系的病区码</param>
        /// <returns></returns>
        Task<bool> SyncMotherAndChildList(string stationCode);
    }
}