﻿using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Interconnect.Models;


namespace Interconnect.Data.Context
{
    public partial class DataOutConnection : DbContext
    {
        public DataOutConnection(DbContextOptions<DataOutConnection> options)
           : base(options)
        { }
        protected override void OnModelCreating(ModelBuilder builder)
        {
            builder.Entity<LogInfo>().<PERSON><PERSON><PERSON>(t => new { t.Guid, t.ItemNo });
            base.OnModelCreating(builder);

            builder.Entity<NurseHeatRecordInfo>().<PERSON><PERSON><PERSON>(t => new { t.CureNo, t.Date,t.Time });
            base.OnModelCreating(builder);

            builder.Entity<SettingDescriptionInfo>().<PERSON><PERSON><PERSON>(t => new { t.SettingType, t.TypeCode});
            base.OnModelCreating(builder);

        }
          
    }
}
