﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    /// <summary>
    /// 入院记录
    /// </summary>
    [Serializable]
    [Table("Inpatient")]
    public class InpatientInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///住院号
        ///</summary>
        public string CaseNumber { get; set; }
        /// <summary>
        ///病案号
        ///</summary>
        public string ChartNo { get; set; }
        /// <summary>
        ///身分证号
        ///</summary>
        public string IdentityID { get; set; }
        /// <summary>
        ///住院次数
        ///</summary>
        public int? NumberOfAdmissions { get; set; }
        /// <summary>
        ///科别
        ///</summary>
        public string Department { get; set; }
        /// <summary>
        ///科别代码
        ///</summary>
        public string DepartmentCode { get; set; }
        /// <summary>
        ///病区(护理单元)

        ///</summary>
        public string StationName { get; set; }
        /// <summary>
        ///病区(护理单元)代码
        ///</summary>
        public string StationCode { get; set; }
        /// <summary>
        ///床位号码
        ///</summary>
        public string BedNumber { get; set; }
        /// <summary>
        ///床位代码
        ///</summary>
        public string BedCode { get; set; }
        /// <summary>
        ///ICU注记
        ///</summary>
        public string ICUFlag { get; set; }
        /// <summary>
        ///诊断码
        ///</summary>
        public string ICDCode { get; set; }
        /// <summary>
        ///诊断
        ///</summary>
        public string Diagnosis { get; set; }
        /// <summary>
        ///主治医师工号
        ///</summary>
        public string AttendingPhysicianID { get; set; }
        /// <summary>
        ///护理级别
        ///</summary>
        public string NursingLevel { get; set; }
        /// <summary>
        ///护理等级代码

        ///</summary>
        public string NursingLevelCode { get; set; }
        /// <summary>
        ///费用类型

        ///</summary>
        public string BillingPattern { get; set; }
        /// <summary>
        ///入院日期
        ///</summary>
        public DateTime AdmissionDate { get; set; }
        /// <summary>
        ///入院时间
        ///</summary>
        public TimeSpan AdmissionTime { get; set; }
        /// <summary>
        ///出院日期
        ///</summary>
        public DateTime? DischargeDate { get; set; }
        /// <summary>
        ///出院时间
        ///</summary>
        public TimeSpan? DischargeTime { get; set; }

        /// <summary>
        /// 在院标志，1标志在院，0表示出院
        /// </summary>
        public string StatusCode { get; set; }
        /// <summary>
        /// 年龄 int
        /// </summary>
        public int? Age { get; set; }

        public string AgeDetail { get; set; }
    }
}