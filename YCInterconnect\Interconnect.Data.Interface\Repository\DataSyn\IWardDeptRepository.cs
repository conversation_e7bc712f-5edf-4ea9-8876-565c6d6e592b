﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Interconnect.Models;

namespace Interconnect.Data.Interface
{
    public interface IWardDeptRepository
    {
        /// <summary>
        /// 获取没有同步的病区与科室对照信息
        /// </summary>
        /// <param name="tongbuCount"></param>
        /// <returns></returns>
        List<WardDeptInfo> GetAsync(int tongbuCount);

        /// <summary>
        /// 获取全部病区与科室对照信息
        /// </summary>
        /// <returns></returns>
        List<WardDeptInfo> GetAllAsync();

    }
}
