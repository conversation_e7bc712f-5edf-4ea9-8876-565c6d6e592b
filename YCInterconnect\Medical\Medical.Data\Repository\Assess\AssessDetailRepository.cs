﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.Data;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class AssessDetailRepository : IAssessDetailRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public AssessDetailRepository(MedicalDbContext medicalDbContext)
        {
            this._medicalDbContext = medicalDbContext;
        }

        public async Task<List<PatientAssessDetailInfo>> GetAsync(string inpatientID)
        {
            return await _medicalDbContext.AssessDetails.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientAssessDetailInfo>> GetDetailAsync(string mainID)
        {
            return await _medicalDbContext.AssessDetails.Where(m => m.PatientAssessMainID == mainID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientAssessDetail>> GetDetails(string mainID, int language)
        {
            var data = await (from a in _medicalDbContext.AssessDetails
                              join b in _medicalDbContext.AssessLists on new { ID = a.AssessListID, Language = language } equals new { b.ID, b.Language }
                              where a.PatientAssessMainID == mainID && b.DeleteFlag != "*" && a.DeleteFlag != "*"
                              select new PatientAssessDetail
                              {
                                  AssessListID = a.AssessListID,
                                  AssessValue = a.AssessValue,
                                  SignAndSymptom = b.SignAndSymptom
                              }).Distinct().ToListAsync();

            return data;
        }

        public async Task<List<int>> GetDetails(string mainID)
        {
            return await _medicalDbContext.AssessDetails.Where(m => m.PatientAssessMainID == mainID && m.DeleteFlag != "*")
                .Select(m => m.AssessListID).ToListAsync();

        }

        /// <summary>
        /// 获取病人入院评估中产生的明细-AssessListID集合
        /// </summary>
        /// <param name="assessListIDs"></param>
        /// <returns></returns>
        public async Task<List<int>> GeAssessListsByInpatientIDAsync(string inpatientID)
        {
            return await _medicalDbContext.AssessMains.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*")
                .Join(_medicalDbContext.AssessDetails, m => m.ID, n => n.PatientAssessMainID, (m, n) => n.AssessListID).ToListAsync();
        }
        /// <summary>
        /// 获取一定时间段内的评估明细数据(调整传入stationid 为0时，不再区分病区)
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="stationID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public async Task<List<PatientAssessDetailView>> GetPatientAssessDetail(string inpatientID, int stationID, DateTime startDate, DateTime endDate)
        {
            if (stationID == 0)
            {
                return await (from m in _medicalDbContext.AssessMains
                              join n in _medicalDbContext.AssessDetails on m.ID equals n.PatientAssessMainID
                              where m.InpatientID == inpatientID
                              && m.AssessDate >= startDate && m.AssessDate <= endDate
                              && m.DeleteFlag != "*"
                              && n.DeleteFlag != "*"
                              select new PatientAssessDetailView
                              {
                                  AssessDate = m.AssessDate,
                                  AssessTime = m.AssessTime,
                                  AssessListID = n.AssessListID,
                                  AssessValue = n.AssessValue
                              }).ToListAsync();
            }
            var query = await (from m in _medicalDbContext.AssessMains
                               join n in _medicalDbContext.AssessDetails on m.ID equals n.PatientAssessMainID
                               where m.InpatientID == inpatientID && m.StationID == stationID
                               && m.AssessDate >= startDate && m.AssessDate <= endDate
                               && m.DeleteFlag != "*"
                               && n.DeleteFlag != "*"
                               select new PatientAssessDetailView
                               {
                                   AssessDate = m.AssessDate,
                                   AssessTime = m.AssessTime,
                                   AssessListID = n.AssessListID,
                                   AssessValue = n.AssessValue
                               }).ToListAsync();
            return query;

        }

        public async Task<List<AssessContentValue>> GetAssessDetailAsync(string mainID)
        {
            return await _medicalDbContext.AssessDetails.Where(m => m.PatientAssessMainID == mainID && m.DeleteFlag != "*")
                .Select(m => new AssessContentValue
                {
                    AssessListID = m.AssessListID,
                    AssessValue = string.IsNullOrEmpty(m.AssessValueJson) ? m.AssessValue : m.AssessValueJson
                }).ToListAsync();
        }

        public async Task<List<PatientAssessDetailView>> GetPatientAssessDetail(string inpatientID, DateTime startDate, DateTime endDate)
        {
            var query = await (from m in _medicalDbContext.AssessMains
                               join n in _medicalDbContext.AssessDetails on m.ID equals n.PatientAssessMainID
                               where m.InpatientID == inpatientID
                               && m.AssessDate >= startDate && m.AssessDate <= endDate
                               && m.DeleteFlag != "*"
                               && n.DeleteFlag != "*"
                               select new PatientAssessDetailView
                               {
                                   PatientAssessMainID = m.ID,
                                   AssessDate = m.AssessDate,
                                   AssessTime = m.AssessTime,
                                   AssessListID = n.AssessListID,
                                   AssessValue = n.AssessValue,
                                   ModifyPersonID = n.ModifyPersonID
                               }).ToListAsync();
            return query;
        }
        /// <summary>
        /// 获取入院评估中指定项AssessValue
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="assessListID">评估项ID</param>
        /// <returns></returns>
        public async Task<string> GetAdmitAssessValueByAssessListID(string inpatientID, int assessListID)
        {
            return await (from main in _medicalDbContext.AssessMains
                          join detail in _medicalDbContext.AssessDetails
                          on main.ID equals detail.PatientAssessMainID
                          where detail.InpatientID == inpatientID && detail.DeleteFlag != "*" && main.DeleteFlag != "*" &&
                          detail.AssessListID == assessListID && main.NumberOfAssessment == 1
                          select detail.AssessValue).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据分组ID获取明细数据
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="assessListGroupID">分组ID</param>
        /// <param name="language">语言序号</param>
        /// <returns></returns>
        public async Task<List<PatientAssessDetail>> GetAssessDetailsByGroupID(string inpatientID, int assessListGroupID, int language)
        {
            return await (from a in _medicalDbContext.AssessDetails
                          join b in _medicalDbContext.AssessLists on new { ID = a.AssessListID, Language = language } equals new { b.ID, b.Language }
                          where a.InpatientID == inpatientID && b.DeleteFlag != "*" && a.DeleteFlag != "*" && a.AssessListGroupID == assessListGroupID
                          select new PatientAssessDetail
                          {
                              AssessListID = a.AssessListID,
                              AssessValue = a.AssessValue,
                              SignAndSymptom = b.SignAndSymptom,
                              AssessMainID = a.PatientAssessMainID,
                              AssessListGroupID = a.AssessListGroupID,
                              ShowName = b.Description
                          }).ToListAsync();
        }

        /// <summary>
        /// 根据主表ID及评估项ID获取评估值
        /// </summary>
        /// <param name="mainID">评估主表ID</param>
        /// <param name="assessListID">评估项ID</param>
        /// <returns></returns>
        public async Task<string> GetAssessValueByAssessListIDAndMainID(string mainID, int assessListID)
        {
            return await _medicalDbContext.AssessDetails.Where(m => m.PatientAssessMainID == mainID && m.AssessListID == assessListID && m.DeleteFlag != "*")
                .Select(m => m.AssessValue).FirstOrDefaultAsync();
        }

        public async Task<bool> CheckAdmitAssessByAssessListID(string inpatientID, int assessListID)
        {
            return await (from main in _medicalDbContext.AssessMains
                          join detail in _medicalDbContext.AssessDetails
                          on main.ID equals detail.PatientAssessMainID
                          where detail.InpatientID == inpatientID && detail.DeleteFlag != "*" && main.DeleteFlag != "*" &&
                          detail.AssessListID == assessListID && main.NumberOfAssessment == 1
                          select 1).CountAsync() > 0;
        }
        /// <summary>
        /// 主记录删除使用（包含逻辑删除数据）
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        public async Task<List<PatientAssessDetailInfo>> GetDetailsAsync(string mainID)
        {
            return await _medicalDbContext.AssessDetails.Where(m => m.PatientAssessMainID == mainID).ToListAsync();
        }
        /// <summary>
        /// 获取入院评估中指定项Detail
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="assessListID">评估项ID</param>
        /// <returns></returns>
        public async Task<PatientAssessDetailInfo> GetAdmitAssessByAssessListID(string inpatientID, int assessListID)
        {
            return await (from main in _medicalDbContext.AssessMains
                          join detail in _medicalDbContext.AssessDetails
                          on main.ID equals detail.PatientAssessMainID
                          where detail.InpatientID == inpatientID && detail.DeleteFlag != "*" && main.DeleteFlag != "*" &&
                          detail.AssessListID == assessListID && main.NumberOfAssessment == 1
                          select new PatientAssessDetailInfo()
                          {
                              AssessListID = detail.AssessListID,
                              AssessValue = detail.AssessValue,
                              AssessValueJson = detail.AssessValueJson,
                              ModifyDate = detail.ModifyDate,
                          }).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据评估主记录ID和asessListID数组获取数据
        /// </summary>
        /// <param name="mainID"></param>
        /// <param name="assessListIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientAssessDetailInfo>> GeListByAssessListIDsAndMainID(string mainID, int[] assessListIDs)
        {
            return await _medicalDbContext.AssessDetails.Where(m => m.PatientAssessMainID == mainID && assessListIDs.Any(n => n == m.AssessListID) && m.DeleteFlag != "*")
                .Select(m => new PatientAssessDetailInfo
                {
                    AssessValue = m.AssessValue,
                    AssessListID = m.AssessListID,
                    AssessListGroupID = m.AssessListGroupID,
                })
                .ToListAsync();
        }
    }
}