﻿
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Interconnect.Models;

namespace Interconnect.Data.Interface
{
    public interface IFoodAllergyRepository
    {
        /// <summary>
        /// 获取全部未同步食物过敏信息
        /// </summary>
        /// <param name="tongbuCount"></param>
        /// <returns></returns>
        Task<List<FoodAllergyInfo>> GetAsync(int tongbuCount);

    }
}