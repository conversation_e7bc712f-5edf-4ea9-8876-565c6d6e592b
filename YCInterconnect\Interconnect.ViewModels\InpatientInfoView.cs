﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Interconnect.ViewModels
{
    public class InpatientInfoView
    {
        /// <summary>
        ///住院号码
        ///</summary>
        public string CaseNumber { get; set; }
        /// <summary>
        ///病案号码
        ///</summary>
        public string ChartNo { get; set; }
        /// <summary>
        ///科别代码
        ///</summary>
        public string DepartmentCode { get; set; }
        /// <summary>
        ///病区(护理单元)代码
        ///</summary>
        public string StationCode { get; set; }
        /// <summary>
        /// 出院日期时间
        /// </summary>
        public DateTime DischargeDateTime { get; set; }
        /// <summary>
        /// 开嘱时间
        /// </summary>
        public DateTime ConfirmDate { get; set; }
    }
}
