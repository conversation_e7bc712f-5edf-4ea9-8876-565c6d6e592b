﻿//using Medical.Data.Context;
//using Medical.Data.Interface;
//using Medical.Models;
//using Medical.Services.Interface;
//using Microsoft.EntityFrameworkCore;
//using Microsoft.Extensions.Options;
//using Newtonsoft.Json;
//using NLog;
//using Interconnect.Models;
//using Interconnect.Services.Interface;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Threading.Tasks;

//namespace Interconnect.Services
//{
//    public class NRInputOutputWaterService : INRInputOutputWaterService
//    {
//        private static Logger _logger = LogManager.GetCurrentClassLogger();
//        private readonly IOptions<SystemConfig> _config;
//        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
//        private readonly IPatientIntakeOutputRepository _patientIntakeOutputRepository;
//        private readonly ISettingDescriptionRepository _settingDescriptionRepository;
//        private readonly IIntakeOutputSettingRepository _intakeOutputSettingRepository;
//        private readonly IInPatientRepository _inPatientRepository;
//        private readonly ISwitchRecordFormatRepository _switchRecordFormatRepository;
//        private readonly IRecordCommonService _recordCommonService;
//        private readonly ITubeListRepository _tubeListRepository;
//        private readonly IPatientTubeRepository _patientTubeRepository;
     
//      private readonly List<string> TubeTypeList = new List<string> { "Drainage", "Stomy" };
 
//        public NRInputOutputWaterService(IOptions<SystemConfig> config
//            , IUnitOfWork<MedicalDbContext> unitOfWork
//            , IPatientIntakeOutputRepository patientIntakeOutputRepository
//            , ISettingDescriptionRepository settingDescriptionRepository
//            , IIntakeOutputSettingRepository intakeOutputSettingRepository
//            , IInPatientRepository inPatientRepository
//            , ISwitchRecordFormatRepository switchRecordFormatRepository
//            , IRecordCommonService recordCommonService
//            ,ITubeListRepository tubeListRepository
//            ,IPatientTubeRepository patientTubeRepository
          
//          )
//        {
//            _config = config;
//            _unitOfWork = unitOfWork;
//            _patientIntakeOutputRepository = patientIntakeOutputRepository;
//            _settingDescriptionRepository = settingDescriptionRepository;
//            _intakeOutputSettingRepository = intakeOutputSettingRepository;
//            _inPatientRepository = inPatientRepository;
//            _switchRecordFormatRepository = switchRecordFormatRepository;
//            _recordCommonService = recordCommonService;
//            _tubeListRepository = tubeListRepository;
//            //_patientTubeRepository = patientTubeRepository;
           
//        }
       
//        public async Task<bool> ToDataCenter()
//        {
//            var patientInputOutputWaters = await _patientIntakeOutputRepository.GetNoPumpAsync();
//            if (patientInputOutputWaters.Count == 0)
//            {
//                return false;
//            }

//            //获取需要的导管信息  
//            var tubeList = await _tubeListRepository.GetAllAsync<TubeListInfo>();
//            tubeList = tubeList.Where(m => m.Language == _config.Value.Language && m.HospitalID == _config.Value.HospitalID &&
//            TubeTypeList.Contains(m.TubeType)).ToList();
//            //获取输入输出信息
//            var IntakeOutputSetting = await _intakeOutputSettingRepository.GetAllAsync<IntakeOutputSettingInfo>();
//            IntakeOutputSetting= IntakeOutputSetting.Where(m => m.Language == _config.Value.Language).ToList();
//            //取出符合的输入输出信息(需要使用单位)
//            IntakeOutputSetting = IntakeOutputSetting.Where(m => tubeList.Any(n=>n.TubeCode==m.FullCode)).ToList();

//            //获取类别名称
//            var SettingDescription =await  _settingDescriptionRepository.GetAllAsync<Medical.Models.SettingDescriptionInfo>();
//            SettingDescription = SettingDescription.Where(m => TubeTypeList.Contains(m.TypeValue) && m.Language== _config.Value.Language
//            && m.SettingTypeCode== "TubeType").ToList();
//            var patientFormat = await _switchRecordFormatRepository.GetAsync("Admission", _config.Value.HospitalID, _config.Value.Language);
//            var employeeFormat = await _switchRecordFormatRepository.GetAsync("Employee", _config.Value.HospitalID, _config.Value.Language);

//            var DataSwitchCenterList = new List<DataSwitchCenterInfo>();
//            foreach (var item in patientInputOutputWaters)
//            {
//                //判断是否有病人信息
//                var PatientTubeID = new string[1];
//                if (item.PatientTubeID != null)
//                {
//                    PatientTubeID[0] = item.PatientTubeID;
//                }
//                else
//                {
//                    continue;
//                }
//                //获取病人导管ID
//                var patientTube = await _patientTubeRepository.GetPatientTube(PatientTubeID);
//                if (patientTube.Count<1)
//                {
//                    continue;
//                }
//                //获取导管
//                var tubeListTemp = tubeList.Where(m => m.ID == patientTube[0].TubeID).ToList();
//                if (tubeListTemp.Count<1)
//                {
//                    continue;
//                }
//                //获取中文名称
//                var SettingDescriptionTemp = SettingDescription.Where(m => m.TypeValue == tubeListTemp[0].TubeType).ToList();
//                if (SettingDescriptionTemp.Count<1)
//                {
//                    continue;
//                }

//                //获取导管单位
//                //var IntakeOutputSettingTemp = IntakeOutputSetting.Where(m => m.FullCode == tubeListTemp[0].ID.ToString()).ToList();
//                //if (IntakeOutputSettingTemp.Count < 1)
//                //{
//                //   // continue;
//                //}

//                var model = new NROutflowWater
//                {
//                    MainSN = item.ID
//                    ,
//                    CureNo=item.CaseNumber
//                };
//                model.PatientScheduleMainID = item.PatientScheduleMainID;
//                model.Category = SettingDescriptionTemp[0].Description;
//                model.SubCategory = tubeListTemp[0].TubeName;
//               // model.Unit = IntakeOutputSettingTemp[0].Unit;
//                model.Outflow = Convert.ToDecimal(item.IntakeOutputVolume);
//                model.WaterDateTime = item.IODate.Add(item.IOTime);
//                //var patientJson = await _recordCommonService.GetPatientJson(item, patientFormat, item.PatientID);
//                var patientJson =  _recordCommonService.GetJson(item, patientFormat);
                

//                var modifyPerson = item.ModifyPersonID == null ? item.AddEmployeeID : item.ModifyPersonID;
//                var modifyDate = item.ModifyDate == null ? item.AddDate : item.ModifyDate;

//                 var employeeJson = await _recordCommonService.GetEmployeeJson(modifyPerson, employeeFormat);
//                //var employeeJson =  _recordCommonService.GetJson(modifyPerson, employeeFormat);

//                var recordJson = JsonConvert.SerializeObject(model);
//                recordJson = recordJson.Replace("{", "");
//                recordJson = recordJson.Replace("}", "");
//                var switchData = _recordCommonService.SetSwitchData(item.InpatientID, item.CaseNumber, item.ChartNo
//                    , 1005, item.ID, patientJson, employeeJson, recordJson, modifyPerson, modifyDate);
//                //插入记录
//                DataSwitchCenterList.Add(switchData);

//                //*****插入导管明细************************************************************************************************
//                var modelStatistics = new NRWaterStatisticsInfo
//                {
//                    CureNo = item.CaseNumber,
//                    CategoryName = SettingDescriptionTemp[0].Description,
//                    Category = 7//后期需要调整
//                };
//                try
//                {
//                    if (item.IntakeOutputVolume != null)
//                    {
//                        modelStatistics.Volume = item.IntakeOutputVolume.Value;
//                       // modelStatistics.Unit = IntakeOutputSettingTemp[0].Unit;
//                    }
//                    else
//                    {
//                        modelStatistics.Volume = 0;
//                    }
//                }
//                catch
//                {
//                    _logger.Error(string.Format("出入水量转换失败,ID={0},IntakeOutputVolume={1}", item.ID, item.IntakeOutputVolume.Value));
//                }
//                modelStatistics.Remark = item.IntakeOuputNote;
//                modelStatistics.SrcKey = item.ID;
//                modelStatistics.WaterTime = item.IODate.Add(item.IOTime);
//                //病区编码还是流水ID
//                modelStatistics.WardCode = item.StationID.ToString();


//                patientJson = _recordCommonService.GetJson(item, patientFormat);
//                modifyPerson = item.ModifyPersonID == null ? item.AddEmployeeID : item.ModifyPersonID;
//                modifyDate = item.ModifyDate == null ? item.AddDate : item.ModifyDate;

//                 employeeJson = await _recordCommonService.GetEmployeeJson(modifyPerson, employeeFormat);
//                recordJson = JsonConvert.SerializeObject(modelStatistics);
//                recordJson = recordJson.Replace("{", "");
//                recordJson = recordJson.Replace("}", "");
//                switchData = _recordCommonService.SetSwitchData(item.InpatientID, item.CaseNumber, item.ChartNo
//                    , 2, item.ID, patientJson, employeeJson, recordJson, modifyPerson, modifyDate);
//                //插入记录
//                DataSwitchCenterList.Add(switchData);
//                item.DataPumpDate = DateTime.Now;
//                item.DataPumpFlag = "*";
//            }
//            _unitOfWork.GetRepository<DataSwitchCenterInfo>().Insert(DataSwitchCenterList);
//            _unitOfWork.GetRepository<PatientIntakeOutputInfo>().Update(patientInputOutputWaters);
//            return  _unitOfWork.SaveChanges() > 0;
//        }
//    }
//}