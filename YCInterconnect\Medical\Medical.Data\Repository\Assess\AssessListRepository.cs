﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Internal;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class AssessListRepository : IAssessListRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly IOptions<SystemConfig> _systemConfig;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public AssessListRepository(
              MedicalDbContext db
            , IMemoryCache memoryCache
            , IOptions<SystemConfig> systemConfig
             , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService
            )
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _systemConfig = systemConfig;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        /// <summary>
        /// 获取需要写入Profile的清单
        /// </summary>
        /// <returns></returns>
        public async Task<List<AssessListInfo>> GetAsync()
        {
            var datas = (List<AssessListInfo>)await GetCacheAsync();
            return datas.Where(m => m.ProfileMark != "").ToList();
        }

        public async Task<List<AssessListInfo>> GetAsync(int language = 1)
        {
            var datas = (List<AssessListInfo>)await GetCacheAsync();

            if (datas != null)
            {
                return datas;
            }

            return new List<AssessListInfo>();
        }
        /// <summary>
        /// 根据主键数组获取数据
        /// </summary>
        /// <param name="idArr"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public async Task<List<AssessListInfo>> GetAssessListByID(int[] idArr, int language)
        {
            var datas = await GetAsync(language);
            return datas.Where(m => idArr.Contains(m.ID)).OrderBy(m => m.ID).ToList();
        }

        
        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<AssessListInfo>>(key, GetDataBaseListData);
        }         
        /// <summary>
        /// 获取数据库数据
        /// </summary>
        /// <param name="language"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            dict.TryGetValue("language", out var language);
            var data = await _medicalDbContext.AssessLists.Where(m => m.Language == (Int32)language && m.DeleteFlag != "*").ToListAsync();
            var showNameMapping = await _medicalDbContext.LocalShowNameMappingInfos.Where(m => m.SpecialListType == "AssessList" && m.Language == (Int32)language 
            && m.HospitalID == hospitalID.ToString() && m.DeleteFlag != "*").ToListAsync();
            foreach (var item in data)
            {
                var temp = showNameMapping.Find(m => m.ListID == item.ID);
                if (temp != null)
                {
                    item.Description = temp.LocalShowName;
                }
            }
            return data;
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.AssessList.GetKey(_sessionCommonServer);
        }

        public async Task<List<AssessListInfo>> GetBySystemCode(string systemCode, int language)
        {
            var datas = await GetAsync(language);

            return datas.Where(m => m.SystemCode == systemCode).ToList();
        }

        public async Task<List<AssessListView>> GetAssessListId(int language)
        {
            var datas = await GetAsync(language);

            return datas.Where(m => m.ProfileMark != "").Select(
                n => new AssessListView
                {
                    ID = n.ID,
                    Language = n.Language,
                    Description = n.Description
                }).ToList();
        }

        public async Task<List<AssessListView>> GetAssessListView(int language, int[] idArr)
        {
            var datas = await GetAsync(language);

            return datas.Where(m => idArr.Contains(m.ID)).Select(
                n => new AssessListView
                {
                    ID = n.ID,
                    Language = n.Language,
                    Description = n.Description
                }).ToList();
        }

        public async Task<AssessListView> GetAssessListInfoById(int assessListID)
        {
            var datas = await GetAsync();

            return datas.Where(m => m.ID == assessListID).Select(
                n => new AssessListView
                {
                    ID = n.ID,
                    Language = n.Language,
                    Description = n.Description
                }).FirstOrDefault();
        }

        public async Task<List<AssessListSelectView>> GetAssessListSelectView(List<int> idArr)
        {
            var datas = await GetAsync();

            return datas.Where(m => idArr.Contains(m.ID)).Select(
                n => new AssessListSelectView
                {
                    Value = n.ID,
                    Label = n.Description
                }).ToList();
        }
        /// <summary>
        /// 获取一下所有的key value
        /// </summary>
        /// <returns></returns>
        public async Task<Dictionary<int, string>> GetKeyValuesAsync()
        {
            var assessLists = await GetAsync();
            var details = (List<NursingInterventionDetailInfo>)await GetCacheAsync();
            var assessDict = assessLists.ToDictionary(m => m.ID, m => m.Description);
            var detailDict = details.ToDictionary(m => m.InterventionDetailID, m => m.ShowName);
            return assessDict.Union(detailDict).ToDictionary(m => m.Key, m => m.Value);
        }
    }
}