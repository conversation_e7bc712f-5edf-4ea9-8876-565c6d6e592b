﻿using System.Threading.Tasks;

namespace Interconnect.Services.Interface
{
    public interface ISyncPatientHISDataService
    {
        /// <summary>
        /// 同步患者排程数据
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncPatientScheduleData();
        /// <summary>
        /// 同步患者病情观察
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncPatientScheduleMeasures();
        /// <summary>
        /// 同步患者出入量
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncPatientIO();
    }
}
