﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientSkinTestRepository : IPatientSkinTestRepository
    {
        private readonly MedicalDbContext _dbContext = null;

        public PatientSkinTestRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }

        public async Task<PatientSkinTestInfo> GetByScheduleMainID(string patientScheduleMainID)
        {
            return await _dbContext.PatientSkinTestInfos.Where(m => m.PatientScheduleMainID == patientScheduleMainID
            && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<List<PatientSkinTestView>> GetSkinTestByChartNo(string chartNo)
        {
            return await _dbContext.PatientSkinTestInfos.Where(m => m.ChartNo == chartNo && m.DeleteFlag != "*")
                .Select(m => new PatientSkinTestView
                {
                    PatientSkinTestID = m.PatientSkinTestID,
                    InpatientID = m.InpatientID,
                    DrugName = m.DrugName,
                    PerformDateTime = m.PerformDateTime,
                    InterventionDetailID = m.InterventionDetailID,
                    PerformPersonID = m.PerformPersonID,
                    PatientScheduleMainID = m.PatientScheduleMainID,
                    AllergyResultName = m.AllergyResultName,
                    AllergyBasicID = m.AllergyBasicID,
                    AllergyName = m.AllergyName,
                    ChemicCode = m.ChemicCode
                }
                ).ToListAsync();
        }
        /// <summary>
        /// 获取还没有回传给HIS 的皮试过敏数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<PatientSkinTestInfo>> GetUnSaveToHisData()
        {
            return await _dbContext.PatientSkinTestInfos.Where(m => !m.HISserialNo.HasValue
           && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取ccc删除的过敏数据（但是HIS中对应的数据还没有删除）
        /// </summary>
        /// <returns></returns>
        public async Task<List<PatientSkinTestInfo>> GetDeletedDataToHIS()
        {
            return await _dbContext.PatientSkinTestInfos.Where(m => m.DeleteFlag == "*" && m.DataPumpFlag != "*").ToListAsync();
        }

        public async Task<List<PatientSkinTestView>> GetAsync(string inpatientID)
        {
            return await _dbContext.PatientSkinTestInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*")
                .Select(m => new PatientSkinTestView
                {
                    ChartNo = m.ChartNo,
                    DrugName = m.DrugName,
                    PerformDateTime = m.PerformDateTime,
                    InpatientID = m.InpatientID,
                    AllergyResultName = m.AllergyResultName,
                    AllergyName = m.AllergyName,
                    AllergyResultCode = m.AllergyResultCode
                }
                ).ToListAsync();
        }
        public async Task<PatientSkinTestInfo> GetByID(string patientSkinTestID)
        {
            return await _dbContext.PatientSkinTestInfos.Where(m => m.PatientSkinTestID == patientSkinTestID
            && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
    }
}