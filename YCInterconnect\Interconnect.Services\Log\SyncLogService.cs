﻿using Interconnect.Services.Interface;
using NLog; 
using Interconnect.Data.Context;
using System;
using Interconnect.Models;
using Arch.EntityFrameworkCore.UnitOfWork;

namespace Interconnect.Services
{
    public class SyncLogService: ISyncLogService
    {
        private readonly IUnitOfWork<DataOutConnection> _unitOfWorkOut;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        public SyncLogService(IUnitOfWork<DataOutConnection> unitOfWork)
        {
            _unitOfWorkOut = unitOfWork;
        }

        public  bool InsertSyncLog(byte logLevel,string logGroupID, string logTypeCode, string logTypeName, string contents, string addPersonID,bool commit)
        {
            var syncLogInfo = new SyncLogInfo
            {
                LogLevel=logLevel,
                LogGroupID= logGroupID,
                LogTypeCode = logTypeCode,
                LogTypeName = logTypeName,
                Contents = contents,
                AddPersonID = addPersonID,
                AddDateTime = DateTime.Now
            };
            //使用同步方法，跨库异步有问题
             _unitOfWorkOut.GetRepository<SyncLogInfo>().Insert(syncLogInfo);
            if (commit)
            {
              return   _unitOfWorkOut.SaveChanges()>0;
            }
            return true;
        }

    }
}
