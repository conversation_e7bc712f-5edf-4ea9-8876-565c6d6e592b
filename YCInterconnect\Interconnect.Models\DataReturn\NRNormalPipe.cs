﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("NursingRecord_NormalPipe")]
    public class NRNormalPipeInfo : ModifyReturnInfo
    {
        /// <summary>
        ///	自增长编号	
        ///</summary>
        [Key]
        [Column("SerialNO")]
        public int SerialNO { get; set; }
        /// <summary>
        ///	Main记录的编号	
        ///</summary>
        public int? SN { get; set; }
        /// <summary>
        ///	导管分类，如：动静脉置管	
        ///</summary>
        public string Category { get; set; }
        /// <summary>
        ///	导管名称，如：PORT泵护理	
        ///</summary>
        public string PipeName { get; set; }
        /// <summary>
        ///	护理措施，如：完好畅通	
        ///</summary>
        public string NurseStep { get; set; }
        /// <summary>
        ///	颜色，如：黄	
        ///</summary>
        public string Color { get; set; }
        /// <summary>
        ///	出水量	
        ///</summary>
        public float? Outflow { get; set; }
        /// <summary>
        ///	备注	
        ///</summary>
        public string Remark { get; set; }
        /// <summary>
		///	"如：<sections>  <values na
		///</summary>
		public string Data { get; set; }
        /// <summary>
        ///	0 有效，-1无效	
        ///</summary>
        public int? Status { get; set; }
        /// <summary>
        ///	创建时间	
        ///</summary>
        public int? CreateTime { get; set; }
        /// <summary>
        ///	最后更新时间	
        ///</summary>
        public DateTime? LastUpdateTime { get; set; }
        /// <summary>
        ///	说明	
        ///</summary>
        public string Description { get; set; }
        /// <summary>
        ///	导管ID,关联NursingRecord_Pati
        ///</summary>
        public int? PipeID { get; set; }
    }
}