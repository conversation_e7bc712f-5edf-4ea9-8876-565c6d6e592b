﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class RelativesRepository : IRelativesRepository
    {
        private readonly MedicalDbContext _medicalDbContext;

        public RelativesRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<RelativesInfo> GetByEmployeeDataIDAnRelativesTypeAsync(int employeeDataID, string relativesType)
        {
            return await _medicalDbContext.RelativesInfos.Where(m => m.EmployeeDataID == employeeDataID
            && m.RelativesType == relativesType).SingleOrDefaultAsync();
        }

        public async Task<List<RelativesInfo>> GetListByEmployeeDataIDAsync(int employeeDataID)
        {
            return await _medicalDbContext.RelativesInfos.Where(m => m.EmployeeDataID == employeeDataID).ToListAsync();
        }
    }
}