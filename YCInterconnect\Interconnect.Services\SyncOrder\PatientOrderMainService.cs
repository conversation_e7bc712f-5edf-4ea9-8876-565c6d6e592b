﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Arch.EntityFrameworkCore.UnitOfWork;
using Interconnect.Data.Context;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.Interface;
using Medical.ViewModels.Submit;
using Medical.ViewModels.View;
using MedicalExternalCommon.Service;
using Newtonsoft.Json;
using NLog;
using ViewModel;

namespace Interconnect.Services
{
    public class PatientOrderMainService : IPatientOrderMainService
    {
        //Mdeical
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IInpatientDataRepository _InPatientRepository;
        private readonly IUserRepository _IUserRepository;
        private readonly IPatientOrderRepository _IPatientOrderRepository;
        private readonly ISettingDescRepository _interconnectSDRepository;
        private readonly IPhysicianToInterventionRepository _physicianToIntervention;
        private readonly Medical.Data.Interface.IPatientOrderDetailRepository _patientOrderDetailRepository;
        private readonly IOrderToAssessListRepository _orderToAssessListRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly IPhysicianOrderRepository _physicianOrderRepository;
        private readonly IHospitalListRepository _hospitalListRepository;
        private readonly IClinicSettingRepository _clinicSettingRepository;
        private readonly IAssessListRepository _assessListRepository;
        private readonly ExternalProfileCommonService _externalProfileCommonService;
        private readonly IPatientEventRepository _patientEventRepository;

        //Interconnect
        private readonly IUnitOfWork<DataOutConnection> _unitOfWorkOut;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ILogInfoServices _ILogInfoServices;
        private readonly ICommonHelper _commonHelper;
        private readonly ISyncAPIConfigRepository _syncAPIConfigRepository;
        private readonly IFrequencyRepository _frequencyRepository;
        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";
        /// <summary>
        /// 系统默认操作人
        /// </summary>
        private string MODIFYPERSONID = "";
        /// <summary>
        /// 日间手术医嘱Code
        /// </summary>
        private const string DAYSURGERYORDERCODE = "204";

        public PatientOrderMainService(
              IUnitOfWork<MedicalDbContext> UnitOfWork
            , IUnitOfWork<DataOutConnection> UnitOfWorkOut
            , IInpatientDataRepository InPatientRepository
            , IUserRepository UserRepository
            , IPatientOrderRepository PatientOrderRepository
            , ILogInfoServices LogInfoServices
            , ICommonHelper commonHelper
            , ISettingDescRepository settingDescRepository
            , IPhysicianToInterventionRepository physicianToInterventionRepository
            , Medical.Data.Interface.IPatientOrderDetailRepository patientOrderDetailRepository
            , IOrderToAssessListRepository orderToAssessListRepository
            , ILogInfoRepository logInfoRepository
            , IAppConfigSettingRepository appConfigSettingRepository
            , ISyncAPIConfigRepository syncAPIConfigRepository
            , IFrequencyRepository frequencyRepository
            , IPhysicianOrderRepository physicianOrderRepository
            , IHospitalListRepository hospitalListRepository
            , IClinicSettingRepository clinicSettingRepository
            , IAssessListRepository assessListRepository
            , ExternalProfileCommonService externalProfileCommonService
            , IPatientEventRepository patientEventRepository
            )
        {
            _unitOfWork = UnitOfWork;
            _unitOfWorkOut = UnitOfWorkOut;
            _InPatientRepository = InPatientRepository;
            _IUserRepository = UserRepository;
            _IPatientOrderRepository = PatientOrderRepository;
            _ILogInfoServices = LogInfoServices;
            _commonHelper = commonHelper;
            _interconnectSDRepository = settingDescRepository;
            _physicianToIntervention = physicianToInterventionRepository;
            _patientOrderDetailRepository = patientOrderDetailRepository;
            _orderToAssessListRepository = orderToAssessListRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
            _syncAPIConfigRepository = syncAPIConfigRepository;
            _frequencyRepository = frequencyRepository;
            _physicianOrderRepository = physicianOrderRepository;
            _hospitalListRepository = hospitalListRepository;
            _clinicSettingRepository = clinicSettingRepository;
            _assessListRepository = assessListRepository;
            _externalProfileCommonService = externalProfileCommonService;
            _patientEventRepository = patientEventRepository;
        }

        //同步医嘱
        public async Task<bool> SyncPatientOrderByDateTime()
        {
            return await SyncPatientOrder(2, false);
        }

        //同步历史医嘱
        public async Task<bool> SyncHistoryPatientOrder()
        {
            _logger.Info("同步历史医嘱");
            return await SyncPatientOrder(24, true);
        }

        /// <summary>
        /// /同步医嘱
        /// </summary>
        /// <param name="apiID"></param>
        /// <param name="syncStopFlag">true 只同步历史停止医嘱数据</param>
        /// <returns></returns>
        private async Task<bool> SyncPatientOrder(int apiID, bool syncStopFlag)
        {
            var hospitalInfo = _hospitalListRepository.GetHospitalInfo();
            if (hospitalInfo == null)
            {
                _logger.Error("获取医院ID失败！");
                return false;
            }
            var hospitalID = hospitalInfo.HospitalID;
            //获取同步API
            var syncAPIConfigInfo = _syncAPIConfigRepository.GetSyncAPIConfigByApiID(apiID);
            if (syncAPIConfigInfo == null)
            {
                _logger.Error("没有找到ApiID为2(医嘱数据API)的数据");
                return false;
            }
            //获取同步时间  
            var nowDateTime = DateTime.Now;
            var lastDateTime = syncAPIConfigInfo.LastSyncDateTime;
            var intervalminutes = syncAPIConfigInfo.IntervalMinutes;
            if (intervalminutes == 0)
            {
                intervalminutes = 30;
            }

            //如果间隔时间小于10分钟，不启用同步，避免接口服务器阻塞
            if (lastDateTime.AddMinutes(10) > nowDateTime)
            {
                return false;
            }
            if (syncStopFlag)
            {
                nowDateTime = nowDateTime.AddMinutes(-100);
            }
            //设置数据同步，时间重叠分钟，避免获取的数据不完整
            var overlapMinutes = syncAPIConfigInfo.OverlapMinutes;
            if (overlapMinutes == null || overlapMinutes <= 1)
            {
                overlapMinutes = 30;
            }
            var minutes = Convert.ToInt32(overlapMinutes);
            var startDateTime = lastDateTime.AddMinutes(-minutes);
            _logger.Info("同步开始时间：" + startDateTime.ToString() + "结束时间：" + nowDateTime.ToString() + "的医嘱数据");
            var resultFlag = await SyncOrder(startDateTime, nowDateTime, intervalminutes, hospitalID, syncAPIConfigInfo, syncStopFlag);
            _logger.Info("开始时间：" + startDateTime.ToString() + "结束时间：" + nowDateTime.ToString() + "的医嘱数据同步结束");
            return resultFlag;
        }


        /// <summary>
        ///  循环同步医嘱
        /// </summary>
        /// <param name="startDateTime"></param>
        /// <param name="nowDateTime"></param>
        /// <param name="intervalminutes"></param>
        /// <param name="hospitalID"></param>
        /// <param name="syncAPIConfigInfo"></param>
        /// <param name="syncStopFlag">只同步停止医嘱</param>
        /// <returns></returns>
        private async Task<bool> SyncOrder(DateTime startDateTime, DateTime nowDateTime
            , int intervalminutes, string hospitalID, SyncAPIConfigInfo syncAPIConfigInfo
            , bool syncStopFlag = false)
        {
            var physicianToInterventionList = await _physicianToIntervention.GetAllData();
            var orderToAssessList = await _orderToAssessListRepository.GetAsync();
            var physicianOrder = await _physicianOrderRepository.GetAllAsync<PhysicianOrderInfo>();
            physicianOrder = physicianOrder.Where(m => m.HospitalID == hospitalID).ToList();
            var frequencyList = await _frequencyRepository.GetAllAsync<FrequencyInfo>();
            var clinicalSettingList = await _clinicSettingRepository.GetAllAsync<ClinicalSettingInfo>();
            var orderKeyWordList = clinicalSettingList.Where(m => m.SettingTypeCode == "OrderKeyWord").Select(m => m.Description).ToList();
            string url = await _appConfigSettingRepository.GetConfigSettingValue("Configs", "CallSavePatientMeasures");
            //url = "http://localhost:56194/api/PatientObservation/SyncPatientObservation";
            while (startDateTime < nowDateTime)
            {
                var endDateTime = startDateTime.AddMinutes(intervalminutes);
                if (endDateTime > nowDateTime)
                {
                    endDateTime = nowDateTime.AddMinutes(1);
                }
                _logger.Info("开始获取开始时间：" + startDateTime.ToString() + "结束时间：" + endDateTime.ToString() + "的医嘱数据");
                var hisData = GetOrderApiData(syncAPIConfigInfo.APIAddress, startDateTime, endDateTime, hospitalID);
                _logger.Info("开始时间：" + startDateTime.ToString() + "结束时间：" + endDateTime.ToString() + "获取医嘱数据" + hisData.Count());
                //获取符合的医嘱
                var orderCodeList = physicianOrder.Select(m => m.OrderCode).Distinct().ToList();
                hisData = hisData.Where(m => orderCodeList.Contains(m.OrderCode) || m.OrderPattern == "901").ToList();

                if (syncStopFlag)
                {
                    hisData = hisData.Where(m => !string.IsNullOrEmpty(m.EndDate)).ToList();
                }

                if (hisData != null && hisData.Count > 0)
                {
                    await SyncHisData(hisData, orderToAssessList, physicianToInterventionList, frequencyList
                    , physicianOrder, orderKeyWordList, hospitalID);
                }
                else
                {
                    _logger.Info("开始时间：" + startDateTime.ToString() + "结束时间：" + endDateTime.ToString() + "没有获取到符合条件的医嘱数据");
                }
                #region 同步日间手术患者数据至病情观察
                var daySurgeryList = hisData.Where(m => m.OrderPattern == DAYSURGERYORDERCODE).ToList();
                if (daySurgeryList.Any() && !string.IsNullOrEmpty(url))
                {
                    await SendDaySurgeryPatientMeasures(daySurgeryList, url);
                }
                #endregion
                //同步成功，更新最后同步时间
                syncAPIConfigInfo.LastSyncDateTime = endDateTime;
                try
                {
                    _unitOfWorkOut.SaveChanges();
                }
                catch (Exception ex)
                {
                    _logger.Error("更新最后同步时间失败" + ex.ToString());
                    return false;
                }
                startDateTime = endDateTime;
            }
            return true;
        }
        /// <summary>
        /// 写入病情观察数据
        /// </summary>
        /// <param name="daySurgeryList"></param>
        /// <param name="url"></param>
        /// <returns></returns>
        private async Task SendDaySurgeryPatientMeasures(List<HISOrderView> daySurgeryList,string url)
        {
            var caseNumbers = daySurgeryList.Select(m => m.CaseNumber).Distinct().ToList();
            var inpatients = _InPatientRepository.GetByCaseNumbersAll(caseNumbers);
            var patientEvents = await _patientEventRepository.GetDataByCaseNumbers(caseNumbers,6218);
            patientEvents = patientEvents.Where(m => m.DeleteFlag != "*").ToList();
            foreach (var daySurgery in daySurgeryList)
            {
                var inpatient = inpatients.Find(m => m.CaseNumber == daySurgery.CaseNumber);
                //没有患者数据或者医嘱开立时间转换失败或者患者没有日间病房患者时间则跳过
                if (inpatient == null || !DateTime.TryParse(daySurgery.ConfirmDate, out DateTime performDateTime) || patientEvents.Find(m => m.CaseNumber == daySurgery.CaseNumber) == null)
                {
                    _logger.Warn($"数据校验失败，患者日间手术写病情观察数据失败CaseNumber：{daySurgery.CaseNumber}");
                    continue;
                }
                var postData = CreatePatientMeasures(inpatient, performDateTime, daySurgery.OrderContent, daySurgery.AddEmployeeID);
                var wrx = new WebRequestSugar();
                wrx.SendObjectAsJsonInBody(url, postData);
            }
        }
        /// <summary>
        /// 创建病情观察模板
        /// </summary>
        /// <param name="inpatient"></param>
        /// <param name="confirmDate"></param>
        /// <param name="description"></param>
        /// <param name="userID"></param>
        /// <returns></returns>
        private PatientObservationSubmit CreatePatientMeasures(InpatientDataInfo inpatient, DateTime confirmDate, string description,string userID)
        {
            return new PatientObservationSubmit
            {
                EvalutionRecordsCode = "",
                BringToShift = "0",
                Details = new List<Detail>(),
                InformPhysician = false,
                InpatientID = inpatient.ID,
                ObserveTemplateID = "f226801862cf40129866146186d5b6a6",
                PerformDate = confirmDate,
                PerformText = description,
                RecordsCode = "ObserveTemp",
                //TriggerList = new List<TriggerView>(),
                SourceID = Guid.NewGuid().ToString("N"),
                UserID = userID,
            };
        }
        //同步指定时间内的医嘱
        public async Task SyncHisOrderByManual(DateTime startDateTime, DateTime endDateTime, string apiAddress, string hospitalID
            , bool syncStopFlag, string caseNumber)
        {
            var physicianToInterventionList =await _physicianToIntervention.GetAllData();
            var orderToAssessList = await _orderToAssessListRepository.GetAsync();
            var physicianOrder = await _physicianOrderRepository.GetAllAsync<PhysicianOrderInfo>();
            physicianOrder = physicianOrder.Where(m => m.HospitalID == hospitalID).ToList();
            var frequencyList = await _frequencyRepository.GetAllAsync<FrequencyInfo>();
            var clinicalSettingList = await _clinicSettingRepository.GetAllAsync<ClinicalSettingInfo>();
            var orderKeyWordList = clinicalSettingList.Where(m => m.SettingTypeCode == "OrderKeyWord").Select(m => m.Description).ToList();

            _logger.Info("开始获取开始时间：" + startDateTime.ToString() + "结束时间：" + endDateTime.ToString() + "的医嘱数据");

            while (startDateTime < endDateTime)
            {
                startDateTime = startDateTime.AddMinutes(30);

                var hisData = GetOrderApiData(apiAddress, startDateTime, endDateTime, hospitalID);
                //获取符合的医嘱
                var orderCodeList = physicianOrder.Select(m => m.OrderCode).Distinct().ToList();
                hisData = hisData.Where(m => orderCodeList.Contains(m.OrderCode) || m.OrderPattern == "901").ToList();

                if (hisData == null)
                {
                    _logger.Info("没有获取到医嘱");
                    continue;
                }
                if (!string.IsNullOrEmpty(caseNumber))
                {
                    hisData = hisData.Where(m => m.CaseNumber == caseNumber).ToList();
                }
                if (hisData.Count <= 0)
                {
                    _logger.Info("caseNumber" + caseNumber + "没有符合的医嘱");
                    continue;
                }

                if (syncStopFlag)
                {
                    hisData = hisData.Where(m => !string.IsNullOrEmpty(m.EndDate)).ToList();
                }
                if (hisData.Count <= 0)
                {
                    _logger.Info("没有符合的停止医嘱");
                    continue;
                }
                await SyncHisData(hisData, orderToAssessList, physicianToInterventionList, frequencyList
                , physicianOrder, orderKeyWordList, hospitalID);
            }
            return;
        }
        /// <summary>
        /// 同步his医嘱数据
        /// </summary>
        /// <param name="hISOrderViews"></param>
        /// <param name="orderToAssessList"></param>
        /// <param name="physicianToInterventionList"></param>
        /// <param name="frequencyList"></param>
        /// <param name="physicianOrderInfos"></param>
        /// <param name="OrderKeyWordList"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task SyncHisData(List<HISOrderView> hISOrderViews
            , List<OrderToAssessListInfo> orderToAssessList
            , List<PhysicianToInterventionInfo> physicianToInterventionList
            , List<FrequencyInfo> frequencyList
            , List<PhysicianOrderInfo> physicianOrderInfos
            , List<string> OrderKeyWordList
            , string hospitalID)
        {

            var caseNumbers = hISOrderViews.Select(m => m.CaseNumber).Distinct().ToList();
            _logger.Info("开始同步" + caseNumbers.Count + "个病人，" + hISOrderViews.Count + "条医嘱数据");
            foreach (var caseNumber in caseNumbers)
            {
                var hISOrderViewsTemp = hISOrderViews.Where(m => m.CaseNumber == caseNumber).ToList();
                if (hISOrderViewsTemp.Count <= 0)
                {
                    continue;
                }
                var inPatientInfo = await _InPatientRepository.GetAsyncByCaseNumber(caseNumber,hospitalID);
                if (inPatientInfo == null)
                {
                    _logger.Warn("PatientOrderMain" + "表: InPatientList CaseNumber [" + caseNumber + "]   查询信息错误!");
                    continue;
                }
                _logger.Info(" 开始进行同步患者" + caseNumber + "的医嘱数据，数据条数：" + hISOrderViewsTemp.Count);
                var resultFlag = await SyncOrderMain(inPatientInfo, hISOrderViewsTemp, orderToAssessList, physicianToInterventionList
                    , frequencyList, physicianOrderInfos, OrderKeyWordList, hospitalID);
                if (!resultFlag)
                {
                    _logger.Warn(" 患者" + caseNumber + "的医嘱数据同步失败");
                }
                else
                {
                    _logger.Info(" 患者" + caseNumber + "的医嘱数据同步成功");
                }
            }
        }
        /// <summary>
        /// 病区同步his医嘱数据
        /// </summary>
        /// <param name="hISOrderViews"></param>
        /// <param name="orderToAssessList"></param>
        /// <param name="physicianToInterventionList"></param>
        /// <param name="frequencyList"></param>
        /// <param name="physicianOrderInfos"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<bool> SyncHisDataByStationCode(List<HISOrderView> hISOrderViews, List<OrderToAssessListInfo> orderToAssessList, List<PhysicianToInterventionInfo> physicianToInterventionList
            , List<FrequencyInfo> frequencyList, List<PhysicianOrderInfo> physicianOrderInfos, string hospitalID)
        {
            var saveFlag = true;
            var caseNumbers = hISOrderViews.Select(m => m.CaseNumber).Distinct().ToList();
            _logger.Info("开始同步" + caseNumbers.Count + "个病人，" + hISOrderViews.Count + "条医嘱数据");
            foreach (var caseNumber in caseNumbers)
            {
                var hISOrderViewsTemp = hISOrderViews.Where(m => m.CaseNumber == caseNumber).ToList();
                if (hISOrderViewsTemp.Count <= 0)
                {
                    continue;
                }
                var inPatientInfo = await _InPatientRepository.GetAsyncByCaseNumber(caseNumber, hospitalID);
                if (inPatientInfo == null)
                {
                    _logger.Warn("PatientOrderMain" + "表: InPatientList CaseNumber [" + caseNumber + "]   查询信息错误!");
                    continue;
                }
                _logger.Info(" 开始进行同步患者" + caseNumber + "的医嘱数据，数据条数：" + hISOrderViewsTemp.Count);
                var resultFlag = await SyncOrderMainByStationCode(inPatientInfo, hISOrderViewsTemp, orderToAssessList, physicianToInterventionList
                    , frequencyList, physicianOrderInfos, hospitalID);
                if (!resultFlag)
                {
                    saveFlag = false;
                    _logger.Warn(" 患者" + caseNumber + "的医嘱数据同步失败");
                }
                else
                {
                    _logger.Info(" 患者" + caseNumber + "的医嘱数据同步成功");
                }
            }
            return saveFlag;
        }
        /// <summary>
        /// 获得医嘱数据
        /// </summary>
        /// <param name="api"></param>
        /// <param name="dictionary"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private List<HISOrderView> GetOrderApiData(string api, DateTime startDateTime, DateTime endDateTime, string hospitalID)
        {
            _logger.Info("呼叫API获取医嘱数据");
            var interconnect_Data = new List<HISOrderView>();
            var dictionary = new Dictionary<string, string>
                 {
                    { "StartDateTime", startDateTime.ToString()},
                    { "EndDateTime", endDateTime.ToString()},
                 };
            var hisData = GetApiData(api, dictionary, hospitalID);
            if (string.IsNullOrEmpty(hisData))
            {
                return interconnect_Data;
            }

            try
            {
                interconnect_Data = JsonConvert.DeserializeObject<List<HISOrderView>>(hisData);
            }
            catch (Exception ex)
            {
                _logger.Error("Json格式化失败:" + ex.ToString());
                return interconnect_Data;
            }
            _logger.Info("转换Json数据完成，获得医嘱" + interconnect_Data.Count() + "条！");
            return interconnect_Data;
        }
        /// <summary>
        /// 呼叫API获取数据
        /// </summary>
        /// <param name="api"></param>
        /// <param name="dictionarys"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private string GetApiData(string api, Dictionary<string, string> dictionarys, string hospitalID)
        {
            if (string.IsNullOrEmpty(api))
            {
                return "";
            }
            //获取环境 ,1 开发环境
            var systemOperatingEnvironment = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "SystemOperatingEnvironment").Result;
            var resultData = "";
            if (systemOperatingEnvironment != "1")
            {
                //呼叫API获得数据             
                resultData = _commonHelper.GetInterconnectData(api, dictionarys);
            }
            else
            {
                // resultData = _ILogInfoServices.GetLog("20")[0].Logs;
                resultData = ReadFile.ReadTxt(@"D:\SyncOrderByDateTime.json");
            }

            var printInterfaceData = 0;
            var resultPrintDate = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "PrintInterfaceData").Result;
            if (StringCheck.IsNumeric(resultPrintDate))
            {
                printInterfaceData = int.Parse(resultPrintDate);
            }

            if (printInterfaceData == 1)
            {
                _logger.Info("Api:" + api + "获取的医嘱数据" + ListToJson.ToJson(dictionarys) + "医嘱数据：" + resultData);
            }

            try
            {
                var result = JsonConvert.DeserializeObject<ResponseResult>(resultData);
                var resultDataStr = result.Data.ToString();
                if (resultDataStr == "" || resultDataStr == "[]" || resultDataStr == "[{}]" || resultDataStr == "{}")
                {
                    return "";
                }
                return resultDataStr;
            }
            catch (Exception ex)
            {
                _logger.Error("Api: " + api + "获取的医嘱数据ResponseResult" + ex.ToString());
                return "";
            }
        }
        private async Task<bool> SyncOrderMain(InpatientDataInfo inpatientDataInfo, List<HISOrderView> hISOrderViews
            , List<OrderToAssessListInfo> orderToAssessList
            , List<PhysicianToInterventionInfo> physicianToInterventionList, List<FrequencyInfo> frequencyList
            , List<PhysicianOrderInfo> physicianOrderInfos
            , List<string> OrderKeyWordList
            , string hospitalID)
        {

            var alllogSetFlag = 0;
            string tablename = "PatientOrderMain";
            var caseNumber = hISOrderViews[0].CaseNumber;
            var orderMainList = new List<Medical.Models.PatientOrderMainInfo>();
            var orderDetailList = new List<Medical.Models.PatientOrderDetailInfo>();
            #region 数据检核

            //获得相关人员的基本信息        
            var checkEmployeeFlag = 0;
            var resultCheckEmployeeFlag = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "CheckEmployeeFlag");
            if (StringCheck.IsNumeric(resultCheckEmployeeFlag))
            {
                checkEmployeeFlag = int.Parse(resultCheckEmployeeFlag);
            }
            var employeeidList = new List<UserInfo>();
            if (checkEmployeeFlag == 1)
            {
                employeeidList = await GetEmployeeList(hISOrderViews);
            }
            #endregion

            var patientOrderMain = await _IPatientOrderRepository.GetAsync(inpatientDataInfo.ID);
            var patientOrderDetail = await _patientOrderDetailRepository.GetPatientOrderDetail(inpatientDataInfo.ID);
            var OrderCodeList = physicianOrderInfos.Select(m => m.OrderCode).ToList();
            //医嘱来自physicianOrderInfos         
            var orderIDListTemp = hISOrderViews.Where(m => OrderCodeList.Contains(m.OrderCode)).Select(m => m.OrderID).ToList();

            //或者类别是901的，并且包含ClinicalSetting的OrderKeyWord
            var hISOrderViewsTemp = hISOrderViews.Where(m => m.OrderPattern == "901").ToList();
            if (hISOrderViewsTemp.Count > 0)
            {
                foreach (var item in OrderKeyWordList)
                {
                    foreach (var itemOrder in hISOrderViewsTemp)
                    {
                        var flag = itemOrder.OrderContent.Contains(item);
                        if (flag)
                        {
                            orderIDListTemp.Add(itemOrder.OrderID);
                        }
                    }
                }
            }

            hISOrderViews = hISOrderViews.Where(m => orderIDListTemp.Contains(m.OrderID)).ToList();
            var orderIDList = hISOrderViews.Select(m => m.OrderID).Distinct().ToList();
            if (orderIDList.Count <= 0)
            {
                _logger.Info(inpatientDataInfo.CaseNumber + "获取可以同步的医嘱数据为0，返回");
                return false;
            }

            foreach (var item in orderIDList)
            {
                //获取插入的数据,避免数据重复插入
                var orderMainListTemp = orderMainList.Find(m => m.OrderID == item);
                if (orderMainListTemp != null)
                {
                    continue;
                }
                var orderTemp = hISOrderViews.Where(m => m.OrderID == item).ToList();
                //检查人员工号是否同步
                if (checkEmployeeFlag == 1)
                {
                    var resultFalg = CheckEmployee(orderTemp[0], employeeidList, tablename);
                    if (!resultFalg)
                    {
                        _logger.Info(orderTemp[0].CaseNumber + "||" + orderTemp[0].OrderID + "检查人员工号失败，返回" + ListToJson.ToJson(orderTemp[0]));
                        continue;
                    }
                }
                //获取Medical表中的医嘱主记录
                var tempOrderMain = patientOrderMain.Where(m => m.OrderID == item).FirstOrDefault();
                //获取插入的数据,避免数据重复插入
                var frequencyID = GetfrequencyID(orderTemp[0].Frequency, frequencyList);
                //新增主医嘱
                var orderMainId = Guid.NewGuid().ToString("N");
                var orderMain = new Medical.Models.PatientOrderMainInfo();

                //确认orderCode 是否需要展排程              
                var orderCodeTemp = orderTemp.Select(m => m.OrderCode).ToList();
                var physicianToInterventionTemp = physicianToInterventionList.Where(m => orderCodeTemp.Contains(m.OrderCode)).ToList();
                var recordFlag = false;
                if (physicianToInterventionTemp.Count >= 1)
                {
                    recordFlag = true;
                }
                if (tempOrderMain == null)
                {
                    orderMain = InsertOrderMain(orderTemp[0], inpatientDataInfo, employeeidList, orderMainId, frequencyID, recordFlag);
                    orderMainList.Add(orderMain);
                }
                else
                {
                    orderMain = await UpdateOrderMain(tempOrderMain, orderTemp[0], inpatientDataInfo, employeeidList, frequencyID, recordFlag);
                };

                //医嘱明细记录
                var patientOrderDetailTemp = patientOrderDetail.Where(m => m.OrderID == item).ToList();
                var returnData = SyncOrderDetail(orderTemp, orderMain, patientOrderDetailTemp, orderToAssessList, hospitalID);

                try
                {
                    _unitOfWork.SaveChanges();
                }
                catch (Exception ex)
                {
                    _logger.Error(tablename + "||同步失败||" + ex.ToString());
                    return false;
                }
                try
                {
                    _commonHelper.AddProfile(returnData.Item2);
                }
                catch (Exception ex)
                {
                    _logger.Error("呼叫Profile失败" + ex.ToString());
                }
                orderDetailList.AddRange(returnData.Item1);
            }


            await SendMessageByNewOrder(orderMainList, orderDetailList, hospitalID);
            return true;

        }
        /// <summary>
        /// 同步医嘱主表数据根据StationCode
        /// </summary>
        /// <param name="inpatientDataInfo"></param>
        /// <param name="hISOrderViews"></param>
        /// <param name="orderToAssessList"></param>
        /// <param name="physicianToInterventionList"></param>
        /// <param name="frequencyList"></param>
        /// <param name="physicianOrderInfos"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<bool> SyncOrderMainByStationCode(InpatientDataInfo inpatientDataInfo, List<HISOrderView> hISOrderViews, List<OrderToAssessListInfo> orderToAssessList
            , List<PhysicianToInterventionInfo> physicianToInterventionList, List<FrequencyInfo> frequencyList, List<PhysicianOrderInfo> physicianOrderInfos, string hospitalID)
        {
            string tablename = "PatientOrderMain";
            var caseNumber = hISOrderViews[0].CaseNumber;
            var orderMainList = new List<Medical.Models.PatientOrderMainInfo>();
            var orderDetailList = new List<Medical.Models.PatientOrderDetailInfo>();
            #region 数据检核
            //获得相关人员的基本信息        
            var checkEmployeeFlag = 0;
            var resultCheckEmployeeFlag = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "CheckEmployeeFlag");
            if (StringCheck.IsNumeric(resultCheckEmployeeFlag))
            {
                checkEmployeeFlag = int.Parse(resultCheckEmployeeFlag);
            }
            var employeeidList = new List<UserInfo>();
            if (checkEmployeeFlag == 1)
            {
                employeeidList = await GetEmployeeList(hISOrderViews);
            }
            #endregion
            var patientOrderMain = await _IPatientOrderRepository.GetAsync(inpatientDataInfo.ID);
            var patientOrderDetail = await _patientOrderDetailRepository.GetPatientOrderDetail(inpatientDataInfo.ID);
            var OrderCodeList = physicianOrderInfos.Select(m => m.OrderCode).ToList();
            //医嘱来自physicianOrderInfos
            hISOrderViews = hISOrderViews.Where(m => OrderCodeList.Contains(m.OrderCode)).ToList();
            var orderIDList = hISOrderViews.Select(m => m.OrderID).Distinct().ToList();
            if (orderIDList.Count <= 0)
            {
                _logger.Info(inpatientDataInfo.CaseNumber + "获取可以同步的医嘱数据为0，返回");
                return false;
            }

            foreach (var item in orderIDList)
            {
                //获取插入的数据,避免数据重复插入
                var orderMainListTemp = orderMainList.Find(m => m.OrderID == item);
                if (orderMainListTemp != null)
                {
                    continue;
                }
                var orderTemp = hISOrderViews.Where(m => m.OrderID == item).ToList();
                //检查人员工号是否同步
                if (checkEmployeeFlag == 1)
                {
                    var resultFalg = CheckEmployee(orderTemp[0], employeeidList, tablename);
                    if (!resultFalg)
                    {
                        _logger.Info(orderTemp[0].CaseNumber + "||" + orderTemp[0].OrderID + "检查人员工号失败，返回" + ListToJson.ToJson(orderTemp[0]));
                        continue;
                    }
                }
                //获取Medical表中的医嘱主记录
                var tempOrderMain = patientOrderMain.Where(m => m.OrderID == item).FirstOrDefault();
                //获取插入的数据,避免数据重复插入
                var frequencyID = GetfrequencyID(orderTemp[0].Frequency, frequencyList);
                //新增主医嘱
                var orderMainId = Guid.NewGuid().ToString("N");
                var orderMain = new Medical.Models.PatientOrderMainInfo();

                //确认orderCode 是否需要展排程              
                var orderCodeTemp = orderTemp.Select(m => m.OrderCode).ToList();
                var physicianToInterventionTemp = physicianToInterventionList.Where(m => orderCodeTemp.Contains(m.OrderCode)).ToList();
                var recordFlag = false;
                if (physicianToInterventionTemp.Count >= 1)
                {
                    recordFlag = true;
                }
                if (tempOrderMain == null)
                {
                    orderMain = InsertOrderMain(orderTemp[0], inpatientDataInfo, employeeidList, orderMainId, frequencyID, recordFlag);
                    orderMainList.Add(orderMain);
                }
                else
                {
                    orderMain = await UpdateOrderMain(tempOrderMain, orderTemp[0], inpatientDataInfo, employeeidList, frequencyID, recordFlag);
                };

                //医嘱明细记录
                var patientOrderDetailTemp = patientOrderDetail.Where(m => m.OrderID == item).ToList();
                var returnData = SyncOrderDetail(orderTemp, orderMain, patientOrderDetailTemp,orderToAssessList, hospitalID);

                try
                {
                    _unitOfWork.SaveChanges();
                }
                catch (Exception ex)
                {
                    _logger.Error(tablename + "||同步失败||" + ex.ToString());
                    return false;
                }
                try
                {
                    _commonHelper.AddProfile(returnData.Item2);
                }
                catch (Exception ex)
                {
                    _logger.Error("呼叫Profile失败" + ex.ToString());
                    return false;
                }
                orderDetailList.AddRange(returnData.Item1);
            }

            await SendMessageByNewOrder(orderMainList, orderDetailList, hospitalID);
            return true;

        }
        /// <summary>
        /// 发送医嘱消息通知
        /// </summary>
        /// <param name="patientOrderMainInfos"></param>
        /// <param name="patientOrderDetailInfos"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task SendMessageByNewOrder(List<Medical.Models.PatientOrderMainInfo> patientOrderMainInfos
            , List<Medical.Models.PatientOrderDetailInfo> patientOrderDetailInfos, string hospitalID)
        {
            foreach (var item in patientOrderMainInfos)
            {
                var patientOrderDetailInfosTemp = patientOrderDetailInfos.Where(m => m.PatientOrderMainID == item.PatientOrderMainID).ToList();
                //发送短信通知
                try
                {
                    await NewOrderNotifyAsync(item, patientOrderDetailInfosTemp, hospitalID);
                }
                catch (Exception ex)
                {
                    _logger.Error("发送短信通知失败" + ex.ToString());
                }
            }
        }
        //同步一组医嘱
        private Tuple<List<Medical.Models.PatientOrderDetailInfo>, List<PatientProfile>> SyncOrderDetail(List<HISOrderView> hISOrderViews
            , Medical.Models.PatientOrderMainInfo patientOrderMainInfo
            , List<Medical.Models.PatientOrderDetailInfo> patientOrderDetail
            , List<OrderToAssessListInfo> orderToAssessList
            , string hospitalID)
        {
            var deitSettings = _assessListRepository.GetBySystemCode("601", 1).Result;
            var insertlist = new List<Medical.Models.PatientOrderDetailInfo>();
            //医嘱明细写Profile
            var patientProfilesList = new List<PatientProfile>();
            foreach (var item in hISOrderViews)
            {
                //获取插入的数据,避免数据重复插入              
                var tempInsertlist = insertlist.Where(m => m.OrderCode == item.OrderCode
                && m.OrderID == item.OrderID).ToList();
                var tempOrderDetail = patientOrderDetail.Where(m => m.OrderCode == item.OrderCode && m.OrderID == item.OrderID).ToList();
                if (tempOrderDetail.Count < 1 && tempInsertlist.Count < 1)
                {
                    var resultData = InsertPatientOrderDetail(item, patientOrderMainInfo, orderToAssessList, hospitalID);
                    insertlist.Add(resultData.Item1);
                    patientProfilesList.Add(resultData.Item2);
                    _unitOfWork.GetRepository<Medical.Models.PatientOrderDetailInfo>().Insert(resultData.Item1);
                }
                if (tempOrderDetail.Count == 1) //如果查询到这条记录，则更新信息      
                {
                    UpdatePatientOrderDetail(item, patientOrderMainInfo, tempOrderDetail[0], orderToAssessList);
                };
            }
            foreach (var item in patientOrderDetail)
            {
                var hISOrderViewTemp = hISOrderViews.Find(m => m.OrderCode == item.OrderCode);
                if (hISOrderViewTemp == null)
                {
                    item.Delete("TongBu");
                }
            }
            patientProfilesList = patientProfilesList.Where(m => m.InpatientID != null).ToList();
            if (patientProfilesList != null && patientProfilesList.Count > 0)
            {
                var addProfiles = patientProfilesList.Select(m => m.AssessListID).ToList();
                _externalProfileCommonService.DelDietProfile(patientProfilesList[0].InpatientID, addProfiles, deitSettings);
            }
            return new Tuple<List<Medical.Models.PatientOrderDetailInfo>, List<PatientProfile>>(insertlist, patientProfilesList);
        }


        /// <summary>
        /// 获取数据
        /// </summary>
        /// <returns></returns>
        public List<HISOrderView> GetOrderData(string CaseNumber, string hospitalID)
        {
            _logger.Info("开始获取医嘱数据");
            var interconnect_Data = new List<HISOrderView>();
            string apiStr = "";
            var apiStrList = _interconnectSDRepository.GetAsync(1, "2");
            if (apiStrList.Count > 0)
            {
                apiStr = apiStrList[0].Description;
            }
            else
            {
                _logger.Error("获取获取医嘱失败");
                return interconnect_Data;
            }
            _logger.Info("获取医嘱数据");
            var data = new Dictionary<string, string>
            {
                { "CaseNumber",CaseNumber }
            };
            //获取环境 ,1 开发环境
            var systemOperatingEnvironment = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "SystemOperatingEnvironment").Result;
            var resultData = "";
            if (systemOperatingEnvironment != "1")
            {
                //呼叫API获得数据               
                resultData = _commonHelper.GetInterconnectData(apiStr, data);
            }
            else
            {
                resultData = _ILogInfoServices.GetLog("20")[0].Logs;
            }

            //呼叫API获得数据
            var result = new ResponseResult();
            result = JsonConvert.DeserializeObject<ResponseResult>(resultData);

            //从配置当中获取数据 梁宝华 2020-04-29
            var printInterfaceData = 0;
            var resultPrintDate = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "PrintInterfaceData").Result;
            if (StringCheck.IsNumeric(resultPrintDate))
            {
                printInterfaceData = int.Parse(resultPrintDate);
            }
            if (printInterfaceData == 1)
            {
                _logger.Info("获得医嘱数据" + resultData);
            }
            try
            {
                interconnect_Data = JsonConvert.DeserializeObject<List<HISOrderView>>(result.Data.ToString());
            }
            catch (Exception ex)
            {
                _logger.Error("Json格式化失败:" + ex.ToString());
                return interconnect_Data;
            }
            _logger.Info("转换Json数据完成，获得医嘱数据" + interconnect_Data.Count() + "条！");
            return interconnect_Data;
        }

        //检核人员
        private async Task<List<UserInfo>> GetEmployeeList(List<HISOrderView> hISOrderViews)
        {
            List<string> inpCaseNumber = new List<string>();
            List<string> employeeidList = new List<string>();
            foreach (var item in hISOrderViews)
            {
                //新增人员工号
                if (!employeeidList.Contains(item.AddEmployeeID))
                {
                    employeeidList.Add(item.AddEmployeeID);
                }
                //确认医嘱人员工号
                if (!employeeidList.Contains(item.ConfirmPersonID))
                {
                    employeeidList.Add(item.ConfirmPersonID);
                }
                //作废医嘱人员工号
                if (!employeeidList.Contains(item.CancalPersonID))
                {
                    employeeidList.Add(item.CancalPersonID);
                }
            }
            return await _IUserRepository.GetByEmployeeIDList(employeeidList);
        }
        //检核人员信息
        private bool CheckEmployee(HISOrderView hISOrderView, List<UserInfo> employeeidList, string tableName)
        {
            //获取Medical表: EmployeelData中的人员相关信息(新增人员工号)
            var tempAddEmployeeID = employeeidList.Where(m => m.PhysicianID == hISOrderView.AddEmployeeID).ToList();
            if (tempAddEmployeeID.Count < 1)
            {
                _logger.Error(tableName + "表: EmployeelData AddEmployeeID人员工号[" + hISOrderView.AddEmployeeID + "]查询信息错误!");
                return false;
            }
            //获取Medical表: EmployeelData中的人员相关信息(确认医嘱人员工号)
            var tempConfirmPersonID = employeeidList.Where(m => m.PhysicianID == hISOrderView.ConfirmPersonID).ToList();
            if (tempConfirmPersonID.Count < 1)
            {
                _logger.Error(tableName + "表: EmployeelData  ConfirmPersonID人员工号 [" + hISOrderView.ConfirmPersonID + "]查询信息错误!");
                return false;
            }
            //获取Medical表: EmployeelData中的人员相关信息(作废医嘱人员工号)
            var tempCancalPersonID = employeeidList.Where(m => m.PhysicianID == hISOrderView.CancalPersonID).ToList();
            if (tempCancalPersonID.Count < 1)
            {
                _logger.Error(tableName, "表: EmployeelData CancalPersonID人员工号[" + hISOrderView.CancalPersonID + "]查询信息错误!");
                return false;
            }
            return true;
        }

        /// <summary>
        /// 新增医嘱主记录
        /// </summary>
        /// <param name="hISOrderView"></param>
        /// <param name="inpatientDataInfo"></param>
        /// <param name="userList"></param>
        /// <param name="orderMainID"></param>
        /// <param name="dictionary"></param>
        /// <param name="recordFlag"> 护理执行展排程 true 拆分排程</param>
        /// <returns></returns>
        private Medical.Models.PatientOrderMainInfo InsertOrderMain(HISOrderView hISOrderView
           , InpatientDataInfo inpatientDataInfo, List<UserInfo> userList, string orderMainID, Dictionary<int, string> dictionary, bool recordFlag)
        {
            var t = new Medical.Models.PatientOrderMainInfo
            {
                PatientOrderMainID = orderMainID, //医嘱主序号
                OrderID = hISOrderView.OrderID, //序号
                InpatientID = inpatientDataInfo.ID, //住院序号
                PatientID = inpatientDataInfo.PatientID,  //病人序号
                StationID = inpatientDataInfo.StationID,   //病区序号
                BedID = inpatientDataInfo.BedID, //床位序号
                CaseNumber = inpatientDataInfo.CaseNumber, //住院号
                ChartNo = inpatientDataInfo.ChartNo, //病历号
                BedNumber = inpatientDataInfo.BedNumber, //床号
                HISFrequency = hISOrderView.Frequency.ToUpper(),
                FrequencyID = dictionary.Keys.First(),
                HISFrequencySchedule = dictionary.Values.First(),
                RecordFlag = recordFlag
            };
            if (!string.IsNullOrEmpty(hISOrderView.StartDate))
            {
                t.StartDate = Convert.ToDateTime(hISOrderView.StartDate).Date; //开始日期
                t.StartTime = Convert.ToDateTime(hISOrderView.StartDate).TimeOfDay; //开始时间
            }
            if (!string.IsNullOrEmpty(hISOrderView.EndDate))
            {
                t.EndDate = Convert.ToDateTime(hISOrderView.EndDate).Date; //结束日期
                t.EndTime = Convert.ToDateTime(hISOrderView.EndDate).TimeOfDay; //结束时间
            }
            if (!string.IsNullOrEmpty(hISOrderView.AddDate))
            {
                t.AddDate = Convert.ToDateTime(hISOrderView.AddDate).Date; //开立日期
            }
            if (!string.IsNullOrEmpty(hISOrderView.ConfirmDate))
            {
                t.ConfirmDate = Convert.ToDateTime(hISOrderView.ConfirmDate).Date; //确认时间
            }
            if (!string.IsNullOrEmpty(hISOrderView.CancalDate))
            {
                t.CancalDate = Convert.ToDateTime(hISOrderView.CancalDate).Date; //取消时间
            }
            var userListTemp = userList.Where(m => m.PhysicianID == hISOrderView.AddEmployeeID).ToList();
            if (userListTemp.Count > 0)
            {
                t.AddEmployeeID = userListTemp[0].ID.ToString() ?? ""; //开立人员
            }
            else
            {
                t.AddEmployeeID = "";
            }
            userListTemp = userList.Where(m => m.PhysicianID == hISOrderView.ConfirmPersonID).ToList();
            if (userListTemp.Count > 0)
            {
                t.ConfirmPersonID = userListTemp[0].ID.ToString() ?? ""; //确认人员
            }
            else
            {
                t.ConfirmPersonID = "";
            }
            userListTemp = userList.Where(m => m.PhysicianID == hISOrderView.CancalPersonID).ToList();
            if (userListTemp.Count > 0)
            {
                t.CancalPersonID = userListTemp[0].ID.ToString() ?? ""; //取消人员
            }
            else
            {
                t.CancalPersonID = "";
            }
            if (hISOrderView.OrderType == "2")
            {
                t.OrderType = "0";
            }
            else
            {
                t.OrderType = hISOrderView.OrderType;
            }
            t.OrderStatus = hISOrderView.OredrStatus;
            t.ModifyPersonID = MODIFYPERSONID;
            t.ModifyDate = DateTime.Now;
            t.DeleteFlag = "";
            _unitOfWork.GetRepository<Medical.Models.PatientOrderMainInfo>().Insert(t);
            return t;
        }

        //更新医嘱主记录
        private async Task<Medical.Models.PatientOrderMainInfo> UpdateOrderMain(Medical.Models.PatientOrderMainInfo patientOrderMainInfo
            , HISOrderView hISOrderView
            , InpatientDataInfo inpatientDataInfo, List<UserInfo> userList, Dictionary<int, string> frequencyDictionary
            , bool recordFlag)
        {
            var orderStopSign = false;
            var orderStorDateTime = DateTime.Now;
            var upFlag = false;

            if (patientOrderMainInfo.RecordFlag != recordFlag)
            {
                patientOrderMainInfo.RecordFlag = recordFlag;
                upFlag = true;
            }


            if (patientOrderMainInfo.InpatientID != inpatientDataInfo.ID)
            {
                patientOrderMainInfo.InpatientID = inpatientDataInfo.ID;
                upFlag = true;
            }

            if (patientOrderMainInfo.PatientID != inpatientDataInfo.PatientID)
            {
                patientOrderMainInfo.PatientID = inpatientDataInfo.PatientID;
                upFlag = true;
            }


            if (patientOrderMainInfo.StationID != inpatientDataInfo.StationID)
            {
                patientOrderMainInfo.StationID = inpatientDataInfo.StationID;
                upFlag = true;
            }

            if (patientOrderMainInfo.BedID != inpatientDataInfo.BedID)
            {
                patientOrderMainInfo.BedID = inpatientDataInfo.BedID;
                upFlag = true;
            }

            if (patientOrderMainInfo.ChartNo != inpatientDataInfo.ChartNo)
            {
                patientOrderMainInfo.ChartNo = inpatientDataInfo.ChartNo;
                upFlag = true;
            }
            if (patientOrderMainInfo.BedNumber != inpatientDataInfo.BedNumber)
            {
                patientOrderMainInfo.BedNumber = inpatientDataInfo.BedNumber;
                upFlag = true;
            }


            if (!string.IsNullOrEmpty(hISOrderView.StartDate))
            {
                var startDateTime = Convert.ToDateTime(hISOrderView.StartDate);
                if (patientOrderMainInfo.StartDate != startDateTime.Date)
                {
                    patientOrderMainInfo.StartDate = Convert.ToDateTime(hISOrderView.StartDate).Date; //开始日期
                    upFlag = true;
                }
                if (patientOrderMainInfo.StartTime != startDateTime.TimeOfDay)
                {
                    patientOrderMainInfo.StartTime = Convert.ToDateTime(hISOrderView.StartDate).TimeOfDay; //开始时间
                    upFlag = true;
                }
            }

            if (!string.IsNullOrEmpty(hISOrderView.EndDate))
            {
                var endDateTime = Convert.ToDateTime(hISOrderView.EndDate);

                if (patientOrderMainInfo.EndDate != endDateTime.Date)
                {
                    orderStopSign = true;
                    patientOrderMainInfo.EndDate = endDateTime.Date;
                    upFlag = true;
                }
                if (patientOrderMainInfo.EndTime != endDateTime.TimeOfDay)
                {
                    patientOrderMainInfo.EndTime = endDateTime.TimeOfDay;
                    orderStopSign = true;
                    upFlag = true;
                }
                orderStorDateTime = Convert.ToDateTime(hISOrderView.EndDate);
            }
            //重置医嘱结束时间
            if (string.IsNullOrEmpty(hISOrderView.EndDate) && patientOrderMainInfo.EndDate.HasValue)
            {
                patientOrderMainInfo.EndDate = null;
                patientOrderMainInfo.EndTime = null;
                upFlag = true;
            }

            if (!string.IsNullOrEmpty(hISOrderView.AddDate))
            {
                var AddDate = Convert.ToDateTime(hISOrderView.StartDate);
                if (patientOrderMainInfo.AddDate != AddDate)
                {
                    patientOrderMainInfo.AddDate = AddDate; //开立日期
                    upFlag = true;
                }

            }

            if (!string.IsNullOrEmpty(hISOrderView.ConfirmDate))
            {
                var ConfirmDate = Convert.ToDateTime(hISOrderView.ConfirmDate);
                if (patientOrderMainInfo.ConfirmDate != ConfirmDate)
                {
                    patientOrderMainInfo.ConfirmDate = ConfirmDate;
                    upFlag = true;
                }
            }
            //取消时间
            if (!string.IsNullOrEmpty(hISOrderView.CancalDate))
            {
                var CancalDate = Convert.ToDateTime(hISOrderView.CancalDate);
                if (patientOrderMainInfo.CancalDate != CancalDate)
                {
                    patientOrderMainInfo.CancalDate = CancalDate;
                    upFlag = true;
                    orderStopSign = true;
                    orderStorDateTime = Convert.ToDateTime(hISOrderView.CancalDate);
                }
            }

            //重置医嘱结束时间
            if (string.IsNullOrEmpty(hISOrderView.CancalDate) && patientOrderMainInfo.CancalDate.HasValue)
            {
                patientOrderMainInfo.CancalDate = null;
                upFlag = true;
            }

            if (hISOrderView.OrderType == "2")
            {
                hISOrderView.OrderType = "0";
            }
            if (patientOrderMainInfo.OrderType != hISOrderView.OrderType)
            {
                patientOrderMainInfo.OrderType = hISOrderView.OrderType;
                orderStopSign = true;
            }

            if (patientOrderMainInfo.OrderStatus != hISOrderView.OredrStatus)
            {
                patientOrderMainInfo.OrderStatus = hISOrderView.OredrStatus;
                orderStopSign = true;
            }

            var userListTemp = userList.Where(m => m.PhysicianID == hISOrderView.AddEmployeeID).FirstOrDefault();
            if (userListTemp != null && patientOrderMainInfo.AddEmployeeID != userListTemp.ID.ToString())
            {
                patientOrderMainInfo.AddEmployeeID = userListTemp.ID.ToString(); //开立人员
                upFlag = true;
            }

            userListTemp = userList.Where(m => m.PhysicianID == hISOrderView.ConfirmPersonID).FirstOrDefault();
            if (userListTemp != null && patientOrderMainInfo.ConfirmPersonID != userListTemp.ID.ToString())
            {
                patientOrderMainInfo.ConfirmPersonID = userListTemp.ID.ToString(); //确认人员
                upFlag = true;
            }

            userListTemp = userList.Where(m => m.PhysicianID == hISOrderView.CancalPersonID).FirstOrDefault();
            if (userListTemp != null && patientOrderMainInfo.CancalPersonID != userListTemp.ID.ToString())
            {
                patientOrderMainInfo.CancalPersonID = userListTemp.ID.ToString(); //取消人员
                upFlag = true;
            }
            if (patientOrderMainInfo.OrderStatus != hISOrderView.OredrStatus)
            {
                patientOrderMainInfo.OrderStatus = hISOrderView.OredrStatus;
                upFlag = true;
            }
            if (patientOrderMainInfo.HISFrequency != hISOrderView.Frequency)
            {
                patientOrderMainInfo.HISFrequency = hISOrderView.Frequency.ToUpper();
            }
            var frequencyID = frequencyDictionary.Keys.First();
            var hISFrequencySchedule = frequencyDictionary.Values.First();
            if (patientOrderMainInfo.FrequencyID != frequencyID)
            {
                patientOrderMainInfo.FrequencyID = frequencyID;
                upFlag = true;
            }
            if (patientOrderMainInfo.HISFrequencySchedule != hISFrequencySchedule)
            {
                patientOrderMainInfo.HISFrequencySchedule = hISFrequencySchedule;
                upFlag = true;
            }

            if (upFlag)
            {
                _logger.Info("医嘱主记录进行更新！");
                patientOrderMainInfo.ModifyPersonID = MODIFYPERSONID;
                patientOrderMainInfo.ModifyDate = DateTime.Now;
                patientOrderMainInfo.DeleteFlag = "";
            }

            //医嘱停住，停止时间点后的排程
            if (orderStopSign)
            {
                _logger.Info("调用医嘱停住，停止时间点后的排程API");
                await _commonHelper.StopSchduleByOrder(patientOrderMainInfo.InpatientID, patientOrderMainInfo.PatientOrderMainID, orderStorDateTime);
            }
            return patientOrderMainInfo;
        }




        private Tuple<Medical.Models.PatientOrderDetailInfo, PatientProfile> InsertPatientOrderDetail(HISOrderView hISOrderView
            , Medical.Models.PatientOrderMainInfo patientOrderMain
            , List<OrderToAssessListInfo> orderToAssessList, string hospitalID)
        {
            var patientProfile = new PatientProfile();
            var t = new Medical.Models.PatientOrderDetailInfo();
            {
                t.PatientOrderDetailID = t.GetId();  //医嘱明细档序号
                t.PatientOrderMainID = patientOrderMain.PatientOrderMainID;  //医嘱主序号
                t.OrderID = hISOrderView.OrderID ?? ""; //序号
                t.OrderCode = hISOrderView.OrderCode ?? "";   //医嘱编码
                t.OrderPattern = hISOrderView.OrderPattern ?? "";    //医嘱类别
                t.OrderContent = hISOrderView.OrderContent ?? "";    //医嘱内容
                if (StringCheck.IsNumeric(hISOrderView.OrderDose))
                {
                    t.OrderDose = decimal.Parse(hISOrderView.OrderDose);//每次執行量/剂量
                }

                t.Frequency = hISOrderView.Frequency ?? "";  //频次
                t.Unit = hISOrderView.Unit ?? "";  //单位
                if (StringCheck.IsNumeric(hISOrderView.TotalVolume))
                {
                    t.TotalVolume = decimal.Parse(hISOrderView.TotalVolume);//总剂量
                }

                t.OrderRule = hISOrderView.OrderRule ?? "";  //服法/途径/姿势
                t.Location = hISOrderView.Location ?? "";//部位
                t.SpecimenCategory = hISOrderView.MethodCategory ?? "";   //检验类别
                t.OrderType = hISOrderView.OrderType ?? "";
                if (StringCheck.IsNumeric(hISOrderView.NumberOfExecution))
                {
                    t.NumberOfExecution = (byte)int.Parse(hISOrderView.NumberOfExecution);//执行次数
                }
                t.StartDate = patientOrderMain.StartDate; //开始日期
                t.StartTime = patientOrderMain.StartTime; //开始时间
                t.EndDate = patientOrderMain.EndDate; //结束日期
                t.EndTime = patientOrderMain.EndTime; //结束时间
                t.ModifyPersonID = MODIFYPERSONID;
                t.ModifyDate = DateTime.Now;
                t.DeleteFlag = "";
            };
            //查找是否存在AssessListID
            var assessListToOrderCode = orderToAssessList.Where(m => m.OrderCode == t.OrderCode).FirstOrDefault();
            if (assessListToOrderCode != null)
            {
                patientProfile = AddProfile(patientOrderMain, t, assessListToOrderCode.AssessListID, hospitalID);
            }
            return new Tuple<Medical.Models.PatientOrderDetailInfo, PatientProfile>(t, patientProfile);
        }

        private PatientProfile AddProfile(Medical.Models.PatientOrderMainInfo orderMainInfo,
         Medical.Models.PatientOrderDetailInfo orderDetailInfo, int assessListID, string hospitalID)
        {
            var patientProfile = new PatientProfile
            {
                HospitalID = hospitalID,
                InpatientID = orderMainInfo.InpatientID,
                CaseNumber = orderMainInfo.CaseNumber,
                PatientID = orderMainInfo.PatientID,
                ChartNo = orderMainInfo.ChartNo,
                ModelName = "HIS.Order",
                Source = "I",
                ProfileDate = orderMainInfo.StartDate,
                ProfileTime = orderMainInfo.StartTime,

                AutoAddFlag = "",
                Note = "",
                ModifyPersonID = MODIFYPERSONID,
                ModifyDate = DateTime.Now
            };
            patientProfile.SerialNumber = orderDetailInfo.PatientOrderDetailID;
            patientProfile.AssessListID = assessListID;
            return patientProfile;
        }

        private void UpdatePatientOrderDetail(HISOrderView hISOrderView
            , Medical.Models.PatientOrderMainInfo patientOrderMain
            , Medical.Models.PatientOrderDetailInfo patientOrderDetail
            , List<OrderToAssessListInfo> orderToAssessList)
        {
            var upFlag = false;
            //医嘱编码
            if (patientOrderDetail.OrderCode != hISOrderView.OrderCode)
            {
                patientOrderDetail.OrderCode = hISOrderView.OrderCode;
                upFlag = true;
            }

            //医嘱类别
            if (patientOrderDetail.OrderPattern != (hISOrderView.OrderPattern ?? ""))
            {
                patientOrderDetail.OrderPattern = hISOrderView.OrderPattern ?? "";
                upFlag = true;
            }
            //医嘱内容
            if (patientOrderDetail.OrderContent != (hISOrderView.OrderContent ?? ""))
            {
                patientOrderDetail.OrderContent = hISOrderView.OrderContent ?? "";
                upFlag = true;
            }

            //每次執行量/ 剂量
            if (StringCheck.IsNumeric(hISOrderView.OrderDose))
            {
                if (patientOrderDetail.OrderDose != decimal.Parse(hISOrderView.OrderDose))
                {
                    patientOrderDetail.OrderDose = decimal.Parse(hISOrderView.OrderDose);
                    upFlag = true;
                }
            }

            //频次
            if (patientOrderDetail.Frequency != (hISOrderView.Frequency ?? ""))
            {
                patientOrderDetail.Frequency = hISOrderView.Frequency ?? "";
                upFlag = true;
            }

            //单位
            if (patientOrderDetail.Unit != (hISOrderView.Unit ?? ""))
            {
                patientOrderDetail.Unit = hISOrderView.Unit ?? "";
                upFlag = true;
            }

            //总剂量
            if (StringCheck.IsNumeric(hISOrderView.TotalVolume))
            {
                if (patientOrderDetail.TotalVolume != decimal.Parse(hISOrderView.TotalVolume))
                {
                    patientOrderDetail.TotalVolume = decimal.Parse(hISOrderView.TotalVolume);
                    upFlag = true;
                }
            }

            //服法/ 途径 / 姿势
            if (patientOrderDetail.OrderRule != (hISOrderView.OrderRule ?? ""))
            {
                patientOrderDetail.OrderRule = hISOrderView.OrderRule ?? "";
                upFlag = true;
            }

            //部位
            if (patientOrderDetail.Location != (hISOrderView.Location ?? ""))
            {
                patientOrderDetail.Location = hISOrderView.Location ?? "";
                upFlag = true;
            }

            //检验类别
            if (patientOrderDetail.SpecimenCategory != (hISOrderView.MethodCategory ?? ""))
            {
                patientOrderDetail.SpecimenCategory = hISOrderView.MethodCategory ?? "";
                upFlag = true;
            }

            if (patientOrderDetail.OrderType != (patientOrderMain.OrderType ?? ""))
            {
                patientOrderDetail.OrderType = patientOrderMain.OrderType ?? "";
                upFlag = true;
            }

            //执行次数
            if (StringCheck.IsNumeric(hISOrderView.NumberOfExecution))
            {
                if (patientOrderDetail.NumberOfExecution != (byte)int.Parse(hISOrderView.NumberOfExecution))
                {
                    patientOrderDetail.NumberOfExecution = (byte)int.Parse(hISOrderView.NumberOfExecution);
                    upFlag = true;
                }

            }
            //开始日期
            if (patientOrderDetail.StartDate != patientOrderMain.StartDate)
            {
                patientOrderDetail.StartDate = patientOrderMain.StartDate;
                upFlag = true;
            }

            //开始时间
            if (patientOrderDetail.StartTime != patientOrderMain.StartTime)
            {
                patientOrderDetail.StartTime = patientOrderMain.StartTime;
                upFlag = true;
            }

            //结束日期
            if (patientOrderDetail.EndDate != patientOrderMain.EndDate)
            {
                patientOrderDetail.EndDate = patientOrderMain.EndDate;
                upFlag = true;
            }
            //结束时间
            if (patientOrderDetail.EndTime != patientOrderMain.EndTime)
            {
                patientOrderDetail.EndTime = patientOrderMain.EndTime;
                upFlag = true;
            }

            if (upFlag)
            {
                _logger.Info("医嘱明细记录进行更新！");
                patientOrderDetail.ModifyPersonID = MODIFYPERSONID; //异动人员
                patientOrderDetail.ModifyDate = DateTime.Now; //异动时间
                patientOrderDetail.DeleteFlag = "";
            }
        }
        /// <summary>
        /// 获得频次
        /// </summary>
        /// <param name="hisFrequency"></param>
        /// <param name="frequencyList"></param>
        /// <returns></returns>
        private Dictionary<int, string> GetfrequencyID(string hisFrequency, List<FrequencyInfo> frequencyList)
        {
            //频次
            var frequencyID = 0;
            var FrequencyDescription = "";
            var frequencyInfo = frequencyList.Where(m => m.Frequency.ToLower() == hisFrequency.ToLower()).FirstOrDefault();
            if (frequencyInfo == null)
            {
                frequencyID = 0;
            }
            else
            {
                frequencyID = frequencyInfo.ID;
                FrequencyDescription = frequencyInfo.FrequencyDescription;
            }
            var frequency = new Dictionary<int, string>
            {
                {frequencyID, FrequencyDescription }
            };
            return frequency;
        }
        /// <summary>
        /// 发送短信
        /// </summary>
        /// <param name="patientOrders"></param>
        private async Task NewOrderNotifyAsync(Medical.Models.PatientOrderMainInfo item, List<Medical.Models.PatientOrderDetailInfo> patientOrderDetailInfos
            , string hospitalID)
        {
            string content = "";
            var list = new List<MessageModel>();

            content = "【" + item.BedNumber + "床】，";
            if (item.OrderType == "0")
            {
                content += "临时：";
            }
            else
            {
                content += "长期：";
            }
            foreach (var detail in patientOrderDetailInfos)
            {
                content += detail.OrderContent + ",";
            }
            var message = new MessageModel
            {
                MessageKind = "M",
                SendType = "NewOrder",
                SystemID = "HIS.Order",
                MessageTitle = "新医嘱",
                SourceKey = item.OrderID,
                MessageContent = content,
                SendEmployeeID = "",
                SendStationID = item.StationID.ToString(),
                AddDateTime = DateTime.Now,
                IdentifyNumber = item.InpatientID
            };
            if (message.MessageContent.Length > 0)
            {
                message.MessageContent =
                    message.MessageContent.Remove(message.MessageContent.Length - 1);
            }
            if (message.SourceKey.Length > 0)
            {
                message.SourceKey =
                    message.SourceKey.Remove(message.SourceKey.Length - 1);
            }
            list.Add(message);
            var sendMessageAPI = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "SendMQMessageAPI").Result;
            _logger.Info("sendMessageAPI地址:" + sendMessageAPI);
            _logger.Info("sendMessageAPI参数:" + JsonConvert.SerializeObject(list));
            var str = await HttpHelper.HttpPostAsync(sendMessageAPI, JsonConvert.SerializeObject(list), "application/json", 600, null);
            _logger.Info("sendMessageAPI结果:" + str);
        }
    }
}
