﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Arch.EntityFrameworkCore.UnitOfWork;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.Query;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NLog;
using NPOI.SS.Formula.Functions;

namespace Interconnect.Services
{
    public class EmployeeService : IEmployeeService
    {
        //Interconnect数据库          
        private readonly IStationListRepository _IStationListRepository;
        private readonly IVirtualStationListRepository _virtualstationListRepository;
        private readonly IDepartmentListRepository _IDepartmentListRepository;
        private readonly IEmployeeDepartmentSwitchRepository _IEmployeeDepartmentSwitchRepository;
        private readonly ICommonHelper _commonHelper;
        private readonly ISettingDescRepository _ICSettingDescriptionRepository;
        private readonly IHospitalListRepository _hospitalListRepository;
        //Mdeical数据库
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IUserRepository _userRepository;
        private readonly IUserRoleRepository _userRoleRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly ILogInfoServices _ILogInfoServices;
        private readonly IEmployeeCADataRepository _employeeCADataRepository;
        private readonly IOptions<SystemConfig> _config;

        //日志
        private static Logger _logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";
        /// <summary>
        /// 系统默认操作人
        /// </summary>
        private string MODIFYPERSONID = "SYS_TongBu";

        public EmployeeService(
              IUnitOfWork<MedicalDbContext> UnitOfWork
            , IUserRepository userRepository
            , IStationListRepository stationListRepository
            , IEmployeeDepartmentSwitchRepository EmployeeDepartmentSwitchRepository
            , IDepartmentListRepository DepartmentListRepository
            , IUserRoleRepository userRoleRepository
            , ICommonHelper commonHelper
            , ISettingDescRepository settingDescriptionRepository
            , IVirtualStationListRepository virtualstationListRepository
            , IAppConfigSettingRepository appConfigSettingRepository
            , IHospitalListRepository hospitalListRepository
            , ILogInfoServices logInfoServices
            , IEmployeeCADataRepository employeeCADataRepository
            , IOptions<SystemConfig> config
          )
        {
            _unitOfWork = UnitOfWork;
            _userRepository = userRepository;
            _IStationListRepository = stationListRepository;
            _IEmployeeDepartmentSwitchRepository = EmployeeDepartmentSwitchRepository;
            _IDepartmentListRepository = DepartmentListRepository;
            _userRoleRepository = userRoleRepository;
            _commonHelper = commonHelper;
            _virtualstationListRepository = virtualstationListRepository;
            _ICSettingDescriptionRepository = settingDescriptionRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
            _hospitalListRepository = hospitalListRepository;
            _ILogInfoServices = logInfoServices;
            _employeeCADataRepository = employeeCADataRepository;
            _config = config;
        }

        /// <summary>
        /// 获取人员信息
        /// </summary>
        /// <returns></returns>
        public async Task<bool> Synchronization()
        {
            var hospitalInfo =  _hospitalListRepository.GetHospitalInfo();
            if (hospitalInfo == null)
            {
                _logger.Error("获取医院信息失败");
                return false;
            }
            var hospitalID = hospitalInfo.HospitalID;
            var hisResultData = GetApiData(hospitalID);
           // var hisResultData = ReadFile.ReadTxt(@"D:\YCInterconnectJson\病人基本信息.txt");
            if (hisResultData == "")
            {
                return false;
            }
            var hisEmployeeList = CreateEmployeeInfo(hisResultData);
            if (hisEmployeeList == null || hisEmployeeList.Count <= 0)
            {
                return false;
            }
            _logger.Info("获得人员数据：" + hisEmployeeList.Count() + "条记录");

            //同步人员信息
            var returFlag = await SynchronizationDetail(hisEmployeeList, hospitalID);

            //var deleteFlag = EmployeeDelete(hisEmployeeList);
            //if (!deleteFlag)
            //{
            //    _logger.Error("删除对方不存在的人员信息失败！||" + DateTime.Now.ToString());
            //}
            try
            {
                //更新人员缓存
                CacheQuery query = new CacheQuery
                {
                    Type = CacheType.EmployeelData
                };
                _commonHelper.UpdateCache(query);
                //更新护士级别缓存
                query.Type = CacheType.UserRole;
                _commonHelper.UpdateCache(query);
                //更新护士病区权限缓存
                query.Type = CacheType.EmployeeDepartmentSwitch;
                _commonHelper.UpdateCache(query);
            }
            catch (Exception ex)
            {
                _logger.Info("人员缓存更新失败" + ex.Message);
            }
            return returFlag;
        }

        private List<EmployeeInfo> CreateEmployeeInfo(string hisResultData)
        {
            var responseResult = new ResponseResult();
            try
            {
                responseResult = JsonConvert.DeserializeObject<ResponseResult>(hisResultData);
                if (responseResult == null || responseResult.Data == null)
                {
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.Error("hisResultData格式化失败:" + ex.ToString());
                return null;
            }

            var hisEmployeeList = new List<EmployeeInfo>();
            try
            {
                hisEmployeeList = JsonConvert.DeserializeObject<List<EmployeeInfo>>(responseResult.Data.ToString());
                //hisEmployeeList = JsonConvert.DeserializeObject<List<EmployeeInfo>>(hisResultData);
            }
            catch (Exception ex)
            {
                _logger.Error("Json格式化失败:" + ex.ToString());
                return null;
            }

            if (hisEmployeeList.Count <= 0)
            {
                _logger.Error("从接口获取数据失败，没有获得到任何数据");
                return null;
            }
            return hisEmployeeList;
        }

        /// <summary>
        /// 同步新增人员数据
        /// </summary>
        /// <returns></returns>
        #region 同步新增人员数据
        private async Task<bool> SynchronizationDetail(List<EmployeeInfo> hisEmployeeList, string hospitalID)
        {
            string Tablename = "Employee";
            var medicalStationList =  _IStationListRepository.GetAllStation();
            //记录预同步数据条数
            _logger.Info(Tablename + " 开始进行数据同步，数据条数：" + hisEmployeeList.Count);
            var user = await _userRepository.GetAllUser();
            user = user.Where(m => m.HospitalID == hospitalID).ToList();
            var userRoleList =  _userRoleRepository.NoCacheGetAllRole();
            var caUserList = await _employeeCADataRepository.GetEmployeeCANoCacheAsync();
            List<UserRoleInfo> addRoleList = new List<UserRoleInfo>();
            List<UserInfo> addUserInfo = new List<UserInfo>();
            List<EmployeeCADataInfo> addCaUser = new List<EmployeeCADataInfo>();
            foreach (var item in hisEmployeeList)
            {   //根据Employee；现在改成了直接从新表中查询
                var employeeInfo = user.Find(m => m.UserID == item.EmployeeID.Trim());
                //获取Medical中的病区信息 
                int medicalSataionID = 0;
                var medicalStationInfo = medicalStationList.Where(m => m.StationCode.Trim() == item.StationCode.Trim()).FirstOrDefault();
                if (medicalStationInfo != null)
                {
                    medicalSataionID = medicalStationInfo.ID;
                }
                //处理人员病区权限
                if (medicalStationInfo != null)
                {
                    await UpEmployeeDepartmentSwitch(item.EmployeeID, item.StationCode, MODIFYPERSONID);
                }
                //同步人员权限
                var userRoleInfo = UpEmployeeRole(item, userRoleList, medicalStationInfo);
                if (userRoleInfo != null)
                {
                    addRoleList.Add(userRoleInfo);
                }
                //同步CAUser
                var caUserInfo = UpCaUser(item, caUserList);
                if (caUserInfo != null)
                {
                    addCaUser.Add(caUserInfo);
                }
                if (employeeInfo != null)
                {
                    if (employeeInfo.SpecialFlag == 1)
                    {
                        continue;
                    };
                    //把图片写入新表中
                    // await _certificateAuthorityService.QuerySignImageAsync(employeeCADataInfo.CAUserID, hospitalID, 1);
                    UPEmployeeInfo(item, employeeInfo, medicalSataionID, hospitalID);
                    continue;
                }
                var currEmployeeCAID = _employeeCADataRepository.GetNextCAIDAsync();
                //操作Medical库数据
                var userInfo = CreatreEmployeeInfo(item, medicalSataionID, hospitalID, currEmployeeCAID);
                addUserInfo.Add(userInfo);
            }
            try
            {
                _unitOfWork.GetRepository<UserRoleInfo>().Insert(addRoleList);
                _unitOfWork.GetRepository<UserInfo>().Insert(addUserInfo);
                _unitOfWork.GetRepository<EmployeeCADataInfo>().Insert(addCaUser);
                _unitOfWork.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error(Tablename + "|| 同步人员" + ListToJson.ToJson(hisEmployeeList) + "保存失败" + ex.ToString());
            }
            _logger.Info(Tablename + "    同步完成!" + DateTime.Now);
            return true;
            #endregion
        }
        private EmployeeCADataInfo UpCaUser(EmployeeInfo hisEmployeeInfo, List<EmployeeCADataInfo> employeeCADataInfos)
        {
            //根据EmployeeCADataInfo
            var oldCaUser = employeeCADataInfos.Find(m => m.UserID == hisEmployeeInfo.EmployeeID);
            if (oldCaUser == null)
            {
                var employeeCADataInfo = new EmployeeCADataInfo()
                {
                    UserID = hisEmployeeInfo.EmployeeID,
                    HospitalID = "3",
                    DeleteFlag = "",
                    ModifyPersonID = MODIFYPERSONID,
                    ModifyDate = DateTime.Now,
                    CAUserID = hisEmployeeInfo.UserID,
                    AddDateTime = DateTime.Now,
                    AddPersonID = MODIFYPERSONID,
                };
                return employeeCADataInfo;
            }
            else
            {
                if (oldCaUser.CAUserID!= hisEmployeeInfo.UserID)
                {
                    oldCaUser.CAUserID = hisEmployeeInfo.UserID;
                    oldCaUser.ModifyPersonID = MODIFYPERSONID;
                    oldCaUser.ModifyDate = DateTime.Now;
                }
            }
            return null;
        }
        /// <summary>
        /// EmployeeRole同步
        /// </summary>
        /// <param name="hisEmployee"></param>
        /// <param name="userRoleList"></param>
        /// <returns></returns>
        private UserRoleInfo UpEmployeeRole(EmployeeInfo hisEmployee, List<UserRoleInfo> userRoleList,StationListInfo station)
        {
            UserRoleInfo addRole = null;
            var roles = userRoleList.Where(m => m.EmployeeID.Trim() == hisEmployee.EmployeeID).ToList();
            if(hisEmployee.Title== "护士长")
            {
                var sucRole = roles.Find(m => m.AuthorityRoleListID == 40);
                if (sucRole == null)
                {
                    addRole = new UserRoleInfo()
                    {
                        EmployeeID = hisEmployee.EmployeeID,
                        AuthorityRoleListID = 40,
                        AddEmployeeID = "sys",
                        DeleteFlag = "",
                        AddDate = DateTime.Now,
                        ModifyPersonID = "sys",
                        ModifyDate = DateTime.Now,
                        HospitalID="3"
                    };
                }
                //处理病区护士长
                //if(station!=null&&station.HeadNurse!= hisEmployee.EmployeeName)
                //{
                //    station.HeadNurse = hisEmployee.EmployeeName;
                //}
            }
            else
            {
                var sucRole = roles.Find(m => m.AuthorityRoleListID == 30);
                if (sucRole == null)
                {
                    addRole= new UserRoleInfo()
                    {
                        EmployeeID = hisEmployee.EmployeeID,
                        AuthorityRoleListID = 30,
                        AddEmployeeID = "sys",
                        DeleteFlag = "",
                        AddDate = DateTime.Now,
                        ModifyPersonID = "sys",
                        ModifyDate = DateTime.Now,
                        HospitalID = "3"
                    };
                }
            }
            return addRole;
        }

        /// <summary>
        /// 同步人员权限
        /// </summary>
        /// <param name="employeeID"></param>
        /// <param name="stationCode"></param>
        /// <param name="addUserID"></param>
        /// <returns></returns>
        public async Task<bool> UpEmployeeDepartmentSwitch(string employeeID, string stationCode, string addUserID)
        {
            //获取所有的病区
            List<EmployeeStationSwitchInfo> list = new List<EmployeeStationSwitchInfo>();

            var result = await CreateEmployeeDepartmentSwitch(employeeID, stationCode, addUserID);
            if (!result)
            {
                return result;
            }
            //获取所有虚拟病区
           // var virtualStationList = await _virtualstationListRepository.GetVirtualStationInfoByHISstationCodeAsync(stationCode);
            //获取虚拟病区，如果虚拟病区
            //foreach (var virtualStation in virtualStationList)
            //{
            //    result = await CreateEmployeeDepartmentSwitch(employeeID, virtualStation.VirtualStationCode, addUserID);
            //    if (!result)
            //    {
            //        return result;
            //    }
            //}
            return result;
        }

        /// <summary>
        /// 确认权限是否存在,进行处理
        /// </summary>
        /// <param name="employeeID"></param>
        /// <param name="stationCode"></param>
        /// <param name="addUserID"></param>
        /// <returns></returns>
        private async Task<bool> CreateEmployeeDepartmentSwitch(string employeeID, string stationCode, string addUserID)
        {
            var hospitalID = _config.Value.HospitalID;
            //根据人员的id，获取人员科别对应关系
            var employeeDepartmentSwitch = await _IEmployeeDepartmentSwitchRepository.GetEmployeeSwitchByemployeeID(employeeID, stationCode);
            //遍历所有病区，找到病区的Code
            if (employeeDepartmentSwitch == null)
            {
                var t = new EmployeeDepartmentSwitchInfo
                {
                    EmployeeID = employeeID,
                    DepartmentCode = stationCode,
                    ModifyPersonID = addUserID,
                    ModifyDate = DateTime.Now,
                    DeleteFlag = "",
                    HospitalID = hospitalID
                };
                _unitOfWork.GetRepository<EmployeeDepartmentSwitchInfo>().Insert(t);
            }
            else
            {
                if (employeeDepartmentSwitch.DeleteFlag == "*")
                {
                    employeeDepartmentSwitch.DeleteFlag = "";
                    employeeDepartmentSwitch.ModifyDate = DateTime.Now;
                }
            }
            return true;
        }

        /// <summary>
        /// 更新数据
        /// </summary>
        /// <param name="item"></param>
        /// <param name="cccUserInfo"></param>
        /// <param name="stationID"></param>
        /// <param name="hospitalID"></param>
        private void UPEmployeeInfo(EmployeeInfo item, UserInfo cccUserInfo, int stationID, string hospitalID)
        {
            var upFlag = false;
            if (cccUserInfo.Name != item.EmployeeName)
            {
                cccUserInfo.Name = item.EmployeeName;
                upFlag = true;
            }

            //if (cccUserInfo.Password != item.Password)
            //{
            //    cccUserInfo.Password = item.Password;
            //    upFlag = true;
            //}
            // 同步修改数据
            if (cccUserInfo.PhysicianID != item.PhysicianID)
            {
                cccUserInfo.PhysicianID = item.PhysicianID;
                upFlag = true;
            }

            if (cccUserInfo.StationID != stationID)
            {
                cccUserInfo.StationID = stationID;
                upFlag = true;
            }

            if (cccUserInfo.Title != (item.Title ?? ""))
            {
                cccUserInfo.Title = item.Title ?? "";
                upFlag = true;
            }

            if (cccUserInfo.Rank != (item.Rank ?? ""))
            {
                cccUserInfo.Rank = item.Rank ?? "";
                upFlag = true;
            }

            if (cccUserInfo.HospitalID != hospitalID)
            {
                cccUserInfo.Rank = hospitalID;
                upFlag = true;
            }

            if (cccUserInfo.DeleteFlag != (item.DeleteFlag??""))
            {
                cccUserInfo.DeleteFlag = item.DeleteFlag??"";
                upFlag = true;
            }
            if (upFlag)
            {
                cccUserInfo.ModifyPersonID = MODIFYPERSONID;
                cccUserInfo.ModifyDate = DateTime.Now;
            }
        }
        /// <summary>
        /// 同步新增数据到新表CA中
        /// </summary>
        /// <param name="item"></param>
        /// <param name="hospitalID"></param>
        private void InsertEmployeeCADataInfo(EmployeeInfo item, string hospitalID)
        {
            var employeeCADataInfo = new EmployeeCADataInfo()
            {
                UserID = item.EmployeeID,
                HospitalID = hospitalID,
                DeleteFlag = item.DeleteFlag ?? "",
                ModifyPersonID = MODIFYPERSONID,
                ModifyDate = DateTime.Now,
                CAUserID = item.UserID,
                AddDateTime = DateTime.Now,
                AddPersonID = MODIFYPERSONID,
            };
            _unitOfWork.GetRepository<EmployeeCADataInfo>().Insert(employeeCADataInfo);
        }

        private UserInfo CreatreEmployeeInfo(EmployeeInfo item, int stationID, string hospitalID, int ID)
        {
            // 同步新增数据
            var userInfo = new UserInfo
            {
                UserID = item.EmployeeID,
                Name = item.EmployeeName ?? "",
                Password ="Aa"+ item.EmployeeID,
                PhysicianID = item.PhysicianID ?? item.EmployeeID,
                StationID = stationID,
                Title = item.Title ?? "",
                Rank = item.Rank ?? "",
                HospitalID = hospitalID,
                DeleteFlag = item.DeleteFlag ?? "",
                ModifyPersonID = MODIFYPERSONID,
                ModifyDate = DateTime.Now,
                //CAUserID = item.UserID
            };
            return userInfo;
            
        }

        /// <summary>
        /// 数据对比，删除对方不存在的人员信息
        /// </summary>
        /// <returns></returns>
        private bool EmployeeDelete(List<EmployeeInfo> hisEmployee)
        {
            if (hisEmployee.Count <= 0)
            {
                return true;
            }
            //人员基本信息变更的处理
            var medicalUserList = _userRepository.GetUserList();
            medicalUserList = medicalUserList.Where(m => m.SpecialFlag != 1).ToList();
            foreach (var item in medicalUserList)
            {
                var hisEmployeeInfo = hisEmployee.Where(t => t.EmployeeID == item.UserID).FirstOrDefault();
                // 如果找不到这个人员，则停止这个人员的所有权限信息
                if (hisEmployeeInfo == null)
                {
                    item.DeleteFlag = "*";
                    continue;
                }
            }

            try
            {
                _unitOfWork.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error("数据对比，删除对方不存在的人员信息失败" + ex.ToString());
                return false;
            }
            return true;
        }

        //同步人员权限
        public bool SyncEmployeeRole(string hospitalID)
        {
            var userRoleList = new List<UserRoleInfo>();
            var userList = _userRepository.GetByEmployeeIDNoRole();
            var noRoleEmployeeList = _userRoleRepository.GetNoRoleEmployee();
            var employeeDefaultRole = 0;
            var resultDefaultRole = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "EmployeeDefaultRole").Result;
            if (StringCheck.IsNumeric(resultDefaultRole))
            {
                employeeDefaultRole = int.Parse(resultDefaultRole);
            }
            else
            {
                employeeDefaultRole = 2;
            }

            //新增权限
            foreach (var item in userList)
            {
                var t = new UserRoleInfo()
                {
                    EmployeeID = item,
                    AuthorityRoleListID = Convert.ToInt32(employeeDefaultRole),
                    AddEmployeeID = "sys",
                    DeleteFlag = "",
                    AddDate = DateTime.Now,
                    ModifyPersonID = "sys",
                    ModifyDate = DateTime.Now
                };
                userRoleList.Add(t);
            }

            //停止权限
            foreach (var item in noRoleEmployeeList)
            {
                item.DeleteFlag = "*";
            }

            try
            {
                _unitOfWork.GetRepository<UserRoleInfo>().Insert(userRoleList);
                _unitOfWork.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error("|| 权限同步失败" + ex.ToString());
                return false;
            }
            return true;
        }

        private string GetApiData(string hospitalID)
        {
            _logger.Info("开始获员工基本信息api");
            string apiStr = "";
            var apiStrList = _ICSettingDescriptionRepository.GetAsync(1, "5");
            if (apiStrList.Count > 0)
            {
                apiStr = apiStrList[0].Description;
            }
            else
            {
                _logger.Error("获得员工基本信息API失败");
                return "";
            }
            _logger.Info("开始获人员数据");
            var data = new Dictionary<string, string>();

            //获取环境 ,1 开发环境
            var resultData = "";
            var systemOperatingEnvironment = _appConfigSettingRepository.GetConfigSettingValue("Configs", "SystemOperatingEnvironment").Result;
            if (systemOperatingEnvironment != "1")
            {
                //呼叫API获得数据
                try
                {
                    resultData = _commonHelper.GetInterconnectData(apiStr, data);
                }
                catch (Exception ex)
                {
                    _logger.Error("呼叫API,失败 ，API:[" + apiStr + "]参数: " + ListToJson.ToJson(data) + ex.ToString());
                    return "";
                }
            }
            else
            {
                resultData = _ILogInfoServices.GetLog("50")[0].Logs;
            }

            var printInterfaceData = 0;
            var resultPrintDate = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "PrintInterfaceData").Result;
            if (StringCheck.IsNumeric(resultPrintDate))
            {
                printInterfaceData = int.Parse(resultPrintDate);
            }

            if (printInterfaceData == 1)
            {
                _logger.Info("获得数据如下");
                _logger.Info(resultData);
            }
            return resultData;
        }
    }
}