﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class EventLogInfoRepository : IEventLogInfoRepository
    {
        private MedicalDbContext _dbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        public EventLogInfoRepository(MedicalDbContext db
            , SessionCommonServer sessionCommonServer)
        {
            _dbContext = db;
            _sessionCommonServer = sessionCommonServer;
        }

        public async Task<List<EventLogInfo>> GetAsync()
        {
            return await _dbContext.EventLogs.ToListAsync();
        }

        public async Task<bool> AddAsync(EventLogInfo t)
        {
            if (t == null)
            {
                return false;
            }
            _dbContext.EventLogs.Add(t);
            return await _dbContext.SaveChangesAsync() > 0;
        }

        public async Task<bool> UpdateAsync(EventLogInfo t)
        {
            _dbContext.EventLogs.Update(t);
            return await _dbContext.SaveChangesAsync() > 0;
        }

        public async Task<List<EventLogInfo>> GetOrdersPrintStatusLog()
        {
            var statisticsTimeSpan = 10;
            var eventLogList = await _dbContext.EventLogs.Where(m => m.Target == "OrdersPrintStatus" && m.CreateTime < DateTime.Now && m.CreateTime > DateTime.Now.AddMinutes(-statisticsTimeSpan)).ToListAsync();
            return eventLogList;
        }
        public string GetCacheType()
        {
            return CacheType.SettingDescription.GetKey(_sessionCommonServer);
        }
        public async Task<List<EventLogInfo>> GetEventLogInfosAsync(DateTime dateTime)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            return await _dbContext.EventLogs.Where(m => m.HospitalID == hospitalID && m.CreateTime.Date == dateTime.Date).OrderBy(m => m.CreateTime).ToListAsync();
        }
        /// <summary>
        /// 根据时间段获取日志
        /// </summary>
        /// <param name="startDateTime"></param>
        /// <param name="endDateTime"></param>
        /// <returns></returns>
        public async Task<List<EventLogInfo>> GetEventLogInfosByDateTimeRangeAsync(DateTime startDateTime, DateTime endDateTime)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            return await _dbContext.EventLogs.Where(m => m.HospitalID == hospitalID && m.CreateTime >= startDateTime && m.CreateTime <= endDateTime
            ).OrderBy(m => m.CreateTime).ToListAsync();
        }
    }
}