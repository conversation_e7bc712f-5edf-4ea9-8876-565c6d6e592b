﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Arch.EntityFrameworkCore.UnitOfWork;
using Interconnect.Data.Context;
using Interconnect.Data.Interface;
using Interconnect.Data.Interface.Repository.Log;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Interconnect.ViewModels;
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.Submit;
using Medical.ViewModels.View;
using MedicalExternalCommon.Service;
using Microsoft.Extensions.Options;
using NLog;

namespace Interconnect.Services
{
    public class SyncPatientHISDataService : ISyncPatientHISDataService
    {
        //Mdeical
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IInpatientDataRepository _IInpatientDataRepository;
        private readonly IUnitOfWork<DataOutConnection> _unitOfWorkOut;
        private readonly IOptions<Models.SystemConfig> _config;
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        public readonly IExecTableDictRepository _execTableDictRepository;
        private readonly IJobLogService _jobLogService;
        private readonly ISyncLogService _syncLogService;
        private readonly ISyncDatasLogRepository _syncDatasLogRepository;
        private readonly IPatientScheduleMainRepository _patientScheduleMainRepository;
        private readonly INursingInterventionDetailRepository _nursingInterventionDetailRepository;
        private readonly INursingInterventionMainRepository _nursingInterventionMainRepository;
        private readonly StationaShiftCommonService _stationShiftCommonService;
        private readonly IPatientScheduleDetailRepository _patientScheduleDetailRepository;
        private readonly ICommonNursingRecordDetailLogService _commonDetailLogService;
        private readonly IPatientIntakeOutputRepository _patientIntakeOutputRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly IClinicSettingRepository _clinicSettingRepository;
        private readonly IIntakeOutputSettingRepository _intakeOutputSettingRepository;
        private readonly IAssessListIDToColorRepository _assessListIDToColorRepository;
        #region --常量配置
        /// <summary>
        /// 转出床事件ID
        /// </summary>
        private const int ASSESSLISTID_6633 = 6633;
        /// <summary>
        /// 其他类型的病情观察ID
        /// </summary>
        private const string PATIENTMEASURES_OTHER = "012f9cbdf7e04c88b5297f8810290048";
        #endregion
        public SyncPatientHISDataService(
              IUnitOfWork<MedicalDbContext> UnitOfWork
            , IUnitOfWork<DataOutConnection> UnitOfWorkOut
            , IInpatientDataRepository inpatientDataRepository
            , IOptions<Models.SystemConfig> config
            , IExecTableDictRepository execTableDictRepository
            , IJobLogService jobLogService
            , ISyncLogService syncLogService
            , ISyncDatasLogRepository syncDatasLogRepository
            , IPatientScheduleMainRepository patientScheduleMainRepository
            , INursingInterventionDetailRepository nursingInterventionDetailRepository
            , INursingInterventionMainRepository nursingInterventionMainRepository
            , StationaShiftCommonService stationShiftCommonService
            , IPatientScheduleDetailRepository patientScheduleDetailRepository
            , ICommonNursingRecordDetailLogService commonNursingRecordDetailLogService
            , IPatientIntakeOutputRepository patientIntakeOutputRepository
            , IAppConfigSettingRepository appConfigSettingRepository
            , IClinicSettingRepository clinicSettingRepository
            , IIntakeOutputSettingRepository intakeOutputSettingRepository
            , IAssessListIDToColorRepository assessListIDToColorRepository

            )
        {
            _unitOfWork = UnitOfWork;
            _unitOfWorkOut = UnitOfWorkOut;
            _IInpatientDataRepository = inpatientDataRepository;
            _config = config;
            _execTableDictRepository = execTableDictRepository;
            _jobLogService = jobLogService;
            _syncLogService = syncLogService;
            _syncDatasLogRepository = syncDatasLogRepository;
            _patientScheduleMainRepository = patientScheduleMainRepository;
            _nursingInterventionDetailRepository = nursingInterventionDetailRepository;
            _nursingInterventionMainRepository = nursingInterventionMainRepository;
            _stationShiftCommonService = stationShiftCommonService;
            _patientScheduleDetailRepository = patientScheduleDetailRepository;
            _commonDetailLogService = commonNursingRecordDetailLogService;
            _patientIntakeOutputRepository = patientIntakeOutputRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
            _clinicSettingRepository = clinicSettingRepository;
            _intakeOutputSettingRepository = intakeOutputSettingRepository;
            _assessListIDToColorRepository = assessListIDToColorRepository;
        }

        /// <summary>
        /// 同步患者排程数据
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncPatientScheduleData()
        {
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.ScheduleVitalSign).ToString();
            var jobName = "同步病人生命体征数据到排程";
            var logMsg = "作业编号:" + guid + "启动同步任务:";
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            if (!jobStatus)
            {
                _logger.Info("作业正在启动" + logMsg);
                return false;
            }
            _syncLogService.InsertSyncLog(3, guid, "SyncPatientScheduleData", "", "开始同步生命体征", "Sys", true);
            var successFlag = true;
            try
            {
                successFlag = await SyncPatientSchedule();
            }
            catch (Exception ex)
            {
                _logger.Error("SyncPatientScheduleData" + ex.ToString());
                _syncLogService.InsertSyncLog(1, guid, "SyncPatientScheduleData", "", "同步病人生命体征失败", "Sys", true);
            }
            if (successFlag)
            {
                _syncLogService.InsertSyncLog(3, guid, "SyncPatientScheduleData", "", "同步病人生命体征成功", "Sys", true);
            }
            else
            {
                _syncLogService.InsertSyncLog(1, guid, "SyncPatientScheduleData", "", "同步病人生命体征失败", "Sys", true);
            }
            _jobLogService.RemoveJob(jobId, jobName, "");
            return successFlag;
        }
        /// <summary>
        /// 同步患者排程数据
        /// </summary>
        /// <returns></returns>
        private async Task<bool> SyncPatientSchedule()
        {
            var hospitalID = _config.Value.HospitalID;
            bool returnFlag = true;
            //获取同步表的数据
            var syncDatasLogs = _syncDatasLogRepository.GetSyncDataByDataType(hospitalID, "VitalSign");
            if (syncDatasLogs.Count == 0)
            {
                _logger.Error("SyncDatasLog中没有相应的数据");
                return false;
            }
            foreach (var item in syncDatasLogs)
            {
                var syncDatasLogInfo = await _syncDatasLogRepository.GetSyncDataByIDAsync(item);
                if (syncDatasLogInfo == null)
                {
                    ModifySyncDataLog(syncDatasLogInfo, false);
                    continue;
                }
                try
                {
                    returnFlag = await SyncPatientVitalSignData(syncDatasLogInfo.SyncData);
                }
                catch (Exception ex)
                {
                    _logger.Error($"数据转换异常，数据内容【{syncDatasLogInfo.SyncData}】，异常信息{ex}");
                    continue;
                }
                if (!returnFlag)
                {
                    _logger.Error("同步患者数据失败，caseNumber:" + syncDatasLogInfo.CaseNumber);
                }
                ModifySyncDataLog(syncDatasLogInfo, returnFlag);
            }
            //测试代码先不删
            //var syncData = ReadFile.ReadTxt(@"C:\Users\<USER>\Desktop\json\yinchuanschedule.txt");
            //await SyncPatientVitalSignData(syncData);
            //_unitOfWork.SaveChanges();
            //return true;
            return returnFlag;
        }
        /// <summary>
        /// 更新同步记录
        /// </summary>
        /// <param name="syncDataLog"></param>
        /// <param name="syncResult"></param>
        /// <returns></returns>
        public bool ModifySyncDataLog(SyncDataLogInfo syncDataLog, bool syncResult)
        {
            if (syncDataLog == null)
            {
                return false;
            }
            _logger.Info("更新同步标记ModifySyncDataLog,ID:" + syncDataLog.ID);
            if (syncResult)
            {
                syncDataLog.DataPumpFlag = "*";
                syncDataLog.DataPumpDate = DateTime.Now;
            }

            syncDataLog.Counts ??= 0;
            syncDataLog.Counts++;

            syncDataLog.DataPumpDate = DateTime.Now;
            syncDataLog.ModifyPersonID = "Sys";
            try
            {
                _unitOfWorkOut.SaveChanges();
                _logger.Info("更新同步日志完成ModifySyncDataLog,ID:" + syncDataLog.ID);
            }
            catch (Exception ex)
            {
                _logger.Error("更新数据同步记录错误(ModifySyncDataLog:" + syncDataLog.ID + ex.ToString());
                return false;
            }
            return true;
        }
        /// <summary>
        /// 同步生命体征数据至排程
        /// </summary>
        /// <param name="syncData"></param>
        /// <returns></returns>
        private async Task<bool> SyncPatientVitalSignData(string syncData)
        {
            var scheduleVitalSignData = ListToJson.ToList<ScheduleVitalSignView>(syncData);
            if (scheduleVitalSignData == null)
            {
                _logger.Error($"数据转换失败，写入数据库失败，原始数据为：【{ListToJson.ToJson(scheduleVitalSignData)}】");
                return false;
            }
            var inpatient = await _IInpatientDataRepository.GetInpatientViewByCaseNumber(scheduleVitalSignData.Data[0].CaseNumber);
            if (inpatient == null)
            {
                _logger.Error($"患者信息不存在CaseNumber{inpatient.CaseNumber}");
                return false;
            }
            if (!DateTime.TryParse(scheduleVitalSignData.Data[0].ModifyDate, out DateTime modifyDate))
            {
                _logger.Error($"患者接口内时间转换失败{scheduleVitalSignData.Data[0].ModifyDate}");
                return false;
            }
            return await AddPatientSchedule(inpatient, modifyDate, scheduleVitalSignData);
        }
        /// <summary>
        /// 处理患者排程记录
        /// </summary>
        /// <param name="inpatient"></param>
        /// <param name="turnoverTime"></param>
        /// <param name="hisTurnoverView"></param>
        /// <returns></returns>
        private async Task<bool> AddPatientSchedule(InpatientDataInfo inpatient, DateTime scheduleTime, ScheduleVitalSignView scheduleVitalSigns)
        {
            DateTime addDateTime = DateTime.Now;
            //获取未执行生命体征记录排程
            var scheduleMainInfos = await _patientScheduleMainRepository.GetScheduleByTimeAndInterventionID(inpatient.ID, ASSESSLISTID_6633, scheduleTime.AddMinutes(-30), scheduleTime.AddMinutes(30));
            if (scheduleMainInfos.Count > 0)
            {
                //获取旧有数据的明细
                var oldDetailList = await _patientScheduleDetailRepository.GetByMainIDsAsync(inpatient.ID, scheduleMainInfos.Select(m => m.PatientScheduleMainID).ToArray());
                foreach (var item in scheduleMainInfos)
                {
                    UpdateScheduleMainInfo(item, scheduleVitalSigns, scheduleTime, addDateTime);
                    //写明细信息
                    var patientScheduleDetails = await AddPatientScheduleDetail(inpatient, scheduleTime, scheduleVitalSigns, item, addDateTime);
                    if (patientScheduleDetails.Count > 0)
                    {
                        var content = await CombineInterventionData(item, patientScheduleDetails);
                        item.Content = content;
                        item.NursingRecordContent = content;
                    }
                    await SetNursingRecordDetailLog(oldDetailList, patientScheduleDetails, item);
                    SetTprSchedule(inpatient, item, patientScheduleDetails);
                }
            }
            else
            {
                var scheduleMain = await AddPatientScheduleMainInfo(inpatient, scheduleTime, scheduleVitalSigns.Data[0].ModifyPersonID, addDateTime
                    , ASSESSLISTID_6633, "2", "Assist", "A01.0.2.03", "S");
                if (scheduleMain == null)
                {
                    return false;
                }
                var scheduleDetails = await AddPatientScheduleDetail(inpatient, scheduleTime, scheduleVitalSigns, scheduleMain, addDateTime);
                if (scheduleDetails.Count > 0)
                {
                    var content = await CombineInterventionData(scheduleMain, scheduleDetails);
                    scheduleMain.Content = content;
                    scheduleMain.NursingRecordContent = content;
                }
                SetTprSchedule(inpatient, scheduleMain, scheduleDetails);
                await SetNursingRecordDetailLog(new List<PatientScheduleDetailInfo>(), scheduleDetails, scheduleMain);
            }
            
            return _unitOfWork.SaveChanges() >= 0;
        }
        /// <summary>
        /// 写入明细表异动
        /// </summary>
        /// <param name="oldDetailList"></param>
        /// <param name="newDetails"></param>
        /// <param name="scheduleMainInfo"></param>
        /// <returns></returns>
        private async Task SetNursingRecordDetailLog(List<PatientScheduleDetailInfo> oldDetailList, List<PatientScheduleDetailInfo> newDetails, PatientScheduleMainInfo scheduleMainInfo)
        {
            var oldDetails = new List<Detail>();
            if (oldDetailList.Count > 0)
            {
                oldDetailList = oldDetailList.Where(m => m.PatientScheduleMainID == scheduleMainInfo.PatientScheduleMainID).ToList();
                oldDetails = oldDetailList.Select(m => new Detail
                {
                    ID = m.PatientScheduleDetailID,
                    AssessListID = m.AssessListID,
                    AssessValue = m.ScheduleData,
                    InterventionID = m.InterventionID
                }).ToList();
            }

            //新的需要保存排程明细
            var newCommonDetials = newDetails.Count > 0 ? newDetails.Select(m => new Detail
            {
                ID = Guid.NewGuid().ToString("N"),
                AssessListID = m.AssessListID,
                AssessValue = m.ScheduleData,
                BookMarkID = scheduleMainInfo.PatientScheduleMainID,
                InterventionID = scheduleMainInfo.InterventionID
            }).ToList() : new List<Detail>();
            var detailsView = DetailsCompare.GetNewDetailsView(newCommonDetials, oldDetails, scheduleMainInfo.PerformDate.Value.Add(scheduleMainInfo.PerformTime.Value));

            var detailLogAddView = new NursingRecordDetailLogSaveViewByMain<PatientScheduleMainInfo>
            {
                Main = scheduleMainInfo,
                BringToNR = scheduleMainInfo.BringToNursingRecords == "1",
                DetailsView = detailsView,
                MainTableName = "PatientScheduleMain",
                DetailTableName = "PatientScheduleDetail",
                AssessDate = scheduleMainInfo.PerformDate.Value.Add(scheduleMainInfo.PerformTime.Value),
                EventType = 2,
                SupplemnentFlag = false
            };
            await _commonDetailLogService.AddDetailLog(detailLogAddView);
        }
        /// <summary>
        /// 新增排程主记录
        /// </summary>
        /// <param name="inpatient"></param>
        /// <param name="scheduleDateTime"></param>
        /// <param name="addDateTime"></param>
        /// <returns></returns>
        private async Task<PatientScheduleMainInfo> AddPatientScheduleMainInfo(InpatientDataInfo inpatient, DateTime scheduleDateTime, string userID, DateTime addDateTime
            , int interventionID, string actionType, string actionTypeName, string interventionCode, string sourceFlag)
        {
            var currentShiftInfo = await _stationShiftCommonService.GetShiftAsync(inpatient.StationID, scheduleDateTime.TimeOfDay);
            if (currentShiftInfo == null)
            {
                _logger.Error($"获取当前病区的StationShift数据失败，StationID：{inpatient.StationID}");
                return null;
            }
            var scheduleMain = new PatientScheduleMainInfo
            {
                PatientInterventionID = "",
                PatientProblemID = "",
                InpatientID = inpatient.ID,
                PatientID = inpatient.PatientID,
                StationID = inpatient.StationID,
                DepartmentListID = inpatient.DepartmentListID,
                BedID = inpatient.BedID,
                CaseNumber = inpatient.CaseNumber,
                ChartNo = inpatient.ChartNo,
                BedNumber = inpatient.BedNumber,
                InterventionID = interventionID,
                ActionType = actionType,
                ActionTypeName = actionTypeName,
                InterventionCode = interventionCode,
                SourceFlag = sourceFlag,
                ScheduleDate = scheduleDateTime.Date,
                ScheduleTime = scheduleDateTime.TimeOfDay,
                Frequency = "32",
                Shift = currentShiftInfo.Shift,
                BringToShift = "0",
                BringToNursingRecords = "1",
                AddEmployeeID = "Tongbu",
                AddDate = addDateTime,
                DeleteFlag = "",
                PatientAssessMainID = "",
                TPRPerform = "",
                OriginalDate = scheduleDateTime.Date,
                OriginalTime = scheduleDateTime.TimeOfDay,
                Content = "",
                NursingRecordContent = "",
                NurseEmployeeID = userID,
                PerformDate = scheduleDateTime.Date,
                PerformTime = scheduleDateTime.TimeOfDay,
                CompleteMark = "1",
                ModifyPersonID = userID,
                ModifyDate = addDateTime,
                ClientType = "1",
                InformPhysician = false,
                ShiftDate = scheduleDateTime.Date
            };
            scheduleMain.PatientScheduleMainID = scheduleMain.GetId();
            scheduleMain.ScheduleDate = _stationShiftCommonService.GetShiftDate(currentShiftInfo, scheduleDateTime);
            _unitOfWork.GetRepository<PatientScheduleMainInfo>().Insert(scheduleMain);
            return scheduleMain;
        }
        /// <summary>
        /// 更新排程主表信息为已执行
        /// </summary>
        /// <param name="scheduleMainInfo"></param>
        /// <param name="hisTurnoverView"></param>
        /// <param name="scheduleDateTime"></param>
        /// <param name="addDateTime"></param>
        /// <returns></returns>
        private PatientScheduleMainInfo UpdateScheduleMainInfo(PatientScheduleMainInfo scheduleMainInfo, ScheduleVitalSignView scheduleVitalSign, DateTime scheduleDateTime, DateTime addDateTime)
        {
            scheduleMainInfo.NurseEmployeeID = scheduleVitalSign.Data[0].ModifyPersonID;
            scheduleMainInfo.PerformDate = scheduleDateTime.Date;
            scheduleMainInfo.PerformTime = scheduleDateTime.TimeOfDay;
            scheduleMainInfo.CompleteMark = "1";
            scheduleMainInfo.ModifyPersonID = scheduleVitalSign.Data[0].ModifyPersonID;
            scheduleMainInfo.ModifyDate = addDateTime;
            scheduleMainInfo.ClientType = "1";
            scheduleMainInfo.BringToNursingRecords = "1";
            scheduleMainInfo.InformPhysician = false;
            scheduleMainInfo.Content = "";
            scheduleMainInfo.NursingRecordContent = "";
            return scheduleMainInfo;
        }
        /// <summary>
        /// 写排程明细
        /// </summary>
        /// <param name="inpatient"></param>
        /// <param name="turnoverTime"></param>
        /// <param name="hisTurnoverView"></param>
        /// <param name="patientScheduleMainID"></param>
        /// <param name="time"></param>
        /// <returns></returns>
        private async Task<List<PatientScheduleDetailInfo>> AddPatientScheduleDetail(InpatientDataInfo inpatient, DateTime scheduleDateTime, ScheduleVitalSignView scheduleVitalSigns, PatientScheduleMainInfo scheduleMainInfo, DateTime addDateTime)
        {
            var patientScheduleDetailInfos = new List<PatientScheduleDetailInfo>();
            var interventionDetailInfos = await _nursingInterventionDetailRepository.GetNursingInterventionDetailByMainID(ASSESSLISTID_6633);
            var keyValues = new List<KeyValueString>();
            scheduleVitalSigns.Data.ForEach(m =>
            {
                var temp = interventionDetailInfos.Find(x => x.AssessListID.ToString() == m.MonitorCode);
                if (temp != null)
                {
                    keyValues.Add(new KeyValueString { Key = temp.AssessListID.ToString(), Value = m.DataValue, Type = temp.InterventionDetailID.ToString() });
                }
            });
            if (keyValues.Count == 0)
            {
                _logger.Warn($"明细数据获取失败，数据唯一标识RecordID=【{scheduleVitalSigns.RecordID}】");
                return patientScheduleDetailInfos;
            }
            else
            {
                foreach (var item in keyValues)
                {
                    if (int.TryParse(item.Key, out int intKey))
                    {
                        var patientScheduleDetailInfo = CreateScheduleDetail(inpatient, scheduleMainInfo.PatientScheduleMainID, item, scheduleDateTime, addDateTime);
                        patientScheduleDetailInfos.Add(patientScheduleDetailInfo);
                    }
                }
            }
            _unitOfWork.GetRepository<PatientScheduleDetailInfo>().Insert(patientScheduleDetailInfos);
            return patientScheduleDetailInfos;
        }
        /// <summary>
        /// 创建排程明细表模板
        /// </summary>
        /// <param name="inpatient"></param>
        /// <param name="patientScheduleMainID"></param>
        /// <param name="interventionDetailID"></param>
        /// <param name="scheduleData"></param>
        /// <param name="scheduleDateTime"></param>
        /// <param name="dateTime"></param>
        /// <returns></returns>
        private PatientScheduleDetailInfo CreateScheduleDetail(InpatientDataInfo inpatient, string patientScheduleMainID, KeyValueString keyValueString, DateTime scheduleDateTime, DateTime dateTime)
        {
            var patientScheduleDetailInfo = new PatientScheduleDetailInfo
            {
                PatientScheduleMainID = patientScheduleMainID,
                PatientProblemID = "",
                InpatientID = inpatient.ID,
                PatientID = inpatient.PatientID,
                CaseNumber = inpatient.CaseNumber,
                ChartNo = inpatient.ChartNo,
                InterventionID = ASSESSLISTID_6633,
                InterventionCode = "K33.0.1.01",
                AssessListID = int.Parse(keyValueString.Key),
                AddEmployeeID = "TongBu",
                AddDate = dateTime,
                ModifyPersonID = "TongBu",
                ModifyDate = dateTime,
                ScheduleDate = scheduleDateTime.Date,
                ScheduleTime = scheduleDateTime.TimeOfDay,
                InterventionDetailID = int.Parse(keyValueString.Type),
                ScheduleData = keyValueString.Value
            };
            patientScheduleDetailInfo.PatientScheduleDetailID = patientScheduleDetailInfo.GetId();
            return patientScheduleDetailInfo;
        }
        /// <summary>
        /// 组合措施数据
        /// </summary>
        /// <param name="schedulemain"></param>
        /// <param name="scheduleDetails"></param>
        /// <returns></returns>
        private async Task<string> CombineInterventionData(PatientScheduleMainInfo schedulemain,
            List<PatientScheduleDetailInfo> scheduleDetails)
        {
            var mainInfo = await _nursingInterventionMainRepository.GetByID(schedulemain.InterventionID);

            var interventionDetail = await _nursingInterventionDetailRepository.GetNursingInterventionDetailByMainID(schedulemain.InterventionID);
            interventionDetail = interventionDetail.Where(m => m.Style.Trim() != "B").ToList();
            //取得该措施明细数据
            var details = scheduleDetails.Where(m => m.PatientScheduleMainID == schedulemain.PatientScheduleMainID).ToList();

            var temp = SetDetailDatas(details, interventionDetail, mainInfo.RecordsContent.Trim(), mainInfo.Intervention, schedulemain.ActionTypeName);
            return ReplaceContent(temp);
        }
        /// <summary>
        /// 写措施明细
        /// </summary>
        /// <param name="details"></param>
        /// <param name="interventionDetail"></param>
        /// <param name="temp"></param>
        /// <param name="intervention"></param>
        /// <param name="actionTypeName"></param>
        /// <returns></returns>
        private string SetDetailDatas(List<PatientScheduleDetailInfo> details, List<NursingInterventionDetailInfo> interventionDetail, string temp, string intervention, string actionTypeName)
        {
            #region "初始化"
            //措施明细码
            int detailID = 0;
            //位置
            string recordLocation = "";
            //群组名称
            string groupTitle = "";
            //明细内容
            string detailData = "";
            #endregion
            foreach (var detail in details)
            {
                detailID = detail.InterventionDetailID;

                var detailInfo = interventionDetail.Find(m => m.InterventionDetailID == detailID);

                if (detailInfo == null)
                {
                    continue;
                }
                if (detailInfo.Stratum == 0 && actionTypeName != "Assess" || (actionTypeName == "Assess" && detail.ScheduleData.Trim() == "2"))
                {
                    //表示虽然画面上要执行明细,但护士没有勾选,只有勾选了执行
                    if (details.Count == 1)
                    {
                        temp = intervention;
                    }
                    else
                    {
                        continue;
                    }
                }

                recordLocation = detailInfo.RecordsLocation;

                //取得群组头
                groupTitle = SetTittle(interventionDetail, detailID);

                //取得明细内容
                detailData = SetDetailData(detail, detailInfo, detailID);

                //需要判断内容是否为空
                if (string.IsNullOrEmpty(recordLocation) || detailData.Length == 0)
                {
                    continue;
                }

                //格式化字串内容
                temp = FormatStr(groupTitle, detailData, temp, recordLocation);
            }
            return temp;
        }
        /// <summary>
        /// 写标题
        /// </summary>
        /// <param name="settings"></param>
        /// <param name="detailID"></param>
        /// <returns></returns>
        private string SetTittle(List<NursingInterventionDetailInfo> settings, int detailID)
        {
            string groupTitle = "";

            var setting = settings.Find(m => m.InterventionDetailID == detailID);

            if (setting == null)
            {
                return groupTitle;
            }

            var titleData = settings.Find(m => m.ContentGroup == setting.ContentGroup && m.Style.Trim() == "L");

            if (titleData != null)
            {
                groupTitle = titleData.RecordsShowName;
            }

            return groupTitle;
        }
        /// <summary>
        /// 分离拼接Content
        /// </summary>
        /// <param name="nursingRecordContent"></param>
        /// <returns></returns>
        private string ReplaceContent(string nursingRecordContent)
        {
            if (string.IsNullOrEmpty(nursingRecordContent))
            {
                return "";
            }

            while (nursingRecordContent.IndexOf("[") != -1 && nursingRecordContent.IndexOf("[") < nursingRecordContent.IndexOf("]"))
            {
                var startIndex = nursingRecordContent.IndexOf("[");
                var endIndex = nursingRecordContent.IndexOf("]");
                var replaceStr = nursingRecordContent[startIndex..(endIndex + 1)];
                nursingRecordContent = nursingRecordContent.Replace(replaceStr, "");
            }
            return nursingRecordContent;
        }
        /// <summary>
        /// 写明细数据
        /// </summary>
        /// <param name="detail"></param>
        /// <param name="detailInfo"></param>
        /// <param name="detailID"></param>
        /// <returns></returns>
        private string SetDetailData(PatientScheduleDetailInfo detail, NursingInterventionDetailInfo detailInfo, int detailID)
        {
            string str = detailInfo.RecordsShowName.Trim();

            string unit = detailInfo.Unit.Trim();

            if (detailInfo.Style.Trim() == "T" || detailInfo.Style.Trim() == "TN")
            {
                if (str == "")
                {
                    str = detail.ScheduleData;
                }
                else
                {
                    str += "：" + detail.ScheduleData;
                }

                if (unit != "")
                {
                    str += unit;
                }
            }

            return str;
        }
        /// <summary>
        /// 格式化字符串
        /// </summary>
        /// <param name="groupTitle"></param>
        /// <param name="detailData"></param>
        /// <param name="temp"></param>
        /// <param name="recordLocation"></param>
        /// <returns></returns>
        private string FormatStr(string groupTitle, string detailData, string temp, string recordLocation)
        {
            if (temp.IndexOf(groupTitle) < 0)
            {
                if (detailData.IndexOf(groupTitle) >= 0)
                {
                    detailData = detailData.Replace(groupTitle, "");
                }
                detailData = groupTitle + "：" + detailData;
            }
            //判断recordLocation的前一个位置，是否有标点符号，如果有不在增加标点符号                           
            temp = temp.Replace(" ", "");

            if (CheckStrContintSubstr(temp, recordLocation))
            {
                temp = temp.Replace(recordLocation, detailData.Trim());
            }
            else
            {
                temp = temp.Replace(recordLocation, "，" + detailData.Trim());
            }

            return temp;
        }
        /// <summary>
        /// 检核字符串
        /// </summary>
        /// <param name="str"></param>
        /// <param name="subStr"></param>
        /// <returns></returns>
        private bool CheckStrContintSubstr(string str, string subStr)
        {
            //符号字典
            var strArray = new string[] { ",", "，", ".", "。", ";", "；", ":", "：", "、", ",", "\\", "/" };
            var findIndex = str.IndexOf(subStr);
            if (findIndex <= 0)
            {
                return true;
            }
            //取出前一个字符            
            var frontSubStr = str.Substring(findIndex - 1, 1);
            //判断是否是标点符号
            if (strArray.Contains(frontSubStr))
            {
                return true;
            }
            return false;
        }
        /// <summary>
        /// 同步患者病情观察数据
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncPatientScheduleMeasures()
        {
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.ScheduleMeasures).ToString();
            var jobName = "同步病人病情观察数据";
            var logMsg = "作业编号:" + guid + "启动同步任务:";
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            if (!jobStatus)
            {
                _logger.Info("作业正在启动" + logMsg);
                return false;
            }
            _syncLogService.InsertSyncLog(3, guid, "ScheduleMeasures", "", "开始同步病情观察", "Sys", true);
            var successFlag = true;
            try
            {
                successFlag = await SyncScheduleMeasures();
            }
            catch (Exception ex)
            {
                _logger.Error("ScheduleMeasures" + ex.ToString());
                _syncLogService.InsertSyncLog(1, guid, "ScheduleMeasures", "", "同步病人病情观察失败", "Sys", true);
            }
            if (successFlag)
            {
                _syncLogService.InsertSyncLog(3, guid, "ScheduleMeasures", "", "同步病人病情观察成功", "Sys", true);
            }
            else
            {
                _syncLogService.InsertSyncLog(1, guid, "ScheduleMeasures", "", "同步病人病情观察失败", "Sys", true);
            }
            _jobLogService.RemoveJob(jobId, jobName, "");
            return successFlag;
        }
        /// <summary>
        /// 同步患者病情观察数据
        /// </summary>
        /// <returns></returns>
        private async Task<bool> SyncScheduleMeasures()
        {
            var hospitalID = _config.Value.HospitalID;
            bool returnFlag = true;
            //获取同步表的数据
            var syncDatasLogs = _syncDatasLogRepository.GetSyncDataByDataType(hospitalID, "ScheduleMeasures");
            if (syncDatasLogs.Count == 0)
            {
                _logger.Info("SyncDatasLog中没有相应的数据");
                return false;
            }
            foreach (var item in syncDatasLogs)
            {
                var syncDatasLogInfo = await _syncDatasLogRepository.GetSyncDataByIDAsync(item);
                if (syncDatasLogInfo == null)
                {
                    ModifySyncDataLog(syncDatasLogInfo, false);
                    continue;
                }
                try
                {
                    returnFlag = await SyncPatientHISMeasures(syncDatasLogInfo.SyncData);
                }
                catch (Exception ex)
                {
                    _logger.Error($"数据转换异常，数据内容【{syncDatasLogInfo.SyncData}】，异常信息{ex}");
                    continue;
                }
                if (!returnFlag)
                {
                    _logger.Warn("同步患者数据失败，caseNumber:" + syncDatasLogInfo.CaseNumber);
                }
                ModifySyncDataLog(syncDatasLogInfo, returnFlag);
            }
            ////测试代码先不删
            //var a = new SyncDataLogInfo()
            //{
            //    ID = 196,
            //    HospitalID = "3",
            //    SyncDataType = "SyncDataLogInfo",
            //    SyncData = "{\"Data\": {\"CaseNumber\": \"741164\", \"ChartNo\": \"4460228\", \"DeleteFlag\": \"\", \"ID\": 3860839, \"MeasureContent\": \"cccc测试1\", \"MeasureType\": \"观察\", \"ModifyDate\": 1710802980000, \"ModifyPersonID\": \"1223\", \"StationCode\": \"605\", \"StationName\": \"新生儿病区\"}, \"RecordID\": 3860840, \"SourceType\": \"2\"}",
            //    AddPersonID = "1223",
            //    AddDate = DateTime.Parse("2024-03-21 16:22:44.000"),
            //    ModifyPersonID = "1223",
            //    ModifyDate = DateTime.Parse("2024-03-21 16:22:44.000"),
            //    DataPumpFlag = "",
            //    DataPumpDate = null,
            //    CaseNumber = "741164",
            //};
            //var returnFlag = await SyncPatientHISMeasures(a.SyncData);
            //ModifySyncDataLog(a, returnFlag);
            //_unitOfWork.SaveChanges();
            //return true;
            return returnFlag;
        }

        /// <summary>
        /// 同步中间库病情观察数据
        /// </summary>
        /// <param name="syncData"></param>
        /// <returns></returns>
        private async Task<bool> SyncPatientHISMeasures(string syncData)
        {
            var scheduleMeasuresData = ListToJson.ToList<ScheduleMeasuresView>(syncData);
            if (scheduleMeasuresData == null)
            {
                _logger.Warn($"数据转换失败，写入数据库失败，原始数据为：【{ListToJson.ToJson(scheduleMeasuresData)}】");
                return false;
            }
            var inpatient = await _IInpatientDataRepository.GetInpatientViewByCaseNumber(scheduleMeasuresData.Data.CaseNumber);
            if (inpatient == null)
            {
                _logger.Error($"患者信息不存在CaseNumber{inpatient.CaseNumber}");
                return false;
            }
            //var modifyDate = DateTimeOffset.FromUnixTimeSeconds((scheduleMeasuresData.Data.ModifyDate / 1000)).ToOffset(TimeSpan.FromHours(8));
            var postData = CreatePatientMeasures(inpatient, scheduleMeasuresData.Data.ModifyDate, scheduleMeasuresData);
            string url = await _appConfigSettingRepository.GetConfigSettingValue("Configs", "CallSavePatientMeasures");
            //url = "http://localhost:56194/api/PatientObservation/SyncPatientObservation";
            if (!string.IsNullOrEmpty(url))
            {
                var wrx = new WebRequestSugar();
                string result = wrx.SendObjectAsJsonInBody(url, postData);
                return result != "0";
            }
            else
            {
                _logger.Error($"获取配置失败settingcode：【CallSavePatientMeasures】");
                return false;
            }
        }
        /// <summary>
        /// 创建病情观察模板
        /// </summary>
        /// <param name="inpatient"></param>
        /// <param name="scheduleTime"></param>
        /// <param name="scheduleMeasures"></param>
        /// <returns></returns>
        private PatientObservationSubmit CreatePatientMeasures(InpatientDataInfo inpatient, DateTime scheduleTime, ScheduleMeasuresView scheduleMeasures)
        {
            return new PatientObservationSubmit
            {
                EvalutionRecordsCode = "",
                BringToShift = "0",
                Details = new List<Detail>(),
                InformPhysician = false,
                InpatientID = inpatient.ID,
                ObserveTemplateID = PATIENTMEASURES_OTHER,
                PerformDate = scheduleTime,
                PerformText = scheduleMeasures.Data.MeasureContent,
                RecordsCode = "ObserveTemp",
                //TriggerList = new List<TriggerView>(),
                SourceID = scheduleMeasures.RecordID,
                UserID = scheduleMeasures.Data.ModifyPersonID,
            };
        }
        /// <summary>
        /// 同步患者出入量
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncPatientIO()
        {
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.ScheduleMeasures).ToString();
            var jobName = "同步病人病情观察数据";
            var logMsg = "作业编号:" + guid + "启动同步任务:";
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            if (!jobStatus)
            {
                _logger.Info("作业正在启动" + logMsg);
                return false;
            }
            _syncLogService.InsertSyncLog(3, guid, "ScheduleMeasures", "", "开始同步出入量", "Sys", true);
            var successFlag = true;
            try
            {
                successFlag = await SyncPatientHISIntakeOutput();
            }
            catch (Exception ex)
            {
                _logger.Error("ScheduleMeasures" + ex.ToString());
                _syncLogService.InsertSyncLog(1, guid, "ScheduleMeasures", "", "同步病人病情观察失败", "Sys", true);
            }
            if (!successFlag)
            {
                _syncLogService.InsertSyncLog(1, guid, "ScheduleMeasures", "", "同步病人病情观察失败", "Sys", true);
            }
            _jobLogService.RemoveJob(jobId, jobName, "");
            return successFlag;
        }
        /// <summary>
        /// 同步患者病情观察数据
        /// </summary>
        /// <returns></returns>
        private async Task<bool> SyncPatientHISIntakeOutput()
        {
            var hospitalID = _config.Value.HospitalID;
            bool returnFlag = true;
            //获取同步表的数据
            var syncDatasLogs = _syncDatasLogRepository.GetSyncDataByDataType(hospitalID, "IntakeOutput");
            if (syncDatasLogs.Count == 0)
            {
                _logger.Info("SyncDatasLog中没有相应的数据");
                return false;
            }
            foreach (var item in syncDatasLogs)
            {
                var syncDatasLogInfo = await _syncDatasLogRepository.GetSyncDataByIDAsync(item);
                if (syncDatasLogInfo == null)
                {
                    ModifySyncDataLog(syncDatasLogInfo, false);
                    continue;
                }
                try
                {
                    returnFlag = await SyncPatientIntakeOutput(syncDatasLogInfo.SyncData);
                }
                catch (Exception ex)
                {
                    _logger.Error($"数据转换异常，数据内容【{syncDatasLogInfo.SyncData}】，异常信息{ex}");
                    continue;
                }
                if (!returnFlag)
                {
                    _logger.Warn("同步患者数据失败，caseNumber:" + syncDatasLogInfo.CaseNumber);
                }
                ModifySyncDataLog(syncDatasLogInfo, returnFlag);
            }
            //测试代码先不删
            //var syncData = ReadFile.ReadTxt(@"C:\Users\<USER>\Desktop\json\yinchuanIO.txt");
            //await SyncPatientIntakeOutput(syncData);
            //_unitOfWork.SaveChanges();
            //return true;
            return returnFlag;
        }
        /// <summary>
        /// 同步中间库出入量数据
        /// </summary>
        /// <param name="syncData"></param>
        /// <returns></returns>
        private async Task<bool> SyncPatientIntakeOutput(string syncData)
        {
            var scheduleIntakeOutputData = ListToJson.ToList<PatientHisIntakeOutputView>(syncData);
            if (scheduleIntakeOutputData == null)
            {
                _logger.Warn($"数据转换失败，写入数据库失败，原始数据为：【{ListToJson.ToJson(scheduleIntakeOutputData)}】");
                return false;
            }
            var inpatient = await _IInpatientDataRepository.GetInpatientViewByCaseNumber(scheduleIntakeOutputData.Data.CaseNumber);
            if (inpatient == null)
            {
                _logger.Error($"患者信息不存在CaseNumber{inpatient.CaseNumber}");
                return false;
            }
            //var modifyDate = DateTimeOffset.FromUnixTimeSeconds((scheduleIntakeOutputData.Data.ModifyDate / 1000)).ToOffset(TimeSpan.FromHours(8));
            var currentShiftInfo = await _stationShiftCommonService.GetShiftAsync(inpatient.StationID, scheduleIntakeOutputData.Data.ModifyDate.TimeOfDay);
            if (currentShiftInfo == null)
            {
                _logger.Error($"获取当前病区的StationShift数据失败，StationID：{inpatient.StationID}");
                return false;
            }
            if (scheduleIntakeOutputData.Data.DeleteFlag == "*")
            {
                var iodata = await _patientIntakeOutputRepository.GetRecordBySourceIDAndInpatientIDAsync(inpatient.ID, scheduleIntakeOutputData.RecordID);
                iodata?.Delete(scheduleIntakeOutputData.Data.ModifyPersonID);
                return true;
            }
            try
            {
                var model = await CreatePatientIntakeOut(inpatient, scheduleIntakeOutputData, scheduleIntakeOutputData.Data.ModifyDate, currentShiftInfo);
                await _commonDetailLogService.AddDetailLog(model, true, "PatientIntakeOutput", scheduleIntakeOutputData.Data.ModifyDate, 1);
                return _unitOfWork.SaveChanges()>=0;
            }
            catch (Exception ex)
            {
                _logger.Error($"同步出入量数据失败{ex}");
                return false;
            }
        }
        /// <summary>
        /// 创建出入量模板
        /// </summary>
        /// <param name="inpatient"></param>
        /// <param name="scheduleMeasuresData"></param>
        /// <param name="modifyDate"></param>
        /// <param name="currentShiftInfo"></param>
        /// <returns></returns>
        private async Task<PatientIntakeOutputInfo> CreatePatientIntakeOut(InpatientDataInfo inpatient, PatientHisIntakeOutputView scheduleMeasuresData, DateTime modifyDate
            , Medical.Models.StationShiftInfo currentShiftInfo)
        {
            var result = new PatientIntakeOutputInfo
            {
                IntakeOutputSettingID = int.TryParse(scheduleMeasuresData.Data.IntakeOutputSettingID, out int intakeOutputSettingID) ? intakeOutputSettingID : 0,
                InpatientID = inpatient.ID,
                PatientID = inpatient.PatientID,
                DepartmentID = inpatient.DepartmentListID,
                StationID = inpatient.StationID,
                BedID = inpatient.BedID,
                CaseNumber = inpatient.CaseNumber,
                ChartNo = inpatient.ChartNo,
                BedNumber = inpatient.BedNumber,
                IntakeOutputKind = scheduleMeasuresData.Data.IOKindCode,
                IODate = modifyDate.Date,
                IOTime = modifyDate.TimeOfDay,
                Shift = currentShiftInfo.Shift,
                IntakeOutputVolume = decimal.TryParse(scheduleMeasuresData.Data.IntakeOutputVolume, out decimal intakeOutputVolume) ? intakeOutputVolume : 0,
                Color = scheduleMeasuresData.Data.Color,
                Characteristic = scheduleMeasuresData.Data.Characteristic,
                Smell = scheduleMeasuresData.Data.Smell,
                AddEmployeeID = scheduleMeasuresData.Data.ModifyPersonID,
                AddDate = DateTime.Now,
                ModifyPersonID = scheduleMeasuresData.Data.ModifyPersonID,
                ModifyDate = DateTime.Now,
                DeleteFlag = "",
                DataPumpFlag = "",
                ClientType = "1",
                InPutContent = scheduleMeasuresData.Data.IONme,
                NursingLevel = inpatient.NursingLevel,
                ShiftDate = modifyDate.Date,
                CDFlag = false,
                SupplementFlag = false,
                SourceType = "IO",
                BringToNursingRecords = true,
                InformPhysician = false,
                IntoTheNursingRecordsText = scheduleMeasuresData.Data.IOKingName,
                SourceID = scheduleMeasuresData.RecordID,
                InPutType = int.TryParse(scheduleMeasuresData.Data.IOWayCode, out int inPutType) ? inPutType : 0
            };

            result.ID = result.GetId();
            result.IntoTheNursingRecordsText = await CreateIntoTheNursingRecordsText(result, _config.Value.HospitalID, _config.Value.Language);
            _unitOfWork.GetRepository<PatientIntakeOutputInfo>().Insert(result);
            return result;
        }
        /// <summary>
        /// 创建PatientIntakeOutput拼接好的带入护理记录单的出入量内容
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private async Task<string> CreateIntoTheNursingRecordsText(PatientIntakeOutputInfo model, string hospitalID, int language)
        {
            string result = "";
            //输入
            if (model.IntakeOutputKind.StartsWith("1"))
            {
                result = await CreateIntakeIntoTheNursingRecordsText(result, model);
            }
            //输出
            if (model.IntakeOutputKind.StartsWith("2"))
            {
                result = await CreateOutputIntoTheNursingRecordsText(result, model, hospitalID, language);
            }
            return result;
        }
        /// <summary>
        /// 入量添加护理记录单
        /// </summary>
        /// <param name="result"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        private async Task<string> CreateIntakeIntoTheNursingRecordsText(string result, PatientIntakeOutputInfo model)
        {
            var settingValue = await _clinicSettingRepository.GetSettingValueByTypeValue("IntakeOutputTemplate", "IntakeTemplate");
            if (!string.IsNullOrEmpty(settingValue))
            {
                var settings = settingValue.Split(',').ToList();
                try
                {
                    //内容
                    string content = settings.Find(m => m.Contains("[0]"));
                    if (content != null && !string.IsNullOrEmpty(model.InPutContent))
                    {
                        result = content.Replace("[0]", model.InPutContent);
                    }
                    //入量
                    string volum = settings.Find(m => m.Contains("[1]"));
                    if (volum != null && model.IntakeOutputVolume.HasValue)
                    {
                        result += volum.Replace("[1]", model.IntakeOutputVolume.ToString());
                    }
                    //备注
                    string note = settings.Find(m => m.Contains("[2]"));
                    if (note != null && !string.IsNullOrEmpty(model.IntakeOuputNote))
                    {
                        result += note.Replace("[2]", model.IntakeOuputNote.ToString());
                    }
                    return result;
                }
                catch (Exception ex)
                {
                    _logger.Error($"入量转换失败，settingtypecpde{0},InpatientID:{1}" + ex.ToString(), "IntakeOutputTemplate", model.InpatientID);
                    return result;
                }
            }
            else
            {
                return result;
            }
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="result"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        private async Task<string> CreateOutputIntoTheNursingRecordsText(string result, PatientIntakeOutputInfo model, string hospitalID, int language)
        {
            if (model.IntakeOutputSettingID == 0)
            {
                return "";
            }
            var ioSetting = await _intakeOutputSettingRepository.GetAsync(model.IntakeOutputSettingID);
            var settingValue = await _clinicSettingRepository.GetSettingValueByTypeValue("IntakeOutputTemplate", "OutputTemplate");
            if (!string.IsNullOrEmpty(settingValue))
            {
                //'输出项目:[0],次数:[1],量:[2]毫升,性状:[3],气味:[4],颜色:[5],输出备注:[6]'
                var settings = settingValue.Split(',').ToList();
                //项目
                string item = settings.Find(m => m.Contains("[0]"));
                if (item != null)
                {
                    if (model.IntakeOutputKind == "240" || (model.IntakeOutputKind == "210" && !string.IsNullOrEmpty(model.PatientTubeRecordID)))
                    {
                        result = "";
                    }
                    else
                    {
                        result = item.Replace("[0]", ioSetting.IntakeOutput);
                    }
                }
                string volum = settings.Find(m => m.Contains("[2]"));
                if (volum != null && model.IntakeOutputVolume.HasValue)
                {
                    result += volum.Replace("[2]", model.IntakeOutputVolume.Value.ToString());
                }
                string character = settings.Find(m => m.Contains("[3]"));
                if (character != null && !string.IsNullOrEmpty(model.Characteristic))
                {
                    result += character.Replace("[3]", model.Characteristic);
                }
                string smell = settings.Find(m => m.Contains("[4]"));
                if (smell != null && !string.IsNullOrEmpty(model.Smell))
                {
                    result += smell.Replace("[4]", model.Smell);
                }
                string color = settings.Find(m => m.Contains("[5]"));
                if (color != null && !string.IsNullOrEmpty(model.Color))
                {
                    string colorName = await _assessListIDToColorRepository.GetColorNameByBit(model.Color, ioSetting.AssessListID.Value);
                    result += color.Replace("[5]", colorName.Trim());
                }
                string note = settings.Find(m => m.Contains("[6]"));
                if (note != null && !string.IsNullOrEmpty(model.IntakeOuputNote))
                {
                    result += note.Replace("[6]", model.IntakeOuputNote);
                }
                return result;
            }
            else
            {
                return result;
            }
        }
        private void SetTprSchedule(InpatientDataInfo inpatient, PatientScheduleMainInfo patientScheduleMain, List<PatientScheduleDetailInfo> patientScheduleDetailInfos)
        {
            //创建初始集合
            var KeyValueStrings = new List<KeyValueString>
            {
                new KeyValueString { Key = "72", Value = "1295"},
                new KeyValueString { Key = "74", Value = "1338"},
                new KeyValueString { Key = "75", Value = "1943"},
                new KeyValueString { Key = "76", Value = "1296"},
                new KeyValueString { Key = "86", Value = "1344"},
                new KeyValueString { Key = "106", Value = "1299"},
            };

            Dictionary<string, bool> keyValuePairs = new Dictionary<string, bool>
            {
                { "72", true },
                { "74", true },
                { "75", true },
                { "76", true },
                { "86", true },
                { "106", true },
            };
            
            var result = new Medical.Models.TPRScheduleInfo
            {
                InpatientID = inpatient.ID,
                CaseNumber = inpatient.CaseNumber,
                ChartNo = inpatient.ChartNo,
                ScheduleDate = patientScheduleMain.ScheduleDate,
                ScheduleTime = patientScheduleMain.ScheduleTime,
                PatientScheduleMainID = patientScheduleMain.PatientScheduleMainID,
                PatientAttachedInterventionID = "",
                InterventionID = patientScheduleMain.InterventionID,
                ModifyPersonID = patientScheduleMain.ModifyPersonID,
                ModifyDate = patientScheduleMain.ModifyDate,
                DeleteFlag = "",
                TprPriority = TPRPriority.Schedule,
                EMRFieldFlag = ListToJson.ToJson(keyValuePairs),
            };
            Dictionary<string, string> keyValuePairsStr = new Dictionary<string, string>();

            foreach (var item in KeyValueStrings)
            {
                var kvp = patientScheduleDetailInfos.Find(m => m.AssessListID.ToString() == item.Value);
                if (kvp!=null)
                {
                    keyValuePairsStr.Add(item.Key,kvp.ScheduleData);
                }
            }
            result.EMRField = ListToJson.ToJson(keyValuePairsStr);

            _unitOfWork.GetRepository<Medical.Models.TPRScheduleInfo>().Insert(result);
        }
    }
}