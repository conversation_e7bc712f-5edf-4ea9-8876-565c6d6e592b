﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class DepartmentPostRepository : IDepartmentPostRepository
    {
        private readonly MedicalDbContext _medicalDbContext;

        public DepartmentPostRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<List<DepartmentPostInfo>> GetListAsync()
        {
            return await _medicalDbContext.DepartmentPostInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
        }
    }
}