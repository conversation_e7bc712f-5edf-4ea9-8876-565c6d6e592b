﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;


namespace Interconnect.Data.Repository
{
    public class InpatientRepository : IInpatientRepository
    {
        private DataOutConnection _DataOutConnection = null;

        public InpatientRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }
        /// <summary>
        /// 获取所有没有抽取的病区数据
        /// </summary>
        /// <param name="inPatientDay"></param>
        /// <param name="stations"></param>
        /// <returns></returns>
        public List<InpatientInfo> GetAsync(int inPatientDay,string stations)
        {
            List<InpatientInfo> InpatientInfos = null;
            var InPatientDay = inPatientDay;
            if (InPatientDay>=0)
            {
                InPatientDay = -1;
            }
            if (stations.Trim() == "*")
            {
                InpatientInfos =  _DataOutConnection.Inpatients.Where(m => m.DataPumpFlag != "*" && m.ModifyDate>=(DateTime.Now.AddDays(inPatientDay))).ToList();
            }
            else
            {
                var Stations = stations.Split(",");
                InpatientInfos =  _DataOutConnection.Inpatients.Where(m => m.DataPumpFlag != "*"
                && m.ModifyDate >= (DateTime.Now.AddDays(inPatientDay))
                && Stations.Contains(m.StationCode)).ToList();
            }
            return InpatientInfos;
        }


    }

}