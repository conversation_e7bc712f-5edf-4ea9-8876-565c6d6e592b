﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("NursingRecord_PhysicalRestraintReEvaluation")]
    public class NRPhysicalRestraintReEvaluationInfo : ModifyReturnInfo
    {
        /// <summary>
        ///	自增长编号	
        ///</summary>
        [Key]
        [Column("ReEvaluationID")]
        public int ReEvaluationID { get; set; }
        /// <summary>
        ///	评估基本信息的ID	
        ///</summary>
        public int? EvaluationID { get; set; }
        /// <summary>
        ///	评估日期	
        ///</summary>
        public DateTime? ReportDate { get; set; }
        /// <summary>
        ///	状态：0 待审核，1护士长已审核
        ///</summary>
        public int? Status { get; set; }
        /// <summary>
        ///	1 首次评估，0 非首次评估	
        ///</summary>
        public int? IsFirst { get; set; }
        /// <summary>
        ///	患者是否需要继续约束：否,是	
        ///</summary>
        public string ContinuingRestraint { get; set; }
        /// <summary>
        ///	解除约束时间	
        ///</summary>
        public DateTime? NoRestraintTime { get; set; }
        /// <summary>
        ///	身体约束工具：约束手/网套,约
        ///</summary>
        public string RestraintTool { get; set; }
        /// <summary>
        ///	约束部位：左手,右手,左脚,右脚
        ///</summary>
        public string RestraintLocation { get; set; }
        /// <summary>
        ///	约束原因：预防非计划性拔管,预
        ///</summary>
        public string RestraintReason { get; set; }
        /// <summary>
        ///	肢端皮肤颜色：正常,发紫,苍白	
        ///</summary>
        public string AcraSkinColor { get; set; }
        /// <summary>
        ///	肢端皮肤温度：温,凉,冷	
        ///</summary>
        public string AcraSkinTemperature { get; set; }
        /// <summary>
        ///	肢端皮肤感觉：正常,麻木,疼痛	
        ///</summary>
        public string AcraSkinFeel { get; set; }
        /// <summary>
        ///	肢端活动度：正常,僵硬	
        ///</summary>
        public string AcraSkinActivity { get; set; }
        /// <summary>
        ///	约束处皮肤完整性：完整,破损	
        ///</summary>
        public string AcraSkinIntactness { get; set; }
        /// <summary>
        ///	约束处循环情况：正常,淤紫	
        ///</summary>
        public string AcraSkinCirculation { get; set; }
        /// <summary>
        ///	护理措施：告知患者或家属，签
        ///</summary>
        public string Nursing { get; set; }
        /// <summary>
        ///	患者是否出现不良结局：否，是	
        ///</summary>
        public string Result { get; set; }
        /// <summary>
        ///	不良结局：坠床，非计划性拔管	
        ///</summary>
        public string ResultDesc { get; set; }
        /// <summary>
        ///	评估护士工号	
        ///</summary>
        public int? InputerCode { get; set; }
        /// <summary>
        ///	录入时间	
        ///</summary>
        public DateTime? InputTime { get; set; }
        /// <summary>
        ///	评估护士姓名	
        ///</summary>
        public string InputerName { get; set; }
        /// <summary>
        ///	护士长审核人工号	
        ///</summary>
        public int? HeadNurseCode { get; set; }
        /// <summary>
        ///	护士长审核时间	
        ///</summary>
        public DateTime? HeadNurseTime { get; set; }
        /// <summary>
        ///	删除人工号	
        ///</summary>
        public int? DelOpCode { get; set; }
        /// <summary>
        ///	删除时间	
        ///</summary>
        public DateTime? DelTime { get; set; }
        /// <summary>
        ///	最后更新时间	
        ///</summary>
        public DateTime? LastUpdateTime { get; set; }
        /// <summary>
        ///	是否镇静：否，是	
        ///</summary>
        public string IsSedation { get; set; }
        /// <summary>
        ///	RASS评分	
        ///</summary>
        public float? RASSScore { get; set; }
    }
}