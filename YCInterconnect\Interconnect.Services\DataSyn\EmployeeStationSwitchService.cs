﻿using Medical.Data.Context;
using Medical.Data.Interface;
using NLog;
using Interconnect.Models;
using Interconnect.Services.Interface;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using Medical.Common;
using Arch.EntityFrameworkCore.UnitOfWork;
using ViewModel;
using Medical.ViewModels.Query;
using Medical.Models;
using NPOI.SS.Formula.Functions;

namespace Interconnect.Services
{
    public class EmployeeStationSwitchService : IEmployeeStationSwitchService
    {
        //Mdical
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IEmployeeDepartmentSwitchRepository _employeeDepartmentSwitchRepository;
        private readonly IUserRepository _userRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly IHospitalListRepository _hospitalListRepository;
        private readonly IStationListRepository _stationListRepository;
        //Interconnect
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        public readonly IEmployeeService _employeeService;
        private readonly ICommonHelper _commonHelper;


        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";

        public EmployeeStationSwitchService(IUnitOfWork<MedicalDbContext> unitOfWork
            , IEmployeeDepartmentSwitchRepository employeeDepartmentSwitchRepository
            , IUserRepository userRepository
            , IAppConfigSettingRepository appConfigSettingRepository
            , IEmployeeService employeeService
            , IHospitalListRepository hospitalListRepository
            , IStationListRepository stationListRepository
            , ICommonHelper commonHelper
            )
        {
            _unitOfWork = unitOfWork;
            _employeeDepartmentSwitchRepository = employeeDepartmentSwitchRepository;
            _userRepository = userRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
            _employeeService = employeeService;
            _hospitalListRepository = hospitalListRepository;
            _stationListRepository = stationListRepository;
            _commonHelper = commonHelper;
        }



        /// <summary>
        /// 同步人员病区权限
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SynchronizationMain()
        {
            var hospitalInfo =  _hospitalListRepository.GetHospitalInfo();
            if (hospitalInfo == null)
            {
                _logger.Error("获取病区失败");
                return false;
            }
            var interconnect_Data = await GetApiData();
            if (interconnect_Data == null || interconnect_Data.Count <= 0)
            {
                return false;
            }
            _logger.Info("获得" + interconnect_Data.Count() + "条数据");
            _logger.Info("同步病区权限");
            await SynchronizationDetail(interconnect_Data);
            //_logger.Info("删除病区权限");
            // await DeleteEmployeeStationSwitch(interconnect_Data);
            try
            {
                //更新人员病区权限缓存
                CacheQuery query = new CacheQuery
                {
                    Type = CacheType.EmployeeDepartmentSwitch
                };
                _commonHelper.UpdateCache(query);
            }
            catch (Exception ex)
            {
                _logger.Info("人员病区权限缓存更新失败" + ex.Message);
            }
            return true;
        }
        /// <summary>
        /// 同步明细
        /// </summary>
        /// <param name="OriginalList"></param>
        /// <returns></returns>
        public async Task<bool> SynchronizationDetail(List<EmployeeStationSwitchInfo> originalList)
        {
            string tablename = "EmployeeStationSwitch";
            _logger.Info(tablename + " 开始进行数据同步，数据条数：" + originalList.Count);
            var stationList = await _stationListRepository.GetAllAsync();
            var stationCodeList = stationList.Where(m => !string.IsNullOrEmpty(m.StationCode)).Select(m => m.StationCode).ToList();
            var allUser = await _userRepository.GetAllUser();
            var userIDList = allUser.Where(m => m.DeleteFlag != "*" && !string.IsNullOrEmpty(m.UserID)).Select(m => m.UserID.Trim()).ToList();
            originalList = originalList.Where(m => !string.IsNullOrEmpty(m.EmployeeID) && userIDList.Contains(m.EmployeeID.Trim())&&!string.IsNullOrEmpty(m.StationCode) &&stationCodeList.Contains(m.StationCode)).ToList();
            var switchs = await _employeeDepartmentSwitchRepository.GetAllSwitch();
            List<EmployeeDepartmentSwitchInfo> addSwitche = new List<EmployeeDepartmentSwitchInfo>();
            foreach (var item in originalList)
            {
                //根据Employee
                var employeeInfo = allUser.Find(m => m.UserID.Trim() == item.EmployeeID.Trim());
                if (employeeInfo == null)
                {
                    continue;
                }
                //if (employeeInfo.SpecialFlag == 1)
                //{
                //    continue;
                //}
                var stationInfo = stationList.Where(m => m.StationCode == item.StationCode).FirstOrDefault();
                if (stationInfo == null)
                {
                    continue;
                }
                var oldSwitch = switchs.Find(m => m.EmployeeID == item.EmployeeID && m.DepartmentCode == item.StationCode);
                if (oldSwitch == null)
                {
                    var switchInfo = new EmployeeDepartmentSwitchInfo
                    {
                        EmployeeID = item.EmployeeID,
                        DepartmentCode = item.StationCode,
                        ModifyPersonID = "SYS_TongBu",
                        ModifyDate = DateTime.Now,
                        DeleteFlag = "",
                        HospitalID = "3"
                    };
                    addSwitche.Add(switchInfo);
                }
            }
            try
            {
                _unitOfWork.GetRepository<EmployeeDepartmentSwitchInfo>().Insert(addSwitche);
                _unitOfWork.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error(tablename + "||保存人员权限失败||" + ex.ToString());
            }
            return true;
        }

        /// <summary>
        /// 数据对比，删除对方不存在的科室权限
        /// </summary>
        /// <returns></returns>
        private async Task<bool> DeleteEmployeeStationSwitch(List<EmployeeStationSwitchInfo> hisEmployeeStationSwitchList)
        {   //获取所有权限数据
            var employeeDepartmentSwitchList = await _employeeDepartmentSwitchRepository.GetAllSwitch();
            //系统中所有用户
            var userInfoList = await _userRepository.GetAsync();
            //对比删除
            foreach (var item in employeeDepartmentSwitchList)
            {
                //获取人员特使标记，是否使用反向删除权限               
                var employee = userInfoList.Find(m => m.UserID == item.EmployeeID);
                if (employee == null)
                {
                    continue;
                }
                if (employee.SpecialFlag.HasValue && Convert.ToInt32(employee.SpecialFlag.Value) == 1) //特殊人员，不进行权限的反向对比
                {
                    continue;
                }
                var hisEmployeeStationSwitchInfo = hisEmployeeStationSwitchList.Where(m => m.EmployeeID == item.EmployeeID && m.StationCode == item.DepartmentCode).FirstOrDefault();
                if (hisEmployeeStationSwitchInfo == null)
                {
                    item.DeleteFlag = "*";
                }
                try
                {
                    _unitOfWork.SaveChanges();
                }
                catch (Exception ex)
                {
                    _logger.Error("删除护士" + item.EmployeeID + "employeeDepartmentSwitch信息失败||" + ex.ToString());
                    continue;
                }
            }
            return true;
        }
        /// <summary>
        /// 获取HIS病区权限数据
        /// </summary>
        /// <returns></returns>
        private async Task<List<EmployeeStationSwitchInfo>>  GetApiData()
        {
            _logger.Info("开始获取EmployeeStationSwitch-API");
            var employeeStationSwitchDatas = new List<EmployeeStationSwitchInfo>();
            var systemOperatingEnvironment = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "SystemOperatingEnvironment");
            if (systemOperatingEnvironment != "1")
            {
                string hisData = "";
                var datas = new List<HISEmployeeStationSwitch>();
                _logger.Info("开始进行员工与病区数据同步");
                var api = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "SyncHISEmployeeStationSwitch");
                try
                {
                    hisData = HttpHelper.HttpGet(api);
                    //hisData = ReadFile.ReadTxt(@"D:\YCInterconnectJson\switch.txt");
                }
                catch (Exception ex)
                {
                    _logger.Error("呼叫API,失败appConfigSettingCode:【SyncHISEmployeeStationSwitch】" + ListToJson.ToJson(hisData) + ex.ToString());
                    return null;
                }
                _logger.Info($"获取到的his数据{hisData}");
                try
                {
                    datas = ListToJson.ToList<List<HISEmployeeStationSwitch>>(hisData);
                    _logger.Info("HIS数据转换成功");
                }
                catch (Exception ex)
                {
                    _logger.Error("员工与病区转List失败" + hisData + ex.ToString());
                    return null;
                }
                foreach (var item in datas)
                {
                    var insertData = new EmployeeStationSwitchInfo()
                    {
                        EmployeeID = item.EmployeeID,
                        StationCode = item.StationCode,
                        ModifyPersonID = "HIS",
                        ModifyDate = DateTime.Now,
                        DeleteFlag = ""
                    };
                    employeeStationSwitchDatas.Add(insertData);
                }
            }
            return employeeStationSwitchDatas;
        }
    }
}
