﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class AssessToProblemRepository : IAssessToProblemRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly GetCacheService _getCacheService;

        public AssessToProblemRepository(MedicalDbContext db, IMemoryCache memoryCache, GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _getCacheService = getCacheService;
        }

        public async Task<List<AssessToProblemInfo>> GetAsync(int? problemId)
        {
            var datas = await GetCacheAsync() as List<AssessToProblemInfo>;
            if (problemId != null)
            {
                return datas.Where(t => t.ProblemID == problemId && t.DeleteFlag != "*").ToList();
            }
            return datas;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<AssessToProblemInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            return await _medicalDbContext.AssessToProblems.Where(m => m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.AssessToProblem.ToString();
        }

        public async Task<AssessToProblemInfo> GetByID(int assessToProblemID)
        {
            return await _medicalDbContext.AssessToProblems.Where(m => m.ID == assessToProblemID && m.DeleteFlag != "*").SingleOrDefaultAsync();
        }
    }
}
