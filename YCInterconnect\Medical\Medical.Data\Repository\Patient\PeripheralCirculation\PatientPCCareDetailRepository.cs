﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientPCCareDetailRepository : IPatientPCCareDetailRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientPCCareDetailRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 根据末梢血运评估主表获取明细列表
        /// </summary>
        /// <param name="woundCareMainID"></param>
        /// <param name=""></param>
        /// <returns></returns>
        public async Task<List<PatientPCCareDetailInfo>> GetByMainIDAsync(string pcCareMainID)
        {
            return await _medicalDbContext.PatientPCCareDetailInfos.Where(t => t.PatientPCCareMainID == pcCareMainID && t.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientPCCareDetailInfo>> GetByCareMainIDsAsNoTrackAsync(List<string> careMainIDs)
        {
            return await _medicalDbContext.PatientPCCareDetailInfos.AsNoTracking().
                Where(m => careMainIDs.Contains(m.PatientPCCareMainID) && m.DeleteFlag != "*").ToListAsync();
        }
    }
}