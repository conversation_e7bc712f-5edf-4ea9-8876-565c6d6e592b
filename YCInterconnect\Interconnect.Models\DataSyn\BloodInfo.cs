﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("Blood")]
    public class BloodInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///病案号
        ///</summary>
        public string ChartNo { get; set; }
        /// <summary>
        ///输血日期
        ///</summary>
        public DateTime BloodTransfusionDate { get; set; }
        /// <summary>
        ///输血时间
        ///</summary>
        public TimeSpan? BloodTransfusionTime { get; set; }
        /// <summary>
        ///输血量///</summary>
        public short BloodTransfusionVolume { get; set; }
        /// <summary>
        ///血品
        ///</summary>
        public string BloodType { get; set; }
        /// <summary>
        ///有无输血反应
        ///</summary>
        public string TransfusionReaction { get; set; }
    }
}