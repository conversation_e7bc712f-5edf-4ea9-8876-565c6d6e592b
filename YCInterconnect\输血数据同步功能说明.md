# 输血数据同步到 NursingRecordDetail 功能说明

## 概述

本功能实现了输血数据的 API 接口获取和同步到 NursingRecordDetail 表的处理，支持两种数据获取方式：

1. 普通输血数据同步到 NursingRecordDetail（基于时间范围）
2. Wiley 补录单病人输血数据同步到 NursingRecordDetail（基于时间范围和病案号）

## 功能特点

- ✅ 支持新增和修改功能
- ✅ 遵循项目编码规范（驼峰命名，方法首字母大写，禁止空行）
- ✅ 预留具体处理逻辑，便于后续扩展
- ✅ 优化的错误处理和日志记录
- ✅ 参数验证和业务逻辑验证
- ✅ 删除了不必要的 try-catch，提高代码可读性
- ✅ 统一使用 ResponseResult 的 Error()和 Sucess()方法处理状态
- ✅ 简化返回类型，使用 bool 代替复杂的响应模型
- ✅ 删除不必要的 model 类，复用现有的 ResponseResult
- ✅ 参数检核移到 Service 层，减少 Controller 中的操作
- ✅ Service 层只暴露 Controller 需要的方法，其他方法设为私有

## 文件结构

### 1. ViewModel 层

- **BloodTransfusionSyncView.cs** - 输血数据同步视图模型
- **BloodTransfusionApiRequest.cs** - API 请求参数模型

### 2. Service 层

- **IBloodTransfusionSyncService.cs** - 输血数据同步服务接口
- **BloodTransfusionSyncService.cs** - 输血数据同步服务实现

### 3. Controller 层

- **BloodTransfusionSyncController.cs** - 输血数据同步到 NursingRecordDetail 控制器

## API 接口说明

### 1. 同步输血数据到 NursingRecordDetail

**接口地址：** `GET /api/BloodTransfusionSync/SyncTransfusionToRecordDetail`

**参数：**

- `startDateTime` (DateTime, 必填) - 开始时间
- `endDateTime` (DateTime, 必填) - 结束时间

**示例：**

```
GET /api/BloodTransfusionSync/SyncTransfusionToRecordDetail?startDateTime=2024-01-01 00:00:00&endDateTime=2024-01-02 00:00:00
```

### 2. 同步单个病人输血数据到 NursingRecordDetail

**接口地址：** `GET /api/BloodTransfusionSync/SyncSinglePatientTransfusionToRecordDetail`

**参数：**

- `startDateTime` (DateTime, 必填) - 开始时间
- `endDateTime` (DateTime, 必填) - 结束时间
- `caseNumber` (string, 必填) - 病案号

**示例：**

```
GET /api/BloodTransfusionSync/SyncSinglePatientTransfusionToRecordDetail?startDateTime=2024-01-01 00:00:00&endDateTime=2024-01-02 00:00:00&caseNumber=202401010001
```

## 响应格式

### 同步响应格式

```json
{
  "Code": 1,
  "Message": "处理完成",
  "Data": {
    "Code": 1,
    "Message": "处理完成",
    "SuccessCount": 10,
    "FailCount": 0,
    "AddCount": 8,
    "UpdateCount": 2,
    "ResponseTime": "2024-01-01T12:00:00"
  }
}
```

### 数据获取响应格式

```json
{
  "Code": 1,
  "Message": "获取到 5 条输血数据",
  "Data": [
    {
      "TransfusionID": "T202401010001",
      "BloodDonorBagsCode": "B202401010001",
      "LocalCaseNumber": "202401010001",
      "PatientName": "张三",
      "Gender": "男",
      "Age": "45"
      // ... 其他字段
    }
  ]
}
```

## 配置说明

在 `appsettings.json` 的 `Configs` 节点中需要配置以下 API 地址：

```json
{
  "Configs": {
    "DataInterfaceAPI": "http://localhost:5000"
  }
}
```

系统会自动拼接以下 API 端点：

- 输血数据 API：`{DataInterfaceAPI}/api/BloodTransfusion/GetData`
- Wiley 补录单 API：`{DataInterfaceAPI}/api/BloodTransfusion/GetWileyData`

## 待完善功能

以下功能已预留接口，需要根据实际业务需求实现：

1. **数据库操作逻辑**

   - `AddBloodTransfusionToNursingRecordDetail` - 添加输血数据到 NursingRecordDetail
   - `UpdateBloodTransfusionToNursingRecordDetail` - 更新输血数据到 NursingRecordDetail
   - `CheckBloodTransfusionExistsInNursingRecordDetail` - 检查输血数据在 NursingRecordDetail 中是否存在

2. **外部 API 对接**
   - 需要根据实际的外部 API 接口格式调整请求和响应处理逻辑

## 使用说明

1. 确保项目编译成功
2. 配置正确的 API 地址
3. 启动项目
4. 通过 Swagger UI 或直接调用 API 接口进行测试

## 注意事项

- 所有时间参数都需要按照 `yyyy-MM-dd HH:mm:ss` 格式传递
- 参数检核在 Service 层进行，包括：
  - 开始时间不能大于或等于结束时间
  - 病案号参数不能为空
- 系统会自动记录详细的操作日志
- 支持依赖注入，会自动注册到容器中

## 代码优化说明

### 优化前后对比

**优化前（Controller）：**

```csharp
try
{
    if (startDateTime >= endDateTime)
    {
        var errorResult = new ResponseResult
        {
            Code = 0,
            Message = "开始时间不能大于或等于结束时间"
        };
        return errorResult.ToJson();
    }
    // 业务逻辑...
}
catch (Exception ex)
{
    var result = new ResponseResult
    {
        Code = 0,
        Message = "异常：" + ex.Message
    };
    return result.ToJson();
}
```

**优化后（Controller）：**

```csharp
var result = new ResponseResult();
if (startDateTime >= endDateTime)
{
    result.Error("开始时间不能大于或等于结束时间");
    return result.ToJson();
}
// 业务逻辑...
result.Data = syncResult;
result.Code = syncResult.Code;
result.Message = syncResult.Message;
return result.ToJson();
```

### 优化内容

1. **删除不必要的 try-catch**：

   - Controller 层删除了外层 try-catch，让异常自然向上抛出
   - Service 层删除了不必要的 try-catch，只在真正需要处理异常的地方保留

2. **统一 ResponseResult 使用**：

   - 在 Controller 开始就初始化 ResponseResult
   - 使用`result.Error()`方法设置错误状态
   - 使用`result.Sucess()`方法设置成功状态

3. **简化代码结构**：

   - 减少了代码嵌套层级
   - 提高了代码可读性
   - 保持了异常处理的一致性

4. **简化返回类型**：

   - Service 方法返回 bool 类型，简化业务逻辑
   - 删除了 BloodTransfusionSyncResponse 和 BloodTransfusionApiResponse
   - 使用现有的 ResponseResult 处理 API 响应
   - 通过日志记录详细的执行信息，而不是复杂的返回对象

5. **优化 HTTP 请求处理**：

   - 统一使用 Medical.Common.ResponseResult 解析 API 响应
   - 简化了 API 数据解析逻辑
   - 减少了不必要的 model 类定义

6. **方法重命名和简化**：

   - `SyncBloodTransfusionToNursingRecordDetail` → `SyncTransfusionToRecordDetail`
   - `SyncWileyBloodTransfusionToNursingRecordDetail` → `SyncSinglePatientTransfusionToRecordDetail`
   - 删除了不必要的 API 接口（仅获取数据的接口）
   - Service 层只暴露 Controller 需要的方法，其他方法设为私有

7. **参数检核优化**：
   - 将参数检核从 Controller 移到 Service 层
   - 减少了 Controller 中的重复代码
   - 统一了参数验证逻辑
