# 输血数据同步功能说明

## 概述
本功能实现了输血数据的API接口获取和同步处理，支持两种数据获取方式：
1. 普通输血数据获取（基于时间范围）
2. Wiley补录单病人输血数据获取（基于时间范围和病案号）

## 功能特点
- ✅ 支持新增和修改功能
- ✅ 遵循项目编码规范（驼峰命名，方法首字母大写，禁止空行）
- ✅ 预留具体处理逻辑，便于后续扩展
- ✅ 完整的错误处理和日志记录
- ✅ 参数验证和业务逻辑验证

## 文件结构

### 1. ViewModel层
- **BloodTransfusionSyncView.cs** - 输血数据同步视图模型
- **BloodTransfusionApiRequest.cs** - API请求参数模型
- **BloodTransfusionApiResponse.cs** - API响应数据模型

### 2. Service层
- **IBloodTransfusionSyncService.cs** - 输血数据同步服务接口
- **BloodTransfusionSyncService.cs** - 输血数据同步服务实现

### 3. Controller层
- **BloodTransfusionSyncController.cs** - 输血数据同步控制器

## API接口说明

### 1. 获取输血数据并同步
**接口地址：** `GET /api/BloodTransfusionSync/GetAndSyncBloodTransfusionData`

**参数：**
- `startDateTime` (DateTime, 必填) - 开始时间
- `endDateTime` (DateTime, 必填) - 结束时间

**示例：**
```
GET /api/BloodTransfusionSync/GetAndSyncBloodTransfusionData?startDateTime=2024-01-01 00:00:00&endDateTime=2024-01-02 00:00:00
```

### 2. 获取Wiley补录单病人输血数据并同步
**接口地址：** `GET /api/BloodTransfusionSync/GetAndSyncWileyBloodTransfusionData`

**参数：**
- `startDateTime` (DateTime, 必填) - 开始时间
- `endDateTime` (DateTime, 必填) - 结束时间
- `caseNumber` (string, 必填) - 病案号

**示例：**
```
GET /api/BloodTransfusionSync/GetAndSyncWileyBloodTransfusionData?startDateTime=2024-01-01 00:00:00&endDateTime=2024-01-02 00:00:00&caseNumber=202401010001
```

### 3. 获取输血数据（仅获取不同步）
**接口地址：** `GET /api/BloodTransfusionSync/GetBloodTransfusionData`

**参数：**
- `startDateTime` (DateTime, 必填) - 开始时间
- `endDateTime` (DateTime, 必填) - 结束时间

### 4. 获取Wiley补录单病人输血数据（仅获取不同步）
**接口地址：** `GET /api/BloodTransfusionSync/GetWileyBloodTransfusionData`

**参数：**
- `startDateTime` (DateTime, 必填) - 开始时间
- `endDateTime` (DateTime, 必填) - 结束时间
- `caseNumber` (string, 必填) - 病案号

## 响应格式

### 同步响应格式
```json
{
  "Code": 1,
  "Message": "处理完成",
  "Data": {
    "Code": 1,
    "Message": "处理完成",
    "SuccessCount": 10,
    "FailCount": 0,
    "AddCount": 8,
    "UpdateCount": 2,
    "ResponseTime": "2024-01-01T12:00:00"
  }
}
```

### 数据获取响应格式
```json
{
  "Code": 1,
  "Message": "获取到 5 条输血数据",
  "Data": [
    {
      "TransfusionID": "T202401010001",
      "BloodDonorBagsCode": "B202401010001",
      "LocalCaseNumber": "202401010001",
      "PatientName": "张三",
      "Gender": "男",
      "Age": "45",
      // ... 其他字段
    }
  ]
}
```

## 配置说明

在 `appsettings.json` 的 `Configs` 节点中需要配置以下API地址：
```json
{
  "Configs": {
    "DataInterfaceAPI": "http://localhost:5000"
  }
}
```

系统会自动拼接以下API端点：
- 输血数据API：`{DataInterfaceAPI}/api/BloodTransfusion/GetData`
- Wiley补录单API：`{DataInterfaceAPI}/api/BloodTransfusion/GetWileyData`

## 待完善功能

以下功能已预留接口，需要根据实际业务需求实现：

1. **数据库操作逻辑**
   - `AddBloodTransfusionData` - 添加输血数据到数据库
   - `UpdateBloodTransfusionData` - 更新输血数据到数据库
   - `CheckBloodTransfusionDataExists` - 检查输血数据是否存在

2. **外部API对接**
   - 需要根据实际的外部API接口格式调整请求和响应处理逻辑

## 使用说明

1. 确保项目编译成功
2. 配置正确的API地址
3. 启动项目
4. 通过Swagger UI或直接调用API接口进行测试

## 注意事项

- 所有时间参数都需要按照 `yyyy-MM-dd HH:mm:ss` 格式传递
- 开始时间不能大于或等于结束时间
- 病案号参数不能为空
- 系统会自动记录详细的操作日志
- 支持依赖注入，会自动注册到容器中
