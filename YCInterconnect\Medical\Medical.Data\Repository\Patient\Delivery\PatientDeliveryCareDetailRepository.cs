﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.Data;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientDeliveryCareDetailRepository : IPatientDeliveryCareDetailRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientDeliveryCareDetailRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 根据分娩照护主记录获取照护明细
        /// </summary>
        /// <param name="mainID">分娩照护主记录ID</param>
        /// <returns></returns>
        public async Task<List<PatientDeliveryCareDetailInfo>> GetByMainID(string mainID)
        {
            return await _medicalDbContext.PatientDeliveryCareDetailInfos.Where(t => t.PatientDeliveryCareMainID == mainID && t.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据患者住院ID获取明细
        /// </summary>
        /// <param name="inpaitentID">患者住院ID</param>
        /// <returns></returns>
        public async Task<List<PatientDeliveryCareDetailInfo>> GetByInpatientID(string inpaitentID)
        {
            return await _medicalDbContext.PatientDeliveryCareDetailInfos.Where(t => t.InpatientID == inpaitentID && t.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientDeliveryCareDetailInfo>> GetByAssessListID(string inpaitentID, List<int> assessListID)
        {
            return await _medicalDbContext.PatientDeliveryCareDetailInfos.Where(t => t.InpatientID == inpaitentID && assessListID.Contains(t.AssessListID) && t.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据主记录获取所有的评估明细
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<List<PatientAssessDetail>> GetAllDetailsByRecordID(string recordID)
        {
            var query = from m in _medicalDbContext.PatientDeliveryCareMainInfos
                        join n in _medicalDbContext.PatientDeliveryCareDetailInfos
                            on m.PatientDeliveryCareMainID equals n.PatientDeliveryCareMainID
                        where m.DeleteFlag != "*" && m.PatientDeliveryRecordID == recordID && n.DeleteFlag != "*"
                        select new PatientAssessDetail
                        {
                            AssessMainID = n.PatientDeliveryCareMainID,
                            AssessListID = n.AssessListID,
                            AssessValue = n.AssessValue,
                            AssessListGroupID = n.AssessListGroupID,
                            ModifyDate = n.ModifyDate,
                            BookMarkID = n.BookMarkID
                        };
            return await query.ToListAsync();
        }

        /// <summary>
        /// 根据分娩照护主记录获取照护明细
        /// </summary>
        /// <param name="mainID">分娩照护主记录ID</param>
        /// <param name="assessListGroupIDs">评估项组ID</param>
        /// <returns></returns>
        public async Task<List<Detail>> GetDetailsByMainIDAndGroupIDs(string mainID, List<int> assessListGroupIDs)
        {
            return await _medicalDbContext.PatientDeliveryCareDetailInfos.Where(t => t.PatientDeliveryCareMainID == mainID && assessListGroupIDs.Contains(t.AssessListGroupID.Value) && t.DeleteFlag != "*")
                .Select(m => new Detail
                {
                    AssessListID = m.AssessListID,
                    AssessListGroupID = m.AssessListGroupID,
                    AssessValue = m.AssessValue
                }).ToListAsync();
        }
        /// <summary>
        /// 根据主键ID集合获取明细数据
        /// </summary>
        /// <param name="mainIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientDeliveryCareDetailInfo>> GetDetailsByMainIDs(List<string> mainIDs)
        {
            return await _medicalDbContext.PatientDeliveryCareDetailInfos.Where(t => mainIDs.Contains(t.PatientDeliveryCareMainID) && t.DeleteFlag != "*")
                .ToListAsync();
        }

        public async Task<List<PatientDeliveryCareDetailInfo>> GetAllDeliveryDetailsByRecordID(string recordID)
        {
            var query = from m in _medicalDbContext.PatientDeliveryCareMainInfos
                        join n in _medicalDbContext.PatientDeliveryCareDetailInfos
                            on m.PatientDeliveryCareMainID equals n.PatientDeliveryCareMainID
                        where m.DeleteFlag != "*" && m.PatientDeliveryRecordID == recordID && n.DeleteFlag != "*"
                        select n;
            return await query.ToListAsync();
        }
        /// <summary>
        /// 根据主记录ID集合获取明细数据
        /// </summary>
        /// <param name="recordIDs">主记录集合</param>
        /// <returns></returns>
        public async Task<List<PatientAssessDetail>> GetDetailsByRecordIDs(string[] recordIDs)
        {
            var query = from m in _medicalDbContext.PatientDeliveryCareMainInfos
                        join n in _medicalDbContext.PatientDeliveryCareDetailInfos
                            on m.PatientDeliveryCareMainID equals n.PatientDeliveryCareMainID
                        where m.DeleteFlag != "*" && recordIDs.Contains(m.PatientDeliveryRecordID) && n.DeleteFlag != "*"
                        select new PatientAssessDetail
                        {
                            RecordID = m.PatientDeliveryRecordID,
                            InpatientID = m.InpatientID,
                            AssessMainID = n.PatientDeliveryCareMainID,
                            AssessListID = n.AssessListID,
                            AssessValue = n.AssessValue,
                            AssessListGroupID = n.AssessListGroupID,
                            ModifyDate = n.ModifyDate,
                            BookMarkID = n.BookMarkID
                        };
            return await query.ToListAsync();
        }
    }
}