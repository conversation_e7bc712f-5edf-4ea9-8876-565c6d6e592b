﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using NLog;
using Interconnect.Data.Context;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Services.Interface;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using Newtonsoft.Json;
using Medical.Common;
using Arch.EntityFrameworkCore.UnitOfWork;
using Medical.ViewModels.Query;

namespace Interconnect.Services
{
    public class DepartmentService : IDepartmentService
    {
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IDepartmentRepository _departmentRepository;
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IUnitOfWork<DataOutConnection> _unitOfWorkOut;
        private readonly IOptions<SystemConfig> _config;
        private readonly ILogInfoServices _ILogInfoServices;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISettingDescRepository _ICSettingDescriptionRepository;
        private readonly ICommonHelper _commonHelper;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;

        /// <summary>
        /// 医疗院代码
        /// </summary>
        private string HOSPITALID = "";

        public DepartmentService(IDepartmentListRepository departmentListRepository
            , IDepartmentRepository departmentRepository
            , IUnitOfWork<MedicalDbContext> unitOfWork
            , IUnitOfWork<DataOutConnection> unitOfWorkOut
            , IOptions<SystemConfig> config
            , ILogInfoServices LogInfoServices
            , ISettingDescRepository settingDescriptionRepository
            , ICommonHelper commonHelper
            , IAppConfigSettingRepository  appConfigSettingRepository
            )
        {
            _departmentListRepository = departmentListRepository;
            _departmentRepository = departmentRepository;
            _unitOfWork = unitOfWork;
            _unitOfWorkOut = unitOfWorkOut;
            _config = config;
            _ILogInfoServices = LogInfoServices;
            _ICSettingDescriptionRepository = settingDescriptionRepository;
            _commonHelper = commonHelper;
            _appConfigSettingRepository = appConfigSettingRepository;
            HOSPITALID = _config.Value.HospitalID;
        }

        /// <summary>
        /// 如果发现有没有同步的数据，则一直获取，直至完全同步
        /// </summary>
        /// <returns></returns>
        public bool DataPump()
        {
            _logger.Info("开始获取科室 API");
            string apiStr = "";
            var apiStrList = _ICSettingDescriptionRepository.GetAsync(1, "6");
            if (apiStrList.Count > 0)
            {
                apiStr = apiStrList[0].Description;
            }
            else
            {
                _logger.Error("获取科室API失败");
                return false;
            }
            _logger.Info("获取科室信息数据");
            var data = new Dictionary<string, string>();
            //呼叫API获得数据
            var resultData = _commonHelper.GetInterconnectData(apiStr, data);

            //从配置当中获取数据 梁宝华 2020-04-29
            var printInterfaceData = 0;
            var resultPrintDate = _appConfigSettingRepository.GetConfigSettingValue("Configs", "PrintInterfaceData").Result;
            if (StringCheck.IsNumeric(resultPrintDate))
            {
                printInterfaceData = int.Parse(resultPrintDate);
            }

            if (printInterfaceData == 1)
            {
                _logger.Info("获得数据如下" + resultData);
            }
            var result = new ResponseResult();
            result = JsonConvert.DeserializeObject<ResponseResult>(resultData);
            if (result == null || result.Data == null)
            {
                _logger.Error("获取resultData失败:" + resultData);
                return false;
            }
            var Interconnect_Data = new List<DepartmentInfo>();
            try
            {
                Interconnect_Data = JsonConvert.DeserializeObject<List<DepartmentInfo>>(result.Data.ToString());
            }
            catch (Exception ex)
            {
                _logger.Error("Json格式化失败:" + ex.ToString());
                return false;
            }
            _logger.Info("获得" + Interconnect_Data.Count() + "条数据");
            if (Interconnect_Data.Count > 0)
            {
                if (!DataPumpDetail(Interconnect_Data))
                {
                    return false;
                }
            }
            try
            {
                //更新科室缓存
                CacheQuery query = new CacheQuery
                {
                    Type = CacheType.Department
                };
                _commonHelper.UpdateCache(query);
            }
            catch (Exception ex)
            {
                _logger.Info("科室缓存更新失败" + ex.Message);
            }
            return true;
        }

        private bool DataPumpDetail(List<DepartmentInfo> pumpDatas)
        {
            //更新比数
            int updateCount = 0;        
            //现有数据
            var nowDatas = _departmentListRepository.GetAllDeparment();
            List<DepartmentInfo> newList = pumpDatas.GetRange(0, pumpDatas.Count);
            var maxID = nowDatas.OrderByDescending(m => m.ID).Select(m => m.ID).FirstOrDefault();
            string Tablename = "department";
            _logger.Info(Tablename + " 开始进行数据同步，数据条数：" + pumpDatas.Count.ToString());
            if (pumpDatas.Count == 0)
            {
                _logger.Debug("日期:" + DateTime.Now + "同步结束,无数据需要同步");
                return true;
            }
            bool checkData;
            for (int i = pumpDatas.Count - 1; i >= 0; i--)
            {
                checkData = false;
                foreach (var nowData in nowDatas)
                {
                    if (pumpDatas[i].DepartmentCode != nowData.DepartmentCode)
                    {
                        continue;
                    }
                    checkData = true;
                    //表示更新
                    nowData.Department = pumpDatas[i].Department;
                    nowData.DeleteFlag = pumpDatas[i].DeleteFlag;
                    nowData.ModifyDate = pumpDatas[i].ModifyDate;
                    nowData.ModifyPersonID = pumpDatas[i].ModifyPersonID;
                    updateCount += 1;
                }
                if (checkData)
                {
                    //移除更新数据保留需要新增数据
                    pumpDatas.RemoveAt(i);
                }
            }

            foreach (var item in pumpDatas)
            {
                maxID++;
                var departmentList = new DepartmentListInfo
                {
                    ID = maxID,
                    HospitalID = HOSPITALID,
                    Department = item.Department,
                    DepartmentCode = item.DepartmentCode,
                    DepartmentPattern = item.DepartmentPattern,
                    ModifyPersonID = item.ModifyPersonID,
                    ModifyDate = item.ModifyDate,
                    SpecialListType ="",
                };
                _unitOfWork.GetRepository<DepartmentListInfo>().Insert(departmentList);
            }

            _logger.Info(Tablename + " 同步结束" + pumpDatas.Count.ToString());
            try
            {
                _unitOfWork.SaveChanges();
                _logger.Info(Tablename + "同步完成!");
            }
            catch (Exception ex)
            {
                _logger.Error(Tablename + "同步错误!" + ex.ToString());
                return false;
            }

            return true;
        }

        /// <summary>
        /// 数据对比，删除对方不存在的科室信息
        /// </summary>
        /// <returns></returns>
        private bool DataDelete()
        {
            var medicalDeptList = _departmentListRepository.GetDepartmentList();
            var InterconnectDeptList = _departmentRepository.GetAsync();

            //对比删除
            foreach (var item in medicalDeptList)
            {
                var tempInterconnectDeptList = InterconnectDeptList.Where(m => m.DepartmentCode == item.DepartmentCode).ToList();
                if (tempInterconnectDeptList.Count < 0)
                {
                    item.DeleteFlag = "*";
                }
            }
            try
            {             
                _unitOfWork.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error("||删除科室失败||" + ex.ToString());
                return false;
            }
            _logger.Info("删除科室成功");
            return true;
        }
    }
}
