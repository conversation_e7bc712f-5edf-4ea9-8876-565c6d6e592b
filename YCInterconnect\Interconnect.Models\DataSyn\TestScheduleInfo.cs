﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("TestSchedule")]
    public class TestScheduleInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///住院号
        ///</summary>
        public string CaseNumber { get; set; }
        /// <summary>
        ///检验申请号
        ///</summary>
        public string TestNO { get; set; }
        /// <summary>
        ///检验日期
        ///</summary>
        public DateTime TestDate { get; set; }
        /// <summary>
        ///检验项目
        ///</summary>
        public string TestItem { get; set; }
        /// <summary>
        ///检验项目码
        ///</summary>
        public string TestItemCode { get; set; }
        /// <summary>
        ///检验值
        ///</summary>
        public string TestValue { get; set; }
        /// <summary>
        ///单位
        ///</summary>
        public string Unit { get; set; }
        /// <summary>
        ///正异常范围
        ///</summary>
        public string NormalRange { get; set; }
        /// <summary>
        ///正常或异常
        ///</summary>
        public string NormalAbnormal { get; set; }
        /// <summary>
        ///说明
        ///</summary>
        public string Description { get; set; }
    }
}