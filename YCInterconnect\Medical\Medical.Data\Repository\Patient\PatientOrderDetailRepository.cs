﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientOrderDetailRepository : IPatientOrderDetailRepository
    {
        private MedicalDbContext _dbContext = null;

        public PatientOrderDetailRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }
        /// <summary>
        /// 获取病人医嘱
        /// </summary>
        /// <param name="patientOrderMainID">医嘱主序号</param>
        /// <param name="orderID">医嘱序号</param>
        /// <param name="orderType">医嘱类别</param>
        /// <returns></returns>
        public async Task<List<PatientOrderDetailInfo>> GetByOrderType(string patientOrderMainID, string orderID, string orderType)
        {
            return await _dbContext.PatientOrderDetails.Where(m =>
            m.PatientOrderMainID == patientOrderMainID &&
            m.OrderID == orderID &&
            m.OrderType == orderType &&
            m.OrderCode != "" &&
            m.DeleteFlag != "*").OrderBy(m => m.OrderID).ThenBy(m => m.OrderDetailID).ToListAsync();
        }
        /// <summary>
        /// 获取病人医嘱明细
        /// </summary>
        /// <param name="patientOrderMainID">医嘱主序号</param>
        /// <param name="orderID">医嘱序号</param>
        /// <returns></returns>
        public async Task<List<PatientOrderDetailInfo>> GetAsync(string patientOrderMainID, string orderID, string ordercode)
        {
            return await _dbContext.PatientOrderDetails.Where(m =>
            m.PatientOrderMainID == patientOrderMainID &&
            m.OrderID == orderID &&
            m.OrderCode == ordercode &&
            m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取病人医嘱明细
        /// </summary>
        /// <param name="patientOrderMainID">医嘱主序号</param>
        /// <param name="orderID">医嘱序号</param>
        /// <returns></returns>
        public List<PatientOrderDetailInfo> GetOneOrderDetail(string patientOrderMainID, string orderID, string ordercode)
        {
            return _dbContext.PatientOrderDetails.Where(m =>
           m.PatientOrderMainID == patientOrderMainID &&
           m.OrderID == orderID &&
           m.OrderCode == ordercode &&
           m.DeleteFlag != "*").ToList();
        }
        /// <summary>
        /// 获取病人医嘱
        /// </summary>
        /// <param name="patientOrderMainID">医嘱主序号</param>
        /// <returns></returns>
        public async Task<List<PatientOrderDetailInfo>> GetAsync(string patientOrderMainID)
        {
            return await _dbContext.PatientOrderDetails.Where(m =>
            m.PatientOrderMainID == patientOrderMainID &&
            m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取病人医嘱
        /// </summary>
        /// <param name="patientOrderDetailID">医嘱名细序号</param>
        /// <returns></returns>
        public async Task<List<PatientOrderDetailInfo>> GetByIDAsync(string[] patientOrderDetailID)
        {
            return await _dbContext.PatientOrderDetails.Where(m =>
                  patientOrderDetailID.Contains(m.PatientOrderDetailID) &&
                  m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取病人医嘱
        /// </summary>
        /// <param name="patientOrderDetailID">医嘱名细序号</param>
        /// <returns></returns>
        public async Task<PatientOrderDetailInfo> GetByIDAsync(string patientOrderDetailID)
        {
            return await _dbContext.PatientOrderDetails.Where(m =>
            m.PatientOrderDetailID == patientOrderDetailID &&
            m.DeleteFlag != "*").SingleOrDefaultAsync();
        }

        /// <summary>
        /// 根据医嘱明细序号获取医嘱码
        /// </summary>
        /// <param name="patientOrderDetailID">医嘱名细序号</param>
        /// <returns></returns>
        public async Task<string> GetOrderCodeByIDAsync(string patientOrderDetailID)
        {
            var orderDetail = await _dbContext.PatientOrderDetails.Where(m =>
           m.PatientOrderDetailID == patientOrderDetailID &&
           m.DeleteFlag != "*").SingleOrDefaultAsync();
            if (orderDetail != null)
            {
                return orderDetail.OrderCode;
            }
            return null;
        }

        /// <summary>
        /// 获取病人医嘱
        /// </summary>
        /// <param name="orderIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientOrderDetailInfo>> GetByOrderIDs(string[] orderIDs)
        {
            var patientOrderDetailList = new List<PatientOrderDetailInfo>();
            for (int i = 0; i < orderIDs.Length; i++)
            {
                var tempList = await _dbContext.PatientOrderDetails.Where(m =>
               m.OrderID == orderIDs[i] && m.DeleteFlag != "*").ToListAsync();
                patientOrderDetailList = patientOrderDetailList.Union(tempList).ToList();
            }
            return patientOrderDetailList;
        }
        /// <summary>
        /// 获取病人医嘱
        /// </summary>
        /// <param name="orderID">医嘱ID</param>
        /// <returns></returns>
        public async Task<List<PatientOrderDetailInfo>> GetByOrderID(string orderID)
        {
            return await _dbContext.PatientOrderDetails.Where(m => m.OrderID == orderID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据医嘱主ID获取医嘱明细
        /// </summary>
        /// <param name="mainIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientOrderDetailInfo>> GetByMainIDs(string[] mainIDs)
        {
            var patientOrderDetailList = await _dbContext.PatientOrderDetails.Where(m =>
            mainIDs.Contains(m.PatientOrderMainID) && m.DeleteFlag != "*").ToListAsync();

            return patientOrderDetailList.OrderBy(m => m.StartDate).ThenBy(m => m.StartTime)
                .ThenBy(m => m.OrderID).ThenBy(m => m.HISOrderSort).ToList();

        }
        /// <summary>
        /// 获取医嘱明细表最后一条数据
        /// </summary>
        /// <returns></returns>
        public async Task<PatientOrderDetailInfo> GetLastData()
        {
            return await _dbContext.PatientOrderDetails.OrderByDescending(m => m.ModifyDate).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取所有医嘱OrderID集合
        /// </summary>
        /// <returns></returns>
        public async Task<List<string>> GetAllData()
        {
            return await _dbContext.PatientOrderDetails.Select(m => m.OrderID).ToListAsync();
        }
        /// <summary>
        /// 获取一个病人的医嘱明细
        /// </summary>
        /// <param name="inPatientId"></param>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<List<PatientOrderDetailInfo>> GetPatientOrderDetailData(string inPatientId, int stationID)
        {
            var patientOrderDetailList = new List<PatientOrderDetailInfo>();
            var query = (from a in _dbContext.PatientOrderMains
                         join
                         b in _dbContext.PatientOrderDetails
                         on a.PatientOrderMainID equals b.PatientOrderMainID
                         where a.InpatientID == inPatientId
                         && a.StationID == stationID
                         && a.CancalDate == null && a.DeleteFlag != "*"
                         select b
                         );
            patientOrderDetailList = await query.ToListAsync();
            return patientOrderDetailList;
        }
        /// <summary>
        /// 获取一个病人的医嘱
        /// </summary>
        /// <param name="inPatientId"></param>
        /// <returns></returns>
        public async Task<List<PatientOrderView>> GetPatientOrder(string inPatientId)
        {
            var patientOrderView = new List<PatientOrderView>();

            var query = (from a in _dbContext.PatientOrderMains
                         join
                        b in _dbContext.PatientOrderDetails
                        on a.PatientOrderMainID equals b.PatientOrderMainID
                         where a.InpatientID == inPatientId && a.DeleteFlag != "*"
                         select new PatientOrderView
                         {
                             RecordFlag = a.RecordFlag,
                             PrintFlag = a.PrintFlag,
                             OrderType = a.OrderType,
                             OrderPattern = b.OrderPattern,
                             OrderAlertFlag = b.OrderAlertFlag,
                             StartDateTime = a.StartDate + a.StartTime,
                             StartDate = a.StartDate,
                             OrderContent = b.OrderContent,
                             OrderDose = b.OrderDose,
                             Unit = b.Unit,
                             OrderRule = b.OrderRule,
                             HISFrequency = a.HISFrequency,
                             HISFrequencySchedule = a.HISFrequencySchedule,
                             OrderDescription = b.OrderDescription,
                             EndDateTime = a.EndDate + a.EndTime,
                             LastPerformTime = a.LastPerformTime,
                             PatientOrderMainID = a.PatientOrderMainID,
                             PatientOrderDetailID = b.PatientOrderDetailID,
                             InpatientID = a.InpatientID,
                             FirstDayStartTime = a.FirstDayStartTime,
                             CheckEmployeeId = a.CheckEmployeeID,
                             CheckDateTime = a.CheckDateTime,
                             FrequencyID = a.FrequencyID,
                             EndDate = a.EndDate,
                             EndTime = a.EndTime,
                             OrderStatus = a.OrderStatus,
                             CancalDate = a.CancalDate,
                             StationID = a.StationID,
                             OrderCode = b.OrderCode
                         }
                         );
            patientOrderView = await query.ToListAsync();
            patientOrderView = patientOrderView.Where(m => m.CancalDate == null).ToList();//没有取消的医嘱
            patientOrderView = patientOrderView.OrderBy(m => m.StartDateTime).ThenBy(m => m.PatientOrderMainID).ThenBy(m => m.HISOrderSort).ToList();
            return patientOrderView;
        }
        /// <summary>
        /// 获取一个病人的医嘱
        /// </summary>
        /// <param name="inPatientId"></param>
        /// <returns></returns>
        public async Task<List<PatientOrderDetailInfo>> GetPatientOrderDetail(string inPatientId)
        {
            var query = (from a in _dbContext.PatientOrderMains
                         join
                         b in _dbContext.PatientOrderDetails
                        on a.PatientOrderMainID equals b.PatientOrderMainID
                         where a.InpatientID == inPatientId
                         select b
                         );
            return await query.ToListAsync();
        }
        /// <summary>
        /// 获取一个病人的医嘱明细
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="OrderCode"></param>
        /// <param name="orderNO"></param>
        /// <param name="orderSubNO"></param>
        /// <returns></returns>
        public PatientOrderDetailInfo GetPatientOneOrderDetail(string caseNumber, string OrderCode, string orderNO, string orderSubNO)
        {
            var query = (from a in _dbContext.PatientOrderMains
                         join
                         b in _dbContext.PatientOrderDetails
                        on a.PatientOrderMainID equals b.PatientOrderMainID
                         where a.CaseNumber == caseNumber
                         && b.OrderCode == OrderCode && b.OrderNO == orderNO && b.OrderDetailID == orderSubNO
                         select b
                        );
            return query.FirstOrDefault();
        }

        /// <summary>
        /// 根据医嘱主记录ID集合获取医嘱明细
        /// </summary>
        /// <param name="mainIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientOrderDetailInfo>> GetByMainIDs(HashSet<string> mainIDs)
        {
            return await _dbContext.PatientOrderDetails.Where(m =>
               mainIDs.Contains(m.PatientOrderMainID) && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 取得同时间点内相同未执行的医嘱排程
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="scheduleDate"></param>
        /// <param name="scheduleTime"></param>
        /// <param name="interventionID"></param>
        /// <param name="interventionCode"></param>
        /// <returns></returns>
        public async Task<List<PatientOrderDetailInfo>> GetAsync(string inpatientID, DateTime scheduleDate, TimeSpan scheduleTime, int interventionID, string interventionCode)
        {
            return await (from a in _dbContext.PatientScheduleMain
                          join b in _dbContext.PatientOrderDetails on new { PatientOrderDetailID = a.PatientProblemID, PatientOrderMainID = a.PatientInterventionID }
                                                               equals new { b.PatientOrderDetailID, b.PatientOrderMainID }
                          where a.InpatientID == inpatientID && a.ScheduleDate == scheduleDate && a.ScheduleTime == scheduleTime && a.InterventionID == interventionID && a.InterventionCode == interventionCode
                                && a.PerformDate != null && a.SourceFlag == "O" && a.DeleteFlag != "*" && b.OrderType == "0" && b.DeleteFlag != "*"
                          select b).ToListAsync();
        }
        /// <summary>
        /// 更具OrderCode集合获取数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="orderCodes"></param>
        /// <returns></returns>
        public async Task<List<SimpleOrderView>> GetSimplePatientOrder(string inpatientID, List<string> orderCodes)
        {
            var query = await (from a in _dbContext.PatientOrderMains
                               join b in _dbContext.PatientOrderDetails
                                  on a.PatientOrderMainID equals b.PatientOrderMainID
                               where a.InpatientID == inpatientID && a.DeleteFlag != "*" && a.CancalDate == null && a.FrequencyID.HasValue
                               select new SimpleOrderView
                               {
                                   StationID = a.StationID,
                                   OrderCode = b.OrderCode,
                                   StartDate = a.StartDate,
                                   StartTime = a.StartTime,
                                   EndDate = a.EndDate,
                                   EndTime = a.EndTime,
                                   FrequencyID = a.FrequencyID,
                               }
                        ).ToListAsync();

            query = query.Where(m => orderCodes.Contains(m.OrderCode)).ToList();
            return query;
        }
        /// <summary>
        /// 获取药物用于抢救
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="stationID"></param>
        /// <param name="orderTime"></param>
        /// <returns></returns>
        public async Task<List<PatientOrderDetailInfo>> GetOrderDrugUserRescue(string inpatientID, int stationID, int orderTime)
        {
            List<PatientOrderDetailInfo> result = new List<PatientOrderDetailInfo>();
            var initData = await (from a in _dbContext.PatientOrderMains
                                  join b in _dbContext.PatientOrderDetails
                                  on a.PatientOrderMainID equals b.PatientOrderMainID
                                  where a.InpatientID == inpatientID && a.StationID == stationID && a.DeleteFlag != "*" && a.CancalDate == null && b.OrderPattern == ""
                                     && b.EndDate != null
                                  select b).ToListAsync();
            foreach (var item in initData)
            {
                var orderData = item.StartDate.ToString("yyyy-MM-dd");
                var ordert = item.StartTime.ToString();
                var merge = orderData + " " + ordert;
                if (Convert.ToDateTime(merge) >= DateTime.Now.AddHours(orderTime))
                {
                    result.Add(item);
                }
            }
            return result;
        }
        /// <summary>
        /// 获取医嘱明细
        /// </summary>
        /// <param name="inpatientIDs"></param>
        /// <param name="orderCodes"></param>
        /// <returns></returns>
        public async Task<List<Dictionary<string, string>>> GetOrderDetailsByOrderCodes(List<string> inpatientIDs, List<string> orderCodes)
        {
            var query = await (from a in _dbContext.PatientOrderMains
                               join b in _dbContext.PatientOrderDetails
                                  on a.PatientOrderMainID equals b.PatientOrderMainID
                               where inpatientIDs.Contains(a.InpatientID) && a.DeleteFlag != "*" && a.CancalDate == null
                               && orderCodes.Contains(b.OrderCode)
                               select new Dictionary<string, string>()
                               {
                                   { "inpatientID" , a.InpatientID},
                                   { "PatientOrderMainID" , a.PatientOrderMainID},
                                   { "patientOrderDetailID" , b.PatientOrderDetailID}
                               }
                        ).ToListAsync();
            return query;
        }
        /// <summary>
        ///根据orderContent查找数据
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="orderContent"></param>
        /// <param name="orderPattern"></param>
        /// <returns></returns>
        public async Task<List<PatientOrderView>> GetOrderDatasByOrderContent(int stationID, string orderContent, string orderPattern)
        {
            var list = await (from a in _dbContext.PatientOrderMains
                              join b in _dbContext.PatientOrderDetails
                              on a.PatientOrderMainID equals b.PatientOrderMainID
                              join c in _dbContext.InpatientDatas
                              on a.InpatientID equals c.ID
                              where a.StationID == stationID && a.OrderStatus != "4" && a.OrderStatus != "3"
                              && InHospitalStatus.INHOSPITALLIST.Contains(c.InHospitalStatus ?? -1)
                              && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                              select new PatientOrderView
                              {
                                  PatientOrderMainID = a.PatientOrderMainID,
                                  InpatientID = a.InpatientID,
                                  StationID = a.StationID,
                                  OrderType = a.OrderType,
                                  StartDate = a.StartDate,
                                  StartTime = a.StartTime,
                                  EndDate = a.EndDate,
                                  EndTime = a.EndTime,
                                  BedNumber = a.BedNumber,
                                  BedID = a.BedID,
                                  OrderPattern = b.OrderPattern,
                                  OrderContent = b.OrderContent,
                                  OrderStatus = a.OrderStatus,
                              }
                              ).ToListAsync();
            if (!string.IsNullOrEmpty(orderPattern))
            {
                list = list.Where(m => m.OrderPattern == orderPattern).ToList();
            }
            else
            {
                list = list.Where(m => m.OrderContent.Trim().Contains(orderContent)).ToList();
            }
            return list;
        }
        public async Task<bool> CheckExistsByCode(string inpatientID, string orderCode)
        {
            var query = await (from a in _dbContext.PatientOrderMains
                               join b in _dbContext.PatientOrderDetails
                                  on a.PatientOrderMainID equals b.PatientOrderMainID
                               where a.InpatientID == inpatientID && a.DeleteFlag != "*" && a.CancalDate == null && a.EndDate == null && b.OrderCode == orderCode
                               select 1
                        ).ToListAsync();

            return query.Count > 0;
        }

        public async Task<List<PatientOrderMainInfo>> GetPatientOrderByCode(DateTime startDate, DateTime endDate, string orderCode)
        {
            return await (from a in _dbContext.PatientOrderMains
                          join b in _dbContext.PatientOrderDetails
                             on a.PatientOrderMainID equals b.PatientOrderMainID
                          where a.StartDate >= startDate && a.StartDate <= endDate && a.DeleteFlag != "*" && a.CancalDate == null && b.OrderCode == orderCode
                          select new PatientOrderMainInfo
                          {
                              InpatientID = a.InpatientID,
                              StartDate = a.StartDate,
                              EndDate = a.EndDate,
                              StartTime = a.StartTime,
                              EndTime = a.EndTime,
                              StationID = a.StationID,
                          }
                         ).ToListAsync();
        }

        public async Task<List<string>> GetDetailIDsByMainIDS(List<string> OrderMainIDs)
        {
            return await _dbContext.PatientOrderDetails.Where(m => OrderMainIDs.Contains(m.PatientOrderMainID)).Select(m => m.PatientOrderDetailID).ToListAsync();
        }
        /// <summary>
        /// 判断对应的明细数据是否存在
        /// </summary>
        /// <param name="orderMainID">医嘱主表主键ID</param>
        /// <param name="labSheetSN">抗菌药物类别</param>
        /// <returns>bool</returns>
        public async Task<bool> CheckExistOrNotByCodeLabSheetSNAsync(string orderMainID, string labSheetSN)
        {
            return await _dbContext.PatientOrderDetails.AsNoTracking().Where(m => m.PatientOrderMainID == orderMainID && m.DeleteFlag != "*"
            && m.LabSheetSN == labSheetSN
            ).CountAsync() > 0;

        }
        /// <summary>
        /// 根据医嘱主记录ID获取部分明细内容
        /// </summary>
        /// <param name="orderMainID"></param>
        /// <param name="labSheetSN"></param>
        /// <returns>（PatientOrderDetailID,OrderContent）</returns>
        public async Task<PatientOrderDetailInfo> GetPartOrderDetailByMainIDAsNoTrackAsync(string orderMainID, string labSheetSN)
        {
            return await _dbContext.PatientOrderDetails.AsNoTracking().Where(m => m.PatientOrderMainID == orderMainID && m.LabSheetSN == labSheetSN && m.DeleteFlag != "*")
                .Select(m => new PatientOrderDetailInfo
                {
                    OrderContent = m.OrderContent,
                    PatientOrderDetailID = m.PatientOrderDetailID
                }).FirstOrDefaultAsync();

        }
    }
}
