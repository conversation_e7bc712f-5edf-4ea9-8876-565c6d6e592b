﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class MobileVersionRepository : IMobileVersionRepository
    {
        private readonly MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public MobileVersionRepository(
              MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            ,GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        /// <summary>
        /// 获取最后一个版本内容
        /// </summary>
        /// <param name="clientType"></param>
        /// <param name="system"></param>
        /// <returns></returns>
        public async Task<MobileVersionInfo> GetLastVersionAsync(string clientType, string system)
        {
            var datas = await this.GetAllAsync<MobileVersionInfo>();
            return datas.Where(m => m.ClientType.Trim() == clientType && m.System.Trim() == system)
           .OrderByDescending(m => m.VersionCode).FirstOrDefault();
        }
        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<MobileVersionInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            return await _medicalDbContext.MobileVersionInfos.Where(m => m.HospitalID == hospitalID.ToString()&& m.DeleteFlag != "*").ToListAsync();
        }
        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.MobileVersion.GetKey(_sessionCommonServer);
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }
    }
}
