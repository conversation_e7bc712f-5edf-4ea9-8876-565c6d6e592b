﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class NursingManagementFileRepository : INursingManagementFileRepository
    {
        private MedicalDbContext _dbContext = null;

        public NursingManagementFileRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }
        //查询出年度计划上传文件中所有数据
        public async Task<List<NursingManagementFileinfo>> GetFileAll()
        {
            return await _dbContext.NursingManagementFileinfos.Where(t => t.DeleteFlag != "*").ToListAsync();
        }
        //根据年度计划ID获取上传文件数据
        public async Task<List<NursingManagementFileinfo>> GetFileBySourceID(string sourceID)
        {
            return await _dbContext.NursingManagementFileinfos.Where(m => m.DeleteFlag != "*" && m.SourceID == sourceID).ToListAsync();
        }
        //跟据主键ID获取上传文件数据
        public async Task<NursingManagementFileinfo> GetFileByID(int ID)
        {
            var List = await _dbContext.NursingManagementFileinfos.Where(m => m.DeleteFlag != "*" && m.NursingManagementFileID == ID).ToListAsync();
            if (List.Count > 0)
            {
                return List[0];
            }
            else
            {
                return null;
            }

        }
        //根据文件ID获取上传文件数据
        public async Task<List<NursingManagementFileinfo>> GetFileByFileID(string fileID)
        {
            return await _dbContext.NursingManagementFileinfos.Where(m => m.DeleteFlag != "*" && m.FileID == fileID).ToListAsync();
        }
    }
}
