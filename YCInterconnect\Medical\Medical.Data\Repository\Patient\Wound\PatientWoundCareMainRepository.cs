﻿/*
 * 2022-03-31 2529 获取维护数据时增加带入护理记录标记 -杨欣欣
 * */
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.Data;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientWoundCareMainRepository : IPatientWoundCareMainRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientWoundCareMainRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 根据伤口记录ID和伤口评估次数获取伤口评估主表
        /// </summary>
        /// <param name="woundID"></param>
        /// <param name="num"></param>
        /// <returns></returns>
        public async Task<PatientWoundCareMainInfo> GetWoundCareAsync(string woundID, int num)
        {
            return await _medicalDbContext.PatientWoundCareMainInfos.Where(t => t.PatientWoundRecordID == woundID && t.NumberOfAssessment == num && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据伤口记录ID获取伤口评估列表
        /// </summary>
        /// <param name="woundID"></param>
        /// <returns></returns>
        public async Task<List<PatientWoundCareMainInfo>> GetWoundCareAsync(string woundID)
        {
            return await _medicalDbContext.PatientWoundCareMainInfos.Where(t => t.PatientWoundRecordID == woundID && t.DeleteFlag != "*").OrderBy(t => t.NumberOfAssessment).ToListAsync();
        }
        public async Task<List<PaitentWoundCareView>> GetWoundCareViewAsync(string woundID)
        {
            var data = await _medicalDbContext.PatientWoundCareMainInfos
                .Join(_medicalDbContext.PatientWoundRecordInfos, m => m.PatientWoundRecordID, n => n.PatientWoundRecordID,
                (m, n) => new { m, n }).Where(t => t.m.PatientWoundRecordID == woundID && t.m.DeleteFlag != "*" && t.n.DeleteFlag != "*")
                .Select(t => new PaitentWoundCareView
                {
                    PatientWoundRecordID = t.m.PatientWoundRecordID,
                    PatientWoundCareMainID = t.m.PatientWoundCareMainID,
                    PatientScheduleMainID = t.m.PatientScheduleMainID,
                    InpatientID = t.m.InpatientID,
                    NursingLevel = t.m.NursingLevel,
                    RecordsCode = t.m.RecordsCode,
                    DepartmentListID = t.m.DepartmentListID,
                    StationID = t.m.StationID,
                    NumberOfAssessment = t.m.NumberOfAssessment,
                    AssessDate = t.m.AssessDate,
                    AssessTime = t.m.AssessTime,
                    PressureInjuryNo = t.n.PressureInjuryNo,
                    PressureInjuryArea = t.n.PressureInjuryArea,
                    BodyShowName = t.n.BodyShowName,
                    PressureSoreStage = t.m.PressureSoreStage,
                    PressureSoreArea = t.m.PressureSoreArea,
                    SeepageVolume = t.m.SeepageVolume,
                    OrganizationType = t.m.OrganizationType,
                    PUSH = t.m.PUSH,
                    Outcome = t.m.Outcome,
                    CareIntervention = t.m.CareIntervention,
                    WoundAssess = t.m.WoundAssess,
                    WoundTreatment = t.m.WoundTreatment,
                    UserID = t.m.AddEmployeeID,
                    BringToShift = t.m.BringToShift,
                    InformPhysician = t.m.InformPhysician,
                    BringToNursingRecord = t.m.BringToNursingRecord,
                    Grading = t.m.Grading,
                    DiaperDermatitisRisk = t.m.DiaperDermatitisRisk
                })
                .OrderBy(t => t.AssessDate).ThenBy(t => t.AssessTime).ThenBy(t => t.NumberOfAssessment).ToListAsync();
            return data;
        }
        /// <summary>
        ///  获取按时间排序的最后一次评估主记录
        /// </summary>
        /// <param name="woundID"></param>
        /// <returns></returns>
        public async Task<PatientWoundCareMainInfo> GetLastByTimeAsync(string woundID)
        {
            var list = await _medicalDbContext.PatientWoundCareMainInfos.Where(t => t.PatientWoundRecordID == woundID && t.DeleteFlag != "*")
                        .OrderByDescending(t => t.AssessDate).ThenByDescending(t => t.AssessTime).ThenByDescending(t => t.NumberOfAssessment).ToListAsync();
            if (list != null && list.Count > 0)
            {
                return list[0];
            }
            return null;
        }
        /// <summary>
        ///  获取按次数排序的最后一次评估主记录
        /// </summary>
        /// <param name="woundID"></param>
        /// <returns></returns>
        public async Task<PatientWoundCareMainInfo> GetLastByNumAsync(string woundID)
        {
            var list = await _medicalDbContext.PatientWoundCareMainInfos.Where(t => t.PatientWoundRecordID == woundID && t.DeleteFlag != "*").OrderByDescending(t => t.NumberOfAssessment).ToListAsync();
            if (list != null && list.Count > 0)
            {
                return list[0];
            }
            return null;
        }
        /// <summary>
        /// 根据伤口评估主表ID获取数据
        /// </summary>
        /// <param name="woundID"></param>
        /// <param name="num"></param>
        /// <returns></returns>
        public async Task<PatientWoundCareMainInfo> GetWoundCareByIDAsync(string woundCareMainID)
        {
            return await _medicalDbContext.PatientWoundCareMainInfos.Where(t => t.PatientWoundCareMainID == woundCareMainID && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        ///  获取措施码对应的所有维护记录
        /// </summary>
        /// <param name="scheduleID"></param>
        /// <returns></returns>
        public async Task<List<PatientWoundCareMainInfo>> GetWoundCareByScheduleID(string scheduleID)
        {
            return await _medicalDbContext.PatientWoundCareMainInfos.Where(t => t.PatientScheduleMainID == scheduleID && t.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据RecordsCode获取伤口评估数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="recordsCode"></param>
        /// <returns></returns>
        public async Task<PatientWoundCareMainInfo> GetCareByRecordsCode(string recordID, string recordsCode)
        {
            var list = await _medicalDbContext.PatientWoundCareMainInfos.Where(t => t.PatientWoundRecordID == recordID && t.RecordsCode == recordsCode && t.DeleteFlag != "*").ToListAsync();
            if (list != null && list.Count > 0)
            {
                return list[0];
            }
            return null;
        }

        public async Task<List<PatientWoundCareMainInfo>> GetByHandoverID(string handoverID)
        {
            var list = await _medicalDbContext.PatientWoundCareMainInfos.Where(t => t.HandoverID == handoverID).ToListAsync();

            return list;
        }

        public async Task<List<PatientWoundCareMainInfo>> GetByAssessMainID(string assessMainID)
        {
            var list = await _medicalDbContext.PatientWoundCareMainInfos.Where(m => m.PatientAssessMainID == assessMainID
                              && m.DeleteFlag != "*").ToListAsync();
            return list;
        }

        public async Task<List<PatientWoundCareMainInfo>> GetNoAssessMainIDData(string inpatientID)
        {
            var list = await _medicalDbContext.PatientWoundCareMainInfos.Where(m => m.InpatientID == inpatientID
                            && (m.PatientAssessMainID == null || m.PatientAssessMainID == "") && m.DeleteFlag != "*").ToListAsync();
            return list;
        }

        public async Task<List<PatientWoundCareMainInfo>> GetByRecordCodeIDDataAsync(string recordID)
        {
            return await _medicalDbContext.PatientWoundCareMainInfos.Where(m => m.PatientWoundRecordID == recordID
                              && m.DeleteFlag != "*").ToListAsync();
        }
        public async Task<List<SchedulePerformDetail>> GetDayPerformDetail(string inpatientID, int stationID, DateTime shiftDate)
        {
            var query = await _medicalDbContext.PatientWoundCareMainInfos.Where(
                               m => m.InpatientID == inpatientID
                                 && m.StationID == stationID
                                 && m.ShiftDate == shiftDate
                                 && m.DeleteFlag != "*")
                                .Join(_medicalDbContext.PatientWoundCareDetailInfos.Where(m => m.DeleteFlag != "*")
                                , m => m.PatientWoundCareMainID
                                , n => n.PatientWoundCareMainID
                                , (m, n) => new SchedulePerformDetail
                                {
                                    PerformDate = m.AssessDate,
                                    PerformTime = m.AssessTime,
                                    AssessListID = n.AssessListID,
                                    ScheduleData = n.AssessValue,
                                    InterventionDetailID = n.AssessListID
                                }).ToListAsync();

            return query;
        }
        /// <summary>
        /// 根据伤口主记录获取伤口维护明细
        /// </summary>
        /// <param name="woundRecordIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientAssessDetailView>> GetWoundCareDetailByRecordIDs(List<string> woundRecordIDs)
        {
            var query = from main in _medicalDbContext.PatientWoundCareMainInfos.AsNoTracking().Where(m => woundRecordIDs.Contains(m.PatientWoundRecordID) && m.DeleteFlag != "*")
                        join detail in _medicalDbContext.PatientWoundCareDetailInfos.AsNoTracking().Where(m => m.DeleteFlag != "*")
                        on main.PatientWoundCareMainID equals detail.PatientWoundCareMainID
                        select new PatientAssessDetailView
                        {
                            AssessDate = main.AssessDate,
                            AssessTime = main.AssessTime,
                            AssessListID = detail.AssessListID,
                            AssessValue = detail.AssessValue,
                            AssessListGroupID = detail.AssessListGroupID,
                        };
            return await query.ToListAsync();
        }

        public async Task<List<PatientWoundCareMainInfo>> GetRecordsBySourceID(string sourceID, string sourceType)
        {
            var data = await _medicalDbContext.PatientWoundCareMainInfos.Where(m => m.SourceID == sourceID && m.DeleteFlag != "*").ToListAsync();
            if (!string.IsNullOrEmpty(sourceType))
            {
                data = data.Where(m => !string.IsNullOrEmpty(m.SourceType) && m.SourceType.Trim() == sourceType.Trim()).ToList();
            }
            return data;
        }
        /// <summary>
        /// 根据RecordsCode获取伤口评估CareMain中的部分字段内容
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="recordsCode"></param>
        /// <returns></returns>
        public async Task<PatientWoundCareMainInfo> GetPartCareMainByRecordsCode(string recordID, string recordsCode)
        {
            var list = await _medicalDbContext.PatientWoundCareMainInfos.Where(t =>
                t.PatientWoundRecordID == recordID && t.RecordsCode == recordsCode && t.DeleteFlag != "*")
                .Select(m => new PatientWoundCareMainInfo
                {
                    CareIntervention = m.CareIntervention,
                    PatientWoundCareMainID = m.PatientWoundCareMainID
                })
                .ToListAsync();
            if (list != null && list.Count > 0)
            {
                return list[0];
            }
            return null;
        }
        /// <summary>
        /// 获得压伤的护理结局
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="recordsCode"></param>
        /// <returns>string</returns>
        public async Task<string> GetOutComeByRecordsCode(string recordID, string recordsCode)
        {
            var list = await _medicalDbContext.PatientWoundCareMainInfos.Where(t =>
                t.PatientWoundRecordID == recordID && t.RecordsCode == recordsCode && t.DeleteFlag != "*").Select(m => m.Outcome).ToListAsync();
            if (list != null && list.Count > 0)
            {
                return list[0];
            }
            return null;
        }

        public async Task<List<PatientWoundNameView>> GetWoundName(List<string> careMainIDs)
        {
            var data = await (from m in _medicalDbContext.PatientWoundCareMainInfos.Where(m => careMainIDs.Contains(m.PatientWoundCareMainID) && m.DeleteFlag != "*")
                              join n in _medicalDbContext.PatientWoundRecordInfos.Where(m => m.DeleteFlag != "*") on m.PatientWoundRecordID equals n.PatientWoundRecordID
                              select new PatientWoundNameView
                              {
                                  PatientWoundCareMainID = m.PatientWoundCareMainID,
                                  PatientWoundRecordID = m.PatientWoundRecordID,
                                  BodyPartName = n.BodyShowName,
                                  WoundKindCode = n.WoundKind,
                                  WoundCode = n.WoundCode
                              }).ToListAsync();
            return data;
        }

        /// <summary>
        /// 获取交班时伤口内容
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <returns></returns>
        public async Task<List<WoundHandoverView>> GetPatientWoundHandoverView(string inpatientID)
        {
            var datas = await (from m in _medicalDbContext.PatientWoundCareMainInfos.Where(m => m.DeleteFlag != "*" && m.InpatientID == inpatientID)
                               join n in _medicalDbContext.PatientWoundRecordInfos.Where(m => m.DeleteFlag != "*" && m.InpatientID == inpatientID)
                               on m.PatientWoundRecordID equals n.PatientWoundRecordID
                               select new WoundHandoverView
                               {
                                   PatientWoundRecordID = n.PatientWoundRecordID,
                                   RecordsCode = m.RecordsCode,
                                   AssessDate = m.AssessDate,
                                   AssessTime = m.AssessTime,
                                   WoundKind = n.WoundKind,
                                   PatientWoundCareMainID = m.PatientWoundCareMainID,
                                   StationID = m.StationID,
                                   BodyShowName = n.BodyShowName,
                                   WoundCode = n.WoundCode,
                                   BodyPartID = n.BodyPartID,
                                   BringToShift = m.BringToShift,
                                   Shift = m.Shift,
                                   ShiftDate = m.ShiftDate ?? DateTime.MinValue,
                                   PressureSoreStage = m.PressureSoreStage,
                                   PressureInjuryNo = n.PressureInjuryNo,
                                   EndDate = n.EndDate,
                                   EndTime = n.EndTime
                               }).OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime).ToListAsync();
            return datas.GroupBy(m => m.PatientWoundRecordID).Select(m => m.First()).ToList();
        }

        public async Task<List<WoundHandoverView>> GetWoundHandoverView(string inpatientID, DateTime startDateTime, DateTime endDateTime)
        {
            return await (from m in _medicalDbContext.PatientWoundCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*")
                          join n in _medicalDbContext.PatientWoundRecordInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*"
                          && (m.StartDate < endDateTime.Date || (m.StartDate == endDateTime.Date && m.StartTime <= endDateTime.TimeOfDay))
                          && (!m.EndDate.HasValue || (m.EndDate.HasValue && m.EndDate.Value > startDateTime.Date || (m.EndDate.Value == startDateTime.Date && m.EndTime >= startDateTime.TimeOfDay))))
                          on m.PatientWoundRecordID equals n.PatientWoundRecordID
                          select new WoundHandoverView
                          {
                              PatientWoundRecordID = n.PatientWoundRecordID,
                              RecordsCode = m.RecordsCode,
                              AssessDate = m.AssessDate,
                              AssessTime = m.AssessTime,
                              WoundKind = n.WoundKind,
                              PatientWoundCareMainID = m.PatientWoundCareMainID,
                              StationID = m.StationID,
                              BodyShowName = n.BodyShowName,
                              WoundCode = n.WoundCode,
                              BodyPartID = n.BodyPartID,
                              BringToShift = m.BringToShift,
                              PressureSoreStage = m.PressureSoreStage,
                              PressureInjuryNo = n.PressureInjuryNo,
                              EndDate = n.EndDate,
                              EndTime = n.EndTime
                          }).OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).ToListAsync();
        }
    }
}