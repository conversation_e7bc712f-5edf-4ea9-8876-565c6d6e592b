﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Interconnect.Models;

namespace Interconnect.Data.Interface
{
   public interface  IStationRepository
    {
        /// <summary>
        /// 获取护士站（病区）信息
        /// </summary>
        /// <param name="tongbuCount"></param>
        /// <param name="takeRows"></param>
        /// <returns></returns>
        List<StationInfo> GetAsync(int tongbuCount, int takeRows);

        /// <summary>
        /// 获取所有病区信息
        /// </summary>
        /// <returns></returns>
        List<StationInfo> GetAllAsync();

    }
}
