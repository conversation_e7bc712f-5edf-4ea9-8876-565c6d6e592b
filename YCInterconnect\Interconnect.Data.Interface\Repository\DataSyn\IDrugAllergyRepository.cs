﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Interconnect.Models;

namespace Interconnect.Data.Interface
{
    public interface IDrugAllergyRepository
    {
        /// <summary>
        /// 获取全部未同步药品过敏信息
        /// </summary>
        /// <param name="tongbuCount"></param>
        /// <returns></returns>
        List<DrugAllergyInfo> GetAsync(int tongbuCount);

    }
}