﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Interconnect.Services.Interface;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using MedicalExternalCommon.Service;
using NLog;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Interconnect.Services
{
    public class AsyncService : IAsyncService
    {
        //Mdeical
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IPatientScheduleMainRepository _patientScheduleMainRepository;
        private readonly IPatientScoreMainRepository _patientScoreMainRepository;
        private readonly INursingInterventionMainRepository _nursingInterventionMainRepository;
        private readonly IProblemToInterventionRepository _problemToInterventionRepository;
        private readonly IBodyPartListRepository _bodyPartListRepository;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly StationaShiftCommonService _stationaShiftCommonService;
        public AsyncService(
              IUnitOfWork<MedicalDbContext> UnitOfWork
            , IPatientScheduleMainRepository patientScheduleMainRepository
            , IPatientScoreMainRepository patientScoreMainRepository
             , INursingInterventionMainRepository nursingInterventionMainRepository
            , IProblemToInterventionRepository problemToInterventionRepository
            , IBodyPartListRepository bodyPartListRepository
            , StationaShiftCommonService stationaShiftCommonService
            )
        {
            _unitOfWork = UnitOfWork;
            _patientScheduleMainRepository = patientScheduleMainRepository;
            _patientScoreMainRepository = patientScoreMainRepository;
            _nursingInterventionMainRepository = nursingInterventionMainRepository;
            _problemToInterventionRepository = problemToInterventionRepository;
            _bodyPartListRepository = bodyPartListRepository;
            _stationaShiftCommonService = stationaShiftCommonService;
        }

        /// <summary>
        /// 获得护理措施
        /// </summary>
        /// <returns></returns>
        public async Task<string> GetNurseStepAsync(string scoreMainID, int problemID, int Language)
        {
            string getvalue = "";
            var nursingInterventionMain = await _nursingInterventionMainRepository.GetAllAsync<NursingInterventionMainInfo>();
            nursingInterventionMain = nursingInterventionMain.Where(t => t.Language == Language).ToList();


            //从缓存里取相应的InterventionID数据
            var ProblemtoIntervention = await _problemToInterventionRepository.GetAllAsync<ProblemToInterventionInfo>();
            var InterventionList = ProblemtoIntervention.Where(t => t.ProblemID == problemID).ToList();

            var ResultList = await _patientScoreMainRepository.GetPatientScoreMainByID(scoreMainID);
            if (ResultList != null)
            {
                //获得班别
                var stationshift = await _stationaShiftCommonService.GetShiftAsync(ResultList.StationID, ResultList.AssessTime);
                //获得当天及以后的数据
                var maininfo = await _patientScheduleMainRepository.GetPatientSchedule(
                    ResultList.StationID, ResultList.AssessDate, stationshift.Shift, ResultList.InpatientID);

                //存在跨天数据，进行处理,分两部分
                if (stationshift.CrossDayFlag == "*")
                {
                    var endDate = ResultList.AssessDate.AddDays(1);
                    //第二天时间点内的数据归到当天
                    var PatientscheduleListTomorrow = maininfo.Where(m => m.ScheduleDate == ResultList.AssessDate && m.ScheduleTime >= stationshift.ShiftStartTime).OrderBy(m => m.ScheduleTime).ToList();
                    var PatientscheduleListToday = maininfo.Where(m => m.ScheduleDate == endDate && m.ScheduleTime <= stationshift.ShiftEndTime).OrderBy(m => m.ScheduleTime).ToList();
                    foreach (var item in PatientscheduleListToday)
                    {
                        PatientscheduleListTomorrow.Add(item);
                    }
                    maininfo = PatientscheduleListTomorrow;
                }
                else
                {
                    maininfo = maininfo.Where(m => m.ScheduleDate == ResultList.AssessDate).OrderBy(m => m.ScheduleTime).ToList();
                }

                maininfo = maininfo.Where(t => InterventionList.Any(g => g.InterventionID == t.InterventionID)).ToList();
                //取出对应InterventionID
                var mainlist = maininfo.Select(t => new
                {
                    t.InterventionID
                });
                //去重
                mainlist = mainlist.Distinct().ToList();
                foreach (var item in mainlist)
                {
                    var InterventionTemp = nursingInterventionMain.Where(m => m.ID == item.InterventionID).ToList();
                    if (InterventionTemp.Count >= 0)
                    {
                        if (getvalue == "")
                        {
                            getvalue = InterventionTemp[0].Intervention;
                        }
                        getvalue = getvalue + InterventionTemp[0].Intervention + ",";
                    }
                }
            }
            if (getvalue.Length > 0)
            {
                getvalue = getvalue.Remove(getvalue.Length - 1);
            }
            return getvalue;
        }

        Task<string> IAsyncService.GetPatientPressureSoresMainAsync(string PressureSoresMainID, int Language)
        {
            throw new NotImplementedException();
        }
    }
}

