﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("Orders")]
    public class OrderInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///医嘱类别
        ///</summary>
        public string OrderType { get; set; }
        /// <summary>
        ///类别名称
        ///</summary>
        public string TypeName { get; set; }
        /// <summary>
        ///医嘱代码
        ///</summary>
        public string OrderCode { get; set; }
        /// <summary>
        ///医嘱名称
        ///</summary>
        public string OrderName { get; set; }
    }
}