﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("ExamineSchedule")]
    public class ExamineScheduleInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///住院号
        ///</summary>
        public string CaseNumber { get; set; }
        /// <summary>
        ///检查日期
        ///</summary>
        public DateTime ExamineDate { get; set; }
        /// <summary>
        ///检查时间
        ///</summary>
        public TimeSpan? ExamineTime { get; set; }
        /// <summary>
        ///
        ///</summary>
        public string ExamineItem { get; set; }
    }
}