﻿using Medical.Models;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public partial class MedicalDbContext
    {
        /// <summary>
        /// 常见问题和答案记录表
        /// </summary>
        public DbSet<QuestionAndAnaswerInfo> QuestionAndAnaswers { get; set; }

        /// <summary>
        /// 导管对身体部位
        /// </summary>
        public DbSet<TubeToBodyPartInfo> TubeToBodyPartInfos { get; set; }

        /// <summary>
        /// 查询所有版本更新信息
        /// </summary>
        public DbSet<SystemVersionRecordInfo> SystemVersionRecordInfos { get; set; }
    }
}
