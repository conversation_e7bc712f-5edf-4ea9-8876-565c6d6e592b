﻿using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;

namespace Medical.Data.Context
{
    public partial class MedicalDbContext
    {
        public DbSet<PatientScoreMainInfo> PatientScoreMainInfos { get; set; }

        public DbSet<PatientScoreDetailInfo> PatientScoreDetailInfos { get; set; }

        public DbSet<RecordsFormatInfo> RecordsFormat { get; set; }

        public DbSet<ReportToInterventionDetail> ReportToInterventionDetails { get; set; }

        public DbSet<ReportToInterventionMain> ReportToInterventionMains { get; set; }

        public DbSet<AssessToRecordFormatInfo> AssessToRecordFormats { get; set; }

        public DbSet<HandOverSignInfo> HandoverSignInfos { get; set; }

        public DbSet<PatientOrdersMergeInfo> PatientOrdersMergeInfos { get; set; }

        public DbSet<InterventionBillingInfo> InterventionBillingInfos { get; set; }

        public Task<PatientScheduleJsonInfo> Where { get; internal set; }
        /// <summary>
        /// 评估序号对应护理措施明细配置
        /// </summary>
        public DbSet<AssessToNursingInterventionDetailInfo> AssessToNursingInterventionDetailInfos { get; set; }
    }
}
