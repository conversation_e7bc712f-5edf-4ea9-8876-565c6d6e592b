﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientRescueRecordRepository : IPatientRescueRecordRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientRescueRecordRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<PatientRescueRecordInfo> GetByID(string id)
        {
            return await _medicalDbContext.PatientRescueRecordInfos.Where(
                t => t.PatientRescueRecordID == id && t.DeleteFlag != "*").SingleOrDefaultAsync();
        }

        public async Task<List<PatientRescueRecordInfo>> GetListByInpatientIDAsync(string inPatientID)
        {
            var query = _medicalDbContext.PatientRescueRecordInfos.Where(t => t.InpatientID == inPatientID && t.DeleteFlag != "*");
            query = query.OrderByDescending(t => t.CPRTime);
            return await query.ToListAsync();
        }

        //抢救带交班
        public async Task<List<HandoverRescueCareIntervention>> GetPatientRescueCareIntervention(string inpatientID, DateTime startDate, TimeSpan startTime, DateTime endDate, TimeSpan endTime)
        {
            var datas = await (from a in _medicalDbContext.PatientRescueCareMainInfos
                               join b in _medicalDbContext.PatientRescueRecordInfos on a.PatientRescueRecordID equals b.PatientRescueRecordID
                               where a.InpatientID == inpatientID && a.AssessDate >= startDate && a.AssessDate <= endDate && a.BringToShift == true && a.BringToShift == true && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                               select new HandoverRescueCareIntervention
                               {
                                   InpatientID = a.InpatientID,
                                   AssessDate = a.AssessDate,
                                   AssessTime = a.AssessTime,
                                   StopReason = b.StopReason,
                                   Result = b.Result,
                                   CareIntervention = a.CareIntervention,
                                   Sort = 0,
                                   BodyPartName = "",
                                   CPRReason = b.CPRReason,
                                   PatientRescueRecordID = a.PatientRescueRecordID,
                                   HeartRate = a.HeartRate,
                                   Intervention = a.Intervention,
                                   RecordsCode = a.RecordsCode
                               }).ToListAsync();

            if (datas.Count == 0)
            {
                return datas;
            }

            datas = datas.Where(m => m.AssessDate.Value.Add(m.AssessTime.Value) >= startDate.Date.Add(startTime) && m.AssessDate.Value.Add(m.AssessTime.Value) <= endDate.Date.Add(endTime))
                .GroupBy(m => m.PatientRescueRecordID)
                .Select(m => m.OrderByDescending(n => n.AssessDate.Value.Date.Add(n.AssessTime.Value)).FirstOrDefault()).ToList();

            return datas;
        }
    }
}