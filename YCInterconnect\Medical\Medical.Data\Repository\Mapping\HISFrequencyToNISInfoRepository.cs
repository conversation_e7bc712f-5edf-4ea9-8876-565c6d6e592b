﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class HISFrequencyToNISInfoRepository : IHISFrequencyToNISInfoRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        public HISFrequencyToNISInfoRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 获取医嘱频次对照内容
        /// </summary>
        /// <returns></returns>
        public async Task<List<HISFrequencyToNISInfo>> GetAsync()
        {
            return await _medicalDbContext.HISFrequencyToNISInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
        }
    }
}
