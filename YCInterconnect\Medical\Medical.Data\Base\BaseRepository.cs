﻿// <copyright file="QueryRepository.cs" company="TanvirArjel">
// Copyright (c) TanvirArjel. All rights reserved.
// </copyright>

using MathNet.Numerics.Statistics.Mcmc;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.BaseRepository;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Query;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;

namespace Medical.Data.Base
{
    public class BaseRepository<T, TDbContext>(TDbContext dbContext) : IBaseRepository<T>, IBaseRepository<T, TDbContext> where T : ModifyInfo
        where TDbContext : DbContext
    {
        private readonly DbSet<T> _dbSet = dbContext.Set<T>();

        public Task<List<T>> GetListAsync(CancellationToken cancellationToken = default)
        {
            return GetListAsync(false, cancellationToken);
        }

        public Task<List<T>> GetListAsync(bool asNoTracking, CancellationToken cancellationToken = default)
        {
            Func<IQueryable<T>, IIncludableQueryable<T, object>> nullValue = null;
            return GetListAsync(nullValue, asNoTracking, cancellationToken);
        }

        public Task<List<T>> GetListAsync(
            Func<IQueryable<T>, IIncludableQueryable<T, object>> includes,
            CancellationToken cancellationToken = default)
        {
            return GetListAsync(includes, false, cancellationToken);
        }

        public async Task<List<T>> GetListAsync(
            Func<IQueryable<T>, IIncludableQueryable<T, object>> includes,
            bool asNoTracking,
            CancellationToken cancellationToken = default)
        {
            IQueryable<T> query = _dbSet;

            if (includes != null)
            {
                query = includes(query);
            }

            if (asNoTracking)
            {
                query = query.AsNoTracking();
            }

            List<T> items = await query.Where(m => m.DeleteFlag != "*").ToListAsync(cancellationToken).ConfigureAwait(false);

            return items;
        }

        public Task<List<T>> GetListAsync(Expression<Func<T, bool>> condition, CancellationToken cancellationToken = default)
        {
            return GetListAsync(condition, false, cancellationToken);
        }

        public Task<List<T>> GetListAsync(
            Expression<Func<T, bool>> condition,
            bool asNoTracking,
            CancellationToken cancellationToken = default)
        {
            return GetListAsync(condition, null, asNoTracking, cancellationToken);
        }

        public async Task<List<T>> GetListAsync(
            Expression<Func<T, bool>> condition,
            Func<IQueryable<T>, IIncludableQueryable<T, object>> includes,
            bool asNoTracking,
            CancellationToken cancellationToken = default)
        {
            IQueryable<T> query = _dbSet;

            if (condition != null)
            {
                query = query.Where(condition);
            }

            if (includes != null)
            {
                query = includes(query);
            }

            if (asNoTracking)
            {
                query = query.AsNoTracking();
            }

            List<T> items = await query.Where(m => m.DeleteFlag != "*").ToListAsync(cancellationToken).ConfigureAwait(false);

            return items;
        }

        public Task<List<T>> GetListAsync(Specification<T> specification, CancellationToken cancellationToken = default)
        {
            return GetListAsync(specification, false, cancellationToken);
        }

        public async Task<List<T>> GetListAsync(
            Specification<T> specification,
            bool asNoTracking,
            CancellationToken cancellationToken = default)
        {
            IQueryable<T> query = _dbSet;

            if (specification != null)
            {
                query = query.GetSpecifiedQuery(specification);
            }

            if (asNoTracking)
            {
                query = query.AsNoTracking();
            }

            return await query.Where(m => m.DeleteFlag != "*").ToListAsync(cancellationToken).ConfigureAwait(false);
        }

        public async Task<List<TProjectedType>> GetListAsync<TProjectedType>(
            Expression<Func<T, TProjectedType>> selectExpression,
            CancellationToken cancellationToken = default)
        {
            if (selectExpression == null)
            {
                throw new ArgumentNullException(nameof(selectExpression));
            }

            List<TProjectedType> entities = await _dbSet
                .Where(m => m.DeleteFlag != "*")
                .Select(selectExpression).ToListAsync(cancellationToken).ConfigureAwait(false);

            return entities;
        }

        public async Task<List<TProjectedType>> GetListAsync<TProjectedType>(
            Expression<Func<T, bool>> condition,
            Expression<Func<T, TProjectedType>> selectExpression,
            CancellationToken cancellationToken = default)
        {
            if (selectExpression == null)
            {
                throw new ArgumentNullException(nameof(selectExpression));
            }

            IQueryable<T> query = _dbSet;

            if (condition != null)
            {
                query = query.Where(condition);
            }

            List<TProjectedType> projectedEntites = await query
                .Where(m => m.DeleteFlag != "*")
                .Select(selectExpression)
                .ToListAsync(cancellationToken).ConfigureAwait(false);

            return projectedEntites;
        }

        public async Task<List<TProjectedType>> GetListAsync<TProjectedType>(
            Specification<T> specification,
            Expression<Func<T, TProjectedType>> selectExpression,
            CancellationToken cancellationToken = default)
        {
            if (selectExpression == null)
            {
                throw new ArgumentNullException(nameof(selectExpression));
            }

            IQueryable<T> query = _dbSet;

            if (specification != null)
            {
                query = query.GetSpecifiedQuery(specification);
            }

            return await query
                .Where(m => m.DeleteFlag != "*")
                .Select(selectExpression)
                .ToListAsync(cancellationToken).ConfigureAwait(false);
        }

        public Task<T> GetByIdAsync(object id, CancellationToken cancellationToken = default)
        {
            if (id == null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            return GetByIdAsync(id, false, cancellationToken);
        }

        public Task<T> GetByIdAsync(object id, bool asNoTracking, CancellationToken cancellationToken = default)
        {
            if (id == null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            return GetByIdAsync(id, null, asNoTracking, cancellationToken);
        }

        public Task<T> GetByIdAsync(
            object id,
            Func<IQueryable<T>, IIncludableQueryable<T, object>> includes,
            CancellationToken cancellationToken = default)
        {
            if (id == null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            return GetByIdAsync(id, includes, false, cancellationToken);
        }

        public async Task<T> GetByIdAsync(
            object id,
            Func<IQueryable<T>, IIncludableQueryable<T, object>> includes,
            bool asNoTracking = false,
            CancellationToken cancellationToken = default)
        {
            if (id == null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            IEntityType entityType = dbContext.Model.FindEntityType(typeof(T));

            string primaryKeyName = entityType.FindPrimaryKey().Properties.Select(p => p.Name).FirstOrDefault();
            Type primaryKeyType = entityType.FindPrimaryKey().Properties.Select(p => p.ClrType).FirstOrDefault();

            if (primaryKeyName == null || primaryKeyType == null)
            {
                throw new ArgumentException("Entity does not have any primary key defined", nameof(id));
            }

            object primaryKeyValue = null;

            try
            {
                primaryKeyValue = Convert.ChangeType(id, primaryKeyType, CultureInfo.InvariantCulture);
            }
            catch (Exception)
            {
                throw new ArgumentException($"You can not assign a value of type {id.GetType()} to a property of type {primaryKeyType}");
            }

            ParameterExpression pe = Expression.Parameter(typeof(T), "entity");
            MemberExpression me = Expression.Property(pe, primaryKeyName);
            ConstantExpression constant = Expression.Constant(primaryKeyValue, primaryKeyType);
            BinaryExpression body = Expression.Equal(me, constant);
            Expression<Func<T, bool>> expressionTree = Expression.Lambda<Func<T, bool>>(body, new[] { pe });

            IQueryable<T> query = _dbSet;

            if (includes != null)
            {
                query = includes(query);
            }

            if (asNoTracking)
            {
                query = query.AsNoTracking();
            }

            T entity = await query
                .Where(m => m.DeleteFlag != "*")
                .FirstOrDefaultAsync(expressionTree, cancellationToken).ConfigureAwait(false);
            return entity;
        }

        public async Task<TProjectedType> GetByIdAsync<TProjectedType>(
            object id,
            Expression<Func<T, TProjectedType>> selectExpression,
            CancellationToken cancellationToken = default)
        {
            if (id == null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            if (selectExpression == null)
            {
                throw new ArgumentNullException(nameof(selectExpression));
            }

            IEntityType entityType = dbContext.Model.FindEntityType(typeof(T));

            string primaryKeyName = entityType.FindPrimaryKey().Properties.Select(p => p.Name).FirstOrDefault();
            Type primaryKeyType = entityType.FindPrimaryKey().Properties.Select(p => p.ClrType).FirstOrDefault();

            if (primaryKeyName == null || primaryKeyType == null)
            {
                throw new ArgumentException("Entity does not have any primary key defined", nameof(id));
            }

            object primaryKeyValue = null;

            try
            {
                primaryKeyValue = Convert.ChangeType(id, primaryKeyType, CultureInfo.InvariantCulture);
            }
            catch (Exception)
            {
                throw new ArgumentException($"You can not assign a value of type {id.GetType()} to a property of type {primaryKeyType}");
            }

            ParameterExpression pe = Expression.Parameter(typeof(T), "entity");
            MemberExpression me = Expression.Property(pe, primaryKeyName);
            ConstantExpression constant = Expression.Constant(primaryKeyValue, primaryKeyType);
            BinaryExpression body = Expression.Equal(me, constant);
            Expression<Func<T, bool>> expressionTree = Expression.Lambda<Func<T, bool>>(body, new[] { pe });

            IQueryable<T> query = _dbSet;

            return await query.Where(m => m.DeleteFlag != "*")
                              .Where(expressionTree)
                              .Select(selectExpression)
                              .FirstOrDefaultAsync(cancellationToken)
                              .ConfigureAwait(false);
        }

        public Task<T> GetAsync(
            Expression<Func<T, bool>> condition,
            CancellationToken cancellationToken = default)
        {
            return GetAsync(condition, null, false, cancellationToken);
        }

        public Task<T> GetAsync(
            Expression<Func<T, bool>> condition,
            bool asNoTracking,
            CancellationToken cancellationToken = default)
        {
            return GetAsync(condition, null, asNoTracking, cancellationToken);
        }

        public Task<T> GetAsync(
            Expression<Func<T, bool>> condition,
            Func<IQueryable<T>, IIncludableQueryable<T, object>> includes,
            CancellationToken cancellationToken = default)
        {
            return GetAsync(condition, includes, false, cancellationToken);
        }

        public async Task<T> GetAsync(
            Expression<Func<T, bool>> condition,
            Func<IQueryable<T>, IIncludableQueryable<T, object>> includes,
            bool asNoTracking,
            CancellationToken cancellationToken = default)
        {
            IQueryable<T> query = _dbSet;

            if (condition != null)
            {
                query = query.Where(condition);
            }

            if (includes != null)
            {
                query = includes(query);
            }

            if (asNoTracking)
            {
                query = query.AsNoTracking();
            }

            return await query.Where(m => m.DeleteFlag != "*").FirstOrDefaultAsync(cancellationToken).ConfigureAwait(false);
        }

        public Task<T> GetAsync(Specification<T> specification, CancellationToken cancellationToken = default)
        {
            return GetAsync(specification, false, cancellationToken);
        }

        public async Task<T> GetAsync(Specification<T> specification, bool asNoTracking, CancellationToken cancellationToken = default)
        {
            IQueryable<T> query = _dbSet;

            if (specification != null)
            {
                query = query.GetSpecifiedQuery(specification);
            }

            if (asNoTracking)
            {
                query = query.AsNoTracking();
            }

            return await query.Where(m => m.DeleteFlag != "*").FirstOrDefaultAsync(cancellationToken).ConfigureAwait(false);
        }

        public async Task<TProjectedType> GetAsync<TProjectedType>(
            Expression<Func<T, bool>> condition,
            Expression<Func<T, TProjectedType>> selectExpression,
            CancellationToken cancellationToken = default)
        {
            if (selectExpression == null)
            {
                throw new ArgumentNullException(nameof(selectExpression));
            }

            IQueryable<T> query = _dbSet;

            if (condition != null)
            {
                query = query.Where(condition);
            }

            return await query.Where(m => m.DeleteFlag != "*").Select(selectExpression).FirstOrDefaultAsync(cancellationToken).ConfigureAwait(false);
        }

        public async Task<TProjectedType> GetAsync<TProjectedType>(
            Specification<T> specification,
            Expression<Func<T, TProjectedType>> selectExpression,
            CancellationToken cancellationToken = default)
        {
            if (selectExpression == null)
            {
                throw new ArgumentNullException(nameof(selectExpression));
            }

            IQueryable<T> query = _dbSet;

            if (specification != null)
            {
                query = query.GetSpecifiedQuery(specification);
            }

            return await query.Where(m => m.DeleteFlag != "*").Select(selectExpression).FirstOrDefaultAsync(cancellationToken).ConfigureAwait(false);
        }

        public Task<bool> ExistsAsync(CancellationToken cancellationToken = default)
        {
            return ExistsAsync(null, cancellationToken);
        }

        public async Task<bool> ExistsAsync(Expression<Func<T, bool>> condition, CancellationToken cancellationToken = default)
        {
            IQueryable<T> query = _dbSet;

            if (condition == null)
            {
                return await query.Where(m => m.DeleteFlag != "*").Where(m => m.DeleteFlag != "*").AnyAsync(cancellationToken);
            }

            bool isExists = await query.Where(m => m.DeleteFlag != "*").Where(m => m.DeleteFlag != "*").AnyAsync(condition, cancellationToken).ConfigureAwait(false);
            return isExists;
        }

        public async Task<bool> ExistsByIdAsync(object id, CancellationToken cancellationToken = default)
        {
            if (id == null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            IEntityType entityType = dbContext.Model.FindEntityType(typeof(T));

            string primaryKeyName = entityType.FindPrimaryKey().Properties.Select(p => p.Name).FirstOrDefault();
            Type primaryKeyType = entityType.FindPrimaryKey().Properties.Select(p => p.ClrType).FirstOrDefault();

            if (primaryKeyName == null || primaryKeyType == null)
            {
                throw new ArgumentException("Entity does not have any primary key defined", nameof(id));
            }

            object primaryKeyValue = null;

            try
            {
                primaryKeyValue = Convert.ChangeType(id, primaryKeyType, CultureInfo.InvariantCulture);
            }
            catch (Exception)
            {
                throw new ArgumentException($"You can not assign a value of type {id.GetType()} to a property of type {primaryKeyType}");
            }

            ParameterExpression pe = Expression.Parameter(typeof(T), "entity");
            MemberExpression me = Expression.Property(pe, primaryKeyName);
            ConstantExpression constant = Expression.Constant(primaryKeyValue, primaryKeyType);
            BinaryExpression body = Expression.Equal(me, constant);
            Expression<Func<T, bool>> expressionTree = Expression.Lambda<Func<T, bool>>(body, new[] { pe });

            IQueryable<T> query = _dbSet;

            bool isExistent = await query.Where(m => m.DeleteFlag != "*").AnyAsync(expressionTree, cancellationToken).ConfigureAwait(false);
            return isExistent;
        }

        public async Task<int> GetCountAsync(CancellationToken cancellationToken = default)
        {
            int count = await _dbSet.Where(m => m.DeleteFlag != "*").CountAsync(cancellationToken).ConfigureAwait(false);
            return count;
        }

        public async Task<int> GetCountAsync(Expression<Func<T, bool>> condition, CancellationToken cancellationToken = default)
        {
            IQueryable<T> query = _dbSet;

            if (condition != null)
            {
                query = query.Where(condition);
            }

            return await query.Where(m => m.DeleteFlag != "*").CountAsync(cancellationToken).ConfigureAwait(false);
        }

        public async Task<int> GetCountAsync(IEnumerable<Expression<Func<T, bool>>> conditions, CancellationToken cancellationToken = default)
        {
            IQueryable<T> query = _dbSet;

            if (conditions != null)
            {
                foreach (Expression<Func<T, bool>> expression in conditions)
                {
                    query = query.Where(expression);
                }
            }

            return await query.Where(m => m.DeleteFlag != "*").CountAsync(cancellationToken).ConfigureAwait(false);
        }

        public async Task<long> GetLongCountAsync(CancellationToken cancellationToken = default)
        {
            long count = await _dbSet.Where(m => m.DeleteFlag != "*").LongCountAsync(cancellationToken).ConfigureAwait(false);
            return count;
        }

        public async Task<long> GetLongCountAsync(Expression<Func<T, bool>> condition, CancellationToken cancellationToken = default)
        {
            IQueryable<T> query = _dbSet;

            if (condition != null)
            {
                query = query.Where(condition);
            }

            return await query.Where(m => m.DeleteFlag != "*").LongCountAsync(cancellationToken).ConfigureAwait(false);
        }

        public async Task<long> GetLongCountAsync(IEnumerable<Expression<Func<T, bool>>> conditions, CancellationToken cancellationToken = default)
        {
            IQueryable<T> query = _dbSet;

            if (conditions != null)
            {
                foreach (Expression<Func<T, bool>> expression in conditions)
                {
                    query = query.Where(expression);
                }
            }

            return await query.Where(m => m.DeleteFlag != "*").LongCountAsync(cancellationToken).ConfigureAwait(false);
        }

        // DbContext level members
        public async Task<List<T1>> GetFromRawSqlAsync<T1>(string sql, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(sql))
            {
                throw new ArgumentNullException(nameof(sql));
            }

            IEnumerable<object> parameters = new List<object>();

            List<T1> items = await dbContext.GetFromQueryAsync<T1>(sql, parameters, cancellationToken);
            return items;
        }

        public async Task<List<T1>> GetFromRawSqlAsync<T1>(string sql, object parameter, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(sql))
            {
                throw new ArgumentNullException(nameof(sql));
            }

            List<object> parameters = [parameter];
            List<T1> items = await dbContext.GetFromQueryAsync<T1>(sql, parameters, cancellationToken);
            return items;
        }

        public async Task<List<T1>> GetFromRawSqlAsync<T1>(string sql, IEnumerable<DbParameter> parameters, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(sql))
            {
                throw new ArgumentNullException(nameof(sql));
            }

            List<T1> items = await dbContext.GetFromQueryAsync<T1>(sql, parameters, cancellationToken);
            return items;
        }

        public async Task<List<T1>> GetFromRawSqlAsync<T1>(string sql, IEnumerable<object> parameters, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(sql))
            {
                throw new ArgumentNullException(nameof(sql));
            }

            List<T1> items = await dbContext.GetFromQueryAsync<T1>(sql, parameters, cancellationToken);
            return items;
        }
    }
}
