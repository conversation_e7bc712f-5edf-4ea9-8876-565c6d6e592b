﻿using System.Collections.Generic;

namespace ViewModel
{
    /// <summary>
    /// 使用人员
    /// </summary>
    public class NewHISDiagnosis
    {
        /// <summary>
        /// 住院号														
        /// </summary>
        public int code { get; set; }
        /// <summary>
        /// 入院诊断
        /// </summary>
        public string msg { get; set; }
        /// <summary>
        /// 入院诊断明细
        /// </summary>
        public List<DiagnosisItem> data { get; set; }    
    }
    public class DiagnosisItem
    {
        /// <summary>
        /// 住院号
        /// </summary>
        public string PATIENT_NO { get; set; }
        /// <summary>
        /// 病案号
        /// </summary>
        public string PATI_ID { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string INPATIENT_NO { get; set; }
        /// <summary>
        /// 诊断类型 初步诊断 最后诊断 修改诊断
        /// </summary>
        public string DIAG_KIND_NAME { get; set; }
        /// <summary>
        /// 诊断码
        /// </summary>
        public string DIAG_CODE { get; set; }
        /// <summary>
        /// 诊断
        /// </summary>
        public string DIAG_NAME { get; set; }
        /// <summary>
        /// 诊断时间
        /// </summary>
        public string DIAG_DATE { get; set; }
        /// <summary>
        /// 下诊断人
        /// </summary>
        public string DIAG_DOCT_NAME { get; set; }
        /// <summary> 
        /// 诊断类型 西医诊断 中医诊断
        /// </summary>
        public string DIAG_TYPE { get; set; }
        /// <summary>
        /// 主诊断标记
        /// </summary>
        public string MAIN_FLAG { get; set; }
        /// <summary>
        /// 诊断序号
        /// </summary>
        public string DIAG_INDEX{ get; set; }

    }
}
