﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Interconnect.Models;

namespace Interconnect.Data.Interface
{
    public interface IInpatientRepository
    {
        /// <summary>
        /// 获取所有没有抽取的病区数据
        /// </summary>
        /// <param name="inPatientDay"></param>
        /// <param name="stations"></param>
        /// <returns></returns>
        List<InpatientInfo> GetAsync(int inPatientDay, string stations);

       
    }
}

