﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientRescueCareDetailRopository : IPatientRescueCareDetailRopository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientRescueCareDetailRopository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<List<PatientRescueCareDetailInfo>> GetByCareMainIDAsync(string patientRescueCareMainID)
        {
            return await _medicalDbContext.PatientRescueCareDetailInfos.Where(t => t.PatientRescueCareMainID == patientRescueCareMainID && t.DeleteFlag != "*").ToListAsync();
        }
    }
}