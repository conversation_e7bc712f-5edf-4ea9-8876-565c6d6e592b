﻿using System;
using System.Threading.Tasks;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Medical.Data.Interface;
using Medical.Models;
using NLog;
using System.Linq;
using Microsoft.Extensions.Options;
using Interconnect.Data.Context;
using Medical.Common;
using Arch.EntityFrameworkCore.UnitOfWork;
using Interconnect.ViewModels;

namespace Interconnect.Services
{
    public class SyncOrderService : ISyncOrderService
    {
        //Interconnect
        private readonly IUnitOfWork<DataOutConnection> _unitOfWorkOut;
        private readonly IOptions<SystemConfig> _config;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IJobLogService _jobLogService;
        private readonly IGetHisJsonService _getHisJsonService;
        private readonly IPhysicianToInterventionRepository _physicianToIntervention;
        private readonly IOrderToAssessListRepository _orderToAssessListRepository;
        private readonly IPhysicianOrderRepository _physicianOrderRepository;
        private readonly IClinicSettingRepository _clinicSettingRepository;
        private readonly IFrequencyRepository _frequencyRepository;
        private readonly IPatientOrderMainService _patientOrderMainService;

        public SyncOrderService(
            IOptions<SystemConfig> config
            , IJobLogService jobLogService
            , IGetHisJsonService getHisJsonService
            , IOrderToAssessListRepository orderToAssessListRepository
            , IPhysicianToInterventionRepository physicianToInterventionRepository
            , IPhysicianOrderRepository physicianOrderRepository
            , IFrequencyRepository frequencyRepository
            , IClinicSettingRepository clinicSettingRepository
            , IPatientOrderMainService patientOrderMainService

            )
        {
            _config = config;
            _jobLogService = jobLogService;
            _getHisJsonService = getHisJsonService;
            _orderToAssessListRepository = orderToAssessListRepository;
            _physicianToIntervention = physicianToInterventionRepository;
            _physicianOrderRepository = physicianOrderRepository;
            _frequencyRepository = frequencyRepository;
            _clinicSettingRepository = clinicSettingRepository;
            _patientOrderMainService = patientOrderMainService;
        }
        /// <summary>
        /// 根据住院号同步病人医嘱
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        public async Task<bool> SyncHisOrderByCaseNumber(string caseNumber)
        {
            if (string.IsNullOrEmpty(caseNumber))
            {
                _logger.Error("根据CaseNumber同步病人医嘱失败||CaseNumber为空");
                return false;
            }
            var infoName = "根据CaseNumber同步病人医嘱数据||CaseNumber:" + caseNumber;
            //生成一个随机的日志序号
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.PatientOrderJob).ToString();
            var jobName = infoName;
            var logMsg = "作业编号:" + guid + "启动同步任务:" + jobName;
            _logger.Info("启动作业" + logMsg);
            var subJobId = caseNumber;
            var jobStatus = false;
            try
            {
                jobStatus = _jobLogService.GetJobStatus(jobId, jobName, subJobId);
            }
            catch (Exception ex)
            {
                _logger.Error(jobName + "|| 判断作业状态失败||" + ex.ToString());
                return false;
            }
            if (!jobStatus)
            {
                _logger.Error(infoName + " ||同步失败||该作业正在执行");
                return false;
            }
            _logger.Info(infoName + "||开始同步");
            //获取平台医嘱Json数据
            var hisData = await _getHisJsonService.GetOrderHisJsonByCaseNumber(caseNumber);
            if (hisData == null)
            {
                _logger.Error(infoName + "||获取平台医嘱数据失败");
                return false;
            }
            if (hisData.Count==0)
            {
                _logger.Error(infoName + "||获取平台医嘱数据为空");
                return false;
            }
            //字典档获取
            var physicianToInterventionList = await _physicianToIntervention.GetAllData();
            var orderToAssessList = await _orderToAssessListRepository.GetAsync();
            var physicianOrder = await _physicianOrderRepository.GetAllAsync<PhysicianOrderInfo>();
            var frequencyList = await _frequencyRepository.GetAllAsync<FrequencyInfo>();
            var orderKeyWordList = await _clinicSettingRepository.GetClinicSetting("OrderKeyWord");
            var orderKeyNameList = orderKeyWordList.Select(m => m.Description).ToList();
            //获取符合同步的His医嘱数据
            var orderCodeList = physicianOrder.Select(m => m.OrderCode).Distinct().ToList();
            hisData = hisData.Where(m => orderCodeList.Contains(m.OrderCode) || m.OrderPattern == "901").ToList();
            //无符合医嘱数据直接返回
            if (hisData.Count == 0)
            {
                _logger.Error(infoName + "||无符合同步的医嘱数据");
                return true;
            }
            //医嘱数据开始同步
            _logger.Info(infoName + "||需要同步医嘱JSON数据||" + ListToJson.ToJson(hisData));
            await _patientOrderMainService.SyncHisData(hisData, orderToAssessList, physicianToInterventionList, frequencyList, physicianOrder, orderKeyNameList, _config.Value.HospitalID);
            try
            {
                _unitOfWorkOut.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error(infoName+ "||根据CaseNumber同步医嘱数据失败||" + ex.ToString());
                return false;
            }
            _logger.Info(infoName + "||根据CaseNumber同步医嘱数据成功");
            return true;
        }
        /// <summary>
        /// 根据病区码同步医嘱
        /// </summary>
        /// <param name="stationCode"></param>
        /// <returns></returns>
        public async Task<bool> SyncHisOrderByStationCode(string stationCode)
        {
            if (string.IsNullOrEmpty(stationCode))
            {
                _logger.Error("根据StationCode同步病人医嘱失败stationCode为空");
                return false;
            }
            var infoName = "根据StationCode同步病人医嘱数据||StationCode:" + stationCode;
            //生成一个随机的日志序号
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.PatientOrderJob).ToString();
            var jobName = infoName;
            var logMsg = "作业编号:" + guid + "启动同步任务:" + jobName;
            _logger.Info("启动作业" + logMsg);
            var subJobId = stationCode;
            var jobStatus = false;
            try
            {
                jobStatus = _jobLogService.GetJobStatus(jobId, jobName, subJobId);
            }
            catch (Exception ex)
            {
                _logger.Error(jobName + "|| 判断作业状态失败||" + ex.ToString());
                return false;
            }
            if (!jobStatus)
            {
                _logger.Warn(infoName + " ||同步失败||该作业正在执行");
                return false;
            }
            _logger.Info(infoName + "||开始同步");
            //获取平台医嘱Json数据
            var hisData = await _getHisJsonService.GetOrderHisJsonBystationCode(stationCode);
            if (hisData == null)
            {
                _logger.Error(infoName + "||获取平台医嘱数据失败");
                return false;
            }
            if (hisData.Count == 0)
            {
                _logger.Warn(infoName + "||获取平台医嘱数据为空");
                return false;
            }
            //字典档获取
            var physicianToInterventionList = await _physicianToIntervention.GetAllData();
            var orderToAssessList = await _orderToAssessListRepository.GetAsync();
            var physicianOrder = await _physicianOrderRepository.GetAllAsync<PhysicianOrderInfo>();
            var frequencyList = await _frequencyRepository.GetAllAsync<FrequencyInfo>();
            var orderKeyWordList = await _clinicSettingRepository.GetClinicSetting("OrderKeyWord");
            //获取符合同步的His医嘱数据
            var orderCodeList = physicianOrder.Select(m => m.OrderCode).Distinct().ToList();
            hisData = hisData.Where(m => orderCodeList.Contains(m.OrderCode) || m.OrderPattern == "901").ToList();
            var filterHisData = hisData.Where(item => orderKeyWordList.Select(m => m.Description).Any(orderKeyName => item.OrderContent.Contains(orderKeyName))).ToList();
            //无符合医嘱数据直接返回
            if (filterHisData.Count == 0)
            {
                _logger.Error(infoName + "||无符合同步的医嘱数据");
                return true;
            }
            //医嘱数据开始同步
            _logger.Info(infoName + "||需要同步医嘱JSON数据||" + ListToJson.ToJson(filterHisData) + "需要同步医嘱数量" + filterHisData.Count);
            return await _patientOrderMainService.SyncHisDataByStationCode(filterHisData, orderToAssessList, physicianToInterventionList, frequencyList, physicianOrder, _config.Value.HospitalID);
        }
    }
}