﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Interconnect.Data.Interface;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using NLog;
using System.Linq;
using Microsoft.Extensions.Options;
using Medical.Data.Context;
using Interconnect.Data.Context;
using Newtonsoft.Json;
using Medical.Common;
using Arch.EntityFrameworkCore.UnitOfWork;
using Medical.ViewModels.Query;

namespace Interconnect.Services
{
    public class StationService : IStationService
    {
        //Mdeical
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IStationListRepository _IStationListRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        //Interconnect
        private readonly IUnitOfWork<DataOutConnection> _unitOfWorkOut;
        private readonly IOptions<SystemConfig> _config;
        private readonly IStationRepository _IStationRepository;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ILogInfoServices _ILogInfoServices;
        private readonly ISettingDescRepository _ICSettingDescriptionRepository;
        private readonly ICommonHelper _commonHelper;
        private readonly IStationGroupListRepository _stationGroupListRepository;

         
        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";
        /// <summary>
        /// 系统默认操作人
        /// </summary>
        private string MODIFYPERSONID = "TongBu";

        public StationService(
              IUnitOfWork<MedicalDbContext> UnitOfWork
            , IUnitOfWork<DataOutConnection> UnitOfWorkOut
            , IStationRepository StationRepository
            , IStationListRepository StationListRepository
            , IOptions<SystemConfig> config
             , ILogInfoServices LogInfoServices
             , ISettingDescRepository settingDescriptionRepository
            , ICommonHelper commonHelper
            , IStationGroupListRepository stationGroupListRepository     
            , IAppConfigSettingRepository appConfigSettingRepository
            )
        {
            _unitOfWork = UnitOfWork;
            _unitOfWorkOut = UnitOfWorkOut;
            _IStationRepository = StationRepository;
            _IStationListRepository = StationListRepository;
            _config = config;
            _ILogInfoServices = LogInfoServices;
            _ICSettingDescriptionRepository = settingDescriptionRepository;
            _commonHelper = commonHelper;
            _stationGroupListRepository = stationGroupListRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
        }
        

        /// <summary>
        ///  同步
        /// </summary>
        /// <returns></returns>
        public bool SynchronizationMain()
        {
            var interconnectData = GetApiData();
            if (interconnectData.Count <= 0)
            {
                return false;
            }
            var medicalStationList = _IStationListRepository.GetAllStation();
            SynchronizationDetail(interconnectData, medicalStationList); //数据同步
            DataDelete(interconnectData, medicalStationList);//反向对比，删除病区信息 
            medicalStationList = _IStationListRepository.GetStationList();//因为数据刚刚同步过，需要重新获取
            UpdateInterconnectStationGroup(medicalStationList); //处理病区分组数据
            return true;
        }

        private bool SynchronizationDetail(List<StationInfo> OriginalList, List<StationListInfo> medicalStationList)
        {
            _logger.Info("开始运行SynchronizationDetail");            
            int maxID = _IStationListRepository.GetMaxID();
            List<StationListInfo> Insertlist = new List<StationListInfo>();
            StationListInfo t = null;
            int failcount = 0;
            string tablename = "Station";
            List<LogInfo> logList = new List<LogInfo>();
            LogInfo tempLog = null;

            tempLog = _ILogInfoServices.InnsertLogAsync(tablename, " 开始进行数据同步，数据条数：" + OriginalList.Count);
            logList.Add(tempLog);

            #region "数据同步"
            foreach (var item in OriginalList)
            {
                try
                {
                    //获取Medical中的病区信息
                    var tempMedicalStationListInfo = medicalStationList.Where(m => m.StationCode.Trim() == item.StationCode.Trim()).ToList();
                    if (tempMedicalStationListInfo.Count > 1)
                    {
                        tempLog = _ILogInfoServices.InnsertLogAsync(tablename, "[" + item.StationCode + "] [" + item.StationCode + "]查询病区信息错误!");
                        logList.Add(tempLog);
                        failcount++;
                        continue;
                    }

                    //获取插入的数据
                    var tempInsertlist = Insertlist.Where(m => m.StationCode.Trim() == item.StationCode.Trim()).ToList();
                    //如果不存在进行新增
                    if (tempMedicalStationListInfo.Count < 1 && tempInsertlist.Count < 1)
                    {
                        t = new StationListInfo
                        {
                            ID = maxID,
                            HospitalID = _config.Value.HospitalID,
                            StationName = item.StationName,
                            StationCode = item.StationCode.Trim(),
                            Sort = 0,
                            ICUFlag = item.ICUFlag,
                            AttendanceCondition = "a",
                            ModifyPersonID = MODIFYPERSONID,
                            ModifyDate = DateTime.Now,
                            DeleteFlag = ""
                        };
                        Insertlist.Add(t);
                        maxID++;
                    }
                    else //如果根据病区查询到这条记录，则判断其他的记录是否发生了改变
                    {
                        if (tempMedicalStationListInfo.Count() == 1)
                        {
                            tempMedicalStationListInfo[0].StationName = item.StationName;
                            tempMedicalStationListInfo[0].ICUFlag = item.ICUFlag;
                            tempMedicalStationListInfo[0].DeleteFlag = item.DeleteFlag;
                            tempMedicalStationListInfo[0].ModifyPersonID = MODIFYPERSONID;
                            tempMedicalStationListInfo[0].ModifyDate = DateTime.Now;
                        }

                    }
                    item.DataPumpFlag = "*";
                    item.DataPumpDate = DateTime.Now;
                }
                catch (Exception ex)
                {
                    _logger.Error(tablename + "  同步错误：" + item.StationCode + ":" + ex.ToString());
                    return false;
                }
            }
            #endregion
            #region "数据更新"
            if (OriginalList.Count >= 1)
            {
                try
                {
                    _unitOfWork.GetRepository<StationListInfo>().Insert(Insertlist);
                    _unitOfWork.GetRepository<StationListInfo>().Update(medicalStationList);
                    _unitOfWork.SaveChanges();
                }
                catch (Exception ex)
                {
                    _logger.Error(tablename + "||同步失败||" + ex.ToString());
                    return false;
                }
            }
            tempLog = _ILogInfoServices.InnsertLogAsync(tablename, " 同步结束 成功：" + (OriginalList.Count - failcount).ToString() + "条！ 失败：" + failcount.ToString() + "条！");
            logList.Add(tempLog);
            int ItemNo = 0;
            string Guid = "";
            Guid = System.Guid.NewGuid().ToString("N");
            foreach (var item in logList)
            {
                item.Guid = Guid;
                item.ItemNo = ItemNo;
                ItemNo++;
            }
            try
            {
                _unitOfWorkOut.GetRepository<LogInfo>().Insert(logList);
                _unitOfWorkOut.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error(tablename + "同步成功，但写同步日志失败||" + ex.ToString());
            }
            _logger.Info(tablename + "||同步完成!");
            try
            {
                //更新病区缓存
                CacheQuery query = new CacheQuery
                {
                    Type = CacheType.Station
                };
                _commonHelper.UpdateCache(query);
            }
            catch (Exception ex)
            {
                _logger.Info("病区缓存更新失败" + ex.Message);
            }
            return true;
            #endregion
        }
        private bool DataDelete(List<StationInfo> interconnectData, List<StationListInfo> medicalStationList)
        {
            //对比删除
            foreach (var item in medicalStationList)
            {
                var tempInterconnectStationList = interconnectData.Where(m => m.StationCode == item.StationCode).ToList();
                if (interconnectData.Count <= 0)
                {
                    item.DeleteFlag = "*";
                }
            }
            try
            {
                _unitOfWork.GetRepository<StationListInfo>().Update(medicalStationList);
                _unitOfWork.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error("删除病区失败||" + ex.ToString());
                return false;
            }
            _logger.Info("删除病区成功");
            return true;
        }

        //处理病区分组数据
        private bool UpdateInterconnectStationGroup(List<StationListInfo> medicalStationList)
        {
            var addStationGroupListList = new List<StationGroupListInfo>();
            var delStationGroupListList = new List<StationGroupListInfo>();
            var stationGroupList = _stationGroupListRepository.GetStationGroupListAll();

            foreach (var item in medicalStationList)
            {
                var stationGroupInfo = stationGroupList.Where(m => m.StationCode == item.StationCode).ToList();
                if (stationGroupInfo.Count <= 0)
                {
                    addStationGroupListList.Add(GetStationGroupListInfo(item));
                }
            }

            foreach (var item in stationGroupList)
            {
                var medicalStationInfo = medicalStationList.Where(m => m.StationCode == item.StationCode).ToList();
                if (medicalStationInfo.Count <= 0)
                {
                    delStationGroupListList.Add(item);
                }
            }
            try
            {
                _unitOfWorkOut.GetRepository<StationGroupListInfo>().Delete(delStationGroupListList);
                _unitOfWorkOut.GetRepository<StationGroupListInfo>().Insert(addStationGroupListList);
                _unitOfWorkOut.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error("处理病区分组数据||" + ex.ToString());
                return false;
            }
            _logger.Info("处理病区分组数据成功");
            return true;
        }

        //组装病区分组数据
        private StationGroupListInfo GetStationGroupListInfo(StationListInfo stationListInfo)
        {
            var stationGroupListInfo = new StationGroupListInfo
            {
                StationID = stationListInfo.ID
                ,
                StationCode = stationListInfo.StationCode
                ,
                StationName = stationListInfo.StationName
                ,
                StationGroup = 0
            };
            return stationGroupListInfo;
        }

        //获得APi数据
        private List<StationInfo> GetApiData()
        {
            var interconnectData = new List<StationInfo>();
            _logger.Info("开始获取病区API");
            string apiStr = "";
            var apiStrList = _ICSettingDescriptionRepository.GetAsync(1, "4");
            if (apiStrList.Count > 0)
            {
                apiStr = apiStrList[0].Description;
            }
            else
            {
                _logger.Error("获取病区API失败");
                return interconnectData;
            }
            _logger.Info("获取病区信息数据");
            var data = new Dictionary<string, string>();
            //呼叫API获得数据
            var resultData = _commonHelper.GetInterconnectData(apiStr, data);

            //从配置当中获取数据 梁宝华 2020-04-29
            var printInterfaceData = 0;
            var resultPrintDate = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "PrintInterfaceData").Result;
            if (StringCheck.IsNumeric(resultPrintDate))
            {
                printInterfaceData = int.Parse(resultPrintDate);
            }

            if (printInterfaceData == 1)
            {
                _logger.Info("获得数据如下:" + resultData);
            }
            var  result = JsonConvert.DeserializeObject<ResponseResult>(resultData);
            if (result == null)
            {
                _logger.Error("反序列化数据失败:需要判断是否没有获取到api数据");
                return interconnectData;
            }
            try
            {
                interconnectData = JsonConvert.DeserializeObject<List<StationInfo>>(result.Data.ToString());
            }
            catch (Exception ex)
            {
                _logger.Error("Json格式化失败:" + ex.ToString());
                return interconnectData;
            }
            return interconnectData;
        }
    }
}

