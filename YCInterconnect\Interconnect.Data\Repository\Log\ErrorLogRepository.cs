﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
 

namespace Interconnect.Data
{
   public class ErrorLogRepository: IErrorLogRepository
    {
        private DataOutConnection _DataOutConnection = null;

        public ErrorLogRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }
        /// <summary>
        /// 获取没有推送的错误日志
        /// </summary>
        /// <returns></returns>
        public  List<ErrorLogInfo> GetAsync()
        {
            try
            {
                return  _DataOutConnection.ErrorLogInfos.Where(m=>m.SendFlag!="*").ToList();                    
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}
