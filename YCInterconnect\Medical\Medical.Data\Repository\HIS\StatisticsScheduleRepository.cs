﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data
{
    public class StatisticsScheduleRepository : IStatisticsScheduleRepository
    {
        private ExternalDbContext _dbContext = null;

        public StatisticsScheduleRepository(ExternalDbContext db)
        {
            _dbContext = db;
        }

        public async Task<List<StatisticsScheduleOracleInfo>> GetAllAsync(DateTime startDate, DateTime endDate, string stationCode)
        {
            return await _dbContext.StatisticsScheduleOracleInfos.Where(m => m.ScheduleDateTime >= startDate && m.ScheduleDateTime <= endDate && m.StationCode == stationCode).ToListAsync();
        }
        public async Task<List<StatisticsScheduleOracleInfo>> GetByDateAsync(DateTime startDate, DateTime endDate)
        {
            return await _dbContext.StatisticsScheduleOracleInfos.Where(m => m.ScheduleDateTime >= startDate && m.ScheduleDateTime <= endDate).ToListAsync();
        }
    }
}
