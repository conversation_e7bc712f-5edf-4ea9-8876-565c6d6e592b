﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using NPOI.OpenXmlFormats.Dml;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class UserRepository : IUserRepository
    {
        private MedicalDbContext _dbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IUserRoleRepository _userRoleRepository;
        private readonly GetCacheService _getCacheService;


        /// <summary>
        /// 职称
        /// </summary>
        private const string EMPLOYEELDATA_TITLE = "医师";
        /// <summary>
        /// 中山医生职称有的是教授
        /// </summary>
        private const string EMPLOYEELDATA_DOCTOR_TITLE1 = "教授";
        /// <summary>
        /// 中山医生职称有的是科主任
        /// </summary>
        private const string EMPLOYEELDATA_DOCTOR_TITLE2 = "主任";
        /// <summary>
        ///  实习医师没有职称，同步数据里把人员类别放到了Title字段
        /// </summary>
        private const string EMPLOYEELDATA_DOCTOR_TITLE3 = "医生";

        public UserRepository(
              MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , IUserRoleRepository userRoleRepository
            , GetCacheService getCacheService
            )
        {
            _dbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _userRoleRepository = userRoleRepository;
            _getCacheService = getCacheService;
        }

        public async Task<int> GetMaxAsync()
        {
            try
            {
                var userList = await GetCacheAsync() as List<UserInfo>;
                var user = userList.OrderByDescending(m => m.ID).FirstOrDefault();
                if (user == null)
                {
                    return 1;
                }
                else
                {
                    return user.ID + 1;
                }
            }
            catch { }
            return 1;
        }

        public async Task<List<UserInfo>> GetStationUserAsync(int stationID)
        {

            var userList = await GetCacheAsync() as List<UserInfo>;
            return userList.Where(m => m.StationID == stationID&&m.ModifyPersonID != "ElectBoardUser").ToList();
        }

        public async Task<List<UserInfo>> GetAsync(string userId)
        {
            var userList = await GetCacheAsync() as List<UserInfo>;
            return userList.Where(m => m.UserID == userId).ToList();
        }

        public async Task<UserInfo> GetByIDAsync(int id)
        {
            var userList = await GetCacheAsync() as List<UserInfo>;
            return userList.Where(m => m.ID == id).FirstOrDefault();
        }

        /// <summary>
        /// 验证用户
        /// </summary>
        /// <param name="userId">帐号</param>
        /// <param name="passWord">密码</param>
        /// <returns></returns>
        public async Task<UserInfo> GetUserPassAsync(string userId, string passWord)
        {
            var userInfo = await GetByEmployeeID(userId);
            if (userInfo != null && userInfo.Password == passWord)
            {
                return userInfo;
            }
            return null;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<UserInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            dict.TryGetValue("language", out var language);
            var datas =  await _dbContext.Users.Where(m => m.HospitalID == hospitalID.ToString()&& m.DeleteFlag != "*").ToListAsync();
            if (hospitalID.ToString() == "5")
            {
                return datas.Select(m => new UserInfo
                {
                    ID = m.ID,
                    HospitalID = m.HospitalID,
                    UserID = m.UserID,
                    PhysicianID = m.PhysicianID,
                    Password = EncryptionAndDecryption.DecryptStr(m.Password),
                    Name = m.Name,
                    StationID = m.StationID,
                    DepartmentID = m.DepartmentID,
                    Title = m.Title,
                    Rank = m.Rank,
                    JobID = m.JobID,
                    PhoneNumber = EncryptionAndDecryption.DecryptStr(m.PhoneNumber),
                    SpecialFlag = m.SpecialFlag,
                    HREmployeeID = m.HREmployeeID,
                    ExpirationDate = m.ExpirationDate,
                    EmployeeType = m.EmployeeType,
                    CapabilityLevelID = m.CapabilityLevelID,
                }).ToList();
            }
            return datas;
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.EmployeelData.GetKey(_sessionCommonServer);
        }

        /// <summary>
        /// 获取全部用户
        /// </summary>
        /// <returns></returns>
        public async Task<List<UserInfo>> GetAsync()
        {
            var list =  await GetCacheAsync() as List<UserInfo>;
            return list.Where(m => m.ModifyPersonID != "ElectBoardUser").ToList();
        }

        /// <summary>
        /// 获取全部用户,数据同步使用
        /// </summary>
        /// <returns></returns>
        public List<UserInfo> GetUserList()
        {
            return _dbContext.Users.Where(m => m.DeleteFlag != "*").ToList();
        }

        public async Task<List<UserInfoView>> GetUserAsync()
        {
            var list = await GetCacheAsync() as List<UserInfo>;
            return list.Where(m => m.ModifyPersonID != "ElectBoardUser").Select(m => new UserInfoView
            {
                UserID = m.UserID,
                UserName = m.Name
            }).ToList();
        }

        /// <summary>
        /// 根据 EmployeeID集合获取人员数据,同步方法
        /// </summary>
        /// <param name="ids">CaseNumber集合</param>
        /// <returns></returns>
        public async Task<List<UserInfo>> GetByEmployeeIDList(List<string> ids)
        {
            var list = await GetCacheAsync() as List<UserInfo>;
            return list.Where(m => ids.Contains(m.UserID)).ToList();
        }

        /// <summary>
        ///  获取科室里的所有医师
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        public async Task<List<UserInfo>> GetDoctorByDepartmentAsync(int departmentID)
        {
            var list = await GetCacheAsync() as List<UserInfo>;
            if (list == null || list.Count == 0)
            {
                return null;
            }
            list = list.Where(m => !string.IsNullOrEmpty(m.Title)).ToList();
            return list.Where(m => m.Title.Contains(EMPLOYEELDATA_TITLE) && m.DepartmentID == departmentID).ToList();
        }

        public async Task<UserInfo> GetByEmployeeID(string employeeID)
        {
            //先从缓存获取
            var userList = await GetCacheAsync() as List<UserInfo>;
            var userInfo = userList.Where(m => m.UserID == employeeID).FirstOrDefault();
            if (userInfo != null)
            {
                return userInfo;
            }
            var session = await _sessionCommonServer.GetSession();
            //没有从数据库取
            return await _dbContext.Users.Where(m => m.UserID == employeeID && m.HospitalID == session.HospitalID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据员工编号获取UserInfo
        /// </summary>
        /// <param name="employeeIDs">员工编号</param>
        /// <returns></returns>
        public async Task<UserInfo[]> GetByEmployeeIDs(IEnumerable<string> employeeIDs)
        {
            // 先从缓存获取
            var userList = await GetCacheAsync() as List<UserInfo>;
            var userInfo = userList.Where(m => employeeIDs.Contains(m.UserID)).ToArray();
            if (userInfo != null && userInfo.Length == employeeIDs.Count())
            {
                return userInfo;
            }
            // 没有从数据库取
            var session = await _sessionCommonServer.GetSession();
            return await _dbContext.Users.Where(m => employeeIDs.Contains(m.UserID) && m.HospitalID == session.HospitalID && m.DeleteFlag != "*").ToArrayAsync();
        }

        /// <summary>
        /// 返回没有权限的人员列表
        /// </summary>
        /// <returns></returns>
        public List<string> GetByEmployeeIDNoRole()
        {
            var userList = (List<UserInfo>)GetCacheAsync().Result;

            var userRoleList = _userRoleRepository.GetAsync().Result;
            var result = (from a in userList
                          where !userRoleList.Any(m => m.EmployeeID == a.UserID
                          && m.DeleteFlag != "*") && a.DeleteFlag != "*"
                          select a.UserID).ToList();
            return result;
        }

        /// <summary>
        /// 获取全部用户(包括删除数据)
        /// </summary>
        /// <returns></returns>
        public async Task<List<UserInfo>> GetAllUserAsync()
        {
            return await GetCacheAsync() as List<UserInfo>;
        }

        public async Task<UserInfo> GetUserByEmployeeIDAsync(string employeeID)
        {
            return await GetByEmployeeID(employeeID);
        }

        /// <summary>
        /// 根据用户ID ,获取用户姓名
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<string> GetUserNameByEmployeeID(string employeeID)
        {
            var userInfo = await GetEmployeeByEmployeeIDAsync(employeeID);
            if (userInfo != null)
            {
                return userInfo.Name;
            }
            return "";
        }

        /// <summary>
        /// 根据用户ID获取用户信息（有缓存取缓存，没缓存取数据库）
        /// </summary>
        /// <param name="modifyPersonID"></param>
        /// <returns></returns>
        public async Task<UserInfo> GetEmployeeByEmployeeIDAsync(string modifyPersonID)
        {
            //先从缓存获取
            var userList = await GetCacheAsync() as List<UserInfo>;
            var userInfo = userList.Where(m => m.UserID == modifyPersonID).FirstOrDefault();
            if (userInfo != null)
            {
                return userInfo;
            }
            //没有从数据库取
            var session = await _sessionCommonServer.GetSession();
            userInfo = await _dbContext.Users.Where(m => m.UserID == modifyPersonID && m.HospitalID == session.HospitalID).FirstOrDefaultAsync();
            return userInfo;
        }
        /// <summary>
        ///  获取病区里的所有医师
        /// </summary>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<List<UserInfo>> GetDoctorByStationIDAsync(int stationID)
        {
            var data = await GetDoctorAsync();
            if (data == null || data.Count == 0)
            {
                return null;
            }
            return data.Where(m => m.StationID == stationID).ToList();
        }
        /// <summary>
        ///  获取医院所有医师
        /// </summary>
        /// <returns></returns>
        public async Task<List<UserInfo>> GetDoctorAsync()
        {
            var userList = await GetCacheAsync() as List<UserInfo>;
            if (userList == null || userList.Count == 0)
            {
                return null;
            }
            userList = userList.Where(m => !string.IsNullOrEmpty(m.Title)).ToList();
            return userList.Where(m => (m.Title.Contains(EMPLOYEELDATA_TITLE) ||
            m.Title.Contains(EMPLOYEELDATA_DOCTOR_TITLE1) || m.Title.Contains(EMPLOYEELDATA_DOCTOR_TITLE3) ||
            (m.Title.Contains(EMPLOYEELDATA_DOCTOR_TITLE2) && !m.Title.Contains(EMPLOYEELDATA_TITLE)))).ToList();
        }

        public async Task<List<SimpleUser>> GetSimpleUserAsync()
        {
            var allUserList = await GetCacheAsync() as List<UserInfo>;
            return allUserList.Select(m => new SimpleUser
            {
                UserID = m.UserID,
                UserName = m.Name,
                Title = m.Title
            }).ToList();
        }
        /// <summary>
        /// 获取没有缓存数据
        /// </summary>
        /// <param name="userID"></param>
        /// <returns></returns>
        public async Task<string> GetNoCacheUserName(string userID)
        {
            //没有从数据库取
            var session = await _sessionCommonServer.GetSession();
            var data = await _dbContext.Users.Where(m => m.HospitalID == session.HospitalID && m.UserID == userID).Select(m => m.Name).FirstOrDefaultAsync();
            if (data == null)
            {
                return "";
            }

            return data;
        }

        public async Task<List<UserBasicDataView>> GetAllUserBasicAsync()
        {
            var allUserList = await GetCacheAsync() as List<UserInfo>;
            return allUserList.Where(m => m.ModifyPersonID != "ElectBoardUser").Select(m => new UserBasicDataView
            {
                Name = m.Name,
                PhoneNumber = m.PhoneNumber,
                UserID = m.UserID,
                StationID = m.StationID,
                JobID = m.JobID
            }).ToList();
        }
        public async Task<List<UserInfo>> GetEmployeelData(string userId)
        {
            var userList = await GetCacheAsync() as List<UserInfo>;
            return userList
                .Where(t => t.UserID == userId).Select(t => new UserInfo
                {
                    UserID = t.UserID,
                    Password = t.Password,
                    HospitalID = t.HospitalID
                }).ToList();
        }
        /// <summary>
        /// 获取进修医师
        /// </summary>
        /// <returns></returns>
        public async Task<List<UserInfo>> GetVisitingDoctor()
        {
            var userList = await GetCacheAsync() as List<UserInfo>;
            return userList.Where(m => m.EmployeeType == "1").ToList();
        }

        /// <summary>
        /// 从数据库中获取员工工号（2022-11-14同步CA使用，不走缓存）
        /// </summary>
        /// <returns></returns>
        public async Task<List<string>> GetEmployeeIDNoCacheAsync()
        {
            var (hospitalID, _) = await _sessionCommonServer.GetSession();

            return await _dbContext.Users.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*" && m.ModifyPersonID != "ElectBoardUser").Select(m => m.UserID).ToListAsync();
        }
        public async Task<List<UserInfo>> GetNoCache()
        {
            //没有从数据库取
            var session = await _sessionCommonServer.GetSession();
            var data = await _dbContext.Users.Where(m => m.HospitalID == session.HospitalID && m.DeleteFlag != "*").ToListAsync();
            return data;
        }

        public async Task<List<UserInfo>> GetAllUser()
        {
            var session = await _sessionCommonServer.GetSession();
            return await _dbContext.Users.Where(m => m.HospitalID == session.HospitalID).ToListAsync();
        }

        public async Task<List<UserInfo>> GetAllDeletedUserAsync()
        {
            //没有从数据库取
            var session = await _sessionCommonServer.GetSession();
            return await _dbContext.Users.AsNoTracking().Where(m => m.HospitalID == session.HospitalID && m.DeleteFlag == "*").ToListAsync();
        }
        /// <summary>
        /// 根据员工姓名获取数据
        /// </summary>
        /// <param name="employeelName"></param>
        /// <returns></returns>
        public async Task<UserInfo> GetEmployeeByNameAsync(string employeelName)
        {
            var userList = await GetCacheAsync() as List<UserInfo>;
            var userInfo = userList.Where(m => m.Name == employeelName).FirstOrDefault();
            if (userInfo != null)
            {
                return userInfo;
            }
            var session = await _sessionCommonServer.GetSession();
            userInfo = await _dbContext.Users.Where(m => m.UserID == employeelName && m.HospitalID == session.HospitalID && m.DeleteFlag != "*").FirstOrDefaultAsync();
            return userInfo;
        }
    }
}