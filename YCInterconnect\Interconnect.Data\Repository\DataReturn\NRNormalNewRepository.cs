﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;

namespace Interconnect.Data.Repository

{
    public class NRNormalNewRepository : INRNormalNewRepository
    {

        private DataOutConnection _DataOutConnection = null;

        public NRNormalNewRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }
        /// <summary>
        /// 获取所有没有抽取的数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<NRNormalNewInfo>> GetNormalNewList(string PatientScheduleMainID)
        {
            var NRNormalNewList = new List<NRNormalNewInfo>();
            try
            {
                return await _DataOutConnection.NRNormalNewInfos.Where
                    (m => m.PatientScheduleMainID == PatientScheduleMainID).ToListAsync();
            }
            catch (Exception)
            {               
                return NRNormalNewList;
            }
        }
    }
}
