﻿using Interconnect.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Interconnect.Data.Interface.Repository.Log
{
    public interface ISyncDatasLogRepository
    {
        /// <summary>
        /// 根据分类获取没有同步数据的集合
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="syncDataType"></param>
        /// <returns></returns>
        List<int> GetSyncDataByDataType(string hospitalID, string syncDataType);
        /// <summary>
        /// 异步根据ID获取未同步数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<SyncDataLogInfo> GetSyncDataByIDAsync(int id);
    }
}
