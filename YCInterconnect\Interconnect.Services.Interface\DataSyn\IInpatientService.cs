﻿using System;
using System.Threading.Tasks;

namespace Interconnect.Services.Interface
{
    public interface IInpatientService
    {
        /// <summary>
        /// 根据病区分组同步在院病人的数据
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncInPatientByStationGroup(int StationGroup);

        /// <summary>
        ///  根据病区code同步在院病人的数据
        /// </summary>
        /// <param name="statinoCode"></param>
        /// <returns></returns>
        Task<bool> SyncInPatientByStationCode(string statinoCode);

        /// <summary>
        /// 同步新入院病人
        /// </summary>
        Task<bool> SyncNewInPatient();

        /// <summary>
        /// 同步病人主诉
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncPatientChiefComplaintAsync(int? stationID);

        /// <summary>
        /// 出院病人事件同步,同步一段时间内的出院病人数据
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncDischargedPatientsEventByDateTime();

        /// <summary>
        /// 病人出院病人数据补救
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncDisChargeInPatientByDatetimeSupplement();

        /// <summary>
        /// 根据时间段同步出院患者（刷新用）
        /// </summary>
        /// <param name="startDateTime"></param>
        /// <param name="endDateTime"></param>
        /// <returns></returns>
        Task<bool> SyncDischargedByDateTime(DateTime? startDateTime, DateTime? endDateTime);
        /// <summary>
        /// 同步母婴关系
        /// </summary>
        /// <param name="stationCode">有母婴关系的病区码</param>
        /// <returns></returns>
        Task<bool> SyncMotherAndChildList(string stationCode);
    }
}