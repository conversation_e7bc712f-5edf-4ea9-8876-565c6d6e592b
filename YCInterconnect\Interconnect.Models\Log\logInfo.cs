﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("tb_log")]
    public class LogInfo
    {     
        public string Guid { get; set; }
        /// <summary>
        ///表名称
        ///</summary>
        public string TableNames { get; set; }

        /// <summary>
        /// 插入的子序号
        /// </summary>
        public int ItemNo { get; set; }
        /// <summary>
        ///同步日志
        ///</summary>
        public string Logs { get; set; }
        /// <summary>
        ///日志时间
        ///</summary>
        public DateTime Datetimes { get; set; }
    }
}