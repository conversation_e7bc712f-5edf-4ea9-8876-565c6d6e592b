﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models.CDADocument;
using Microsoft.EntityFrameworkCore;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class CDA_DischargeAssessmentRepository : ICDA_DischargeAssessmentRepository
    {
        private readonly CDADBContext _cDADBConnect = null;
        private readonly static Logger _logger = LogManager.GetCurrentClassLogger();

        public CDA_DischargeAssessmentRepository(CDADBContext cDADBConnect)
        {
            _cDADBConnect = cDADBConnect;
        }

        public async Task<CDA_DischargeEvaluationInfo> GetByID(string handOverID)
        {
            return await _cDADBConnect.CDR_DischargeEvaluationInfos.Where(m => m.DCID == handOverID).FirstOrDefaultAsync();
        }

        public async Task<DateTime?> GetLast()
        {
            var data = await _cDADBConnect.CDR_DischargeEvaluationInfos.Select(m => m.TimeStamp)
                .OrderByDescending(m => m).FirstOrDefaultAsync();

            if (data == null)
            {
                return null;
            }

            return data.Value;
        }


        public async Task<bool> Save(CDA_DischargeEvaluationInfo data)
        {
            try
            {
                _cDADBConnect.Add(data);

                return await _cDADBConnect.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {
                _logger.Error("CDA出院评估写入数据失败" + ex.Message + ",异常数据:" + Common.ListToJson.ToJson(data));

                return false;
            }
        }

        public async Task<bool> Update(CDA_DischargeEvaluationInfo data)
        {
            var old = await _cDADBConnect.CDR_DischargeEvaluationInfos.Where(m => m.DCID == data.DCID).FirstOrDefaultAsync();

            if (old == null)
            {
                return await Save(data);
            }

            try
            {
                _cDADBConnect.Entry(old).CurrentValues.SetValues(data);

                return await _cDADBConnect.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {
                _logger.Error("CDA出院评估更新数据失败" + ex.Message + ",异常数据:" + Common.ListToJson.ToJson(data));

                return false;
            }
        }
        /// <summary>
        /// 获取未推送的数据
        /// </summary>
        /// <param name="startDateTime"></param>
        /// <param name="endDateTime"></param>
        /// <returns></returns>
        public async Task<List<CDA_DischargeEvaluationInfo>> GetSyncData(DateTime? startDateTime, DateTime? endDateTime)
        {
            var now = DateTime.Now.Date;
            if (!startDateTime.HasValue)
            {
                startDateTime = now;
            }
            if (!endDateTime.HasValue)
            {
                endDateTime = now.AddDays(1);
            }
            if (startDateTime.Value.Date == endDateTime.Value.Date)
            {
                endDateTime = endDateTime.Value.AddSeconds(86399);
            }
            return await _cDADBConnect.CDR_DischargeEvaluationInfos.Where(m => m.DataPumpFlag != "*"
            && m.TimeStamp >= startDateTime && m.TimeStamp <= endDateTime).ToListAsync();
        }
    }
}
