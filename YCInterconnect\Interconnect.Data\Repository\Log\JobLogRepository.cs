﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;


namespace Interconnect.Data
{
    public class JobLogRepository : IJobLogRepository
    {
        private DataOutConnection _DataOutConnection = null;



        public JobLogRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }

        public JobLogInfo GetJobStatus(int jobId)
        {
            var jogLog = _DataOutConnection.JobLogInfos.Where(m => m.JobID == jobId).FirstOrDefault();
            return jogLog;
        }
    }
}
