﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Interconnect.Data.Interface;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using NLog;
using System.Linq;
using Microsoft.Extensions.Options;
using Medical.Data.Context;
using Interconnect.Data.Context;
using Interconnect.ViewModels;
using Medical.Common;
using Newtonsoft.Json;
using ViewModel;
using Arch.EntityFrameworkCore.UnitOfWork;

namespace Interconnect.Services
{
    public class ICInpatientLogService : IICInpatientLogService
    {
        private readonly IInpatientDataRepository _InpatientDataRepository;
        private readonly IUnitOfWork<DataOutConnection> _UnitOfWorkOut;
        private readonly IUnitOfWork<MedicalDbContext> _UnitOfWork;
        private readonly IOptions<SystemConfig> _Config;
        private readonly ISettingDescRepository _interconnectSDRepository;
        private readonly ICommonHelper _commonHelper;
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ILogInfoServices _ILogInfoServices;
        private readonly IInpatientLogRepository _inpatientLogRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;

        public ICInpatientLogService(
             IICInpatientLogRepository iCInpatientLogRepository
            , IInpatientDataRepository inpatientDataRepository
            , IUnitOfWork<DataOutConnection> unitOfWorkOut
            , IUnitOfWork<MedicalDbContext> unitOfWork
            , IOptions<SystemConfig> options
            , ILogInfoServices logInfoServices
            , ISettingDescRepository settingDescRepository
            , ICommonHelper commonHelper
            , IInpatientLogRepository inpatientLogRepository
            , IAppConfigSettingRepository  appConfigSettingRepository
            )
        {
            _inpatientLogRepository = inpatientLogRepository;
            _InpatientDataRepository = inpatientDataRepository;
            _UnitOfWorkOut = unitOfWorkOut;
            _UnitOfWork = unitOfWork;
            _Config = options;
            _ILogInfoServices = logInfoServices;
            _interconnectSDRepository = settingDescRepository;
            _commonHelper = commonHelper;
            _appConfigSettingRepository = appConfigSettingRepository;
        }

        /// <summary>
        /// 如果发现有没有同步的数据，则一直获取，直至完全同步
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SynchronizationMain(string CaseNumber)
        {
            var resultFalg = false;
            var medicalInPatientList = await _InpatientDataRepository.GetListAsync();

            if (CaseNumber == "" || CaseNumber == null)
            {
                _logger.Info("CaseNumber为空，同步所有在院病人转科记录");
                medicalInPatientList = medicalInPatientList.Where(m => m.DeleteFlag != "*").ToList();
                foreach (var item in medicalInPatientList)
                {
                    var data =  SyncPatientLogData(item.CaseNumber);
                    if (data.Count > 0)
                    {
                        if (!await SynchronizationDetail(data, item))
                        {
                            return false;
                        }
                    }
                }
            }
            else
            {
                var data =  SyncPatientLogData(CaseNumber);
                if (data.Count > 0)
                {
                    var inPatientListTemp = medicalInPatientList.Where(m => m.DeleteFlag != "*" && m.CaseNumber == CaseNumber).FirstOrDefault();
                    if (!await SynchronizationDetail(data, inPatientListTemp))
                    {
                        return false;
                    }
                }
            }
            return resultFalg;
        }

        //同步
        private async Task<bool> SynchronizationDetail(List<HISInpatientLogView> originalList, InpatientDataInfo inpatientData)
        {
            //从配置档中获取数据 梁宝华 2020-04-29
            var modifyPersonID = await _appConfigSettingRepository.GetConfigSettingValue("Configs", "ModifyPersonID");
            foreach (var item in originalList)
            {
                //判断插入的信息是否已经存在，如果存在则更新
                var newStationInfo = _commonHelper.GetStationByCode(item.NewStationCode);
                if (newStationInfo == null)
                {
                    _logger.Error("未找到科室信息NewStationCode=" + item.NewStationCode);
                    continue;
                }
                var bedInfo = _commonHelper.GetBedByCode(item.BedCode, newStationInfo.Id);
                if (newStationInfo == null)
                {
                    _logger.Error("未找到床位信息=" + item.BedCode + "StationdID" + newStationInfo.Id);
                    continue;
                }
                var monifyData = Convert.ToDateTime(item.ModifyDate);
                var newStationID = newStationInfo.Id;
                var inpatientLog = await _inpatientLogRepository.GetByInpatientIDAsync(inpatientData.ID);
                var InpatientLogListTemp = inpatientLog.Where(m => m.InpatientID == inpatientData.ID
                && m.StationID == newStationID && m.LogDateTime == monifyData).ToList();              
                if (InpatientLogListTemp.Count < 1)
                {

                    var InpatientLog = new InpatientLogInfo()
                    {
                        InpatientID = inpatientData.ID,
                        PatientID = inpatientData.PatientID,                   
                        CaseNumber = inpatientData.CaseNumber,
                        ChartNo = inpatientData.ChartNo,
                        StationID = newStationInfo.Id,
                        BedID = inpatientData.BedID,
                        BedNumber = inpatientData.BedNumber,
                        DeleteFlag = "",
                        ModifyPersonID = modifyPersonID,
                        ModifyDate = DateTime.Now
                    };                   
                    //进行提交
                    try
                    {
                        _UnitOfWork.GetRepository<InpatientLogInfo>().Insert(InpatientLog);
                        await _UnitOfWork.SaveChangesAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.Error("InpatientLog同步失败：" + ex.ToString());
                        return false;
                    }
                }
               
            }
            return true;
        }


        /// <summary>
        /// 获取数据
        /// </summary>
        /// <returns></returns>
        public List<HISInpatientLogView> SyncPatientLogData(string CaseNumber)
        {
            _logger.Info("开始转科数据,CaseNumber=" + CaseNumber);
            var Interconnect_Data = new List<HISInpatientLogView>();
            string apiStr = "";
            var apiStrList = _interconnectSDRepository.GetAsync(1, "15");
            if (apiStrList.Count > 0)
            {
                apiStr = apiStrList[0].Description;
            }
            else
            {
                _logger.Error("获取转科数据失败");
                return Interconnect_Data;
            }
            _logger.Info("获取转科数据");
            var data = new Dictionary<string, string>
            {
                { "CaseNumber",CaseNumber }
            };
            //呼叫API获得数据
            var resultData = _commonHelper.GetInterconnectData(apiStr, data);
            var result = new ResponseResult();
            result = JsonConvert.DeserializeObject<ResponseResult>(resultData);

            try
            {
                Interconnect_Data = JsonConvert.DeserializeObject<List<HISInpatientLogView>>(result.Data.ToString());
            }
            catch (Exception ex)
            {
                _logger.Error("Json格式化失败:" + ex.ToString());
                return Interconnect_Data;
            }
            _logger.Info("转换Json数据完成，获得转科数据" + Interconnect_Data.Count() + "条！");
            return Interconnect_Data;
        }

    }
}

