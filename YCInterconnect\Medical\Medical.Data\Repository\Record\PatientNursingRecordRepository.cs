﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientNursingRecordRepository : IPatientNursingRecordRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientNursingRecordRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        ///  获取病人所有护理记录
        /// </summary>
        /// <param name="stationID">单位代码</param>
        /// <param name="inPatientID">病人在院号</param>
        /// <returns></returns>
        public async Task<List<PatientNursingRecordInfo>> GetAsync(int stationID, string inPatientID)
        {
            return await _medicalDbContext.PatientNursingRecordInfos.Where(m =>
            m.StationID == stationID &&
            m.InpatientID == inPatientID &&
            m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<PatientNursingRecordInfo> GetByPatientNursingRecordIDAsync(string patientNursingRecordID)
        {
            return await _medicalDbContext.PatientNursingRecordInfos.Where(m =>
            m.ID == patientNursingRecordID).FirstOrDefaultAsync();
        }

        public async Task<List<PatientNursingRecordInfo>> GetPatientNursingRecordByInpatientIDAsync(string inPatientID)
        {
            return await _medicalDbContext.PatientNursingRecordInfos.AsNoTracking().Where(m =>
            m.InpatientID == inPatientID &&
            m.DeleteFlag != "*").Select(m => new PatientNursingRecordInfo
            {
                ID = m.ID,
                AddDate = m.AddDate,
                NursingRecord = m.NursingRecord,
                AddEmployeeID = m.AddEmployeeID,
                StationID = m.StationID,
                Shift = m.Shift,
                BedNumber = m.BedNumber,
                PatientProblemID = m.PatientProblemID,
                ProblemID = m.ProblemID
            }).ToListAsync();
        }
        /// <summary>
        /// 获取时间段内护理记录
        /// </summary>
        /// <param name="stationID">单位代码</param>
        /// <param name="inPatientID">病人在院号</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns></returns>
        public async Task<List<PatientNursingRecordInfo>> GetAsync(int stationID, string inPatientID, DateTime startDate, DateTime endDate)
        {
            var data = await GetAsync(stationID, inPatientID);

            return data.Where(m => m.AddDate.Date >= startDate && m.AddDate.Date <= endDate).ToList();
        }

        /// <summary>
        /// 获取时间段内护理记录
        /// </summary>
        /// <param name="stationID">单位代码</param>
        /// <param name="inPatientID">病人在院号</param>
        /// <param name="recordDate">病历日期</param>
        /// <returns></returns>
        public async Task<List<PatientNursingRecordInfo>> GetAsync(int stationID, string inPatientID, DateTime recordDate)
        {
            return await _medicalDbContext.PatientNursingRecordInfos.Where(m =>
          m.StationID == stationID &&
          m.InpatientID == inPatientID &&
          m.RecordDate == recordDate &&
          m.DeleteFlag != "*").ToListAsync();
        }
    }
}
