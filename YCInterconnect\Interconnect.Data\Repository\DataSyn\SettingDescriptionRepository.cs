﻿using System.Collections.Generic;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;

namespace Interconnect.Data
{
    public class SettingDescRepository : ISettingDescRepository
    {
        private DataOutConnection _DataOutConnection = null;

        public SettingDescRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }

        /// <summary>
        /// 获取所有没有抽取的数据
        /// </summary>
        /// <returns></returns>
        public List<SettingDescriptionInfo> GetAsync(int settingType, string typeCode)
        {
            return _DataOutConnection.SettingDescriptionInfos.Where
                (m => m.SettingType == settingType && m.TypeCode == typeCode && m.DeleteFlag != "*").ToList();
        }
    }
}
