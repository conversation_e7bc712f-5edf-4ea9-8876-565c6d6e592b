﻿using Interconnect.Services.Interface;
using Interconnect.ViewModels;
using Medical.Common;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using System;

using System.Threading.Tasks;


namespace Interconnect.API.Controllers
{
    [Produces("application/json")]
    [Route("api/Operate")]
    [EnableCors("any")]
    public class SyncOperateController : Controller
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IOperateListService _operateListService;
        private readonly IJobLogService _jobLogService;
        private SyncOperateController(IOperateListService operateListService
            , IJobLogService jobLogService)
        {
            _operateListService = operateListService;
            _jobLogService = jobLogService;
        }

        /// <summary>
        /// 同步手术记录,同步一段时间内的手术数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncOperateData")]
        public async Task<IActionResult> SyncOperateDataByDateTime()
        {
            var resultSrt = await SyncOperateData();
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 同步手术数据
        /// </summary>
        /// <returns></returns>
        private async Task<bool> SyncOperateData()
        {
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.OperateDataJob).ToString();
            var jobName = "手术信息";
            var jobStatus = false;
            var logMsg = "作业编号:" + guid + "启动同步任务:";
            jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                var resultFlag = false;
                resultFlag = await _operateListService.SyncOperateDataByDateTime();
                _jobLogService.RemoveJob(jobId, jobName, "");
            }
            return true;
        }
    }
}
