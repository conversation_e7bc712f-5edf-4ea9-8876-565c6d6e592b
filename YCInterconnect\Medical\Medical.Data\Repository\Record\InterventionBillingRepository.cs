﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class InterventionBillingRepository : IInterventionBillingRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public InterventionBillingRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }
        /// <summary>
        /// 根据住院号获取时间段内的费用数据
        /// </summary>
        /// <param name="chartNo">住院号</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns></returns>
        public async Task<List<InterventionBillingInfo>> GetByChartNoAsync(string chartNo, DateTime startTime, DateTime endTime)
        {
            return await _medicalDbContext.InterventionBillingInfos.Where(m => m.ChartNo == chartNo && m.PerformDate >= startTime && m.PerformDate <= endTime && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据inpatientID获取明细数据
        /// </summary>
        /// <param name="inpatientID">患者住院序号</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns></returns>
        public async Task<List<InterventionBillingInfo>> GetByInpatientIDAsync(string inpatientID, DateTime startTime, DateTime endTime)
        {
            return await _medicalDbContext.InterventionBillingInfos.Where(m => m.InpatientID == inpatientID && m.PerformDate >= startTime && m.PerformDate <= endTime && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取时间段内的费用数据
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns></returns>
        public async Task<List<InterventionBillingInfo>> GetByTimeAsync(DateTime startTime, DateTime endTime)
        {
            return await _medicalDbContext.InterventionBillingInfos.Where(m => m.PerformDate >= startTime && m.PerformDate <= endTime && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据措施ID获取数据
        /// </summary>
        /// <param name="interventionID">措施序号</param>
        /// <returns></returns>
        public async Task<List<InterventionBillingInfo>> GetAllByInterventionIDAsync(int interventionID, DateTime getDataTime)
        {
            return await _medicalDbContext.InterventionBillingInfos.Where(m => m.InterventionID == interventionID && m.ModifyDate >= getDataTime && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<InterventionBillingInfo>> GetBySourceID(List<string> sourceIDs)
        {
            return await _medicalDbContext.InterventionBillingInfos.Where(m =>sourceIDs.Contains(m.SourceID)).ToListAsync();
        }
    }
}
