﻿namespace Interconnect.ViewModels
{
    public class SendMessageView
    {
        /// <summary>
        /// 消息发送类型
        /// </summary>
        public MessageType MessageType { get; set; }
        /// <summary>
        /// 交换机名称
        /// </summary>
        public string ExchangeName { get; set; } = null!;
        /// <summary>
        /// 仅在消息发送类型为广播时可用，路由键/条件
        /// </summary>
        /// <remarks>
        /// 限定为一个由`.`分隔的字符串，其中每词可以包含如下两种特殊字符：
        /// *：匹配一个字符
        /// #：匹配0~n个字符
        /// </remarks>
        /// <example>
        /// *.orage.*：匹配orange开头和结尾的单词
        /// *.*.rabbit：匹配rabbit结尾的单词
        /// lazy.#：匹配lazy开头的单词
        /// #: 如果路由模式仅是一个井号 (#)，那么它将匹配所有的 routing keys。
        /// </example>
        public string? Routing<PERSON>ey { get; set; }
        /// <summary>
        /// 消息体，不可为null
        /// </summary>
        public object Body { get; set; } = null!;
    }
    /// <summary>
    /// 消息发送类型
    /// </summary>
    public enum MessageType
    {
        /// <summary>
        /// 单人
        /// </summary>
        SINGLE,
        /// <summary>
        /// 广播
        /// </summary>
        BROADCAST
    }
}
