﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface.DynamicTable;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class DynamicTableUserSettingRepository : IDynamicTableUserSettingRepository
    {
        private MedicalDbContext _medicalDbContext;
        private readonly SessionCommonServer _sessionCommonServer;
        public DynamicTableUserSettingRepository(
            MedicalDbContext medicalDbContext
            , SessionCommonServer sessionCommonServer
            )
        {
            _medicalDbContext = medicalDbContext;
            _sessionCommonServer = sessionCommonServer;
        }
        /// <summary>
        /// 获取用户自定义表格配置
        /// </summary>
        /// <param name="dynamicTableListID"></param>
        /// <param name="userID"></param>
        /// <returns></returns>
        public async Task<List<DynamicTableUserSettingInfo>> GetByByUserID(int dynamicTableListID, string userID)
        {
            var session = await _sessionCommonServer.GetSession();
            return await _medicalDbContext.DynamicTableUserSettingInfos.Where(m => m.HospitalID == session.HospitalID && m.DynamicTableListID == dynamicTableListID
            && m.UserID == userID && m.DeleteFlag != "*").ToListAsync();
        }
    }
}
