﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientPCRecordRepository : IPatientPCRecordRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientPCRecordRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// /根据ID获取末梢血运记录
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<PatientPCRecordInfo> GetByIDAsync(string recordID)
        {
            return await _medicalDbContext.PatientPCRecordInfos.Where(t => t.PatientPCRecordID == recordID && t.DeleteFlag != "*").SingleOrDefaultAsync();
        }

        /// <summary>
        /// 根据病人序号获取末梢血运记录
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientPCRecordInfo>> GetListByInpatientIDAsync(string inpatientID)
        {
            return await _medicalDbContext.PatientPCRecordInfos.Where(t => t.InpatientID == inpatientID && t.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据病人序号和评估次数获取末梢血运记录
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="num"></param>
        /// <returns></returns>
        public async Task<List<PatientPCRecordInfo>> GetByAssessNumAsync(string inpatientID, string num)
        {
            return await _medicalDbContext.PatientPCRecordInfos.Where(t => t.InpatientID == inpatientID && t.AssessMainID == num && t.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientPCRecordInfo>> GetByDataPumpDate(DateTime yesterday, DateTime today)
        {
            return await _medicalDbContext.PatientPCRecordInfos.Where(
                         m => m.DataPumpDate >= yesterday && m.DataPumpDate < today && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<HandoverPCCareIntervention>> GetPatientPCCareIntervention(string inpatientID, DateTime startDate, TimeSpan startTime, DateTime endDate, TimeSpan endTime)
        {
            var datas = await (from a in _medicalDbContext.PatientPCCareMainInfos
                               join b in _medicalDbContext.PatientPCRecordInfos on a.PatientPCRecordID equals b.PatientPCRecordID
                               where a.InpatientID == inpatientID && a.AssessDate >= startDate && a.AssessDate <= endDate && a.BringToShift == true && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                               select new HandoverPCCareIntervention
                               {
                                   InpatientID = a.InpatientID,
                                   AssessDate = a.AssessDate,
                                   AssessTime = a.AssessTime,
                                   SkinTemperature = a.SkinTemperature,
                                   SubjectiveFeeling = a.SubjectiveFeeling,
                                   Hyperemia = a.Hyperemia,
                                   SkinColor = a.SkinColor,
                                   ArterialPulsation = a.ArterialPulsation,
                                   ArteryName = a.ArteryName,
                                   SwellingDegree = a.SwellingDegree,
                                   StretchPain = a.StretchPain,
                                   CareIntervention = a.CareIntervention,
                                   BodyShowName = b.BodyShowName,
                                   PatientPCRecordID = a.PatientPCRecordID,
                                   HandOverContent = a.HandOverContent
                               }).ToListAsync();

            if (datas.Count == 0)
            {
                return datas;
            }

            datas = datas.Where(m => m.AssessDate.Add(m.AssessTime) >= startDate.Date.Add(startTime) && m.AssessDate.Add(m.AssessTime) <= endDate.Date.Add(endTime))
                .GroupBy(m => m.PatientPCRecordID)
                .Select(m => m.OrderByDescending(n => n.AssessDate.Date.Add(n.AssessTime)).FirstOrDefault()).ToList();

            return datas;
        }
    }
}