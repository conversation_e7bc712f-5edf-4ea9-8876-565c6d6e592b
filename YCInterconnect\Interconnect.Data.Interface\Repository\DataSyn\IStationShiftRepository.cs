﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Interconnect.Models;


namespace Interconnect.Data.Interface
{
    public interface IStationShiftRepository
    {
        /// <summary>
        /// 获取排班信息
        /// </summary>
        /// <param name="tongbuCount"></param>
        /// <param name="takeRows"></param>
        /// <returns></returns>
        Task<List<StationShiftInfo>> GetAsync(int tongbuCount, int takeRows);
    }
}
