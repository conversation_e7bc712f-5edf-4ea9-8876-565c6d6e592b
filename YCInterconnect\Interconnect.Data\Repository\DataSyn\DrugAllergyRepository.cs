﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Interconnect.Data.Repository

{
    public class DrugAllergyRepository : IDrugAllergyRepository
    {
        private DataOutConnection _DataOutConnection = null;

        public DrugAllergyRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }

        /// <summary>
        /// 获取所有没有抽取的数据
        /// </summary>
        /// <param name="tongbuCount"></param>
        /// <returns></returns>
        public List<DrugAllergyInfo> GetAsync(int tongbuCount)
        {
            return  _DataOutConnection.DrugAllergys.Where(m => m.DataPumpFlag != "*" 
            && ((m.Counts ?? 0) < tongbuCount)).ToList();
        }
    }
}
