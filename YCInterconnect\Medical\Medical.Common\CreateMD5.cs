﻿using System;
using System.Security.Cryptography;
using System.Text;

namespace Medical.Common
{
    public static class CreateMD5
    {
        public static string CreateMD5To32(string str)
        {
            MD5CryptoServiceProvider md5 = new MD5CryptoServiceProvider();
            byte[] bytes = Encoding.UTF8.GetBytes(str);
            string result = BitConverter.ToString(md5.ComputeHash(bytes));
            return result.Replace("-", "");
        }
    }
}

