﻿using System.Threading.Tasks;
 

namespace Interconnect.Services.Interface
{
    public interface IAsyncService
    {
        /// <summary>
        /// 获取护理措施（NurseStep）
        /// </summary>
        /// <param name="scoreMainID"></param>
        /// <param name="problemID"></param>
        /// <param name="Language"></param>
        /// <returns></returns>
        Task<string> GetNurseStepAsync(string scoreMainID,int problemID, int Language);


        Task<string> GetPatientPressureSoresMainAsync(string PressureSoresMainID, int Language);
    }
}