using System;
using System.ComponentModel.DataAnnotations;

namespace Interconnect.ViewModels
{
    /// <summary>
    /// 输血数据API请求参数模型
    /// </summary>
    public class BloodTransfusionApiRequest
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        [Required(ErrorMessage = "开始时间不能为空")]
        public DateTime StartDateTime { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        [Required(ErrorMessage = "结束时间不能为空")]
        public DateTime EndDateTime { get; set; }
    }
    /// <summary>
    /// Wiley补录单病人输血数据API请求参数模型
    /// </summary>
    public class WileyBloodTransfusionApiRequest : BloodTransfusionApiRequest
    {
        /// <summary>
        /// 病案号
        /// </summary>
        [Required(ErrorMessage = "病案号不能为空")]
        public string CaseNumber { get; set; }
    }
}
