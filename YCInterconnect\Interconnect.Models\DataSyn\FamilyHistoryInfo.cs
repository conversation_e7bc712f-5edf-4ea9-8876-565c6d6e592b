﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("FamilyHistory")]
    public class FamilyHistoryInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///病案号///</summary>
        public string ChartNo { get; set; }
        /// <summary>
		///关系
        ///</summary>
		public string Relation { get; set; }
        /// <summary>
		///称谓
        ///</summary>
		public string Appellation { get; set; }
        /// <summary>
        ///存殁
        ///</summary>
        public string LifeOrDeath { get; set; }
        /// <summary>
		///疾病
        ///</summary>
		public string Disease { get; set; }
        /// <summary>
        ///同住
        ///</summary>
        public string Cohabitation { get; set; }
        /// <summary>
        ///过敏药物
        ///</summary>
        public string DrugAllergy { get; set; }
    }
}