﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    /// <summary>
    /// 人员基本信息
    /// </summary>
    [Serializable]
    [Table("Employee")]
    public class EmployeeInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///工号
        ///</summary>
        public string EmployeeID { get; set; }
        /// <summary>
        ///医师工号
        ///</summary>
        public string PhysicianID { get; set; }
        /// <summary>
        ///
        ///</summary>
        public string Password { get; set; }
        /// <summary>
        ///姓名
        ///</summary>
        public string EmployeeName { get; set; }
        /// <summary>
        ///护士站代号
        ///</summary>
        public string StationCode { get; set; }     
        /// <summary>
        ///头衔
        ///</summary>
        public string Title { get; set; }
        /// <summary>
        ///级职
        ///</summary>
        public string Rank { get; set; }
        /// <summary>
        /// CAUserID
        /// </summary>
        public string UserID { get; set; }
    }
}