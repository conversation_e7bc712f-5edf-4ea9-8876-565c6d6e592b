﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("LifeHistory")]
    public class LifeHistoryInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///病案号
        ///</summary>
        public string ChartNo { get; set; }
        /// <summary>
        ///每日平均睡眠时数
        ///</summary>
        public short SleepHour { get; set; }
        /// <summary>
        ///每日抽烟支数
        ///</summary>
        public short CigaretteVolumn { get; set; }
        /// <summary>
        ///每日饮酒两数

        ///</summary>
        public short AlcoholVolumn { get; set; }
        /// <summary>
        ///生活内容

        ///</summary>
        public string LifeStyle { get; set; }
    }
}