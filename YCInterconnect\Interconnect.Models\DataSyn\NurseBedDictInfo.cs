﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("NurseBedDict")]
    public class NurseBedDictInfo : ModifyReturnInfo
    {
        /// <summary>
        ///	流水号(自增)	
        ///</summary>
        [Key]
        [Column("NurseBedSetID")]
        public int NurseBedSetID { get; set; }
        /// <summary>
        ///	病区岗位编号(没有不写)	
        ///</summary>
        public string StationJobID { get; set; }
        /// <summary>
        ///岗位名称
        ///</summary>
        public string StationJobName { get; set; }
        /// <summary>
        ///	床位number	
        ///</summary>
        public string Bednumber { get; set; }
        /// <summary>
        ///	病区编码	
        ///</summary>
        public string WardCode { get; set; }
        /// <summary>
        ///	异动人员	
        ///</summary>
        public string ModifyPersonID { get; set; }
        /// <summary>
        ///	异动日期	
        ///</summary>
        public DateTime ModifyDate { get; set; }
        /// <summary>
        ///	删除注记	
        ///</summary>
        public string DeleteFlag { get; set; }        
        /// <summary>
        ///	同步次数	
        ///</summary>
        public int? Counts { get; set; }
    }
}