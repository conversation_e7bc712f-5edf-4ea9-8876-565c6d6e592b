﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Interconnect.Data.Repository

{
    public class BedRepository : IBedRepository
    {
        private DataOutConnection _DataOutConnection = null;

        public BedRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }

        public List<BedInfo> GetAllAsync()
        {
            try
            {
                return _DataOutConnection.Beds.ToList();
            }
            catch (Exception ex)
            {

                throw ex;
            }
        }
    }
}
