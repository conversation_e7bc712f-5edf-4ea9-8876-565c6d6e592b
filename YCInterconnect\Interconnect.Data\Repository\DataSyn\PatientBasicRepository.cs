﻿using Microsoft.EntityFrameworkCore;
using Interconnect.Data.Context;
using Interconnect.Data.Interface;
using Interconnect.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Options;
namespace Interconnect.Data.Repository
{
    public class PatientBasicRepository : IPatientBasic
    {
        private DataOutConnection _DataOutConnection = null;

        public PatientBasicRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }

        public  List<PatientBasicInfo> GetPreAsync(int tongbuCount, int takeRows)
        {
            List<PatientBasicInfo> InpatientInfos =  _DataOutConnection.PatientBasics.Where(m => m.DataPumpFlag != "*"
            && ((m.Counts ?? 0) < tongbuCount)).Take(takeRows).ToList();
            return InpatientInfos;
        }       
    } 
}