﻿using MimeKit;
using System.Collections.Generic;

namespace Interconnect.ViewModels
{
  public  class MailReceptionViewInfo
    {
        /// <summary>
        /// 邮件接受者地址,邮件接受者昵称
        /// </summary>
        public Dictionary<string,string> ReceptionUser { get; set; }
        
        /// <summary>
        /// 邮件标题
        /// </summary>
        public string MailTitle { get; set; }
        /// <summary>
        /// 邮件内容
        /// </summary>
        public TextPart TextParts { get; set; }
    }
}
