﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models.Patient;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientEventDetailRepository : IPatientEventDetailRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientEventDetailRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }
        /// <summary>
        /// 根据患者事件PatientEventID获取明细表
        /// </summary>
        /// <param name="eventMainID"></param>
        /// <returns></returns>
        public async Task<List<PatientEventDetailInfo>> GetByMainIDAsync(string eventMainID)
        {
            return await _medicalDbContext.PatientEventDetailInfos.Where(t => t.PatientEventID == eventMainID).ToListAsync();
        }
    }
}
