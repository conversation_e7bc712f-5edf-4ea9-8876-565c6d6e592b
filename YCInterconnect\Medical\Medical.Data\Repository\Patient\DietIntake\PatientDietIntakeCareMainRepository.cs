﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientDietIntakeCareMainRepository : IPatientDietIntakeCareMainRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientDietIntakeCareMainRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 根据D获取对应数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<PatientDietIntakeCareMainInfo> GetByID(string id)
        {
            return await _medicalDbContext.PatientDietIntakeCareMainInfos.Where(t => t.PatientDietIntakeCareMainID == id && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据inpatientID 和时间获取数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientDietIntakeCareMainInfo>> GetByInpatientID(string inpatientID, DateTime? date)
        {

            var list = await _medicalDbContext.PatientDietIntakeCareMainInfos.Where(t => t.InpatientID == inpatientID && t.DeleteFlag != "*").ToListAsync();
            if (date.HasValue)
            {
                list = list.Where(m => m.AssessDate == date).ToList();
            }
            return list;
        }

        /// <summary>
        /// 交班使用
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="startDate"></param>
        /// <param name="startTime"></param>
        /// <param name="endDate"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public async Task<List<Dictionary<string, string>>> GetHandoverView(string inpatientID, DateTime startDate, TimeSpan startTime, DateTime endDate, TimeSpan endTime)
        {
            var startDataTime = startDate.Add(startTime);

            var endDataTime = endDate.Add(endTime);

            var query = await _medicalDbContext.PatientDietIntakeCareMainInfos.Where(m => m.InpatientID == inpatientID
                                && m.BringToShift == true && m.DeleteFlag != "*"
                                && m.AssessDate >= startDate && m.AssessDate <= endDate).ToListAsync();

            var data1 = query.Where(m => m.AssessDate.Add(m.AssessTime) >= startDataTime && m.AssessDate.Add(m.AssessTime) <= endDataTime).ToList();
            var data = data1.Select(m => new Dictionary<string, string>
                {
                      { "remark", m.Remark}
                }).ToList();

            if (data.Count == 0)
            {
                return new List<Dictionary<string, string>>();
            }
            return data;
        }
    }
}