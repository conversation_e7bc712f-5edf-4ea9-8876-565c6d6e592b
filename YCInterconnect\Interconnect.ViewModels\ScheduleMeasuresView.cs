﻿using System;

namespace Interconnect.ViewModels
{
    public class ScheduleMeasuresView
    {
        /// <summary>
        /// 来源类型
        /// </summary>
        public int SourceType { get; set; }
        /// <summary>
        /// 一条数据的唯一键
        /// </summary>
        public string RecordID { get; set; }
        /// <summary>
        /// 详细数据
        /// </summary>
        public MeasuresData Data { get; set; }
        
    }
    public class MeasuresData
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 患者编号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 住院号
        /// </summary>
        public string ChartNo { get; set; }
        /// <summary>
        /// 病区编码
        /// </summary>
        public string StationCode { get; set; }
        /// <summary>
        /// 病区名
        /// </summary>
        public string StationName { get; set; }
        /// <summary>
        /// 观察类型
        /// </summary>
        public string MeasureType { get; set; }
        /// <summary>
        /// 观察内容
        /// </summary>
        public string MeasureContent { get; set; }
        /// <summary>
        /// 修改人工号
        /// </summary>
        public string ModifyPersonID { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyDate { get; set; }
        /// <summary>
        /// 删除标记
        /// </summary>
        public string DeleteFlag { get; set; }
    }
}
