﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("NursingRecord_PressureScoreNursingEvaluation")]
    public class NRPressureScoreNursingEvaluationInfo : ModifyReturnInfo
    {
        /// <summary>
        ///	自增长编号	
        ///</summary>
        [Key]
        [Column("NursingID")]
        public int NursingID { get; set; }
        /// <summary>
        ///	PressureScoreInfo的PressureSc
        ///</summary>
        public int PressureScoreID { get; set; }
        /// <summary>
        ///	评估日期	
        ///</summary>
        public DateTime? ReportDate { get; set; }
        /// <summary>
        ///	压疮分期：1期,2期,3期,4期,深
        ///</summary>
        public string Stage { get; set; }
        /// <summary>
        ///	压疮面积 长	
        ///</summary>
        public string PressureScoreLength { get; set; }
        /// <summary>
        ///	压疮面积 宽	
        ///</summary>
        public string PressureScoreWidth { get; set; }
        /// <summary>
        ///	创面面积 长	
        ///</summary>
        public float? WoundLength { get; set; }
        /// <summary>
        ///	创面面积 宽	
        ///</summary>
        public float? WoundWidth { get; set; }
        /// <summary>
        ///	创面面积 深	
        ///</summary>
        public float? WoundDepth { get; set; }
        /// <summary>
        ///	渗液量：无,少量,中量,大量	
        ///</summary>
        public string SeepageQty { get; set; }
        /// <summary>
        ///	组织类型：闭合,上皮组织,肉芽
        ///</summary>
        public string TissueType { get; set; }
        /// <summary>
        ///	评分	
        ///</summary>
        public float? PushScore { get; set; }
        /// <summary>
        ///	基底颜色-红：红25%,红50%,红75
        ///</summary>
        public string BaseColor { get; set; }
        /// <summary>
        ///	气味：无,有	
        ///</summary>
        public string Smell { get; set; }
        /// <summary>
        ///	创面处理：无,有	
        ///</summary>
        public string IsNuring { get; set; }
        /// <summary>
        ///	伤口清洗：生理盐水	
        ///</summary>
        public string Clean { get; set; }
        /// <summary>
        ///	其他清洗方式1	
        ///</summary>
        public string CleanOther1 { get; set; }
        /// <summary>
        ///	其他清洗方式2	
        ///</summary>
        public string CleanOther2 { get; set; }
        /// <summary>
        ///	清创：机械清创,自溶性清创,外
        ///</summary>
        public string Debridement { get; set; }
        /// <summary>
        ///	其他清创方式	
        ///</summary>
        public string DebrimentOther { get; set; }
        /// <summary>
        ///	敷料：水胶体,泡沫,软聚硅酮（
        ///</summary>
        public string Dressing { get; set; }
        /// <summary>
        ///	其他敷料	
        ///</summary>
        public string DressingOther { get; set; }
        /// <summary>
        ///	其他措施	
        ///</summary>
        public string NursingMeasures { get; set; }
        /// <summary>
        ///	教育告知：患者,家属	
        ///</summary>
        public string NursingMeasuresEdudation { get; set; }
        /// <summary>
        ///	皮肤护理：清洁,滋润,保护（赛
        ///</summary>
        public string NursingMeasuresSkinNursing { get; set; }
        /// <summary>
        ///	减压设施：气垫床,减压敷料（透
        ///</summary>
        public string NursingMeasuresDecompression { get; set; }
        /// <summary>
        ///	其他减压设施	
        ///</summary>
        public string NursingMeasuresDecompressionOther { get; set; }
        /// <summary>
        ///	输入时间	
        ///</summary>
        public DateTime? InputTime { get; set; }
        /// <summary>
        ///	输入人工号	
        ///</summary>
        public string InputerCode { get; set; }
        /// <summary>
        ///	输入人姓名	
        ///</summary>
        public string InputerName { get; set; }
        /// <summary>
        ///	护士长审核人工号	
        ///</summary>
        public int? HeadNurseCode { get; set; }
        /// <summary>
        ///	护士长审时间	
        ///</summary>
        public DateTime? HeadNurseTime { get; set; }
        /// <summary>
        ///	删除时间	
        ///</summary>
        public DateTime? DelOpTime { get; set; }
        /// <summary>
        ///	删除时间	
        ///</summary>
        public DateTime? DelTime { get; set; }
        /// <summary>
        ///	状态：0 待审核，1护士长已审核
        ///</summary>
        public int? Status { get; set; }
        /// <summary>
        ///	最后更新时间	
        ///</summary>
        public DateTime? LastUpdateTime { get; set; }
        /// <summary>
        ///		
        ///</summary>
        public int? IsFirst { get; set; }
        /// <summary>
        ///	科护士长审核人工号	
        ///</summary>
        public int? DeptHeadNurseCode { get; set; }
        /// <summary>
        ///	科护士长审核时间	
        ///</summary>
        public DateTime? DeptHeadNurseTime { get; set; }
        /// <summary>
        ///		
        ///</summary>
        public int? SpeciallistCode { get; set; }
        /// <summary>
        ///		
        ///</summary>
        public DateTime? SpeciallistAuditTime { get; set; }
        /// <summary>
        ///		
        ///</summary>
        public string PressureScoreRemark { get; set; }
        /// <summary>
        ///	结局：消褪,愈合,好转,无进展,
        ///</summary>
        public string Result { get; set; }
        /// <summary>
        ///	基底颜色-黄：黄25%,黄50%,黄75
        ///</summary>
        public string BaseColor2 { get; set; }
        /// <summary>
        ///	基底颜色-黑：黑25%,黑50%,黑75
        ///</summary>
        public string BaseColor3 { get; set; }

        /// <summary>
        ///	Medical数据库PatientScoreMain的主键,关联表（PatientScoreMain），用来确定PressureScoreID
        ///</summary>
        public string PatientScoreMainID { get; set; }
    }
}