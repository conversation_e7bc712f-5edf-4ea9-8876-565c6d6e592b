<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ServerGarbageCollection>false</ServerGarbageCollection>
    <ConcurrentGarbageCollection>false</ConcurrentGarbageCollection>
    <BaseOutputPath>bin</BaseOutputPath>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DocumentationFile></DocumentationFile>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <WarningLevel>5</WarningLevel>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="10.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.5" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.10" />
    <PackageReference Include="NLog" Version="5.3.2" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.11" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Interconnect.Data.Interface\Interconnect.Data.Interface.csproj" />
    <ProjectReference Include="..\Interconnect.Data\Interconnect.Data.csproj" />
    <ProjectReference Include="..\Interconnect.Models\Interconnect.Models.csproj" />
    <ProjectReference Include="..\Interconnect.Services.Interface\Interconnect.Services.Interface.csproj" />
    <ProjectReference Include="..\Interconnect.Services\Interconnect.Services.csproj" />
    <ProjectReference Include="..\MedicalExternalCommon.Service\MedicalExternalCommon.Service.csproj" />
    <ProjectReference Include="..\Medical\Medical.ViewModels\Medical.ViewModels.csproj" />
  </ItemGroup>

</Project>
