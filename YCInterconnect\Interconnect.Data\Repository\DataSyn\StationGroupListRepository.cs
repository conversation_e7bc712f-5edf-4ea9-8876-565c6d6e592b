﻿using System.Collections.Generic;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;


namespace Interconnect.Data.Repository
{
    public class StationGroupListRepository : IStationGroupListRepository
    {
        private DataOutConnection _DataOutConnection = null; 
        public StationGroupListRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }
        public List<StationGroupListInfo> GetStationGroupListByGroupID(int GroupID)
        {
            return _DataOutConnection.StationGroupListInfos.Where(m => m.StationGroup == GroupID).ToList();
        }

        public List<StationGroupListInfo> GetStationGroupListAll()
        {
            return _DataOutConnection.StationGroupListInfos.ToList();
        }
    }
}
