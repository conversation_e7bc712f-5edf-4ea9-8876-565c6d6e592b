﻿using System.Collections.Generic;
using Interconnect.Services.Interface;
using System.Threading.Tasks;
using Interconnect.ViewModels;
using Medical.Data.Interface;
using NLog;
using System.Linq;
using Interconnect.Models;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Medical.Common;
using System;
 

namespace Interconnect.Services
{
    public class RiskDataService : IRiskDataService
    {
        private IPatientProfileMarkRepository _patientProfileMarkRepository;
        private IInpatientDataRepository _inpatientDataRepository;
        private readonly IOptions<SystemConfig> _systemConfig;
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IAppConfigSettingRepository   _appConfigSettingRepository;
        public RiskDataService(
             IPatientProfileMarkRepository patientProfileMarkRepository
            , IInpatientDataRepository inpatientDataRepository
            , IOptions<SystemConfig> systemConfig
            , IAppConfigSettingRepository  appConfigSettingRepository
            )
        {
            _patientProfileMarkRepository = patientProfileMarkRepository;
            _inpatientDataRepository = inpatientDataRepository;
            _systemConfig = systemConfig;
            _appConfigSettingRepository = appConfigSettingRepository;
        }

        public List<RiskData> GetRiskDataList()
        {
            var riskDatas = new List<RiskData>();
            string api = null;

            //从配置当中获取数据 梁宝华 2020-04-29
            api = _appConfigSettingRepository.GetConfigSettingValue("Configs", "GetPatientRiskDataListAPI").Result;
            if (string.IsNullOrEmpty(api))
            {
                _logger.Error("没有配置获取病人风险数据API");
                return null;
            }
            try
            {
                var wrs = new WebRequestSugar();
                var hisData = wrs.HttpGet(api, null);
                _logger.Info("调用API返回信息:" + hisData);
                var result = JsonConvert.DeserializeObject<ResponseResult>(hisData);
                if (result == null)
                {
                    _logger.Error("接口调用失败,没有返回数据!接口:" + api);
                    return null;
                }
                else
                {
                    if (string.IsNullOrEmpty(result.Data.ToString()))
                    {
                        _logger.Error("没有查到病人相关数据!接口:" + api);
                        return null;
                    }
                    else
                    {
                        riskDatas = JsonConvert.DeserializeObject<List<RiskData>>(result.Data.ToString());
                        return riskDatas;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error("查询患者风险数据接口调用失败：" + ex.Message);
                return null;
            }
        }
    }
}