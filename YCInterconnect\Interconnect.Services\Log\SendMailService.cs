﻿using System;
using Interconnect.Services.Interface;
using MimeKit;
using NLog;
using Interconnect.ViewModels;
using System.Collections.Generic;
using Interconnect.Data.Interface;
using System.Threading.Tasks;
using Interconnect.Models;
using Microsoft.EntityFrameworkCore;
using Interconnect.Data.Context;
using Microsoft.Extensions.Options; 
using Medical.Common;
using Medical.Data.Interface;
using Arch.EntityFrameworkCore.UnitOfWork;

namespace Interconnect.Services
{
    public class SendMailService : ISendMailService
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IMailUserDictRepository _mailUserRepository;
        private readonly IErrorLogRepository _errorLogRepository;
        private readonly IUnitOfWork<DataOutConnection> _unitOfWorkOut;
        private readonly IOptions<SystemConfig> _config;
        private readonly ICommonHelper _commonHelper;
        private readonly IJobLogService _jobLogService;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;

        /// <summary>
        /// 医疗院代码
        /// </summary>
        private readonly string HOSPITALID = "";
        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";

        public SendMailService(IMailUserDictRepository mailUserDictRepository
            , IErrorLogRepository errorLogRepository
            , IUnitOfWork<DataOutConnection> unitOfWork
            , IOptions<SystemConfig> options
            , ICommonHelper commonHelper
            , IJobLogService jobLogService
            , IAppConfigSettingRepository  appConfigSettingRepository
            )
        {
            _mailUserRepository = mailUserDictRepository;
            _errorLogRepository = errorLogRepository;
            _unitOfWorkOut = unitOfWork;
            _config = options;
            _commonHelper = commonHelper;
            _jobLogService = jobLogService;
            _appConfigSettingRepository = appConfigSettingRepository;
            HOSPITALID = _config.Value.HospitalID;
        }



        public bool SendMailMainAsync()
        {
           // 生成一个日志序号
            var JobId = "1000";
            //确认作业是否可以被执行
           var JobStatus = _jobLogService.GetJobStatus(JobId, "邮件发送","");
           // 不能执行，返回
            if (!JobStatus)
            {
                _logger.Info("邮件发送正在执行！，停止本次作业启动！");
                return false;
            }
           // 发送邮件
            var sendFlag = SendMailDetail();
            _jobLogService.RemoveJob(JobId, "邮件发送", "");
            if (!JobStatus)
            {
                _logger.Info("更新作业同步状态失败！");
            }

            return sendFlag;
        }

        //发送邮件
        private bool SendMailDetail()
        {
            //日志内容
            var errLogList = _errorLogRepository.GetAsync();
            if (errLogList.Count<1)
            {
                return false;
            }
            //获取接受者列表
            var receptionUserList = _mailUserRepository.GetAsync();
            if (receptionUserList.Count<1)
            {
                return false;
            }
            //发送方
            var MailBase = GetMailBase();            
          
            //h汇总接收方及邮件内容
            var MailReception = GetMailReception(errLogList, receptionUserList);
            //发送邮件
            var sendFlag = false;
            try
            {
                sendFlag = _commonHelper.SendMail(MailBase, MailReception);
            }
            catch (Exception)
            {
                return false;
            }

            if (sendFlag)
            {
                try
                {
                    _unitOfWorkOut.GetRepository<ErrorLogInfo>().Update(errLogList);
                    _unitOfWorkOut.SaveChanges();
                }
                catch (Exception ex)
                {
                    _logger.Error("标记邮件已经发送标志失败" + ex.ToString());
                    return false;
                }
            }
            else
            {
                return false;
            }
            return true;
        }
        //发送方
        private MailBaseViewInfo GetMailBase()
        {
            //从配置档中获取数据 梁宝华 2020-04-29
            var sentFromUser = _appConfigSettingRepository.GetConfigSettingValue( APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "SentFromUser").Result;
            var sentFromAddress = _appConfigSettingRepository.GetConfigSettingValue( APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "SentFromAddress").Result;
            var smtpAuthorizationCode = _appConfigSettingRepository.GetConfigSettingValue( APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "SmtpAuthorizationCode").Result;
            var mailHost = _appConfigSettingRepository.GetConfigSettingValue( APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "MailHost").Result;
            var smtpUserName = _appConfigSettingRepository.GetConfigSettingValue( APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "SmtpUserName").Result;
            var mailHostPost = _appConfigSettingRepository.GetConfigSettingValue( APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "MailHostPost").Result;

            var MailBase = new MailBaseViewInfo()
            {
                SentFromUser = sentFromUser
                ,
                SentFromAddress = sentFromAddress
                ,
                SmtpAuthorizationCode = smtpAuthorizationCode
                ,
                MailHost = mailHost
                ,
                MailHostPost = StringCheck.IsNumeric(mailHostPost) ? int.Parse(mailHostPost) : 0
                ,
                SmtpUserName = smtpUserName
            };
            return MailBase;
        }
        //日志内容
        public MailReceptionViewInfo GetMailReception(List<ErrorLogInfo> errLog, List<MailUserDictInfo> receptionUserList)
        {
            var userList = new Dictionary<string, string>();

            foreach (var item in receptionUserList)
            {
                userList.Add(item.MailAddress, item.UserName);
            }
            //获取没有发送的错误日志

            var errlogStr = "";
            foreach (var item in errLog)
            {
                if (errlogStr == "")
                {
                    errlogStr = "日志类别：" + item.LogType.ToString() + "||日志时间：" + item.DateTimes.ToString("G") + "||日志内容:" + item.Logs;
                }
                else
                {
                    errlogStr = errlogStr + "<br/>" + "日志类别：" + item.LogType.ToString()+ "||日志时间：" + item.DateTimes.ToString("G") + "||日志内容:" + item.Logs;
                }
                item.SendDate = DateTime.Now;
                item.SendFlag = "*";
            }
            var MailReception = new MailReceptionViewInfo()
            {
                ReceptionUser = userList
                ,
                MailTitle = "作业同步错误"
                ,
                TextParts = new TextPart("plain") { Text = errlogStr }
            };
            return MailReception;
        }
    }
}
