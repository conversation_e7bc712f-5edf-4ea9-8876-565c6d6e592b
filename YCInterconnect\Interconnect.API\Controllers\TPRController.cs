﻿using Interconnect.Models;
using Interconnect.Services.Interface;
using Medical.Common;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using NLog;
using System;
using System.Threading.Tasks;

namespace Interconnect.API.Controllers
{
    /// <summary>
    /// 信息同步
    /// </summary>
    [Produces("application/json")]
    [Route("api/TPR")]
    [EnableCors("any")]
    public class TPRController : Controller
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ITPRService _tPRService;
        private readonly ISendMailService _sendMailService;
        private readonly IEmployeeStationSwitchService _employeeStationSwitchService;
        private readonly IJobLogService _jobLogService;
        private readonly ICommonHelper _commonHelper;
        private readonly IEmployeeService _employeeService;
        private readonly IOptions<SystemConfig> _config;
        private readonly IOptions<Medical.ViewModels.SystemConfig> _medicaConfig;
        private readonly ITestScheduleService _testScheduleService;
        /// <summary>
        ///
        /// </summary>
        /// <param name="tPRService"></param>
        /// <param name="sendMailService"></param>
        /// <param name="employeeStationSwitchService"></param>
        /// <param name="jobLogService"></param>
        /// <param name="commonHelper"></param>
        /// <param name="employeeService"></param>
        /// <param name="config"></param>
        /// <param name="medicaOptions"></param>
        /// <param name="testScheduleService"></param>
        public TPRController(
             ITPRService tPRService
            , ISendMailService sendMailService
            , IEmployeeStationSwitchService employeeStationSwitchService
            , IJobLogService jobLogService
            , ICommonHelper commonHelper
            , IEmployeeService employeeService
            , IOptions<SystemConfig> config
            , IOptions<Medical.ViewModels.SystemConfig> medicaOptions
            , ITestScheduleService testScheduleService
            )
        {
            _tPRService = tPRService;
            _sendMailService = sendMailService;
            _employeeStationSwitchService = employeeStationSwitchService;
            _jobLogService = jobLogService;
            _commonHelper = commonHelper;
            _employeeService = employeeService;
            _config = config;
            _medicaConfig = medicaOptions;
            _medicaConfig.Value.HospitalID = _config.Value.HospitalID;
            _testScheduleService = testScheduleService;
        }

        /// <summary>
        /// 同步生命体征数据
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("SyncTPRSchedule")]
        public async Task<IActionResult> SyncTPRScheduleAsync(DateTime StartDate, DateTime EndDate)
        {
            var hospitalID = _config.Value.HospitalID;
            var resultSrt = await _tPRService.GetTprscheduleListAsync(StartDate, EndDate, hospitalID);
            var result = new ResponseResult();
            foreach (var item in resultSrt)
            {
                if (string.IsNullOrEmpty(item.Key))
                {
                    result.Data = item.Value;
                }
                else
                {
                    result.Message = item.Key.Trim();
                    result.Code = 0;
                }
                break;
            }
            return result.ToJson();
        }


    }
}