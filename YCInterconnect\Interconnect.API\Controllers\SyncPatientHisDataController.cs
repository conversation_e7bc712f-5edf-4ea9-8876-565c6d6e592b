﻿using Interconnect.Services.Interface;
using Medical.Common;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace Interconnect.API.Controllers
{
    /// <summary>
    /// 信息同步
    /// </summary>
    [Produces("application/json")]
    [Route("api/SyncPatient")]
    [EnableCors("any")]
    public class SyncPatientHisDataController : Controller
    {
        private readonly ISyncPatientHISDataService _syncPatientHISDataService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="syncPatientHISDataService"></param>
        public SyncPatientHisDataController(
             ISyncPatientHISDataService syncPatientHISDataService
            )
        {
            _syncPatientHISDataService = syncPatientHISDataService;
        }

        /// <summary>
        /// 同步患者的生命体征排程数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncPatientScheduleData")]
        public async Task<IActionResult> SyncPatientScheduleData()
        {
            var resultSrt = await _syncPatientHISDataService.SyncPatientScheduleData();
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }
        /// <summary>
        /// 同步患者病情观察数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncPatientScheduleMeasures")]
        public async Task<IActionResult> SyncPatientScheduleMeasures()
        {
            var resultSrt = await _syncPatientHISDataService.SyncPatientScheduleMeasures();
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }
        /// <summary>
        /// 同步患者出入量数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncPatientIO")]
        public async Task<IActionResult> SyncPatientIO()
        {
            var resultSrt = await _syncPatientHISDataService.SyncPatientIO();
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }
    }
}