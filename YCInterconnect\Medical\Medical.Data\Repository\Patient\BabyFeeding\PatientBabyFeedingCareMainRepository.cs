﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientBabyFeedingCareMainRepository : IPatientBabyFeedingCareMainRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientBabyFeedingCareMainRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }
        #region



        /// <summary>
        /// 根据recordID获取新生儿数量
        /// </summary>
        /// <param name="recordID">分娩照护主记录ID</param>
        /// <returns></returns>
        //public async Task<List<byte?>> GetNewBornNumByRecordID(string recordID)
        //{
        //    return await _medicalDbContext.PatientDeliveryCareMainInfos.Where(t => t.PatientDeliveryRecordID == recordID && t.DeleteFlag != "*" 
        //    && t.NewBornNum.HasValue).Select(m=>m.NewBornNum).ToListAsync();
        //}
        /// <summary>
        /// 获取新生儿信息
        /// </summary>
        /// <param name="recordID">分娩照护记录ID</param>
        /// <returns></returns>
        //public async Task<List<PatientDeliveryCareMainInfo>> GetNewBornInfo(string recordID)
        //{
        //    return await _medicalDbContext.PatientDeliveryCareMainInfos.Where(t => t.PatientDeliveryRecordID == recordID 
        //    && t.DeleteFlag != "*" && t.NewBornNum.HasValue).ToListAsync();
        //}
        #endregion
        /// <summary>
        /// 获取记录最后一次照护记录
        /// </summary>
        /// <param name="recordID">分娩记录ID</param>
        /// <returns></returns>
        public async Task<PatientBabyFeedingCareMainInfo> GetLastCare(string recordID)
        {
            var list = await _medicalDbContext.PatientBabyFeedingCareMainInfos.Where(t => t.BabyFeedingRecordID == recordID && t.DeleteFlag != "*")
                .OrderBy(m => m.NumberOfAssessment).ToListAsync();
            if (list.Count == 0)
            {
                return null;
            }
            return list.Last();
        }
        /// <summary>
        /// 根据喂养记录获取照护主记录
        /// </summary>
        /// <param name="recordID">婴儿喂养记录ID</param>
        /// <returns></returns>
        public async Task<List<PatientBabyFeedingCareMainInfo>> GetByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientBabyFeedingCareMainInfos.Where(t => t.BabyFeedingRecordID == recordID && t.DeleteFlag != "*").OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).ToListAsync();
        }
        /// <summary>
        /// 根据ID获取对应数据
        /// </summary>
        /// <param name="id">婴儿喂养照护主记录ID</param>
        /// <returns></returns>
        public async Task<PatientBabyFeedingCareMainInfo> GetByID(string id)
        {
            return await _medicalDbContext.PatientBabyFeedingCareMainInfos.Where(t => t.BabyFeedingCareMainID == id && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        public async Task<PatientBabyFeedingCareMainInfo> GetByRecordCode(string recordID, string recordsCode)
        {
            return await _medicalDbContext.PatientBabyFeedingCareMainInfos.Where(t => t.BabyFeedingRecordID == recordID && t.RecordsCode == recordsCode && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获得时间段内的给养记录
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="start"></param>
        /// <param name="end"></param>
        /// <returns></returns>
        public async Task<List<PatientBabyFeedingCareMainInfo>> GetBabyFeedingByInpatientIDAsync(string inpatientID, DateTime start, DateTime end)
        {
            //var endDate = end.AddDays( 1 );
            //var result =  await _medicalDbContext.PatientBabyFeedingCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" &&  m.AssessDate >= start && m.AssessDate <= endDate ).
            //    Select(m => new PatientBabyFeedingCareMainInfo
            //    {
            //        AssessDate = m.AssessDate,
            //        AssessTime = m.AssessTime,
            //        FeedingType = m.FeedingType,
            //        Amount = m.Amount
            //    }).ToListAsync();
            var result = await _medicalDbContext.PatientBabyFeedingCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" && m.ShiftDate == start.Date).
                Select(m => new PatientBabyFeedingCareMainInfo
                {
                    AssessDate = m.AssessDate,
                    AssessTime = m.AssessTime,
                    FeedingType = m.FeedingType,
                    Amount = m.Amount
                }).ToListAsync();
            return result.Where(m => m.AssessDate.Add(m.AssessTime) >= start && m.AssessDate.Add(m.AssessTime) <= end).OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).ToList();
        }

        /// <summary>
        /// 根据病人id获取数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientBabyFeedingCareMainInfo>> GetByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientBabyFeedingCareMainInfos.Where(t => t.InpatientID == inpatientID && t.DeleteFlag != "*").OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).ToListAsync();
        }
        /// <summary>
        /// 根据病人id获取数据某些字段
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientBabyFeedingCareMainInfo>> GetSomeValueByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientBabyFeedingCareMainInfos.Where(t => t.InpatientID == inpatientID && t.DeleteFlag != "*").Select(m => new PatientBabyFeedingCareMainInfo
            {
                AssessDate = m.AssessDate,
                AssessTime = m.AssessTime,
                BabyFeedingCareMainID = m.BabyFeedingCareMainID,
                Amount = m.Amount,
                FeedingMinute_L = m.FeedingMinute_L,
                FeedingMinute_R = m.FeedingMinute_R
            }).OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).ToListAsync();
        }

        /// <summary>
        /// 获取指定病人一段时间内最新的维护内容
        /// </summary>
        /// <param name="caseNumbers">病人本次住院号</param>
        /// <param name="shift">班别</param>
        /// <param name="shiftDate">班别日期</param>
        /// <returns></returns>
        public async Task<string> GetLatestCareMainByShift(List<string> caseNumbers, string shift, DateTime shiftDate)
        {
            return await _medicalDbContext.PatientBabyFeedingCareMainInfos.Where(m => caseNumbers.Contains(m.CaseNumber) && m.Shift == shift
            && m.ShiftDate == shiftDate).OrderByDescending(m => m.AssessDate)
                .ThenByDescending(m => m.AssessTime)
                .Select(m => m.CareIntervention)
                .FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取班内的评估照护数据
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="shift">班别</param>
        /// <param name="shiftDate">班别日期</param>
        /// <returns></returns>
        public async Task<List<string>> GetCareInterventionsByShift(string inpatientID, string shift, DateTime shiftDate)
        {
            return await _medicalDbContext.PatientBabyFeedingCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" &&
            m.Shift == shift && m.ShiftDate == shiftDate && m.BringToShift == true).Select(m => m.CareIntervention).ToListAsync();
        }
        /// <summary>
        /// 根据来源获取喂养方式
        /// </summary>
        /// <param name="sourceID">来源序号</param>
        /// <param name="sourceType">来源类别</param>
        /// <returns></returns>
        public async Task<string> GetFeedingTypeBySource(string sourceID, string sourceType)
        {
            return await _medicalDbContext.PatientBabyFeedingCareMainInfos.Where(m =>
            m.SourceID == sourceID && m.SourceType == sourceType && m.DeleteFlag != "*")
                .Select(m => m.FeedingType).FirstOrDefaultAsync();
        }
    }
}