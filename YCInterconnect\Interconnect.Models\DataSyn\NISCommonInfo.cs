﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("NISCommon")]
    public class NISCommonInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///类别
        ///</summary>
        public string Type { get; set; }
        /// <summary>
        ///住院号
        ///</summary>
        public string CaseNumber { get; set; }
        /// <summary>
        ///病案号
        ///</summary>
        public string ChartNo { get; set; }
        /// <summary>
        ///身分证号
        ///</summary>
        public string IdentityID { get; set; }
        /// <summary>
        ///信息
        ///</summary>
        public string Message { get; set; }
    }
}