﻿using Interconnect.ViewModels;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Interconnect.Services.Interface
{
  public  interface INewBornService
    {
        /// <summary>
        /// 同步新生儿数据
        /// </summary>
        /// <param name="newborns"></param>
        /// <returns></returns>
        Task<bool> SyncNewBorn(List<NewBornView> newborns);
    }
}
