﻿namespace Medical.Common
{
    /// <summary>
    /// 缓存类型
    /// </summary>
    public enum CacheType
    {
        /// <summary>
        /// 0部门
        /// </summary>
        Station = 0,
        /// <summary>
        /// 1病床
        /// </summary>
        Bed = 1,
        /// <summary>
        /// 2频次
        /// </summary>
        Frequency = 2,
        /// <summary>
        /// 3护理目标
        /// </summary>
        NursingGoal = 3,
        /// <summary>
        /// 4护士班别
        /// </summary>
        NurseShift = 4,
        /// <summary>
        /// 5评估列表
        /// </summary>
        AssessList = 5,
        /// <summary>
        /// 6护理问题
        /// </summary>
        NursingProblem = 6,
        /// <summary>
        /// 7设置表
        /// </summary>
        SettingDescription = 7,
        /// <summary>
        /// 8護理措施
        /// </summary>
        NursingInterventionMain = 8,
        /// <summary>
        /// 9措施名细
        /// </summary>
        NursingInterventionDetail = 9,
        /// <summary>
        /// 10正异常检核
        /// </summary>
        AssessNormalCheck = 10,
        /// <summary>
        /// 12单位班別
        /// </summary>
        StationShift = 12,
        /// <summary>
        /// 13功能列表
        /// </summary>
        FunctionList = 13,
        /// <summary>
        /// 14相关因素
        /// </summary>
        RelatedFactor = 14,
        /// <summary>
        /// 15问题TO措施
        /// </summary>
        ProblemToIntervention = 15,
        /// <summary>
        /// 16护理评价
        /// </summary>
        NursingOutCome = 16,
        /// <summary>
        /// 17问题对实际目标
        /// </summary>
        ProblemToOutcome = 17,
        /// <summary>
        /// 18问题TO因素
        /// </summary>
        ProblemToFactor = 18,
        /// <summary>
        /// 19问题TO目标
        /// </summary>
        ProblemToGoal = 19,
        /// <summary>
        /// 20
        /// </summary>
        ProblemToCategory = 20,
        /// <summary>
        /// 21评估对应问题
        /// </summary>
        AssessToProblem = 21,
        /// <summary>
        /// 22人员基本讯息
        /// </summary>
        EmployeelData = 22,
        /// <summary>
        /// 23派班数据
        /// </summary>
        Attendance = 23,
        /// <summary>
        /// 24医嘱
        /// </summary>
        PhysicianOrder = 24,
        /// <summary>
        ///25输入输出
        /// </summary>
        IntakeOutputSetting = 25,
        /// <summary>
        /// 26评估内容
        /// </summary>
        AssessContent = 26,
        /// <summary>
        /// 27功能清单
        /// </summary>
        RoleList = 27,
        /// <summary>
        /// 28使用者权限
        /// </summary>
        UserRole = 28,
        /// <summary>
        /// 29权限清单
        /// </summary>
        AuthorityList = 29,
        /// <summary>
        /// 30权限规则
        /// </summary>
        AuthorityRoleList = 30,
        /// <summary>
        /// 31管路清单
        /// </summary>
        TubeList = 31,
        /// <summary>
        /// 身体部位清单
        /// </summary>        
        BodyPartList = 32,
        /// <summary>
        /// 33管路对身体部位
        /// </summary>
        TubeToBodyPart = 33,
        /// <summary>
        /// 34单位使用管路
        /// </summary>
        StationUseTube = 34,
        /// <summary>
        /// 35评估对应相关因素
        /// </summary>
        AssessToRelatedFactor = 35,
        /// <summary>
        /// 36检验数据对应评估项目
        /// </summary>
        TestItemToAssessList = 36,
        /// <summary>
        /// 37措施触发措施
        /// </summary>
        InterventionTriggerIntervention = 37,
        /// <summary>
        /// Profile条件
        /// </summary>
        PatientProfileCondition = 38,
        /// <summary>
        /// 医院清册
        /// </summary>
        HospitalList = 39,
        /// <summary>
        /// 医院清册
        /// </summary>
        PatientProflieRenewExclude = 40,
        /// <summary>
        /// 表单清册
        /// </summary>
        RecordsList = 41,
        /// <summary>
        /// 表单格式
        /// </summary>
        RecordsFormat = 42,
        /// <summary>
        /// 评量级距
        /// </summary>
        AssessScoreRange = 43,
        /// <summary>
        /// 科室
        /// </summary>
        Department = 44,
        /// <summary>
        /// 科室
        /// </summary>
        StationToDepartment = 45,
        /// <summary>
        /// 评估互斥
        /// </summary>
        AssessInterActon = 46,
        /// <summary>
        /// 统计报表
        /// </summary>
        StatisticsReport = 47,
        /// <summary>
        /// 统计报表明细
        /// </summary>
        StatisticsReportDetail = 48,
        /// <summary>
        /// 病历交换格式
        /// </summary>
        SwitchRecordFormat = 49,
        /// <summary>
        /// 病人标识
        /// </summary>
        PatientListIcon = 50,
        /// <summary>
        /// 排班分组
        /// </summary>
        ScheduleGroup = 51,
        /// <summary>
        /// 排班分组明
        /// </summary>
        ScheduleGroupDetail = 52,
        /// <summary>
        /// 排班规则
        /// </summary>
        ScheduleRule = 53,
        /// <summary>
        /// 
        /// </summary>
        AdministrationSetting = 54,
        /// <summary>
        /// 员工岗位
        /// </summary>
        EmployeeJob = 55,
        /// <summary>
        /// 科室岗位
        /// </summary>
        DeptmentJob = 56,
        /// <summary>
        /// 科室岗位配置
        /// </summary>
        DepartmentJobConfig = 57,
        /// <summary>
        /// 集束护理问题对应
        /// </summary>
        NursingClusterOrder = 58,
        /// <summary>
        /// 评估对应颜色
        /// </summary>
        AssessListIDToColor = 59,
        /// <summary>
        /// 评估包含内容
        /// </summary>
        AssessInclude = 60,
        /// <summary>
        /// 报表维度配置
        /// </summary>
        StatisticsDimension = 61,
        /// <summary>
        /// 统计报表链
        /// </summary>
        StatisticsLink = 62,
        /// <summary>
        /// 护嘱对应措施
        /// </summary>
        //NursingOrderToIntervention = 63,
        /// <summary>
        /// 责护岗位配置
        /// </summary>
        NurseBedSet = 64,
        /// <summary>
        /// 给药途径
        /// </summary>
        MedicationRoute = 65,
        /// <summary>
        /// 岗位
        /// </summary>
        JobArchitecture = 66,
        /// <summary>
        /// 医嘱对应评估项目
        /// </summary>
        OrderToAssessList = 67,
        /// <summary>
        /// 计算公式
        /// </summary>
        FormulaList = 68,
        /// <summary>
        /// 公式参数
        /// </summary>
        FormulaParam = 69,
        /// <summary>
        /// 输入框检核
        /// </summary>
        TextEntry = 70,
        /// <summary>
        /// 临床配置
        /// </summary>
        ClinicalSetting = 71,
        /// <summary>
        /// 评估对表单
        /// </summary>
        AssessToRecordFormat = 72,
        /// <summary>
        /// 护理电子病历
        /// </summary>
        NurseEMR = 73,
        /// 评估对表单
        /// </summary>
        DeptmentToRecordList = 74,
        /// <summary>
        /// 测量点
        /// </summary>
        MeasuringPoint = 75,
        ///<summary>
        /// 病人事件
        /// </summary>
        EventSetting = 76,
        /// <summary>
        ///  质控主题
        /// </summary>
        QCSubjactive = 77,
        /// <summary>
        ///  质控项目
        /// </summary>
        QCDictionary = 78,
        /// <summary>
        /// 质控模板
        /// </summary>
        QCCheckAppraiseContent = 79,
        /// <summary>
        /// 科室对评估
        /// </summary>
        DepartmentToAssessInfo = 80,
        /// <summary>
        /// 会诊目的字典
        /// </summary>
        ConsultGoal = 81,
        /// <summary>
        /// 会诊目的对人员
        /// </summary>
        ConsultGoalToEmployee = 82,
        /// <summary>
        /// 交班配置
        /// </summary>
        HandoverSetting = 83,
        /// <summary>
        /// 交班配置
        /// </summary>
        HandoverKeySignSetting = 84,
        /// <summary>
        /// 排出物对应属性配置
        /// </summary>
        AssessToOutputAttribute = 85,
        /// <summary>
        /// 排出物属性字典
        /// </summary>
        OutputAttribure = 86,
        /// <summary>
        /// HIS.Common使用的API配置
        /// </summary>
        APISettingInfo = 87,
        /// <summary>
        /// AppConfigSetting配置表
        /// </summary>
        AppConfigSetting = 88,
        /// <summary>
        /// 汉字拼音首字母
        /// </summary>
        PinyinIndex = 89,
        /// <summary>
        /// 风险等级
        /// </summary>
        TubeRiskLevel = 90,
        /// <summary>
        /// 统计下载
        /// </summary>
        StasticsDownload = 91,
        /// <summary>
        /// 菜单列表
        /// </summary>
        MenuList = 92,
        /// <summary>
        /// 电子病历档案
        /// </summary>
        EMRField = 93,
        /// <summary>
        /// 电子病历档案配置
        /// </summary>
        EMRRecordField = 94,
        /// <summary>
        /// 体温单数据获取配置
        /// </summary>
        TPRSetting = 95,

        /// <summary>
        /// 检验闭环流程状态
        /// </summary>
        HISClosingControlDict = 96,

        /// <summary>
        /// 导因对措施
        /// </summary>
        EtiologyToIntervention = 97,

        /// <summary>
        /// 数据交换
        /// </summary>
        CDADocumentSetting = 98,

        /// <summary>
        /// 字典映射表
        /// </summary>
        MappingDictionary = 99,

        /// <summary>
        /// 报表对身体部位
        /// </summary>
        RecordsToBodyPart = 100,

        /// <summary>
        /// 药品清单
        /// </summary>
        DrugList = 101,
        /// <summary>
        /// 诊断清单
        /// </summary>
        ICDList = 102,
        /// <summary>
        /// 组织部门字典档，在年度计划使用，医院的实际运营岗位
        /// </summary>
        OrganizationDepartment = 103,
        /// <summary>
        /// 员工基本信息字典
        /// </summary>
        EmployeeData = 104,
        /// <summary>
        /// CA 签章图片表
        /// </summary>
        EmployeeCAData = 105,
        /// <summary>
        /// 诊断对护理评估
        /// </summary>
        ICDToAssess = 106,
        /// <summary>
        /// 药物触发
        /// </summary>
        DrugTriggerSetting = 107,
        /// <summary>
        /// 电子病历清单
        /// </summary>
        EMRList = 108,

        /// <summary>
        /// 岗位分组字典
        /// </summary>
        CareGroupList = 109,
        /// <summary>
        /// 仪器对接表
        /// </summary>
        EquipmentItemToAssess = 110,

        /// <summary>
        /// 语言包
        /// </summary>
        LanguagePackage = 111,
        /// <summary>
        /// 宣教资料文件路径
        /// </summary>
        FilePath = 112,
        /// <summary>
        /// 观察措施模版
        /// </summary>
        ObserveTemplate = 113,
        /// <summary>
        /// 观察措施模版
        /// </summary>
        CascadeSetting = 114,
        /// <summary>
        /// 电子病历数据集来源主表
        /// </summary>
        EMRSource = 115,
        /// <summary>
        /// 电子病历数据集来源明细表
        /// </summary>
        EMRSourceDetail = 116,
        /// <summary>
        /// 工作提醒配置
        /// </summary>
        JobTipSetting = 117,
        /// <summary>
        /// 病人文书头信息
        /// </summary>
        PatientFilesHeader = 118,
        /// <summary>
        /// 行动装置更新版本
        /// </summary>
        MobileVersion = 119,
        /// <summary>
        /// 泵入药物字典
        /// </summary>
        PumpingDrugList = 120,
        /// <summary>
        /// 行政数据转译档
        /// </summary>
        AdministrationTranslation = 121,
        /// <summary>
        /// 跨病区派班配置
        /// </summary>
        AttendanceCross = 122,
        /// <summary>
        /// 病历文本档
        /// </summary>
        EMRDocument = 123,
        /// <summary>
        /// 排程计划
        /// </summary>
        SchedulePlan = 127,
        /// StatisticsRisk
        /// </summary>
        StatisticsRisk = 124,
        /// <summary>
        /// 系统版本记录
        /// </summary>
        SystemVersionRecord = 125,
        /// <summary>
        /// 指控字典明细表
        /// </summary>
        QCDictionaryDetail = 126,
        /// <summary>
        /// 诊断统计表
        /// </summary>
        StatisticsDiagnosis = 128,
        /// <summary>
        /// 护理目标统计
        /// </summary>
        StatisticsGoal = 129,
        /// <summary>
        /// 措施统计
        /// </summary>
        StatisticsIntervention = 130,
        /// <summary>
        /// 护患比统计
        /// </summary>
        StatisticsNursePatient = 131,
        /// <summary>
        /// 护理问题统计
        /// </summary>
        StatisticsProblem = 132,
        /// <summary>
        /// 手术字典
        /// </summary>
        OperationList = 133,
        /// <summary>
        /// 过敏字典
        /// </summary>
        AllergyBasic = 134,
        /// <summary>
        /// 
        /// </summary>
        ReportToInterventionDetail = 135,
        /// <summary>
        /// 
        /// </summary>
        ReportToInterventionMain = 136,
        /// <summary>
        /// 用户病区权限
        /// </summary>
        EmployeeDepartmentSwitch = 137,
        /// <summary>
        /// 医嘱对措施
        /// </summary>
        PhysicianToInterventions = 138,
        /// <summary>
        /// 默认勾选配置
        /// </summary>
        DetailDefaultSetting = 139,
        /// <summary>
        /// 看板配置表
        /// </summary>
        NursingBoardSetting = 145,
        /// <summary>
        /// 按钮字典表
        /// </summary>
        ButtonList = 146,
        /// <summary>
        /// 前端路由字典表
        /// </summary>
        RouterList = 147,
        /// <summary>
        /// 路由按钮关系表
        /// </summary>
        RouterUseButton = 148,
        /// <summary>
        /// 路由按钮权限表
        /// </summary>
        AuthorityRouterUseButton = 149,
        /// <summary>
        /// 字段明细对照表
        /// </summary>
        SpecialMapping = 150,
        /// <summary>
        /// 字段明细对照表
        /// </summary>
        InterventionToRecordSetting = 151,
        /// <summary>
        /// 生长曲线标准差参数表
        /// </summary>
        SDofGrowthCurve = 152,
        /// <summary>
        /// 记录单动态列精确显示描述内容
        /// </summary>
        EMRRecordFieldCondition = 153,
        /// <summary>
        /// 科室字典表
        /// </summary>
        DepartmentList = 154,
        /// <summary>
        /// 动态表格列配置
        /// </summary>
        DynamicTableSetting = 155,
        /// <summary>
        /// 关联执行配置
        /// </summary>
        RelatedExecutionSetting = 156,
        /// <summary>
        /// 评估对护理措施明细
        /// </summary>
        AssessToNursingInterventionDetail = 157,
        /// <summary>
        /// 事件变更关联设置
        /// </summary>
        EventChangeSetting = 158,
        /// <summary>
        /// 闭环配置表
        /// </summary>
        ClosingControlList = 159
    }
    public static class CacheTypeExtensions
    {
        public static string GetKey(this CacheType cacheType, SessionCommonServer _sessionCommonServer)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return cacheType.ToString() + "_H" + session.HospitalID + "_L" + session.Language.ToString();
        }
        /// <summary>
        /// 医院ID及语言使用参数  前端模板配置页面使用 其它页面不允许使用
        /// </summary>
        /// <param name="cacheType"></param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static string GetKeyNotBySession(this CacheType cacheType, string hospitalID, int language)
        {
            return cacheType.ToString() + "_H" + hospitalID + "_L" + language.ToString();
        }
    }
}