﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models.CDADocument;
using Microsoft.EntityFrameworkCore;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class CDA_VitalSignRec_ItemRepository : ICDA_VitalSignRec_ItemRepository
    {
        private readonly CDADBContext _cDADBConnect = null;
        private readonly static Logger _logger = LogManager.GetCurrentClassLogger();

        public CDA_VitalSignRec_ItemRepository(CDADBContext cDADBConnect)
        {
            _cDADBConnect = cDADBConnect;
        }

        public async Task<List<CDA_VitalSignRec_ItemInfo>> GetByDCID(string dCID)
        {
            return await _cDADBConnect.CDA_VitalSignRec_ItemInfos.Where(m => m.DCID == dCID).ToListAsync();
        }

        public async Task<bool> Save(CDA_VitalSignRec_ItemInfo data)
        {
            try
            {
                _cDADBConnect.Add(data);

                return await _cDADBConnect.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {
                _logger.Error("CDA生命体征明细表写入数据失败" + ex.ToString() + "异常数据:"
                    + Common.ListToJson.ToJson(data));

                return false;
            }
        }

        public async Task<bool> Update(CDA_VitalSignRec_ItemInfo data)
        {
            var old = await _cDADBConnect.CDA_VitalSignRec_ItemInfos.Where(m => m.DCID == data.DCID).FirstOrDefaultAsync();

            if (old == null)
            {
                return await Save(data);
            }

            try
            {
                _cDADBConnect.Entry(old).CurrentValues.SetValues(data);

                return await _cDADBConnect.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {

                _logger.Error("CDA生命体征明细表更新数据失败" + ex.ToString() + "异常数据:" + Common.ListToJson.ToJson(data));

                return false;
            }
        }
        public async Task<bool> Delete(List<CDA_VitalSignRec_ItemInfo> datas)
        {
            try
            {
                _cDADBConnect.CDA_VitalSignRec_ItemInfos.RemoveRange(datas);
                return await _cDADBConnect.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {
                _logger.Error("CDA生命体征明细表删除数据失败" + ex.ToString() + "异常数据:" + Common.ListToJson.ToJson(datas));

                return false;
            }
        }

        /// <summary>
        /// 根据主表ID查询明细数据
        /// </summary>
        /// <param name="dCIDs">主表IDs</param>
        /// <returns></returns>
        public async Task<List<CDA_VitalSignRec_ItemInfo>> GetByDCIDList(List<string> dCIDs)
        {
            return await _cDADBConnect.CDA_VitalSignRec_ItemInfos.Where(m => dCIDs.Contains(m.DCID)).AsNoTracking().ToListAsync();
        }
    }
}
