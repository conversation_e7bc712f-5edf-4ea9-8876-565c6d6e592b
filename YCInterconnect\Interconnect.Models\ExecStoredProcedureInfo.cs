﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    /// <summary>
    /// 存储过程调用参数的表字典
    /// </summary>
    [Serializable]
    [Table("ExecStoredProcedure")]
    public class ExecStoredProcedureInfo
    {
        [Key]
        [Column("NameID")]
        public int? NameID { get; set; }
        /// <summary>
        /// 执行的过程名称
        /// </summary>
        [Column("ProcedureName")]
        public string ProcedureName { get; set; }

        /// <summary>
        /// 执行说明
        /// </summary>
        [Column("Memos")]
        public string Memos { get; set; }
        /// <summary>
        /// 执行时间
        /// </summary>
        [Column("InsertDateTime")]
        public DateTime? InsertDateTime { get; set; }

        /// <summary>
        /// 执行标志
        /// </summary>
        [Column("ExecFlag")]
        public int? ExecFlag { get; set; }
        
    }

     
}
