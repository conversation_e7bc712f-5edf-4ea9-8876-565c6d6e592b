﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;



namespace Interconnect.API.Controllers
{
    /// <summary>
    /// 信息同步
    /// </summary>
    [Produces("application/json")]
    [Route("api/CreateEmr")]
    [EnableCors("any")]
    public class CreateNurseEmrController : Controller
    {
        private readonly static Logger _logger = LogManager.GetCurrentClassLogger();
        //private readonly IOptions<SystemConfig> _config;
        //private readonly IOptions<Medical.ViewModels.SystemConfig> _medicalConfig;
        //private readonly INurseEMRProfielService  _nurseEMRProfielService;
        //private readonly IAppConfigSettingService _appConfigSettingService;

       /// <summary>
       /// 
       /// </summary>
       /// <param name="config"></param>
       /// <param name="medicalConfig"></param>
       /// <param name="nurseEMRProfielService"></param>
       /// <param name="appConfigSettingService"></param>
        public CreateNurseEmrController(
           // IOptions<SystemConfig> config
           // , IOptions<Medical.ViewModels.SystemConfig> medicalConfig
           // , INurseEMRProfielService  nurseEMRProfielService
           //, IAppConfigSettingService appConfigSettingService
            )
        {
            //_config = config;
            //_medicalConfig = medicalConfig;
            //_medicalConfig.Value.HospitalID = _config.Value.HospitalID;
            //_nurseEMRProfielService = nurseEMRProfielService;
            //_appConfigSettingService = appConfigSettingService;          
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="inPatientId"></param>
        /// <param name="fileClass"></param>
        /// <returns></returns>
        //[HttpGet]
        //[Route("CreateNurseEMRMain")]
        //public async Task<IActionResult> CreateNurseEmr(string inPatientId, int fileClass)
        //{
        //    _logger.Info("获取医院及语言");
        //    var hospitalID = _config.Value.HospitalID;
        //    _medicalConfig.Value.HospitalID = hospitalID;
        //    var language = 0;
        //    var resultLanguage = await _appConfigSettingService.GetConfigSetting(hospitalID, "Configs", "Language");
        //    if (StringCheck.IsNumeric(resultLanguage))
        //    {
        //        language = int.Parse(resultLanguage);
        //    }
        //    _logger.Info("获取数据条件,医院代码：" + hospitalID + " 语言:" + language + "住院key:" + inPatientId + " 文件类别:" + fileClass.ToString());
        //    var resultData = await _nurseEMRProfielService.CreateNurseEMRMain(hospitalID, language, inPatientId, fileClass);
        //    var result = new ResponseResult
        //    {
        //        Data = resultData,
        //        Code = 1
        //    };
        //    return result.ToJson();   
        //}

    }
}
