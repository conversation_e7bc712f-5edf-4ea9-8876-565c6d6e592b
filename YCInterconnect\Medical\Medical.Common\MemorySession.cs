﻿using System;

namespace Medical.Common
{
    /// <summary>
    /// 本地内存Session
    /// </summary>
    public class MemorySession : ISession
    {
        public bool Contain(string token)
        {
            throw new NotImplementedException();
        }

        public Session Get(string token)
        {
            throw new NotImplementedException();
        }

        public bool Remove(Session session)
        {
            throw new NotImplementedException();
        }

        public bool Set(Session session)
        {
            throw new NotImplementedException();
        }
    }
}