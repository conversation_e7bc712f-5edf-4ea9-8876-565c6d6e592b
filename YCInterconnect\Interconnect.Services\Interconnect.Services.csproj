﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="DataReturn\**" />
    <EmbeddedResource Remove="DataReturn\**" />
    <None Remove="DataReturn\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Hangfire" Version="1.8.12" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Interconnect.Data.Interface\Interconnect.Data.Interface.csproj" />
    <ProjectReference Include="..\Interconnect.Data\Interconnect.Data.csproj" />
    <ProjectReference Include="..\Interconnect.Models\Interconnect.Models.csproj" />
    <ProjectReference Include="..\Interconnect.Services.Interface\Interconnect.Services.Interface.csproj" />
    <ProjectReference Include="..\Interconnect.ViewModels\Interconnect.ViewModels.csproj" />
    <ProjectReference Include="..\MedicalExternalCommon.Service\MedicalExternalCommon.Service.csproj" />
  </ItemGroup>

</Project>
