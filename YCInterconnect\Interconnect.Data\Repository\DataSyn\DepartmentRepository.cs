﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Interconnect.Data.Context;
using Interconnect.Data.Interface;
using Interconnect.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Interconnect.Data.Repository
{
    public class DepartmentRepository : IDepartmentRepository
    {
        private DataOutConnection _DataOutConnection = null;
        public DepartmentRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }

        /// <summary>
        /// 获取所有科室信息
        /// </summary>
        /// <returns></returns>
        public  List<DepartmentInfo> GetAsync()
        {
            return  _DataOutConnection.Departments.ToList();
        }
    }
}
