﻿using System.ComponentModel;

namespace Medical.Common
{
    public class Enums
    {
        /// <summary>
        /// 性别
        /// </summary>
        public enum Gender
        {
            /// <summary>
            /// 男性
            /// </summary>
            [Description("男")]
            Male = 1,
            /// <summary>
            /// 女性
            /// </summary>
            [Description("女")]
            Female = 2
        }
        /// <summary>
        /// 客户端类别
        /// </summary>
        public enum ClientType
        {
            /// <summary>
            /// PC端
            /// </summary>
            [Description("PC端")]
            PC = 1,
            /// <summary>
            /// 移动端
            /// </summary>
            [Description("移动端")]
            Mobile = 2
        }
        /// <summary>
        /// 菜单快捷类别
        /// </summary>
        public enum ShutCutType
        {
            Common = 0,
            /// <summary>
            /// 用户快捷
            /// </summary>
            [Description("用户快捷")]
            User = 1,
            /// <summary>
            /// 病人快捷
            /// </summary>
            [Description("病人快捷")]
            Patient = 2
        }
        /// <summary>
        /// 文档类型
        /// </summary>
        public class FileType
        {
            /// <summary>
            /// 首次评估 （入院评估）
            /// </summary>
            public const string FirstAssess = "入院评估单";
            /// <summary>
            /// 历次评估
            /// </summary>
            public const string HistoryAssess = "历次评估单";
            /// <summary>
            /// 护理问题
            /// </summary>
            public const string NursingProblem = "护理问题单";
            /// <summary>
            /// 护理计划
            /// </summary>
            public const string NursingPlan = "护理计划单";
            /// <summary>
            /// 风险评估
            /// </summary>
            public const string Risck = "风险评估单";
            /// <summary>
            /// 交班列表
            /// </summary>
            public const string HandoverList = "交班列表单";
            /// <summary>
            /// 交班明细
            /// </summary>
            public const string HandoverDetail = "交班明细单";
            public const string NursingRecords = "护理记录单";
            /// <summary>
            /// 输血单
            /// </summary>
            public const string Blood = "输血单";
        }
        /// <summary>
        /// 记录码
        /// </summary>
        public enum RecordsCode
        {
            /// <summary>
            /// 入院评估码
            /// </summary>
            AdmissionAssess,
            /// <summary>
            /// 历次评估
            /// </summary>
            PhysicalAssessment
        }

        public class RiskTableCode
        {
            public const string ADL = "ActivitiesDailyLiving";
        }

        public enum SealDataType
        {
            /// <summary>
            /// 护理记录
            /// </summary>
            NursingRecord = 1,
            /// <summary>
            /// 排程
            /// </summary>
            ScheduleMain = 2,
            /// <summary>
            /// 护理评估
            /// </summary>
            AssessMain = 3,
        }

        /// <summary>
        /// 操作日志事件类型
        /// </summary>
        public enum EventLogType
        {
            [Description("护理评估")]
            NursingAssess = 1,
            [Description("护理级别变化")]
            NursingLevelChange = 2,
            [Description("风险评估")]
            RiskAssess = 7,
            [Description("护理诊断")]
            NursingDiagnosis = 8,
            [Description("护理问题修改")]
            NursingProblemChange = 9,
            [Description("措施保存")]
            InterventionSave = 10,
            [Description("集束护理")]
            ClusterProblem = 11,
            [Description("排程执行")]
            ScheduleExecution = 12,
            [Description("护理记录")]
            NursingRecord = 13,
        }

        public enum OrderScheduleType
        {
            /// <summary>
            /// 医嘱开立
            /// </summary>
            [Description("医嘱开立")]
            OrderStart = 1,
            /// <summary>
            /// 医嘱执行
            /// </summary>
            [Description("医嘱执行")]
            OrderExecute = 2,
            /// <summary>
            /// 医嘱停止
            /// </summary>
            [Description("医嘱停止")]
            OrderEnd = 3
        }

        public enum AgeConditionType
        {
            [Description("年")]
            Year = 1,
            [Description("月")]
            Month = 2,
            [Description("日")]
            Day = 3
        }
    }
}