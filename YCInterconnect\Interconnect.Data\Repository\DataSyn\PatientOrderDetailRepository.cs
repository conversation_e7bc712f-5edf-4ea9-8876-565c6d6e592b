﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Interconnect.Data.Repository
{
    public class PatientOrderDetailRepository : IPatientOrderDetailRepository
    {
        private DataOutConnection _DataOutConnection = null;

        public PatientOrderDetailRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }
        /// <summary>
        /// 获取所有没有抽取的医嘱明细
        /// </summary>
        /// <param name="tongbuCount"></param>
        /// <param name="takeRows"></param>
        /// <returns></returns>
        public List<PatientOrderDetailInfo> GetAsync(int tongbuCount, int takeRows)
        {
            return  _DataOutConnection.PatientOrderDetails.Where(m => m.DataPumpFlag != "*"
            && ((m.Counts ?? 0) < tongbuCount)).Take(takeRows).ToList();
        }
    }
}