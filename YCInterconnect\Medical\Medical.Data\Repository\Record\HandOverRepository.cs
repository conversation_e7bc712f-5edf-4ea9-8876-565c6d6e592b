﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.Models.CDADocument;
using Medical.ViewModels;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class HandOverRepository : IHandOverRepository
    {
        public static Logger _logger = LogManager.GetCurrentClassLogger();
        private MedicalDbContext _medicalDbContext = null;
        private IBedListRepository _bedListRepository;

        public HandOverRepository(
              MedicalDbContext db
            , IBedListRepository bedListRepository
            )
        {
            _medicalDbContext = db;
            _bedListRepository = bedListRepository;
        }

        /// <summary>
        /// 根据交班ID获取记录
        /// </summary>
        /// <param name="handOverID">交班ID</param>
        /// <returns></returns>
        public async Task<HandoverInfo> GetByIDAsync(string handOverID)
        {
            return await _medicalDbContext.HandoverInfos
                .Where(m => m.ID == handOverID).FirstOrDefaultAsync();
        }
        //保存交班数据，对数据进行添加修改时不再调用调用DetectChanges方法,进行扫描上下中所有实体并将当前值和快照中的值进行比较
        public async Task<bool> SaveHandoverInfos(HandoverInfo handoverInfos)
        {
            try
            {
                _medicalDbContext.ChangeTracker.AutoDetectChangesEnabled = false;
                await _medicalDbContext.HandoverInfos.AddAsync(handoverInfos);
                await _medicalDbContext.SaveChangesAsync();
                _medicalDbContext.ChangeTracker.AutoDetectChangesEnabled = true;
            }
            catch (Exception ex)
            {
                _logger.Error("保存交班数据失败" + ex.ToString() + "内容" + ListToJson.ToJson(handoverInfos));
                return false;
            }
            return true;
        }

        public async Task<bool> DeleteHandoverInfos(string handoverID)
        {
            try
            {
                var handoverInfos = new HandoverInfo() { ID = handoverID };//id对应数据库中的id
                _medicalDbContext.HandoverInfos.Attach(handoverInfos);
                _medicalDbContext.HandoverInfos.Remove(handoverInfos);
                await _medicalDbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.Error("删除交班数据失败" + ex.ToString() + "handoverID：" + handoverID);
                return false;
            }
            return true;
        }
        /// <summary>
        /// 获取病人所有交班报告
        /// </summary>
        /// <param name="hospitalID">医院代码</param>
        /// <param name="inPatientID">病人在院号</param>
        /// <returns></returns>
        public async Task<List<HandoverInfo>> GetByPatientAsync(string hospitalID, string inPatientID, bool flag = true)
        {
            if (flag)
            {
                return await _medicalDbContext.HandoverInfos
                .Where(m => m.HospitalID == hospitalID
                    && m.InpatientID == inPatientID
                    && m.DeleteFlag != "*").ToListAsync();
            }
            else
            {
                return await _medicalDbContext.HandoverInfos
                .Where(m => m.HospitalID == hospitalID
                    && m.InpatientID == inPatientID
                    && m.DeleteFlag != "*" && m.EMRFlag != "*").ToListAsync();
            }
        }

        /// <summary>
        /// 获取上次交班记录
        /// </summary>
        /// <param name="stationID">单位代码</param>
        /// <param name="inPatientID">病人在院号</param>
        /// <returns></returns>
        public async Task<HandoverInfo> GetLast(int stationID, string inpatientID)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.InpatientID == inpatientID && m.StationID == stationID && m.DeleteFlag == "")
                       .OrderByDescending(m => m.HandoffDay).OrderByDescending(m => m.HandoffTime).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取单位班次交班记录
        /// </summary>
        /// <param name="hospitalID">医疗院所序号</param>
        /// <param name="shift">交班班别</param>
        /// <param name="handOverDate">交班时间</param>
        /// <returns>病人住院记录</returns>
        public async Task<List<HandoverInfo>> GetHandOvers(string hospitalID, int stationID, string shift, DateTime handOverDate)
        {
            return await _medicalDbContext.HandoverInfos
                .Where(m => m.HospitalID == hospitalID
                    && m.StationID == stationID
                    && m.ShiftDate == handOverDate
                    && m.HandoverShift == shift.Trim()
                    && m.DeleteFlag != "*").ToListAsync();
        }
        //TODO ,因上海中山只使用班内交班，所以暂时排除，后续迭代调整
        public async Task<List<HandoverInfo>> GetHandOvers(string hospitalID, int stationID, string shift, DateTime handOverDate, string handoverType)
        {
            return await _medicalDbContext.HandoverInfos
                .Where(m => m.HospitalID == hospitalID
                    && m.StationID == stationID
                    && m.ShiftDate == handOverDate
                    && m.HandoverShift == shift.Trim()
                    && m.HandoverType == handoverType
                    && m.RecordsCode != "TurnHandover"
                    && m.DeleteFlag != "*")
                .Select(m => new HandoverInfo
                {
                    ID = m.ID,
                    HospitalID = m.HospitalID,
                    HandoffNurseID = m.HandoffNurseID,
                    HandoffDay = m.HandoffDay,
                    HandoffTime = m.HandoffTime,
                    HandonNurseID = m.HandonNurseID,
                    HandonDate = m.HandonDate,
                    HandonTime = m.HandonTime,
                    ShiftDate = m.ShiftDate,
                    HandoverShift = m.HandoverShift,
                    InpatientID = m.InpatientID,
                    PatientID = m.PatientID,
                    StationID = m.StationID,
                    BedID = m.BedID,
                    CaseNumber = m.CaseNumber,
                    ChartNo = m.ChartNo,
                    BedNumber = m.BedNumber,
                    Situation = m.Situation,
                    Background = m.Background,
                    Assement = m.Assement,
                    Recommendation = m.Recommendation,
                    KeySign = m.KeySign,
                    EMRFlag = m.EMRFlag,
                    DepartmentListID = m.DepartmentListID,
                    HandOnDepartmentListID = m.HandOnDepartmentListID,
                    HandOnStationID = m.HandOnStationID,
                    RecordsCode = m.RecordsCode,
                    Content = m.Content,
                    AddDateTime = m.AddDateTime,
                    EndDate = m.EndDate,
                    EndTime = m.EndTime,
                    DataPumpDate = m.DataPumpDate,
                    DataPumpFlag = m.DataPumpFlag,
                    SourceID = m.SourceID,
                    Status = m.Status,
                    HandoverType = m.HandoverType,
                    ModifyPersonID = m.ModifyPersonID,
                    ModifyDate = m.ModifyDate,
                    DeleteFlag = m.DeleteFlag,
                }).ToListAsync();
        }

        /// <summary>
        /// 交班
        /// </summary>
        /// <param name="handoverInfo">交班对象</param>
        /// <returns></returns>
        public async Task<bool> HandOff(HandoverInfo handoverInfo)
        {
            if (handoverInfo == null)
            {
                return false;
            }
            var inPatientData = await _medicalDbContext.InpatientDatas
                .Where(m => m.ID == handoverInfo.InpatientID.Trim()
                && m.StationID == handoverInfo.StationID).AsNoTracking().FirstOrDefaultAsync();
            if (inPatientData == null)
                return false;

            //handoverInfo.HandoffNurseID = handoverInfo.HandoffNurseID;
            //handoverInfo.HandoffDay = handoverInfo.HandoffDay;

            handoverInfo.HandonNurseID = "";
            //handoverInfo.HandonDate = DateTime.MinValue.Date;
            //handoverInfo.HandonTime = DateTime.MinValue.TimeOfDay;

            handoverInfo.BedID = inPatientData.BedID;
            handoverInfo.BedNumber = inPatientData.BedNumber;
            handoverInfo.CaseNumber = inPatientData.CaseNumber;
            handoverInfo.ChartNo = inPatientData.ChartNo;
            handoverInfo.ModifyDate = DateTime.Now;
            handoverInfo.ModifyPersonID = handoverInfo.HandoffNurseID;
            handoverInfo.DeleteFlag = "";

            _medicalDbContext.HandoverInfos.Add(handoverInfo);
            return await _medicalDbContext.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 接班
        /// </summary>
        /// <param name="handOverID"></param>
        /// <param name="HandonNurseID"></param>
        /// <returns></returns>
        public async Task<bool> HandOn(string handOverID, string HandonNurseID)
        {
            var result = await _medicalDbContext.HandoverInfos.
                Where(m => m.ID == handOverID.Trim()).SingleOrDefaultAsync();
            if (result == null)
            {
                return false;
            }
            result.HandonDate = DateTime.Now.Date;
            result.HandonTime = DateTime.Now.TimeOfDay;
            result.HandonNurseID = HandonNurseID;
            result.ModifyDate = DateTime.Now;
            result.ModifyPersonID = HandonNurseID;

            _medicalDbContext.HandoverInfos.Update(result);

            return await _medicalDbContext.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 修改交班内容
        /// </summary>
        /// <param name="handoverInfo">交班对象</param>
        /// <returns></returns>
        public async Task<bool> Modify(HandoverInfo handoverInfo)
        {
            var result = await _medicalDbContext.HandoverInfos.
                Where(m => m.ID == handoverInfo.ID.Trim()).SingleOrDefaultAsync();
            if (result == null)
            {
                return false;
            }
            result.Situation = handoverInfo.Situation;
            result.Background = handoverInfo.Background;
            result.Assement = handoverInfo.Assement;
            result.Recommendation = handoverInfo.Recommendation;

            result.BodyPartImage = handoverInfo.BodyPartImage;

            result.ModifyDate = DateTime.Now;
            result.ModifyPersonID = handoverInfo.ModifyPersonID;

            _medicalDbContext.HandoverInfos.Update(result);

            return await _medicalDbContext.SaveChangesAsync() > 0;
        }

        #region --获取交班记录明细

        /// <summary>
        /// 获取交班记录明细
        /// </summary>
        /// <param name="handOverID">交班ID</param>
        /// <returns></returns>
        public async Task<HandoverInfo> GetDetail(string handOverID)
        {
            return await _medicalDbContext.HandoverInfos.
                 Where(m => m.ID == handOverID.Trim()).FirstOrDefaultAsync();
        }

        public async Task<HandoverInfo> GetByHandoverIDView(string handOverID)
        {
            return await _medicalDbContext.HandoverInfos.
                Where(m => m.ID == handOverID.Trim()).Select(m => new HandoverInfo
                {
                    InpatientID = m.InpatientID,
                    ID = m.ID
                }
                ).FirstOrDefaultAsync();
        }

        #endregion --获取交班记录明细

        #region 获取转运交接班

        public async Task<HandoverInfo> GetHandoverTransfer(string handOverID, string handoverType)
        {
            var result = await _medicalDbContext.HandoverInfos.
                Where(m => m.ID == handOverID && m.RecordsCode == handoverType).ToListAsync();
            if (result == null || result.Count == 0)
            {
                return null;
            }
            return result[0];
        }

        #endregion 获取转运交接班

        public async Task<List<HandoverInfo>> GetHandOvers(string[] handOverIDs)
        {
            var handoverList = new List<HandoverInfo>();
            for (int i = 0; i < handOverIDs.Length; i++)
            {
                var tempList = await _medicalDbContext.HandoverInfos.
                Where(m => m.ID == handOverIDs[i] && m.DeleteFlag != "*").ToListAsync();
                handoverList = handoverList.Union(tempList).ToList();
            }
            return handoverList;
        }

        public async Task<List<string>> GetByInpatientIDAndShift(string inpatientID, string recordsCode)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.InpatientID == inpatientID && m.RecordsCode == recordsCode && m.DeleteFlag != "*")
                .Select(m => m.ID).ToListAsync();
        }

        //获取抽档数据
        public async Task<List<HandoverInfo>> GetPumpGearHandover(int number)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.EMRFlag == null).Take(number).ToListAsync();
        }

        public async Task<List<string>> GetUnHandon(List<string> inpatientIDs)
        {
            List<string> unHandon = new List<string>();

            foreach (var inpatientID in inpatientIDs)
            {
                var data = await _medicalDbContext.HandoverInfos.Where(m => m.InpatientID == inpatientID
                 && m.HandonDate == null && m.DeleteFlag != "*").ToListAsync();

                if (data.Count == 0)
                {
                    continue;
                }
                unHandon.Add(inpatientID);
            }
            return unHandon;
        }

        public async Task<HandoverInfo> GetUnHandonData(string inpatientID)
        {
            return await _medicalDbContext.HandoverInfos.Where(
                m => m.InpatientID == inpatientID && m.HandonDate == null).FirstOrDefaultAsync();
        }

        public async Task<List<HandoverInfo>> GetInClassUnHandonDataByNurse(DateTime shiftDate, string handoverType, string nurseID)
        {
            var data = await _medicalDbContext.HandoverInfos.Where(m => m.ShiftDate == shiftDate
            && m.RecordsCode == handoverType && m.HandoffNurseID == nurseID && m.HandonNurseID == "" && m.DeleteFlag != "*").ToListAsync();

            return data;
        }

        public async Task<List<HandoverInfo>> GetInClassUnHandonData(DateTime shiftDate, string handoverType)
        {
            var data = await _medicalDbContext.HandoverInfos.Where(m => m.ShiftDate == shiftDate && m.HandonDate == null
            && m.RecordsCode == handoverType && m.DeleteFlag != "*").Select(m => new HandoverInfo
            {
                ID = m.ID,
                HospitalID = m.HospitalID,
                HandoffNurseID = m.HandoffNurseID,
                HandoffDay = m.HandoffDay,
                HandoffTime = m.HandoffTime,
                HandonNurseID = m.HandonNurseID,
                HandonDate = m.HandonDate,
                HandonTime = m.HandonTime,
                ShiftDate = m.ShiftDate,
                HandoverShift = m.HandoverShift,
                InpatientID = m.InpatientID,
                PatientID = m.PatientID,
                StationID = m.StationID,
                BedID = m.BedID,
                Situation = m.Situation,
                Background = m.Background,
                Assement = m.Assement,
                Recommendation = m.Recommendation,
                BedNumber = m.BedNumber,
                ModifyDate = m.ModifyDate,
                RecordsCode = m.RecordsCode,
                HandoverType = m.HandoverType,
            }).OrderByDescending(m => m.HandoffDay).ThenBy(m => m.HandoffTime).ThenBy(m => m.BedID).ToListAsync();

            return data;
        }

        public async Task<string> GetLastHistory(string inpatientID, DateTime handoffDay, TimeSpan handoffTime)
        {
            var data = await _medicalDbContext.HandoverInfos.Where(m => m.InpatientID == inpatientID
                                    && ((m.HandoffDay == handoffDay && m.HandoffTime < handoffTime)
                                    || m.HandoffDay < handoffDay)).OrderByDescending(m => m.HandoffDay)
                                    .ThenByDescending(m => m.HandoffTime).Select(m => m.Background).FirstOrDefaultAsync();

            return data;
        }
        /// <summary>
        /// 取得小于时间点最后一次建议内容
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="handoffDay"></param>
        /// <param name="handoffTime"></param>
        /// <param name="handoverID"></param>
        /// <param name="recordsCode"></param>
        /// <returns></returns>
        public async Task<string> GetLastRecomment(string inpatientID, DateTime handoffDay, TimeSpan handoffTime, string handoverID, string recordsCode)
        {
            var oldHandover = new HandoverInfo();
            if (string.IsNullOrEmpty(handoverID))
            {
                oldHandover = await _medicalDbContext.HandoverInfos.Where(m => m.InpatientID == inpatientID

                                  && m.DeleteFlag != "*"
                                  && m.RecordsCode == recordsCode
                                  && ((m.HandoffDay == handoffDay && m.HandoffTime < handoffTime)
                                  || m.HandoffDay < handoffDay))
                                  .OrderByDescending(m => m.HandoffDay)
                                  .ThenByDescending(m => m.HandoffTime).Select(m => new HandoverInfo
                                  {
                                      ID = m.ID,
                                      Recommendation = m.Recommendation
                                  }).FirstOrDefaultAsync();
            }
            else
            {
                oldHandover = await _medicalDbContext.HandoverInfos.Where(m => m.InpatientID == inpatientID
                                   && m.DeleteFlag != "*"
                                   && m.RecordsCode == recordsCode
                                   && m.ID != handoverID
                                   && ((m.HandoffDay == handoffDay && m.HandoffTime < handoffTime)
                                   || m.HandoffDay < handoffDay))
                                   .OrderByDescending(m => m.HandoffDay)
                                   .ThenByDescending(m => m.HandoffTime).Select(m => new HandoverInfo
                                   {
                                       ID = m.ID,
                                       Recommendation = m.Recommendation
                                   }).FirstOrDefaultAsync();
            }
            if (oldHandover == null || string.IsNullOrEmpty(oldHandover.ID))
            {
                return "";
            }
            var handoverContentRecommendation = await _medicalDbContext.PatientHandoverContentsInfos.Where(m => m.HandoverID == oldHandover.ID && !string.IsNullOrEmpty(m.Recommendation)).OrderBy(m => m.ModifyDate).Select(m => m.Recommendation).LastOrDefaultAsync();
            if (handoverContentRecommendation == null && oldHandover.Recommendation == null)
            {
                return "";
            }
            if (handoverContentRecommendation == null)
            {
                return oldHandover.Recommendation;
            }
            if (oldHandover.Recommendation == null)
            {
                return handoverContentRecommendation;
            }
            return "";
        }

        public async Task<string> GetLastBackGround(string inpatientID, DateTime handoffDay, TimeSpan handoffTime)
        {
            var data = await _medicalDbContext.HandoverInfos.Where(m => m.InpatientID == inpatientID
                                    && ((m.HandoffDay == handoffDay && m.HandoffTime < handoffTime)
                                    || m.HandoffDay < handoffDay)).OrderByDescending(m => m.HandoffDay)
                                    .ThenByDescending(m => m.HandoffTime).Select(m => m.Background).FirstOrDefaultAsync();

            return data;
        }

        public async Task<List<HandoveRecordMainView>> GetHandoverRecordMain(string hospitalID, int stationID, DateTime dateTime, string shift)
        {
            var query = await (from m in _medicalDbContext.HandoverInfos
                               join n in _medicalDbContext.InpatientDatas on m.InpatientID equals n.ID
                               join o in _medicalDbContext.Users on m.HandoffNurseID equals o.UserID
                               join p in _medicalDbContext.Users on m.HandonNurseID equals p.UserID
                               join q in _medicalDbContext.PatientBasicDatas on m.PatientID equals q.PatientID
                               join r in _medicalDbContext.StationList on m.StationID equals r.ID
                               where m.HospitalID == hospitalID && m.HandoffDay == dateTime && m.StationID == stationID && m.HandoverShift == shift
                              && m.DeleteFlag != "*" && n.DeleteFlag != "*" && o.DeleteFlag != "*" && p.DeleteFlag != "*" && r.DeleteFlag != "*" && q.DeleteFlag != "*"
                              && p.HospitalID == hospitalID && r.HospitalID == hospitalID && o.HospitalID == hospitalID
                          && r.HospitalID == hospitalID && q.HospitalID == hospitalID
                               select new HandoveRecordMainView
                               {
                                   Important = "",
                                   HandoverID = m.ID,
                                   Station = r.StationName,
                                   Shift = shift,
                                   ShiftDate = m.ShiftDate,
                                   BedNumber = m.BedNumber,
                                   PatientName = q.PatientName,
                                   RecordsCode = m.RecordsCode,
                                   StartDate = m.HandoffDay,
                                   StartTime = m.HandoffTime.Value,
                                   EndDate = m.HandonDate.Value,
                                   EndTime = m.HandonTime.Value,
                                   HandoffNurse = o.Name,
                                   HandonNurse = p.Name,
                                   HandonDate = m.HandonDate.Value,
                                   HandonTime = m.HandonTime.Value
                               }).ToListAsync();
            return query;
        }

        //获取指定病区的所有交班记录
        public async Task<List<HandoverBedSortView>> GetStationHandover(DateTime shiftDate, int stationID, string recordsCode)
        {
            var query = await (from m in _medicalDbContext.HandoverInfos
                               where m.ShiftDate == shiftDate && m.StationID == stationID && m.DeleteFlag != "*"
                               join o in _medicalDbContext.PatientBasicDatas on m.PatientID equals o.PatientID
                               where o.DeleteFlag != "*"
                               select new HandoverBedSortView
                               {
                                   ID = m.ID,
                                   HospitalID = m.HospitalID,
                                   HandoffNurseID = m.HandoffNurseID,
                                   HandoffDay = m.HandoffDay,
                                   HandoffTime = m.HandoffTime,
                                   HandonNurseID = m.HandonNurseID,
                                   HandonDate = m.HandonDate,
                                   HandonTime = m.HandonTime,
                                   ShiftDate = m.ShiftDate,
                                   HandoverShift = m.HandoverShift,
                                   InpatientID = m.InpatientID,
                                   PatientID = m.PatientID,
                                   StationID = m.StationID,
                                   Background = m.Background,
                                   Assement = m.Assement,
                                   Recommendation = m.Recommendation,
                                   BedID = m.BedID,
                                   Situation = m.Situation,
                                   BedNumber = m.BedNumber,
                                   PatientName = o.PatientName
                               }).ToListAsync();
            var bedList = await _bedListRepository.GetAllAsync<BedListInfo>();
            query = (from m in query
                     join n in bedList on m.BedID equals n.ID
                     select new HandoverBedSortView
                     {
                         ID = m.ID,
                         HospitalID = m.HospitalID,
                         HandoffNurseID = m.HandoffNurseID,
                         HandoffDay = m.HandoffDay,
                         HandoffTime = m.HandoffTime,
                         HandonNurseID = m.HandonNurseID,
                         HandonDate = m.HandonDate,
                         HandonTime = m.HandonTime,
                         ShiftDate = m.ShiftDate,
                         HandoverShift = m.HandoverShift,
                         InpatientID = m.InpatientID,
                         PatientID = m.PatientID,
                         StationID = m.StationID,
                         Background = m.Background,
                         Assement = m.Assement,
                         Recommendation = m.Recommendation,
                         BedID = m.BedID,
                         Situation = m.Situation,
                         BedNumber = m.BedNumber,
                         PatientName = m.PatientName,
                         Sort = n.Sort
                     }).ToList();
            return query;
        }

        //获取指定病人指定时间段内的所有交班记录
        public async Task<List<HandoverBedSortView>> GetPatientHandover(DateTime startDate, DateTime endDate, string handoverType, int stationID, string inpatientID, string recordsCode)
        {
            var query = await (from m in _medicalDbContext.HandoverInfos
                               where m.ShiftDate >= startDate && m.ShiftDate <= endDate && m.StationID == stationID
                                && m.DeleteFlag != "*" && m.RecordsCode == recordsCode && m.InpatientID == inpatientID && m.HandoverType == handoverType
                               select new HandoverBedSortView
                               {
                                   ID = m.ID,
                                   HospitalID = m.HospitalID,
                                   HandoffNurseID = m.HandoffNurseID,
                                   HandoffDay = m.HandoffDay,
                                   HandoffTime = m.HandoffTime,
                                   HandonNurseID = m.HandonNurseID,
                                   HandonDate = m.HandonDate,
                                   HandonTime = m.HandonTime,
                                   ShiftDate = m.ShiftDate,
                                   HandoverShift = m.HandoverShift,
                                   InpatientID = m.InpatientID,
                                   PatientID = m.PatientID,
                                   StationID = m.StationID,
                                   BedID = m.BedID,
                                   Situation = m.Situation,
                                   Background = m.Background,
                                   Assement = m.Assement,
                                   Recommendation = m.Recommendation,
                                   BedNumber = m.BedNumber,
                                   ModifyDate = m.ModifyDate
                               }).OrderByDescending(m => m.ModifyDate).ToListAsync();
            return query;
        }

        public async Task<HandoverInfo> GetDeleteHandonData(int stationID, DateTime shiftDate, int shiftID, string handoverType, string inpatientID)
        {
            var data = await _medicalDbContext.HandoverInfos.Where(m => m.ShiftDate == shiftDate && m.StationID == stationID
            && m.ShiftDate == shiftDate
            && m.InpatientID == inpatientID
            && m.RecordsCode == handoverType && m.DeleteFlag == "*").OrderByDescending(m => m.ModifyDate).SingleOrDefaultAsync();
            return data;
        }

        public async Task<List<HandoverInfo>> GetPatientHandOvers(string inpatientID, string hospitalID, int? stationID)
        {
            var list = new List<HandoverInfo>();
            if (stationID.HasValue)
            {
                list = await _medicalDbContext.HandoverInfos.Where(m => m.HospitalID == hospitalID && m.InpatientID == inpatientID
                && m.StationID == stationID
                    && m.DeleteFlag != "*").ToListAsync();
            }
            else
            {
                list = await _medicalDbContext.HandoverInfos.Where(m => m.HospitalID == hospitalID && m.InpatientID == inpatientID
              && m.DeleteFlag != "*").ToListAsync();
            }
            return list;
        }

        public async Task<List<PatientHandoverView>> GetPatientHandoverView(string hospitalID, string inpatientID, DateTime startDate, DateTime endDate)
        {
            var datas = new List<PatientHandoverView>();
            datas = await _medicalDbContext.HandoverInfos.Where(m => m.HospitalID == hospitalID && m.InpatientID == inpatientID
            && m.ShiftDate >= startDate && m.ShiftDate <= endDate && m.DeleteFlag != "*")
           .OrderByDescending(m => m.ShiftDate).ThenByDescending(m => m.HandoverShift).ThenByDescending(m => m.HandoffTime)
           .Select(m => new PatientHandoverView
           {
               HandoverID = m.ID,
               RecordsCode = m.RecordsCode,
               HandoverShift = m.HandoverShift,
               HandoffStation = m.StationID.ToString(),
               HandoffDepartment = m.DepartmentListID.ToString(),
               HandoffDate = m.HandoffDay,
               HandoffTime = m.HandoffTime,
               HandoffNurse = m.HandoffNurseID,
               HandonStation = m.HandOnStationID.ToString(),
               HandonDepartment = m.HandOnDepartmentListID.ToString(),
               HandonDate = m.HandonDate,
               HandonTime = m.HandonTime,
               HandonNurse = m.HandonNurseID,
               Status = "",
               StationID = m.StationID,
               HandoverType = m.HandoverType,
           }).ToListAsync();
            return datas;
        }

        public async Task<HandoverInfo> GetPatientLastHandover(string inpatientID)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag == "")
                         .OrderByDescending(m => m.HandoffDay).ThenByDescending(m => m.HandoffTime).FirstOrDefaultAsync();
        }

        public async Task<HandoverInfo> GetDischargeAssess(string inpatientID, string recordsCode)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.InpatientID == inpatientID && m.RecordsCode == recordsCode
                   && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<HandoverInfo> GetDischargeAssessByRecordsCode(string inpatientID)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.InpatientID == inpatientID && m.RecordsCode.Contains("Discharge")
                   && m.DeleteFlag != "*").OrderByDescending(m => m.ModifyDate).FirstOrDefaultAsync();
        }

        public async Task<List<CDA_DischargeEvaluationInfo>> GetDischargeAssessView(string hospitalID, DateTime handOffTime, List<string> dischargeRecordsCodes)
        {
            var result = await (from handOver in _medicalDbContext.HandoverInfos
                                join bed in _medicalDbContext.BedListInfos on handOver.BedID equals bed.ID
                                join inpatient in _medicalDbContext.InpatientDatas on handOver.InpatientID equals inpatient.ID
                                join patientBasic in _medicalDbContext.PatientBasicDatas on handOver.PatientID equals patientBasic.PatientID
                                join station in _medicalDbContext.StationList on new { ID = handOver.StationID, hospitalID }
                                equals new { station.ID, hospitalID = station.HospitalID }
                                join departmnt in _medicalDbContext.DepartmentListInfos on handOver.DepartmentListID equals departmnt.ID
                                join employee in _medicalDbContext.Users on handOver.HandoffNurseID equals employee.UserID
                                where dischargeRecordsCodes.Contains(handOver.RecordsCode) && handOver.ModifyDate >= handOffTime
                                && patientBasic.DeleteFlag != "*" && station.DeleteFlag != "*" && departmnt.DeleteFlag != "*"
                                && inpatient.DischargeDate != null
                                && departmnt.HospitalID == hospitalID
                                && inpatient.HospitalID == hospitalID
                                && patientBasic.HospitalID == hospitalID
                                && employee.HospitalID == hospitalID
                                && InHospitalStatus.DISCHARGEDHOSPITALLIST.Contains(inpatient.InHospitalStatus ?? -1)
                                select new CDA_DischargeEvaluationInfo
                                {
                                    DCID = handOver.ID,
                                    InpatientID = handOver.InpatientID,
                                    ChartNo = handOver.ChartNo,
                                    CaseNumber = handOver.CaseNumber,
                                    PatientType = "04",
                                    VisitID = inpatient.NumberOfAdmissions.ToString(),
                                    EffectiveFlag = string.IsNullOrEmpty(handOver.DeleteFlag) ? "1" : "0",
                                    Name = patientBasic.PatientName,
                                    Sex = patientBasic.Gender,
                                    BirthDate = patientBasic.DateOfBirth,
                                    Age = inpatient.Age.HasValue ? inpatient.Age.ToString() : null,
                                    DeptCode = departmnt.DepartmentCode,
                                    DeptName = departmnt.Department,
                                    WardAreaCode = station.StationCode,
                                    WardAreaName = station.StationName,
                                    DischargeDateTime = inpatient.DischargeDate.HasValue ? inpatient.DischargeDate.Value.Add(inpatient.DischargeTime.Value) : inpatient.DischargeDate,
                                    TimeStamp = handOver.ModifyDate,
                                    SickbedId = handOver.BedNumber,
                                    NurseSign = employee.Name,
                                    SignDateTime = handOver.HandoffDay.Add(handOver.HandoffTime.Value),
                                    LocalChartNO = patientBasic.LocalChartNO,
                                    WardRoomNo = bed.RoomCode,
                                    DocumentAuthor = handOver.ModifyPersonID,
                                    ResponsibilityNurse = employee.Name,
                                    AdmissionDateTime = inpatient.AdmissionDate.Add(inpatient.AdmissionTime),
                                    DeleteFlag = handOver.DeleteFlag
                                }).ToListAsync();

            return result;
        }

        public async Task<DateTime?> GetFirstDischargeAssessTime(string hospitalID, List<string> dischargeRecordsCodes)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => dischargeRecordsCodes.Contains(m.RecordsCode))
                .Select(m => m.AddDateTime).OrderBy(m => m).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取手术交班数据列表
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="operateIDListID"></param>
        /// <returns></returns>
        public async Task<List<HandoverInfo>> GetOperationHandoverListAsync(string inpatientID, string hisOperationNo)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.DeleteFlag != "*"
            && m.InpatientID == inpatientID && m.SourceID == hisOperationNo).ToListAsync();
        }

        public async Task<string> GetHandoverIDByOperationNo(string inpatientID, string hisOperationNo, string recordsCode)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.DeleteFlag != "*"
            && m.InpatientID == inpatientID && m.SourceID == hisOperationNo && m.RecordsCode.Trim() == recordsCode).Select(m => m.ID).FirstOrDefaultAsync();
        }

        public async Task<HandoverInfo> GetHandoverInfoByOperationNo(string caseNumber, string hisOperationNo, string recordsCode)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.DeleteFlag != "*"
            && m.CaseNumber == caseNumber && m.SourceID == hisOperationNo && m.RecordsCode.Trim() == recordsCode).FirstOrDefaultAsync();
        }

        public async Task<List<HandoverSupplementView>> GetPatientHandoverListAsync(string hospitalID
            , string inpatientID, DateTime startDate, DateTime endDate)
        {
            var datas = await _medicalDbContext.HandoverInfos.Where(m => m.HospitalID == hospitalID
            && m.InpatientID == inpatientID && m.ShiftDate >= startDate && m.ShiftDate <= endDate
            && m.DeleteFlag != "*")
            .OrderByDescending(m => m.ShiftDate)
            .ThenByDescending(m => m.HandoverShift)
            .ThenByDescending(m => m.HandoffTime)
            .Select(m => new HandoverSupplementView
            {
                HandoffDay = m.HandoffDay,
                HandoffTime = m.HandoffTime,
                HandonDay = m.HandonDate,
                HandonTime = m.HandonTime,
                HandoffNurse = m.HandoffNurseID,
                HandonNurse = m.HandonNurseID,
                RecordsCode = m.RecordsCode,
                HandoverType = m.HandoverType,
                HandoffStation = m.StationID,
                HandoffDept = m.DepartmentListID,
                HandonStation = m.HandOnStationID,
                HandonDept = m.HandOnDepartmentListID,
                HandoverShift = m.HandoverShift,
                HandoverID = m.ID,
                InpatientID = m.InpatientID,
                BedID = m.BedID,
            }).ToListAsync();
            return datas;
        }

        public async Task<bool> GetValidDataFlagByinpatientIDAsync(string inpatientID)
        {
            return await _medicalDbContext.HandoverInfos.AnyAsync(m => m.InpatientID == inpatientID);
        }

        public async Task<string> GetHandOverSourceIDByLastPreOP(string inpatientID)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.InpatientID == inpatientID
            && m.RecordsCode == "PreOPHandover"
            && m.DeleteFlag != "*").OrderByDescending(m => m.AddDateTime).Select(m => m.SourceID).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取最近一次交班记录
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="recordsCode"></param>
        /// <returns></returns>
        public async Task<HandoverInfo> GetOldAssessView(string inpatientID, string recordsCode, int departmentID)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.InpatientID == inpatientID && m.RecordsCode == recordsCode
            && m.DepartmentListID == departmentID && m.DeleteFlag != "*").OrderBy(m => m.HandonDate).ThenBy(m => m.HandonTime).LastOrDefaultAsync();
        }

        public async Task<List<HandoverNurseView>> GetHandoverNurseView(string inpatientID, string hospitalID)
        {
            var query = await (from a in _medicalDbContext.HandoverInfos
                               join b in _medicalDbContext.userInfos on a.HandoffNurseID equals b.UserID
                               join c in _medicalDbContext.userInfos.Where(m => m.DeleteFlag != "*" && m.HospitalID == hospitalID) on a.HandonNurseID equals c.UserID into ac
                               from d in ac.DefaultIfEmpty()
                               where b.HospitalID == hospitalID && a.InpatientID == inpatientID && a.DeleteFlag != "*" && b.DeleteFlag != "*" && d.DeleteFlag != "*"
                               select new HandoverNurseView
                               {
                                   HandoverID = a.ID,
                                   RecordsCode = a.RecordsCode,
                                   HandoffNurseName = b.Name,
                                   HandoffNurseID = a.HandoffNurseID,
                                   HandoffDay = a.HandoffDay,
                                   HandoffTime = a.HandoffTime,
                                   HandonNurseName = d.Name,
                                   HandonNurseID = a.HandonNurseID,
                                   HandonDay = a.HandonDate,
                                   HandonTime = a.HandonTime,
                                   SourceID = a.SourceID
                               }).ToListAsync();

            return query;
        }

        public async Task<List<HandoverNurseView>> GetHandoverNurseViewByInpatientIDAndSourceID(string inpatientID, string sourceID)
        {
            var query = await (from a in _medicalDbContext.HandoverInfos
                               where a.InpatientID == inpatientID
                               && a.SourceID == sourceID
                               && a.DeleteFlag != "*"
                               select new HandoverNurseView
                               {
                                   HandoverID = a.ID,
                                   RecordsCode = a.RecordsCode,
                                   HandoffNurseName = "",
                                   HandoffNurseID = a.HandoffNurseID,
                                   HandoffDay = a.HandoffDay,
                                   HandoffTime = a.HandoffTime,
                                   HandonNurseName = "",
                                   HandonNurseID = a.HandonNurseID,
                                   HandonDay = a.HandonDate,
                                   HandonTime = a.HandonTime,
                                   SourceID = a.SourceID
                               }).ToListAsync();
            return query;
        }

        public async Task<List<HandoverNurseView>> GetHandoverNurseViewByInpatientID(string inpatientID, List<string> handoverTypes)
        {
            var query = await (from a in _medicalDbContext.HandoverInfos
                               where a.InpatientID == inpatientID
                               && handoverTypes.Contains(a.HandoverType)
                               && a.DeleteFlag != "*"
                               select new HandoverNurseView
                               {
                                   HandoverID = a.ID,
                                   RecordsCode = a.RecordsCode,
                                   HandoffNurseName = "",
                                   HandoffNurseID = a.HandoffNurseID,
                                   HandoffDay = a.HandoffDay,
                                   HandoffTime = a.HandoffTime,
                                   HandonNurseName = "",
                                   HandonNurseID = a.HandonNurseID,
                                   HandonDay = a.HandonDate,
                                   HandonTime = a.HandonTime,
                                   SourceID = a.SourceID,
                                   HandoverType = a.HandoverType,
                                   AddDateTime = a.AddDateTime,
                                   HandOffStationID = a.StationID,
                                   HandOnStationID = a.HandOnStationID,
                                   HandOffDepartmentID = a.DepartmentListID,
                                   HandOnDepartmentID = a.HandOnDepartmentListID
                               }).OrderBy(m => m.HandoffDay).ThenBy(m => m.HandoffTime).ToListAsync();
            return query;
        }

        public async Task<HandoverInfo> GetLastPreOPHandover(string sourceID)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.SourceID == sourceID && m.HandonDate.HasValue && m.DeleteFlag != "*")
                .OrderBy(m => m.HandoffDay).ThenBy(m => m.HandoffTime).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取最近一次交班记录
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="recordsCode"></param>
        /// <returns></returns>
        public async Task<HandoverInfo> GetHandoverByRecordsCode(string chartNO, string recordsCode)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.ChartNo == chartNO && m.RecordsCode == recordsCode
                   && m.DeleteFlag != "*").OrderByDescending(m => m.HandonDate).ThenByDescending(m => m.HandoffTime).AsNoTracking().FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据手术ID获取交班数据
        /// </summary>
        /// <param name="sourceID"></param>
        /// <returns></returns>
        public async Task<List<HandoverInfo>> GetHandoverBySourceIDAsync(string sourceID, string inpatientID)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.SourceID == sourceID && m.DeleteFlag != "*" && m.InpatientID == inpatientID).ToListAsync();
        }
        public async Task<byte[]> GetBodyImg(string handoverID)
        {
            return await _medicalDbContext.HandoverInfos
                  .Where(m => m.ID == handoverID).Select(m => m.BodyPartImage).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取患者班别日期的责护
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="stationID"></param>
        /// <param name="shiftDate"></param>
        /// <returns></returns>
        public async Task<List<HandoverInfo>> GetNurseByShitfDate(string inpatientID, int stationID, DateTime shiftDate)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.RecordsCode == "ShiftHandover"
            && m.InpatientID == inpatientID && m.StationID == stationID
            && m.ShiftDate == shiftDate && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        ///  获取指定病区的指定类别的所有交班病人
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="stationID"></param>
        /// <param name="inpatientIDArr"></param>
        /// <returns></returns>
        public async Task<List<HandoverBedSortView>> GetPatientHandover(DateTime startDate, DateTime endDate, int stationID, List<string> inpatientIDArr, string recordsCode)
        {
            var query = await (from m in _medicalDbContext.HandoverInfos
                               join n in _medicalDbContext.PatientBasicDatas on m.ChartNo equals n.ChartNo
                               where m.ShiftDate >= startDate && m.ShiftDate <= endDate && m.StationID == stationID
                                && m.DeleteFlag != "*" && inpatientIDArr.Contains(m.InpatientID) && m.RecordsCode == recordsCode
                               select new HandoverBedSortView
                               {
                                   ID = m.ID,
                                   HospitalID = m.HospitalID,
                                   HandoffNurseID = m.HandoffNurseID,
                                   HandoffDay = m.HandoffDay,
                                   HandoffTime = m.HandoffTime,
                                   HandonNurseID = m.HandonNurseID,
                                   HandonDate = m.HandonDate,
                                   HandonTime = m.HandonTime,
                                   ShiftDate = m.ShiftDate,
                                   HandoverShift = m.HandoverShift,
                                   InpatientID = m.InpatientID,
                                   PatientID = m.PatientID,
                                   StationID = m.StationID,
                                   BedID = m.BedID,
                                   Situation = m.Situation,
                                   Background = m.Background,
                                   Assement = m.Assement,
                                   Recommendation = m.Recommendation,
                                   BedNumber = m.BedNumber,
                                   ModifyDate = m.ModifyDate,
                                   PatientName = n.PatientName,
                                   BodyPartImage = m.BodyPartImage,
                                   RecordsCode = m.RecordsCode,
                                   HandoverType = m.HandoverType,
                               }).OrderByDescending(m => m.HandoffDay).ThenBy(m => m.HandoffTime).ThenBy(m => m.BedID).ToListAsync();
            return query;
        }

        /// <summary>
        /// 获取指定病人的指定类别的所有交班病人
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="stationID"></param>
        /// <param name="inpatientID"></param>
        /// <param name="recordsCode"></param>
        /// <param name="handoverType"></param>
        /// <returns></returns>
        public async Task<List<HandoverBedSortView>> GetSinglePatientHandover(DateTime startDate, DateTime endDate, int stationID, string inpatientID, string recordsCode, string handoverType)
        {
            var query = await (from m in _medicalDbContext.HandoverInfos
                               join n in _medicalDbContext.PatientBasicDatas on m.ChartNo equals n.ChartNo
                               where m.ShiftDate >= startDate && m.ShiftDate <= endDate && m.StationID == stationID
                                && m.RecordsCode == recordsCode && m.HandoverType == handoverType
                                && m.DeleteFlag != "*" && inpatientID == m.InpatientID
                               select new HandoverBedSortView
                               {
                                   ID = m.ID,
                                   HospitalID = m.HospitalID,
                                   HandoffNurseID = m.HandoffNurseID,
                                   HandoffDay = m.HandoffDay,
                                   HandoffTime = m.HandoffTime,
                                   HandonNurseID = m.HandonNurseID,
                                   HandonDate = m.HandonDate,
                                   HandonTime = m.HandonTime,
                                   ShiftDate = m.ShiftDate,
                                   HandoverShift = m.HandoverShift,
                                   InpatientID = m.InpatientID,
                                   PatientID = m.PatientID,
                                   StationID = m.StationID,
                                   BedID = m.BedID,
                                   Situation = m.Situation,
                                   Background = m.Background,
                                   Assement = m.Assement,
                                   Recommendation = m.Recommendation,
                                   BedNumber = m.BedNumber,
                                   ModifyDate = m.ModifyDate,
                                   PatientName = n.PatientName,
                                   BodyPartImage = m.BodyPartImage,
                                   RecordsCode = m.RecordsCode,
                                   HandoverType = m.HandoverType,
                               }).OrderByDescending(m => m.HandoffDay).ThenBy(m => m.HandoffTime).ThenBy(m => m.BedID).ToListAsync();
            return query;
        }

        /// <summary>
        /// 根据来源ID集合获取交班数据
        /// </summary>
        /// <param name="sourceID"></param>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<HandoverCheckOpView>> GetHandoverViewsBySourceIDs(string sourceID, string inpatientID)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.DeleteFlag != "*"
            && m.InpatientID == inpatientID && m.SourceID == sourceID)
                .OrderBy(m => m.AddDateTime).Select(m => new HandoverCheckOpView
                {
                    HandoverType = m.HandoverType,
                    HandoffNurseID = m.HandoffNurseID,
                    HandoffDay = m.HandoffDay,
                    HandoffTime = m.HandoffTime,
                    HandonNurseID = m.HandonNurseID,
                    HandonDate = m.HandonDate,
                    HandonTime = m.HandonTime,
                    Situation = m.Situation,
                    Background = m.Background,
                    Assement = m.Assement,
                    Recommendation = m.Recommendation,
                }).ToListAsync();
        }
        /// <summary>
        /// 获取指定患者交班数据
        /// </summary>
        /// <param name="shiftDate"></param>
        /// <param name="inpatientIDArr"></param>
        /// <param name="recordsCode"></param>
        /// <param name="shift"></param>
        /// <returns></returns>
        public async Task<List<HandoverBoardView>> GetHandoverBoardView(DateTime shiftDate, List<string> inpatientIDArr, string recordsCode, string shift)
        {
            var query = await (from m in _medicalDbContext.HandoverInfos
                               join n in _medicalDbContext.PatientBasicDatas on m.PatientID equals n.PatientID
                               where m.ShiftDate == shiftDate && m.RecordsCode == recordsCode && m.HandoverShift == shift
                                && m.DeleteFlag != "*" && inpatientIDArr.Contains(m.InpatientID)
                               select new HandoverBoardView
                               {
                                   HandoverID = m.ID,
                                   InpatientID = m.InpatientID,                               
                                   HandoffDay = m.HandoffDay,
                                   HandoffTime = m.HandoffTime,                                
                                   HandoverShift = m.HandoverShift,                            
                                   StationID = m.StationID, 
                                   Situation = m.Situation,
                                   Background = m.Background,
                                   Assement = m.Assement,
                                   Recommendation = m.Recommendation,                                
                                   PatientName = n.PatientName,
                                   RecordsCode = m.RecordsCode,
                                   BedID =m.BedID
                               }).OrderByDescending(m => m.HandoffDay).ThenBy(m => m.HandoffTime).ThenBy(m => m.BedID).ToListAsync();
            return query;
        }

        /// <summary>
        /// 获取指定日期指定类别的所有交班病人
        /// </summary>
        /// <param name="shiftDate"></param>
        /// <param name="inpatientIDArr"></param>
        /// <param name="recordsCode"></param>
        /// <param name="shift"></param>
        /// <returns></returns>
        public async Task<List<HandoverBedSortView>> GetDailyPatientHandover(DateTime shiftDate, List<string> inpatientIDArr, string recordsCode, string shift)
        {
            var query = await (from m in _medicalDbContext.HandoverInfos
                               join n in _medicalDbContext.PatientBasicDatas on m.PatientID equals n.PatientID
                               where m.ShiftDate == shiftDate && m.RecordsCode == recordsCode && m.HandoverShift == shift
                                && m.DeleteFlag != "*" && inpatientIDArr.Contains(m.InpatientID)
                               select new HandoverBedSortView
                               {
                                   ID = m.ID,
                                   HospitalID = m.HospitalID,
                                   HandoffNurseID = m.HandoffNurseID,
                                   HandoffDay = m.HandoffDay,
                                   HandoffTime = m.HandoffTime,
                                   HandonNurseID = m.HandonNurseID,
                                   HandonDate = m.HandonDate,
                                   HandonTime = m.HandonTime,
                                   ShiftDate = m.ShiftDate,
                                   HandoverShift = m.HandoverShift,
                                   InpatientID = m.InpatientID,
                                   PatientID = m.PatientID,
                                   StationID = m.StationID,
                                   BedID = m.BedID,
                                   Situation = m.Situation,
                                   Background = m.Background,
                                   Assement = m.Assement,
                                   Recommendation = m.Recommendation,
                                   BedNumber = m.BedNumber,
                                   ModifyDate = m.ModifyDate,
                                   PatientName = n.PatientName,
                                   RecordsCode = m.RecordsCode,
                                   HandoverType = m.HandoverType,
                               }).OrderByDescending(m => m.HandoffDay).ThenBy(m => m.HandoffTime).ThenBy(m => m.BedID).ToListAsync();
            return query;
        }
        /// <summary>
        /// 配合嘉会班别交班有多个类型
        /// </summary>
        /// <param name="shiftDate"></param>
        /// <param name="inpatientIDArr"></param>
        /// <param name="shift"></param>
        /// <returns></returns>
        public async Task<List<HandoverBedSortView>> GetNotInShftPatientHandover(DateTime shiftDate, List<string> inpatientIDArr, string shift)
        {
            var query = await (from m in _medicalDbContext.HandoverInfos
                               join n in _medicalDbContext.PatientBasicDatas on m.PatientID equals n.PatientID
                               where m.ShiftDate == shiftDate && m.RecordsCode != "TurnHandover" && m.HandoverType == "HandOverShift" && m.HandoverShift == shift
                                && m.DeleteFlag != "*" && inpatientIDArr.Contains(m.InpatientID)
                               select new HandoverBedSortView
                               {
                                   ID = m.ID,
                                   HospitalID = m.HospitalID,
                                   HandoffNurseID = m.HandoffNurseID,
                                   HandoffDay = m.HandoffDay,
                                   HandoffTime = m.HandoffTime,
                                   HandonNurseID = m.HandonNurseID,
                                   HandonDate = m.HandonDate,
                                   HandonTime = m.HandonTime,
                                   ShiftDate = m.ShiftDate,
                                   HandoverShift = m.HandoverShift,
                                   InpatientID = m.InpatientID,
                                   PatientID = m.PatientID,
                                   StationID = m.StationID,
                                   BedID = m.BedID,
                                   Situation = m.Situation,
                                   Background = m.Background,
                                   Assement = m.Assement,
                                   Recommendation = m.Recommendation,
                                   BedNumber = m.BedNumber,
                                   ModifyDate = m.ModifyDate,
                                   PatientName = n.PatientName,
                                   RecordsCode = m.RecordsCode,
                                   HandoverType = m.HandoverType,
                               }).OrderByDescending(m => m.HandoffDay).ThenBy(m => m.HandoffTime).ThenBy(m => m.BedID).ToListAsync();
            return query;
        }
        /// <summary>
        /// 根据住院号和交班类型获取交班数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="handoverType"></param>
        /// <returns></returns>
        public async Task<List<HandoverInfo>> GetHandoverInfosByInpatientIDAndHandoverType(string inpatientID, string handoverType)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.InpatientID == inpatientID && m.HandoverType == handoverType && m.DeleteFlag != "*").OrderBy(m => m.HandoffDay).ThenBy(m => m.HandoffTime).ToListAsync();
        }

        public async Task<List<HandoverInfo>> GetHandoverInfosByInpatientIDAndRecordsCode(string inpatientID, string recordsCode)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.InpatientID == inpatientID && m.RecordsCode.Trim() == recordsCode && m.DeleteFlag != "*" && m.HandonDate.HasValue && !string.IsNullOrEmpty(m.HandonNurseID))
                .Select(m => new HandoverInfo
                {
                    HandoffDay = m.HandoffDay,
                    HandoffTime = m.HandoffTime,
                })
                .ToListAsync();
        }
        /// <summary>
        /// 根据住院号、交班类型|交班类别和时间区间获取交班数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="handoverType"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="recordCode"></param>
        /// <returns></returns>
        public async Task<List<HandoverInfo>> GetDataByInpatientIDTypeAndDate(string inpatientID, string handoverType, DateTime startDate, DateTime endDate, string recordCode)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.InpatientID == inpatientID
            && m.HandoverType == handoverType && m.RecordsCode == recordCode
            && m.ShiftDate >= startDate
            && m.ShiftDate <= endDate && m.DeleteFlag != "*")
            .OrderByDescending(m => m.HandoffDay).ThenByDescending(m => m.HandoffTime).ToListAsync();
        }
        /// <summary>
        /// 根据班别日期获取交接班数据
        /// </summary>
        /// <param name="inpatientIDs"></param>
        /// <param name="shiftDate"></param>
        /// <param name="recordCode"></param>
        /// <param name="handoverType"></param>
        /// <returns></returns>
        public async Task<List<HandoverInfo>> GetDataByInpatientIDCodeAndDate(List<string> inpatientIDs, DateTime shiftDate, string recordCode, string handoverType)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => inpatientIDs.Contains(m.InpatientID)
            && m.ShiftDate == shiftDate && m.RecordsCode == recordCode && m.HandoverType == handoverType && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取单人数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="shiftDate"></param>
        /// <param name="recordCode"></param>
        /// <param name="handoverType"></param>
        /// <returns></returns>
        public async Task<HandoverInfo> GetSingleDataByInpatientID(string inpatientID, DateTime shiftDate, string recordCode, string handoverType)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => inpatientID == m.InpatientID
            && m.ShiftDate == shiftDate && m.RecordsCode == recordCode && m.HandoverType == handoverType && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<T> GetInClassFieldValue<T>(string inpatientID, DateTime shiftDate, string shift,
            string recordCode, Expression<Func<HandoverInfo, T>> predicate)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => inpatientID == m.InpatientID
            && m.ShiftDate == shiftDate && m.HandoverShift == shift && m.RecordsCode == recordCode && m.DeleteFlag != "*")
                .Select(predicate).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据sourceID获取获取交班主记录（转运交接明细使用）
        /// </summary>
        /// <param name="sourceID"></param>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<HandoverInfo>> GetHandoverBySourceID(string sourceID, string inpatientID)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.InpatientID == inpatientID && m.SourceID == sourceID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<HandoverInfo>> GetUnPumpData(string handoverType, int count)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.HandoverType == handoverType && m.HandonDate != null && m.DeleteFlag != "*" && m.DataPumpFlag != "*")
                .Take(count).ToListAsync();
        }
        /// <summary>
        /// 根据一定优先级获取患者的交班数据
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="stationID">病区ID</param>
        /// <param name="inpatientIDArr">患者住院序号集合</param>
        /// <returns></returns>
        public async Task<List<HandoverBedSortView>> GetPatientStationAllHandoverByDate(DateTime startDate, DateTime endDate, int stationID, List<string> inpatientIDArr)
        {
            var query = await (from m in _medicalDbContext.HandoverInfos
                               where m.ShiftDate >= startDate && m.ShiftDate <= endDate && m.StationID == stationID
                                && m.DeleteFlag != "*" && inpatientIDArr.Contains(m.InpatientID)
                               select new HandoverBedSortView
                               {
                                   ID = m.ID,
                                   HospitalID = m.HospitalID,
                                   HandoffNurseID = m.HandoffNurseID,
                                   HandoffDay = m.HandoffDay,
                                   HandoffTime = m.HandoffTime,
                                   HandonNurseID = m.HandonNurseID,
                                   HandonDate = m.HandonDate,
                                   HandonTime = m.HandonTime,
                                   ShiftDate = m.ShiftDate,
                                   HandoverShift = m.HandoverShift,
                                   InpatientID = m.InpatientID,
                                   PatientID = m.PatientID,
                                   StationID = m.StationID,
                                   BedID = m.BedID,
                                   Situation = m.Situation,
                                   Background = m.Background,
                                   Assement = m.Assement,
                                   Recommendation = m.Recommendation,
                                   BedNumber = m.BedNumber,
                                   ModifyDate = m.ModifyDate,
                                   BodyPartImage = m.BodyPartImage,
                                   RecordsCode = m.RecordsCode,
                                   HandoverType = m.HandoverType,
                               }).OrderByDescending(m => m.HandoffDay).ThenBy(m => m.HandoffTime).ThenBy(m => m.BedID).ToListAsync();
            return query;
        }
        /// <summary>
        /// 获取多患者当班交班数据
        /// </summary>
        /// <param name="inpatientIDs"></param>
        /// <param name="shiftDate"></param>
        /// <param name="recordCode"></param>
        /// <param name="handoverType"></param>
        /// <returns></returns>
        public async Task<List<HandoverInfo>> GetInpatientsDataByShiftDate(List<string> inpatientIDs, DateTime shiftDate, string recordCode, string handoverType)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => inpatientIDs.Contains(m.InpatientID)
            && m.ShiftDate == shiftDate && m.RecordsCode == recordCode && m.HandoverType == handoverType && m.DeleteFlag != "*").AsNoTracking().ToListAsync();
        }
        /// <summary>
        /// 获取患者特定类型的手术交班数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="handoverType"></param>
        /// <param name="recordCode"></param>
        /// <param name="sourceID"></param>
        /// <returns></returns>
        public async Task<HandoverInfo> GetHandover(string inpatientID, string handoverType, string recordCode, string sourceID)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.InpatientID == inpatientID
            && m.HandoverType == handoverType && m.RecordsCode == recordCode && m.SourceID == sourceID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<HandoverInfo> GetHandoverContent(string inpatientID, string recordCode, DateTime shiftDate)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.InpatientID == inpatientID && m.RecordsCode == recordCode && m.DeleteFlag != "*"
           && m.ShiftDate <= shiftDate).OrderByDescending(m => m.ShiftDate).ThenByDescending(m => m.HandoffTime).Select(m => new HandoverInfo
            {
                ID = m.ID,
                Recommendation = m.Recommendation,
                Background = m.Background
            }).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取患者的交班数据
        /// </summary>
        /// <param name="inpatientID">患者主键</param>
        /// <param name="sourceID">ID</param>
        /// <returns></returns>
        public async Task<List<HandoverInfo>> GetHandOverListAsync(string inpatientID, string sourceID)
        {
            return await _medicalDbContext.HandoverInfos.Where(m => m.DeleteFlag != "*" && m.InpatientID == inpatientID && m.SourceID == sourceID)
                .Select(m=>new HandoverInfo {
                    ID = m.ID,
                    HandoffDay = m.HandoffDay,
                    HandoffTime = m.HandoffTime,
                })
                .ToListAsync();
        }
    }
}