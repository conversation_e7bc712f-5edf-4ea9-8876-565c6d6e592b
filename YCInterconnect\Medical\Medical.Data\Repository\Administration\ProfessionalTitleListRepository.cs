﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class ProfessionalTitleListRepository : IProfessionalTitleListRepository
    {
        private readonly MedicalDbContext _medicalDbContext;

        public ProfessionalTitleListRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<ProfessionalTitleListInfo> GetByProfessionalTitleIDAsync(string title)
        {
            return await _medicalDbContext.ProfessionalTitleListInfos.Where(m => m.DeleteFlag != "*"
            && m.StandardCode == title).SingleOrDefaultAsync();
        }

        public async Task<List<ProfessionalTitleListInfo>> GetListAsync()
        {
            return await _medicalDbContext.ProfessionalTitleListInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
        }
    }
}