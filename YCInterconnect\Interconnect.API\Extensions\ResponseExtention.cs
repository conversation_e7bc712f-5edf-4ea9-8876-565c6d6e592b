﻿using Medical.Common;
using Microsoft.AspNetCore.Mvc;

namespace Interconnect.API
    {
    /// <summary>
    /// Response扩展
    /// </summary>
    public static class ResponseExtension
    {
        /// <summary>
        /// 返回Json
        /// </summary>
        /// <returns></returns>
        public static JsonResult ToJson(this ResponseResult _result)
        {
            return new JsonResult(_result);
        }
    }
}