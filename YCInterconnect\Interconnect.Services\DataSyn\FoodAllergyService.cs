﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Interconnect.Data.Interface;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using NLog;
using System.Linq;
using Microsoft.Extensions.Options;
using Medical.Data.Context;
using Interconnect.Data.Context;
using Medical.Common;
using Arch.EntityFrameworkCore.UnitOfWork;

namespace Interconnect.Services
{
    public class FoodAllergyService : IFoodAllergyService
    {
        //Mdeical
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IInpatientDataRepository _IInpatientDataRepository;
        private readonly ILogInfoServices _ILogInfoServices;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly ICommonHelper _commonHelper;
        //Interconnect
        private readonly IUnitOfWork<DataOutConnection> _unitOfWorkOut;
        private readonly IOptions<SystemConfig> _config;
        private readonly IFoodAllergyRepository _IFoodAllergyRepository;
        private static Logger _logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// 医疗院代码
        /// </summary>
        private readonly string HOSPITALID = "";
        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";

        public FoodAllergyService(
             IUnitOfWork<MedicalDbContext> UnitOfWork
            ,IUnitOfWork<DataOutConnection> UnitOfWorkOut
            , IInpatientDataRepository InPatientRepository
            , IFoodAllergyRepository FoodAllergyRepository
            , IOptions<SystemConfig> config
            , ILogInfoServices LogInfoServices
            , IAppConfigSettingRepository  appConfigSettingRepository
            , ICommonHelper commonHelper
            )
        {
            _unitOfWork = UnitOfWork;
            _unitOfWorkOut = UnitOfWorkOut;
            _IInpatientDataRepository = InPatientRepository;
            _IFoodAllergyRepository = FoodAllergyRepository;
            _config = config;           
            _ILogInfoServices = LogInfoServices;
            _appConfigSettingRepository = appConfigSettingRepository;
            HOSPITALID = _config.Value.HospitalID;
            _commonHelper = commonHelper;
        }

        /// <summary>
        /// 输出没有同步的食物过敏信息
        /// </summary>
        /// <returns></returns>
        public async Task<List<FoodAllergyInfo>> GetAllAsync()
        {
            //从配置档中获取数据 梁宝华 2020-04-29
            var tongbuCount = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "TongbuCount").Result;

            List<FoodAllergyInfo> ResultList = await _IFoodAllergyRepository.GetAsync(StringCheck.IsNumeric(tongbuCount)?int.Parse(tongbuCount):0);
            return ResultList;
        }

        //从配置档获取数据 梁宝华 2020-04-29
        public async Task<string> GetSettingValue(string settingType,string settingCode)
        {
            return await _appConfigSettingRepository.GetConfigSettingValue(settingType, settingCode);
        }

        /// <summary>
        /// 如果发现有没有同步的数据，则一直获取，直至完全同步
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SynchronizationMain()
        {
            var OriginalList = await GetAllAsync();
            while (OriginalList.Count > 0) //如果没有同步完成，则继续同步
            {
                if (!await SynchronizationDetail(OriginalList))
                {
                    return false;
                }
                OriginalList = await GetAllAsync();
            }
            return true;
        }


        private async Task<bool> SynchronizationDetail(List<FoodAllergyInfo> OriginalList)
        {
            List<Medical.ViewModels.Interface.PatientProfile> patientProfile = new List<Medical.ViewModels.Interface.PatientProfile>();
            Medical.ViewModels.Interface.PatientProfile t = null;
            string Tablename = "FoodAllergy";
            List<LogInfo> LogList = new List<LogInfo>();
            LogInfo TempLog = null;
            int Failcount = 0;
            //从配置档获取数据 梁宝华 2020-04-29
            int AlllogSetFlag = 0;
            var resultLogSetFlag = await GetSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "AllLogSet");
            if (StringCheck.IsNumeric(resultLogSetFlag))
            {
                AlllogSetFlag = int.Parse(resultLogSetFlag);
            }
            //获取系统默认操作人
            var modifyPersonID = await GetSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "ModifyPersonID");
            //获取系统食物过敏ID
            int foodAllergyID = 0;
            var resultFoodAllergyID = await GetSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "FoodAllergyID");
            if (StringCheck.IsNumeric(resultFoodAllergyID))
            {
                foodAllergyID = int.Parse(resultFoodAllergyID);
            }

            // 获取语言代码
            int language = 0;
            var resultLanguage = await GetSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "Language");
            if (StringCheck.IsNumeric(resultLanguage))
            {
                language = int.Parse(resultLanguage);
            }

            TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, " 开始进行数据同步，数据条数：" + OriginalList.Count);
            LogList.Add(TempLog);            
 
            #region "数据同步"
            foreach (var item in OriginalList)
            {
                item.Counts = item.Counts ?? 0;
                item.Counts = item.Counts + 1;
                try
                {
                    //获取Medical中的病人基本信息
                    InpatientDataInfo TempInpatientData = await _IInpatientDataRepository.GetAsyncByCaseNumber(item.ChartNo,_config.Value.HospitalID);

                    if (TempInpatientData == null)
                    {

                        if (AlllogSetFlag == 1)
                        {
                            TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, "表: InpatientData CaseNumber[" + item.ChartNo + "]没有查询到病人的信息!");
                            LogList.Add(TempLog);
                        }

                        Failcount++;
                        continue;
                    }

                    t = new Medical.ViewModels.Interface.PatientProfile
                    {
                        HospitalID = HOSPITALID,
                        InpatientID = TempInpatientData.ID,
                        CaseNumber = TempInpatientData.CaseNumber,
                        ChartNo= TempInpatientData.ChartNo,
                        PatientID = TempInpatientData.PatientID,
                        ModelName = "HIS",
                        Source = "I",
                        ProfileDate = TempInpatientData.AdmissionDate,
                        ProfileTime = TempInpatientData.AdmissionTime,

                        SerialNumber = item.ID.ToString(), //中间表的主键
                        AssessListID = foodAllergyID,//食物过敏ID
                        AssessValue = item.FoodAllergy, //食物过敏内容
                        AutoAddFlag = "",
                        Note = "",
                        ModifyPersonID = modifyPersonID,
                        ModifyDate = DateTime.Now,
                    };
                    item.DataPumpFlag = "*";
                    item.DataPumpDate = DateTime.Now;
                    patientProfile.Add(t);
                }
                catch (Exception ex)
                {
                    _logger.Error("同步错误：item.ID " + item.ID + ":" + ex.ToString());
                    return false;
                }
            }
            #endregion
            //调用接口
            if (patientProfile.Count > 0)
            {                //呼叫Profile     
                _commonHelper.AddProfile(patientProfile);               
            }


            #region "数据更新"
            //if (OriginalList.Count >= 1)
            //{
            //    try
            //    {
            //        _unitOfWorkOut.GetRepository<FoodAllergyInfo>().Update(OriginalList);
            //        _unitOfWorkOut.SaveChanges();
            //    }
            //    catch (Exception ex)
            //    {
            //        _logger.Error(Tablename + ":同步失败" + ex.ToString());
            //        return false;
            //    }  
            //}
            TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, " 同步结束 成功：" + (OriginalList.Count - Failcount).ToString() + "条！ 失败：" + Failcount.ToString()+"条！");
            LogList.Add(TempLog);
            int ItemNo = 0;
            string Guid = "";
            Guid = System.Guid.NewGuid().ToString("N");
            // item.Guid = Guid;
            foreach (var item in LogList)
            {
                item.Guid = Guid;
                item.ItemNo = ItemNo;
                ItemNo++;
            }
            try
            {
                _unitOfWorkOut.GetRepository<LogInfo>().Insert(LogList);
                _unitOfWorkOut.SaveChanges();
            }
            catch (Exception ex)
            {

                _logger.Error(Tablename + "同步成功，但写同步日志失败||" + ex.ToString());
            }
            _logger.Info(Tablename + "    同步完成!" + DateTime.Now);
            return true;
            #endregion
        }

    }
}


