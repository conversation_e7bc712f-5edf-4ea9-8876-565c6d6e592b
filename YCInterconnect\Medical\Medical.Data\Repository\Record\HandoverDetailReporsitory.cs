﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class HandoverDetailRepository : IHandoverDetailRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public HandoverDetailRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        //获取交班明细
        public async Task<List<HandoverDetailInfo>> GetByHandoverID(string handOverID)
        {
            return await _medicalDbContext.HandoverDetailInfos.Where(m => m.HandoverID == handOverID && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        ///根据handoverID集合获取数据
        /// </summary>
        /// <param name="handOverIDs"></param>
        /// <param name="assessListID"></param>
        /// <returns></returns>
        public async Task<List<HandoverDetailInfo>> GetByHandoverIDList(List<string> handOverIDs, int assessListID)
        {
            return await _medicalDbContext.HandoverDetailInfos.Where(m => handOverIDs.Contains(m.HandoverID) && m.AssessListID == assessListID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据handoverID和AssessListGroupID获取数据
        /// </summary>
        /// <param name="handOverID">交班ID</param>
        /// <param name="assessListGroupID">明细群组ID</param>
        /// <returns></returns>
        public async Task<List<HandoverDetailInfo>> GetByHandoverIDAndAssessListGroupID(string handOverID, int assessListGroupID)
        {
            return await _medicalDbContext.HandoverDetailInfos.Where(m => m.HandoverID == handOverID && m.AssessListGroupID == assessListGroupID && m.DeleteFlag != "*").ToListAsync();
        }
    }
}
