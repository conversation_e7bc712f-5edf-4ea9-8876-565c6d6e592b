﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("NursingRecord_SpecialtyObservation")]
    public class NRSpecialtyObservationInfo : ModifyReturnInfo
    {
        /// <summary>
        ///	自增长编号	
        ///</summary>
        [Key]
        [Column("SN")]
        public int SN { get; set; }
        /// <summary>
        ///	Main记录的编号	
        ///</summary>
        public int? MainSN { get; set; }
        /// <summary>
        ///	分类，如：普外科	
        ///</summary>
        public string DeptCode { get; set; }
        /// <summary>
        ///	子类名称，如：胃疾病围手术期	
        ///</summary>
        public string SpecialtyObervation { get; set; }
        /// <summary>
		///	"如：<sections>  <values na
		///</summary>
		public string EvaluationXML { get; set; }
        /// <summary>
        ///	评估明细，如：恶心:无，呕吐:
        ///</summary>
        public string EvalDescription { get; set; }
        /// <summary>
        ///	0 有效，-1无效	
        ///</summary>
        public int? Status { get; set; }
        /// <summary>
        ///	创建时间	
        ///</summary>
        public int? CreateTime { get; set; }
    }
}