﻿using Interconnect.Models;
using Interconnect.ViewModels;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Interconnect.Services.Interface
{
    public interface ITPRService
    {
        /// <summary>
        /// 获取生命体征数据
        /// </summary>
        /// <returns></returns>
        Task<Dictionary<string, List<TPRschedule>>> GetTprscheduleListAsync(DateTime StartDate, DateTime EndDate,string HospitalID);
    }
}