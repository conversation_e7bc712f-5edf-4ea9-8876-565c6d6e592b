﻿using Medical.ViewModels.View;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Interconnect.Services.Interface
{
    public interface IOpenExternalService
    {
        /// <summary>
        /// 根据参数获取当前病区派班数据（病区码），（病区码+护士工号）
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="stationCode"></param>
        /// <param name="nurseEmployeeID"></param>
        /// <returns></returns>
        Task<List<NowAttendanceView>> GetNowAttendanceByParamAsync(string hospitalID, string stationCode, string nurseEmployeeID, DateTime? shiftDate);
    }
}