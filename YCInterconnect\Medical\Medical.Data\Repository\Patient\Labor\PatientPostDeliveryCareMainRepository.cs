﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    /// <summary>
    /// 维护记录副表,产后记录仓储层
    /// </summary>
    public class PatientPostDeliveryCareMainRepository : IPatientPostDeliveryCareMainRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientPostDeliveryCareMainRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 根据维护记录ID集合获取待产数据
        /// </summary>
        /// <param name="careMainInfos">维护记录主表集合</param>
        /// <returns></returns>
        public async Task<List<dynamic>> GetViewsByCareMainIDs(List<PatientDeliveryCareMainInfo> careMainInfos)
        {
            var careMainIDs = careMainInfos.Select(m => m.PatientDeliveryCareMainID);
            var postDeliveryCareMainInfos = await _medicalDbContext.PatientPostDeliveryCareMainInfos.Where(m =>
            careMainIDs.Contains(m.PatientDeliveryCareMainID)).ToListAsync();
            var postDeliveryCareMainViews = postDeliveryCareMainInfos.Join(careMainInfos,
                m => m.PatientDeliveryCareMainID, n => n.PatientDeliveryCareMainID,
                (m, n) => (dynamic)new PatientPostDeliveryCareMainView
                {
                    PatientDeliveryCareMainID = m.PatientDeliveryCareMainID,
                    InpatientID = n.InpatientID,
                    StationID = n.StationID,
                    DepartmentListID = n.DepartmentListID,
                    AssessDate = n.AssessDate,
                    AssessTime = n.AssessTime,
                    Temperature = m.Temperature,
                    Pulse = m.Pulse,
                    Breath = m.Breath,
                    SystolicPressure = m.SystolicPressure,
                    DiastolicPressure = m.DiastolicPressure,
                    SPO2 = m.SPO2,
                    Diet = m.Diet,
                    UterineContractions = m.UterineContractions,
                    FundalHeight = m.FundalHeight,
                    PerinealWound = m.PerinealWound,
                    PerinealSwelling = m.PerinealSwelling,
                    LochiaColor = m.LochiaColor,
                    LochiaConsistency = m.LochiaConsistency,
                    LochiaAmount = m.LochiaAmount,
                    LochiaSmell = m.LochiaSmell,
                    MuscleTone = m.MuscleTone,
                    KneeReflex = m.KneeReflex,
                    OxygenMethod = m.OxygenMethod,
                    OxygenAmount = m.OxygenAmount,
                    Tenderness = m.Tenderness,
                    BleedingAmount = m.BleedingAmount,
                    VaginalLaceration = m.VaginalLaceration,
                    ESECLeft = m.ESECLeft,
                    ESECRight = m.ESECRight,
                    RecordContent = m.RecordContent,
                    UserID = n.AddEmployeeID,
                    BringToShift = n.BringToShift,
                    InformPhysician = n.InformPhysician,
                    BringToNursingRecord = n.BringToNursingRecord
                }).ToList();
            return postDeliveryCareMainViews;
        }
        /// <summary>
        /// 根据维护记录ID获取副表数据
        /// </summary>
        /// <param name="careMainID">维护记录ID</param>
        /// <returns></returns>
        public async Task<dynamic> GetByCareMainID(string careMainID)
        {
            return await _medicalDbContext.PatientPostDeliveryCareMainInfos.Where(m => careMainID == m.PatientDeliveryCareMainID)
                .Select(m => (dynamic)m).FirstOrDefaultAsync();
        }
    }
}
