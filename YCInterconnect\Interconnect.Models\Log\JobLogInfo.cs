﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("JobLog")]
    public class JobLogInfo
    {
        /// <summary>
        /// 作业编号
        /// </summary>
        [Key]
        [Column("JobID")]
        public int JobID { get; set; }
        /// <summary>
        /// 作业名称
        /// </summary>
        public string JobName { get; set; }
        /// <summary>
        /// 作业状态
        /// </summary>
        public string JobStatus { get; set; }

        /// <summary>
        /// 状态开始时间
        /// </summary>
        public DateTime StartDateTime { get; set; }

        /// <summary>
        /// 执行结束时间
        /// </summary>
        public DateTime? EndDateTime { get; set; }
    }
}