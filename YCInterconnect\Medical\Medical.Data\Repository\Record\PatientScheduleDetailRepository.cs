﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.Interface;
using Medical.ViewModels.patientScheduleView;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientScheduleDetailRepository : IPatientScheduleDetailRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientScheduleDetailRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<List<PatientScheduleDetailInfo>> GetAsync(string PatientScheduleMainID)
        {
            return await _medicalDbContext.PatientScheduleDetail.Where(t => t.PatientScheduleMainID == PatientScheduleMainID.Trim()
            && t.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<SchedulePerformDetailView>> GetViewByMainIDAsync(string PatientScheduleMainID)
        {
            var result = (from t in _medicalDbContext.PatientScheduleDetail
                          where t.PatientScheduleMainID == PatientScheduleMainID && t.DeleteFlag != "*"
                          select new SchedulePerformDetailView
                          {
                              InterventionDetailID = t.InterventionDetailID,
                              AssessListID = t.AssessListID,
                              ScheduleData = t.ScheduleData,
                              PatientScheduleMainID = t.PatientScheduleMainID
                          });
            return await result.ToListAsync();

        }

        /// <summary>
        /// 透过排程主序号获取排程数据
        /// </summary>
        /// <param name="patientScheduleMainID">透过排程主序号</param>
        /// <returns></returns>
        public async Task<List<PatientScheduleDetailInfo>> GetByMainIDAsync(string[] patientScheduleMainID)
        {
            var resultList = new List<PatientScheduleDetailInfo>();
            for (int i = 0; i < patientScheduleMainID.Length; i++)
            {
                var tempList = await _medicalDbContext.PatientScheduleDetail.Where(t => t.PatientScheduleMainID == patientScheduleMainID[i] && t.DeleteFlag != "*").ToListAsync();
                resultList = resultList.Union(tempList).ToList();
            }
            return resultList;
        }

        //获取指定病人指定时间内的明细表记录
        public async Task<List<PatientScheduleDetailInfo>> GetByScheduleStartAndEndTime(string inpatientID, DateTime startDate, DateTime endDate)
        {
            var patientScheduleDetailList = new List<PatientScheduleDetailInfo>();
            var query = (from detail in _medicalDbContext.PatientScheduleDetail
                         where _medicalDbContext.PatientScheduleMain.Any(m => m.PatientScheduleMainID == detail.PatientScheduleMainID
                             && m.InpatientID == inpatientID
                             && m.ScheduleDate >= startDate
                             && m.ScheduleDate <= endDate
                             && m.DeleteFlag != "*"
                             && detail.DeleteFlag != "*")
                         select detail);
            patientScheduleDetailList = await query.ToListAsync();
            return patientScheduleDetailList;
        }

        public async Task<List<PatientScheduleDetailInfo>> GetByScheduleTimeAndAssessListID(string inpatientID, string scheduleMainID
                                                                                            , DateTime scheduleDate, TimeSpan scheduleTime
                                                                                            , DateTime? performDate, TimeSpan? performTime
                                                                                            , int[] assessListIDs)
        {
            return await _medicalDbContext.PatientScheduleDetail.Where(m =>
                                  m.InpatientID == inpatientID
                                   && m.DeleteFlag != "*"
                                  && ((m.PatientScheduleMainID == scheduleMainID)
                                  || ((m.ScheduleDate == scheduleDate && m.ScheduleTime == scheduleTime) || (m.ScheduleDate == performDate && m.ScheduleTime == performTime))
                                      && assessListIDs.Contains(m.AssessListID)
                                     )
                                   ).AsNoTracking().ToListAsync();
        }

        public async Task<List<PatientScheduleDetailInfo>> GetByInpatientIDAsync(string inpatientID)
        {
            return await _medicalDbContext.PatientScheduleDetail.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据措施ID获取措施详情
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="patientScheduleMainIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleDetailInfo>> GetByMainIDsAsync(string inpatientID, string[] patientScheduleMainIDs)
        {
            var set = new HashSet<string>(patientScheduleMainIDs);
            return await _medicalDbContext.PatientScheduleDetail.Where(m => m.InpatientID == inpatientID
            && patientScheduleMainIDs.Contains(m.PatientScheduleMainID)
            && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientScheduleDetailInfo>> GetSchedule(string inpatientID, int interventionID, DateTime startDate, DateTime endDate)
        {
            return await (from a in _medicalDbContext.PatientScheduleMain
                          join b in _medicalDbContext.PatientScheduleDetail on a.PatientScheduleMainID equals b.PatientScheduleMainID
                          where a.InpatientID == inpatientID
                             && a.InterventionID == interventionID
                             && a.ScheduleDate >= startDate
                             && a.ScheduleDate <= endDate
                             && b.DeleteFlag != "*"
                          select b
                          ).ToListAsync();
        }

        /// <summary>
        /// 根据ID和assessListID获取病人排程明细
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="assessListIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleDetailInfo>> GetByinpatientIDAndAssessListID(string inpatientID, int[] assessListIDs)
        {
            var PatientScheduleDetailList = await _medicalDbContext.PatientScheduleDetail.Where(m =>
                                           m.InpatientID == inpatientID && m.DeleteFlag != "*"
                                          ).ToListAsync();

            return PatientScheduleDetailList.Where(m => assessListIDs.Contains(m.AssessListID)).ToList();
        }

        public async Task<List<string>> GetScheduleDetailIDs(string inpatientID, string[] patientScheduleMainID)
        {
            return await _medicalDbContext.PatientScheduleDetail.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*"
                         && patientScheduleMainID.Contains(m.PatientScheduleMainID))
                         .Select(m => m.PatientScheduleDetailID).ToListAsync();

        }
        /// <summary>
        /// 获取InterventionDetailID ScheduleData 根据主ID
        /// </summary>
        /// <param name="PatientScheduleMainID"></param>
        /// <returns></returns>
        public async Task<List<KeyValue>> GetKeyValueAsync(string PatientScheduleMainID)
        {
            return await _medicalDbContext.PatientScheduleDetail.Where(t => t.PatientScheduleMainID == PatientScheduleMainID.Trim()
            && t.DeleteFlag != "*").Select(m => new KeyValue { ID = m.InterventionDetailID, Value = m.ScheduleData }).ToListAsync();
        }
        /// <summary>
        /// 根据InterventionIDs 获取执行的排程明细
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="interventionIDs"></param>
        /// <returns>参数中scheduledate和scheduletime中存储的是排程的执行时间</returns>
        public async Task<List<PatientScheduleDetailInfo>> GetScheduleDetailByInterventions(string inpatientID, List<int> interventionIDs)
        {
            return await (from a in _medicalDbContext.PatientScheduleMain.Where(m => interventionIDs.Contains(m.InterventionID) && m.DeleteFlag != "*")
                          join b in _medicalDbContext.PatientScheduleDetail
                          on a.PatientScheduleMainID equals b.PatientScheduleMainID
                          where a.InpatientID == inpatientID && b.DeleteFlag != "*"
                          select new PatientScheduleDetailInfo
                          {
                              PatientScheduleMainID = a.PatientScheduleMainID,
                              ScheduleDate = a.PerformDate,
                              ScheduleTime = a.PerformTime,
                              InterventionDetailID = b.InterventionDetailID,
                              AssessListID = b.AssessListID,
                              ScheduleData = b.ScheduleData,
                              AddEmployeeID = b.AddEmployeeID,
                              InterventionID = b.InterventionID
                          }
              ).ToListAsync();
        }
        /// <summary>
        /// 根据InterventionID 获取执行的排程明细
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="interventionID"></param>
        /// <returns>参数中scheduledate和scheduletime中存储的是排程的执行时间</returns>
        public async Task<List<PatientScheduleDetailInfo>> GetScheduleDetailByInterventionID(string inpatientID, int interventionID)
        {
            return await (from a in _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID &&
                          interventionID == m.InterventionID && m.DeleteFlag != "*")
                          join b in _medicalDbContext.PatientScheduleDetail
                          on a.PatientScheduleMainID equals b.PatientScheduleMainID
                          where b.DeleteFlag != "*"
                          select new PatientScheduleDetailInfo
                          {
                              PatientScheduleMainID = a.PatientScheduleMainID,
                              ScheduleDate = a.PerformDate,
                              ScheduleTime = a.PerformTime,
                              InterventionDetailID = b.InterventionDetailID,
                              AssessListID = b.AssessListID,
                              ScheduleData = b.ScheduleData,
                              AddEmployeeID = b.AddEmployeeID
                          }
              ).ToListAsync();
        }

        /// <summary>
        /// 根据mainID集合获取明细数据
        /// </summary>
        /// <param name="mainIDs">主档ID集合</param>
        /// <returns></returns>
        public async Task<List<SchedulePerformDetail>> GetScheduleDetailsByMainIDs(params string[] mainIDs)
        {
            return await _medicalDbContext.PatientScheduleDetail.Where(m => mainIDs.Contains(m.PatientScheduleMainID) && m.DeleteFlag != "*")
                .Select(m => new SchedulePerformDetail
                {
                    PerformDate = m.ScheduleDate,
                    PerformTime = m.ScheduleTime,
                    AssessListID = m.AssessListID,
                    ScheduleData = m.ScheduleData,
                    InterventionDetailID = m.InterventionDetailID,
                    PatientScheduleMainID = m.PatientScheduleMainID,
                }).ToListAsync();
        }

        public async Task<List<SchedulePerformDetail>> GetPerformScheduleViewByInterventionAndShift(string inpatientID, int stationID, int interventionID, string shift, DateTime shiftDate)
        {
            var result = (from m in _medicalDbContext.PatientScheduleMain
                          join n in _medicalDbContext.PatientScheduleDetail
                          on new { m.InpatientID, m.PatientScheduleMainID } equals new { InpatientID = inpatientID, n.PatientScheduleMainID }
                          where m.StationID == stationID && m.InterventionID == interventionID
                           && m.Shift == shift && m.ShiftDate == shiftDate && m.CompleteMark == ScheduleCompleteMark.Completed && m.DeleteFlag != "*"
                           && m.BringToShift == "1"
                          select new SchedulePerformDetail
                          {
                              PerformDate = m.PerformDate,
                              PerformTime = m.PerformTime,
                              AssessListID = n.AssessListID,
                              ScheduleData = n.ScheduleData,
                              InterventionDetailID = n.InterventionDetailID,
                              PatientScheduleMainID = m.PatientScheduleMainID,
                          });
            return await result.ToListAsync();
        }
        public async Task<List<PatientScheduleDetailInfo>> GetDataByinpatientIDAndAssessListID(string inpatientID, int[] assessListIDs, DateTime time)
        {
            var patientScheduleDetailList = await _medicalDbContext.PatientScheduleDetail.Where(m =>
                                           m.InpatientID == inpatientID && m.DeleteFlag != "*"
                                          ).ToListAsync();
            return patientScheduleDetailList.Where(m => assessListIDs.Contains(m.AssessListID) && m.ScheduleDate.Value.Date.Add(m.ScheduleTime.Value) >= time).ToList();
        }
        /// <summary>
        /// 透过排程主序号获取排程数据(包含删除数据)
        /// </summary>
        /// <param name="patientScheduleMainID">透过排程主序号</param>
        /// <returns></returns>
        public async Task<List<PatientScheduleDetailInfo>> GetDeleteByMainIDAsync(string[] patientScheduleMainID)
        {
            var resultList = new List<PatientScheduleDetailInfo>();
            for (int i = 0; i < patientScheduleMainID.Length; i++)
            {
                var tempList = await _medicalDbContext.PatientScheduleDetail.Where(t => t.PatientScheduleMainID == patientScheduleMainID[i]).ToListAsync();
                resultList = resultList.Union(tempList).ToList();
            }
            return resultList;
        }
        /// <summary>
        /// 根据InterventionIDs 获取执行的排程明细，只取医嘱带出的和药物触发的
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="interventionIDs"></param>
        /// <returns>参数中scheduledate和scheduletime中存储的是排程的执行时间</returns>
        public async Task<List<PatientScheduleDetailInfo>> GetScheduleDetailByInterventionIDs(string inpatientID, List<int> interventionIDs)
        {
            return await (from a in _medicalDbContext.PatientScheduleMain.Where(m => interventionIDs.Contains(m.InterventionID) && m.DeleteFlag != "*")
                          join b in _medicalDbContext.PatientScheduleDetail
                          on a.PatientScheduleMainID equals b.PatientScheduleMainID
                          where a.InpatientID == inpatientID && b.DeleteFlag != "*" && (a.SourceFlag == "O" || a.SourceFlag == "T")
                          select new PatientScheduleDetailInfo
                          {
                              PatientScheduleMainID = a.PatientScheduleMainID,
                              ScheduleDate = a.PerformDate,
                              ScheduleTime = a.PerformTime,
                              InterventionDetailID = b.InterventionDetailID,
                              AssessListID = b.AssessListID,
                              ScheduleData = b.ScheduleData,
                              AddEmployeeID = b.AddEmployeeID,
                              InterventionID = b.InterventionID
                          }
              ).ToListAsync();
        }

        public async Task<List<KeyValueString>> GetSameTimeAttachedDetails(string inpatientID, List<string> patientScheduleDetailIDs, int interventionID, int assessListID)
        {
            var mainIDs = await (from a in _medicalDbContext.PatientScheduleDetail
                                 join b in _medicalDbContext.PatientScheduleDetail on new { a.InpatientID, a.ScheduleDate, a.ScheduleTime, a.AssessListID, a.ScheduleData }
                                 equals new { b.InpatientID, b.ScheduleDate, b.ScheduleTime, b.AssessListID, b.ScheduleData }
                                 where a.InpatientID == inpatientID && b.InpatientID ==inpatientID
                                    && patientScheduleDetailIDs.Contains(a.PatientScheduleDetailID)
                                 select b.PatientScheduleMainID).ToListAsync();

            return await (from b in _medicalDbContext.PatientAttachedInterventions
                          join c in _medicalDbContext.PatientScheduleMain on b.PatientAttachedInterventionID equals c.PatientInterventionID
                          join d in _medicalDbContext.PatientScheduleDetail on c.PatientScheduleMainID equals d.PatientScheduleMainID
                          where c.InpatientID == inpatientID && b.InpatientID == b.InpatientID && d.InpatientID == inpatientID
                          && mainIDs.Contains(b.PatientScheduleMainID) && b.DeleteFlag != "*" && c.DeleteFlag != "*"
                          && b.InterventionID == interventionID && d.AssessListID == assessListID
                          select new KeyValueString
                          {
                              Key = d.PatientScheduleMainID,
                              Value = d.PatientScheduleDetailID
                          }).ToListAsync();
        }

        public async Task<List<KeyValueString>> GetSameTimeAttachedDetailsByMainIDs(List<string> mainIDs, int interventionID, int assessListID)
        {
            return await (from b in _medicalDbContext.PatientAttachedInterventions
                          join c in _medicalDbContext.PatientScheduleMain on b.PatientAttachedInterventionID equals c.PatientInterventionID
                          join d in _medicalDbContext.PatientScheduleDetail on c.PatientScheduleMainID equals d.PatientScheduleMainID
                          where mainIDs.Contains(b.PatientScheduleMainID) && b.DeleteFlag != "*" && c.DeleteFlag != "*"
                          && b.InterventionID == interventionID && d.AssessListID == assessListID
                          select new KeyValueString
                          {
                              Key = d.PatientScheduleMainID,
                              Value = d.PatientScheduleDetailID
                          }).ToListAsync();
        }
        /// <summary>
        /// 根据排程主表ID和患者住院序号获取排程对应明细
        /// </summary>
        /// <param name="scheduleMainIDs">排程主表ID</param>
        /// <param name="inpatientIDs">患者住院序号</param>
        /// <returns></returns>
        public async Task<List<PatientScheduleDetailInfo>> GetScheduleDetailByMainIDs(List<string> scheduleMainIDs, List<string> inpatientIDs)
        {
            return await _medicalDbContext.PatientScheduleDetail.Where(m => inpatientIDs.Contains(m.InpatientID) && 
                scheduleMainIDs.Contains(m.PatientScheduleMainID) &&  m.DeleteFlag != "*")
                .Select(m=>new PatientScheduleDetailInfo
                {
                    PatientScheduleMainID= m.PatientScheduleMainID,
                    InpatientID= m.InpatientID,
                    InterventionID= m.InterventionID,
                    InterventionDetailID = m.InterventionDetailID,
                    AssessListID = m.AssessListID,
                }).ToListAsync();
        }
    }
}
