﻿using System.Collections.Generic;
using System.Threading.Tasks;
using ViewModel;

namespace Interconnect.Services.Interface
{
    public interface IGetHisJsonService
    {

        /// <summary>
        /// 根据CaseNumber获取医嘱数据
        /// </summary>
        /// <param name="caseNumbe"></param>
        /// <returns></returns>
        Task<List<HISOrderView>> GetOrderHisJsonByCaseNumber(string caseNumbe);
        /// <summary>
        /// 根据StationCode获取医嘱数据
        /// </summary>
        /// <param name="stationCode"></param>
        /// <returns></returns>
        Task<List<HISOrderView>> GetOrderHisJsonBystationCode(string stationCode);
    }
}