﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Interconnect.Data.Repository
{
    public class WardDeptRepository : IWardDeptRepository
    {
        private DataOutConnection _DataOutConnection = null;

        public WardDeptRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }

        /// <summary>
        /// 获取所有没有同步的科室病区对照信息
        /// </summary>
        /// <returns></returns>
        public  List<WardDeptInfo> GetAsync(int tongbuCount)
        {
            try
            {
                return  _DataOutConnection.WardDeptInfos.Where
                    (m => m.DataPumpFlag != "*"&&m.DeleteFlag!="*"
                    && ((m.Counts ?? 0) < tongbuCount)).ToList();
            }
            catch (Exception ex)
            {

                throw ex;
            }
        }

        public  List<WardDeptInfo> GetAllAsync()
        {
            try
            {
                return  _DataOutConnection.WardDeptInfos.ToList();
            }
            catch (Exception ex)
            {

                throw ex;
            }
        }
    }
}