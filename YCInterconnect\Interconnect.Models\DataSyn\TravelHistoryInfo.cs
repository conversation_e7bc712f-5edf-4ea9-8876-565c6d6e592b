﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("TravelHistory")]
    public class TravelHistoryInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///病案号
        ///</summary>
        public string ChartNo { get; set; }
        /// <summary>
        ///旅游开始时间
        ///</summary>
        public DateTime TravelStartDate { get; set; }
        /// <summary>
        ///旅游结束时间
        ///</summary>
        public DateTime TravelEndDate { get; set; }
        /// <summary>
        ///旅游地点
        ///</summary>
        public string TravelLocation { get; set; }
    }
}