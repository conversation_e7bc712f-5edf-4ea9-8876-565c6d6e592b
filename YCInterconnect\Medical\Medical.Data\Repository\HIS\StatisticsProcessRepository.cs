﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data
{
    public class StatisticsProcessRepository : IStatisticsProcessRepository
    {
        private ExternalDbContext _dbContext = null;

        public StatisticsProcessRepository(ExternalDbContext db)
        {
            _dbContext = db;
        }

        public async Task<List<StatisticsProcessOracleInfo>> GetAllAsync(DateTime startDate, DateTime endDate, string stationCode)
        {
            return await _dbContext.StatisticsProcessOracleInfos.Where(m => m.AssessDate >= startDate && m.AssessDate <= endDate && m.StationCode == stationCode).ToListAsync();
        }
        public async Task<List<StatisticsProcessOracleInfo>> GetByDateAsync(DateTime startDate, DateTime endDate)
        {
            return await _dbContext.StatisticsProcessOracleInfos.Where(m => m.AssessDate >= startDate && m.AssessDate <= endDate).ToListAsync();
        }
    }
}
