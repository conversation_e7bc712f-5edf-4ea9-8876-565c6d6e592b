﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientHandoverImageRepository : IPatientHandoverImageRepository
    {
        private readonly MedicalDbContext _dbContext = null;

        public PatientHandoverImageRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }
        public async Task<PatientHandoverImageInfo> GetPatientHandoverImageByHandoverID(string handoverID, string HandoverClass)
        {
            return await _dbContext.PatientHandoverImageInfos.Where(m => m.HandoverID == handoverID
                  && m.HandoverClass == HandoverClass && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        public async Task<List<PatientHandoverImageInfo>> GetByHandoverID(string handoverID)
        {
            return await _dbContext.PatientHandoverImageInfos.Where(m => m.HandoverID == handoverID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取图片集合根根据HandoverID
        /// </summary>
        /// <param name="handoverIDs"></param>
        /// <param name="HandoverClass"></param>
        /// <returns></returns>
        public async Task<List<PatientHandoverImageInfo>> GetPatientHandoverImageByHandoverIDListAndHandoverClass(List<string> handoverIDs, string HandoverClass)
        {
            return await _dbContext.PatientHandoverImageInfos.Where(m => handoverIDs.Contains(m.HandoverID)
                  && m.HandoverClass == HandoverClass && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 获取图片集合根根据HandoverID
        /// </summary>
        /// <param name="handoverIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientHandoverImageInfo>> GetPatientHandoverImageByHandoverIDList(List<string> handoverIDs)
        {
            return await _dbContext.PatientHandoverImageInfos.Where(m => handoverIDs.Contains(m.HandoverID)
                  && m.DeleteFlag != "*").ToListAsync();
        }
    }
}