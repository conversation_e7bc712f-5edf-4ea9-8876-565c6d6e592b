﻿using System;

namespace ViewModel
{
    /// <summary>
    /// 病人主院信息
    /// </summary>
    public class HISInpatient
    {
        /// <summary>
        ///住院号码
        ///</summary>
        public string CaseNumber { get; set; }
        /// <summary>
        ///病案号码
        ///</summary>
        public string ChartNo { get; set; }

        /// <summary>
        ///病案号码2
        ///</summary>
        public string ChartNo2 { get; set; }
        /// <summary>
        ///身分证号
        ///</summary>
        public string IdentityID { get; set; }
        /// <summary>
        ///住院次数
        ///</summary>
        public int? NumberOfAdmissions { get; set; }
        /// <summary>
        ///科别
        ///</summary>
        public string Department { get; set; }
        /// <summary>
        ///科别代码
        ///</summary>
        public string DepartmentCode { get; set; }
        /// <summary>
        ///病区(护理单元)

        ///</summary>
        public string StationName { get; set; }
        /// <summary>
        ///病区(护理单元)代码
        ///</summary>
        public string StationCode { get; set; }
        /// <summary>
        ///床位号码
        ///</summary>
        public string BedNumber { get; set; }
        /// <summary>
        ///床位代码
        ///</summary>
        public string BedCode { get; set; }
        /// <summary>
        ///ICU注记
        ///</summary>
        public string ICUFlag { get; set; }
        /// <summary>
        ///诊断码
        ///</summary>
        public string ICDCode { get; set; }
        /// <summary>
        ///诊断
        ///</summary>
        public string Diagnosis { get; set; }
        /// <summary>
        ///主治医师工号
        ///</summary>
        public string AttendingPhysicianID { get; set; }
        /// <summary>
        ///护理级别
        ///</summary>
        public string NursingLevel { get; set; }
        /// <summary>
        ///护理等级代码

        ///</summary>
        public string NursingLevelCode { get; set; }
        /// <summary>
        ///费用类型

        ///</summary>
        public string BillingPattern { get; set; }
        /// <summary>
        ///入院日期
        ///</summary>
        public DateTime AdmissionDate { get; set; }
        /// <summary>
        ///入院时间
        ///</summary>
        public DateTime AdmissionTime { get; set; }
        /// <summary>
        ///出院日期
        ///</summary>
        public DateTime? DischargeDate { get; set; }
        /// <summary>
        ///出院时间
        ///</summary>
        public DateTime? DischargeTime { get; set; }

        /// <summary>
        /// 在院标志，1标志在院，0表示出院
        /// </summary>
        public string StatusCode { get; set; }
        /// <summary>
        /// 年龄 int
        /// </summary>
        public int? Age { get; set; }

        public string AgeDetail { get; set; }
        /// <summary>
        ///病人姓名
        ///</summary>
        public string PatientName { get; set; }
        /// <summary>
        ///性别

        ///</summary>
        public string Gender { get; set; }
        /// <summary>
        ///出生日期

        ///</summary>
        public DateTime DateOfBirth { get; set; }
        /// <summary>
        ///出生时间
        ///</summary>
        public TimeSpan? TimeOfBirth { get; set; }
        /// <summary>
        ///血型
        ///</summary>
        public string BloodType { get; set; }
        /// <summary>
        ///籍贯
        ///</summary>
        public string NativePlace { get; set; }
        /// <summary>
        ///籍贯码
        ///</summary>
        public string NativePlaceCode { get; set; }

        /// <summary>
        /// 新生儿编号
        /// </summary>
        public string NewbornNumber { get; set; }

        /// <summary>
        /// 新生儿姓名
        /// </summary>
        public string NewbornNmae { get; set; }

        /// <summary>
        /// 新生儿出生日期
        /// </summary>
        public DateTime? NewbornBirthday { get; set; }

        /// <summary>
        /// 新生儿床号
        /// </summary>
        public string NewbornBedNumber { get; set; }

        /// <summary>
        /// 新生儿性别
        /// </summary>
        public string NewbornGender { get; set; }        

    }
}
