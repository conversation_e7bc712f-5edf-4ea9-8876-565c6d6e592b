﻿using System;
using System.Threading;
using System.Threading.Tasks;

namespace Medical.Common
{
    public class AsyncLock
    {
        private readonly SemaphoreSlim _semaphoreSlim = new(1, 1);

        public async Task<IDisposable> LockAsync()
        {
            await _semaphoreSlim.WaitAsync();
            return new Releaser(_semaphoreSlim);
        }

        private class Releaser : IDisposable
        {
            private readonly SemaphoreSlim _semaphoreSlim;
            public Releaser(SemaphoreSlim semaphoreSlim) => _semaphoreSlim = semaphoreSlim;
            public void Dispose() => _semaphoreSlim.Release();
        }
    }
}
