﻿//using Medical.Data.Context;
//using Medical.Data.Interface;
//using Medical.Models;
//using Microsoft.EntityFrameworkCore;
//using Microsoft.Extensions.Options;
//using NLog;
//using Interconnect.Data.Context;
//using Interconnect.Data.Interface;
//using Interconnect.Models;
//using Interconnect.Services.Interface;
//using System;
//using System.Collections.Generic;
//using System.Threading.Tasks;
//using System.Linq;

//namespace Interconnect.Services
//{
//    public class EmployeeJobDictService : IEmployeeJobDictService
//    {
//        //Mdeical
//        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
//        private readonly IEmployeeJobRepository _employeeJobRepository;
//        private readonly IStationListRepository _IStationListRepository;
//        private readonly IUserRepository _userRepository;

//        //Interconnect
//        private readonly IUnitOfWork<DataOutConnection> _unitOfWorkOut;
//        private readonly IOptions<SystemConfig> _config;
//        private readonly IEmployeeJobDictRepository _employeeJobDictRepository;
//        private static Logger _logger = LogManager.GetCurrentClassLogger();
//        private readonly ILogInfoServices _ILogInfoServices;

//        public EmployeeJobDictService(IUnitOfWork<MedicalDbContext> unitOfWork
//            , IEmployeeJobRepository employeeJobRepository
//            , IStationListRepository stationListRepository
//            , IUnitOfWork<DataOutConnection> unitOfWorkOut
//            , IEmployeeJobDictRepository employeeJobDictRepository
//            , ILogInfoServices logInfoServices
//            )
//        {
//            _unitOfWork = unitOfWork;
//            _employeeJobRepository = employeeJobRepository;
//            _IStationListRepository = stationListRepository;
//            _unitOfWorkOut = unitOfWorkOut;
//            _employeeJobDictRepository = employeeJobDictRepository;
//            _ILogInfoServices = logInfoServices;
//        }


//        /// <summary>
//        /// 如果发现有没有同步的数据，则一直获取，直至完全同步
//        /// </summary>
//        /// <returns></returns>
//        public async Task<bool> SynchronizationMain()
//        {
//            _logger.Info("开始获取未同步权限数据");
//            var OriginalList = await _employeeJobDictRepository.GetAsync();
//            var employeeJobList = await _employeeJobRepository.GetList();
//            while (OriginalList.Count > 0) //如果没有同步完成，则继续同步
//            {
//                if (!await SynchronizationDetail(OriginalList, employeeJobList))
//                {
//                    return false;
//                }
//                OriginalList = await _employeeJobDictRepository.GetAsync();
//            }
//            await DataDelete();//反向同步床位信息           
//            return true;
//        }
//        private async Task<bool> SynchronizationDetail(List<EmployeeJobDictInfo> OriginalList, List<EmployeeJobInfo> EmployeeJobList)
//        {
//            var stationList = await _IStationListRepository.GetAllAsync<StationListInfo>();
//            stationList = stationList.Where(m => m.HospitalID == _config.Value.HospitalID).ToList();

//            var Insertlist = new List<EmployeeJobInfo>();
//            EmployeeJobInfo t = null;
//            string Tablename = "EmployeeJobDict";
//            List<LogInfo> LogList = new List<LogInfo>();
//            LogInfo TempLog = null;
//            TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, " 开始进行数据同步，数据条数：" + OriginalList.Count);
//            LogList.Add(TempLog);
//            int Failcount = 0;
//            #region "数据同步"
//            foreach (var item in OriginalList)
//            {
//                item.Counts = item.Counts ?? 0;
//                item.Counts = item.Counts + 1;
//                try
//                {

//                    ////获取病区信息做转换                    
//                    //var TempstationList = stationList.Where(m => m.StationCode == item.WardCode).ToList();
//                    //if (TempstationList.Count < 1)
//                    //{
//                    //    TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, " 人员[" + item.EmployeeBasicID + "],科室信息[" + item.WardCode + "]未找到对应关系！");
//                    //    LogList.Add(TempLog);
//                    //    Failcount++;
//                    //    continue;
//                    //}

//                    // 判断数据是否已经存在(这个判断条件需要修改)
//                    var TempEmployeeJobList = EmployeeJobList.Where(m => m.EmployeeBasicID == item.EmployeeBasicID).ToList();
//                    if (TempEmployeeJobList.Count < 1)
//                    {
//                        TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, " 人员[" + item.EmployeeBasicID + "],科室信息[" + item.WardCode + "]未找到对应关系！");
//                        LogList.Add(TempLog);
//                        Failcount++;
//                        continue;
//                    }

//                    //获取插入的数据,避免数据重复插入
//                    var TempInsertlist = Insertlist.Where(m => m.EmployeeBasicID == item.EmployeeBasicID).ToList();

//                    //如果不存在进行新增
//                    if (TempEmployeeJobList.Count < 1 && TempInsertlist.Count < 1)
//                    {
//                        t = new EmployeeJobInfo
//                        {
//                            EmployeeBasicID = item.EmployeeBasicID
//                            ,
//                            DepartmentJobID = 0
//                            ,
//                            StartDate = item.StartDate
//                            ,
//                            AssumeReason = item.AssumeReason
//                            ,
//                            DimissionReason = item.DimissionReason
//                            ,
//                            PrimaryMark = item.PrimaryMark
//                            ,
//                            ModifyPersonID = _config.Value.ModifyPersonID
//                            ,
//                            ModifyDate = DateTime.Now
//                            ,
//                            DeleteFlag = ""
//                        };
//                        if (item.EndDate != null)
//                        {
//                            t.EndDate = Convert.ToDateTime(item.EndDate);

//                        };
//                        item.DataPumpFlag = "*";
//                        item.DataPumpDate = DateTime.Now;
//                        Insertlist.Add(t);
//                    }
//                }
//                catch (Exception ex)
//                {
//                    _logger.Error(Tablename, "EmployeeBasicID:[" + item.EmployeeBasicID + "] EmployeeID:[" + item.EmployeeBasicID + ex.ToString());
//                    return false;
//                }
//            }
//            #endregion

//            #region "数据更新"
//            if (OriginalList.Count >= 1)
//            {
//                try
//                {
//                    _unitOfWork.GetRepository<EmployeeJobInfo>().Insert(Insertlist);
//                    _unitOfWork.SaveChanges();
//                    _unitOfWorkOut.GetRepository<EmployeeJobDictInfo>().Update(OriginalList);
//                    _unitOfWorkOut.SaveChanges();
//                }
//                catch (Exception ex)
//                {
//                    _logger.Error(Tablename + "||保存失败||" + ex.ToString());
//                    return false;
//                }
//            }

//            TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, " 同步结束 成功：" + OriginalList.Count.ToString() + "条！");
//            LogList.Add(TempLog);
//            int ItemNo = 0;
//            string Guid = "";
//            Guid = System.Guid.NewGuid().ToString("N");
//            foreach (var item in LogList)
//            {
//                item.Guid = Guid;
//                item.ItemNo = ItemNo;
//                ItemNo++;
//            }
//            try
//            {
//                _unitOfWorkOut.GetRepository<LogInfo>().Insert(LogList);
//                _unitOfWorkOut.SaveChanges();
//            }
//            catch (Exception ex)
//            {
//                _logger.Error(Tablename + "同步成功，但写同步日志失败||" + ex.ToString());
//            }
//            _logger.Info(Tablename + "  同步完成!");
//            return true;
//            #endregion
//        }

//        /// <summary>
//        /// 数据对比，删除对方不存在的科室权限
//        /// </summary>
//        /// <returns></returns>
//        public async Task<bool> DataDelete()
//        {
//            var OriginalList = await _employeeJobDictRepository.GetAsync();
//            var employeeJobList = await _employeeJobRepository.GetList();
          

//            //对比删除
//            foreach (var item in employeeJobList)
//            {
//                //获取人员特使标记，是否使用反向删除权限               
//                var employee = await _userRepository.GetByEmployeeID(item.EmployeeBasicID);
//                int employeeFlag = -1;
//                if (employee.SpecialFlag != null)
//                {
//                    employeeFlag = Convert.ToInt32(employee.SpecialFlag);
//                }
//                if (employeeFlag == 1) //特殊人员，不进行权限的反向对比
//                {
//                    continue;
//                }
//                var tempInterconnectList = OriginalList.Where(m => m.EmployeeBasicID == item.EmployeeBasicID  ).ToList();
//                if (tempInterconnectList.Count < 0)
//                {
//                    item.DeleteFlag = "*";
//                }
//            }
//            try
//            {
//                _unitOfWork.GetRepository<EmployeeJobInfo>().Update(employeeJobList);
//                _unitOfWork.SaveChanges();
//            }
//            catch (Exception ex)
//            {
//                _logger.Error("删除employeeDepartmentSwitch信息失败||" + ex.ToString());
//                return false;
//            }
//            _logger.Info("删除employeeDepartmentSwitch信息成功");
//            return true;
//        }

//    }
//}
