﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    /// <summary>
    /// 设定档
    /// </summary>
    [Serializable]
    [Table("SyncAPIConfig")]
    public class SyncAPIConfigInfo
    {
        /// <summary>
        /// 同步接口序号
        /// </summary>
        [Key]
        [Column("APIID")]
        public int APIID { get; set; }

        /// <summary>
        /// API地址
        /// </summary>
        public string APIAddress { get; set; }

        /// <summary>
        /// 最后同步时间
        /// </summary>
        public DateTime LastSyncDateTime { get; set; }

        /// <summary>
        /// 按照时间段，每次获取的时间间隔段，避免获取长时间的数据，造成性能的下降
        /// </summary>
        public int IntervalMinutes { get; set; }

        /// <summary>
        /// 修改人员
        /// </summary>
        public string ModifyPerson { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyDateTime { get; set; }

        /// <summary>
        /// 删除标记
        /// </summary>
        public string DeleteFlag { get; set; }

        /// <summary>
        /// 根据最后的同步时间，减一定的时间获取数据，获取数据的重叠时间
        /// </summary>
        public int? OverlapMinutes { get; set; }
    }
}