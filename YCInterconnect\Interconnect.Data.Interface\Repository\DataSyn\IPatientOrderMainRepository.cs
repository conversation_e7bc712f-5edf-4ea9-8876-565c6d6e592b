﻿
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Interconnect.Models;

namespace Interconnect.Data.Interface
{
    public interface IPatientOrderMainRepository
    {
        /// <summary>
        /// 医嘱主记录
        /// </summary>
        /// <param name="tongbuCount"></param>
        /// <param name="takeRows"></param>
        /// <returns></returns>
        List<PatientOrderMainInfo> GetAsync(int tongbuCount, int takeRows);
    }
}
