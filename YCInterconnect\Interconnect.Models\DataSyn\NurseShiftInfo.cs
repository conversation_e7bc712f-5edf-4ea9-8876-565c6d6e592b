﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("NurseShift")]
    public class NurseShiftInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        /// <summary>
        ///序号
        ///</summary>
        public int? ID { get; set; }
        /// <summary>
        ///日期
        ///</summary>
        public DateTime ShiftDate { get; set; }
        /// <summary>
        ///护士站代号
        ///</summary>
        public string StationCode { get; set; }
        /// <summary>
        ///
        ///</summary>
        public string HRShiftClass { get; set; }
        /// <summary>
        ///
        ///</summary>
        public string HRShiftName { get; set; }
        /// <summary>
        ///HR班别代号
        ///</summary>
        public string HRShift { get; set; }
        /// <summary>
        ///
        ///</summary>
        public string ShowDetail { get; set; }
        /// <summary>
        ///工号
        ///</summary>
        public string EmployeeID { get; set; }       
    }
}



