﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class StatisticsInpatientDateRepository : IStatisticsInpatientDateRepository
    {
        private MedicalDbContext _dbContext = null;

        public StatisticsInpatientDateRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }

        public async Task<List<StatisticsInpatientDataLogInfo>> GetStatisticsDataAsync(int stationID, DateTime StatisticsDate, string statisticsCode)
        {
            return await _dbContext.StatisticsInpatientDataLogInfos.Where(m => m.StationID == stationID && m.StatisticsDate == StatisticsDate && m.SettingTypeCode == statisticsCode).ToListAsync();
        }
        public async Task<DateTime> GetLastTimeStamp()
        {
            return await _dbContext.StatisticsInpatientDataLogInfos.Select(m => m.StatisticsDate).MaxAsync();
        }

        public async Task<int> GetStatisticsDataCountByDateAsync(DateTime StatisticsDate)
        {
            return await _dbContext.StatisticsInpatientDataLogInfos.Where(m => m.StatisticsDate == StatisticsDate).CountAsync();
        }

        public async Task<int> GetStatisticsDataCountByDateAndCodeAsync(DateTime StatisticsDate, string statisticsCode)
        {
            return await _dbContext.StatisticsInpatientDataLogInfos.Where(m => m.StatisticsDate == StatisticsDate && m.SettingTypeCode == statisticsCode).CountAsync();
        }

        public async Task<List<StatisticsInpatientDataLogInfo>> GetStatisticsDataByDateAndCodeAsync(DateTime startDate, DateTime endDate, string statisticsCode)
        {
            return await _dbContext.StatisticsInpatientDataLogInfos.Where(m => m.StatisticsDate >= startDate && m.StatisticsDate <= endDate && m.SettingTypeCode == statisticsCode).ToListAsync();
        }
    }
}
