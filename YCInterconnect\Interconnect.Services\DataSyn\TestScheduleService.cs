﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Interconnect.Data.Context;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Interconnect.ViewModels;
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models; 
using Medical.ViewModels.Interface;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Interconnect.Services
{
    public class TestScheduleService : ITestScheduleService
    {
        //Mdeical
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly ITestItemToAssessListRepository _ITestItemToAssessListRepository;
        private readonly IPatientTestResultInfoRepository _IPatientTestResultInfoRepository;
        private readonly IInpatientDataRepository _InPatientRepository;
        private readonly ISettingDescRepository _interconnectSDRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;

        //Interconnect
        private readonly IUnitOfWork<DataOutConnection> _unitOfWorkOut;
        private readonly IOptions<SystemConfig> _config;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ILogInfoServices _ILogInfoServices;
        private readonly ICommonHelper _commonHelper;
        private readonly ILogInfoServices _logInfoServices;
        private readonly ISyncAPIConfigRepository _syncAPIConfigRepository;
        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";
        private const string MODIFYPERSONID="TongBu";

        public TestScheduleService(
              IUnitOfWork<MedicalDbContext> UnitOfWork
            , IUnitOfWork<DataOutConnection> UnitOfWorkOut
            , IOptions<SystemConfig> config
            , ITestItemToAssessListRepository TestItemToAssessListRepository
            , IPatientTestResultInfoRepository PatientTestResultInfoRepository
            , ILogInfoServices LogInfoServices
            , IInpatientDataRepository InPatientRepository
            , ICommonHelper commonHelper
            , ISettingDescRepository settingDescriptionRepository
            , ILogInfoServices logInfoServices
            , IAppConfigSettingRepository  appConfigSettingRepository
            , ISyncAPIConfigRepository syncAPIConfigRepository
            )
        {
            _unitOfWork = UnitOfWork;
            _unitOfWorkOut = UnitOfWorkOut;
            _config = config;
            _ITestItemToAssessListRepository = TestItemToAssessListRepository;
            _IPatientTestResultInfoRepository = PatientTestResultInfoRepository;
            _ILogInfoServices = LogInfoServices;
            _InPatientRepository = InPatientRepository;
            _commonHelper = commonHelper;
            _interconnectSDRepository = settingDescriptionRepository;
            _logInfoServices = logInfoServices;
            _appConfigSettingRepository = appConfigSettingRepository;
            _syncAPIConfigRepository = syncAPIConfigRepository;
        }


        /// <summary>
        /// 根据时间段同步检验数据
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncTestReportByDateTime()
        {
            //获取最后的同步时间
            var hospitalID = _config.Value.HospitalID;
            var nowDateTime = DateTime.Now;
            var lastDateTime = nowDateTime;
            var intervalminutes = 30;
            //获取检验平台接口配置
            var syncAPIConfigInfo = _syncAPIConfigRepository.GetSyncAPIConfigByApiID(14);
            if (syncAPIConfigInfo == null)
            {
                _logger.Error("没有找到ApiID为14(检验报告API)的配置");
                return false;
            }
            //每次获取检验数据的时间段 默认为30分钟内检验数据
            if (syncAPIConfigInfo.IntervalMinutes > 0)
            {
                intervalminutes = syncAPIConfigInfo.IntervalMinutes;
            }
            lastDateTime = syncAPIConfigInfo.LastSyncDateTime;
            //如果间隔时间小于5分钟，不启用同步，避免接口服务器阻塞
            if (lastDateTime.AddMinutes(5) > nowDateTime)
            {
                _logger.Info("检验同步: 最后检验数据同步时间距当前数据不足五分钟");
                return true;
            }
            //检验项目字典
            var testItemToAssessList = _ITestItemToAssessListRepository.GetByHospital();
            if (testItemToAssessList.Count == 0)
            {
                _logger.Error("TestItemToAssessList字典表无数据");
                return true;
            }
            //每次获取30分钟的数据同步频率
            while (lastDateTime < nowDateTime)
            {
                //设置数据同步，时间重叠1分钟，避免获取的数据不完整
                var startDateTime = lastDateTime.AddMinutes(-1);
                var endDateTime = lastDateTime.AddMinutes(intervalminutes);
                if (endDateTime > nowDateTime)
                {
                    endDateTime = nowDateTime;
                }
                var dictionary = new Dictionary<string, string>
                 {
                    { "StartDateTime", startDateTime.ToString("yyyy-MM-dd HH:mm")},
                    { "EndDateTime", endDateTime.ToString("yyyy-MM-dd HH:mm")},
                 };
                //获取平台数据
                var hisData = GetTestApiData(syncAPIConfigInfo.APIAddress, dictionary, hospitalID);
                //重置最后同步时间
                lastDateTime = endDateTime;
                //时间段内如果没有数据，则继续循环，直到获取数据。
                if (hisData == null || hisData.Count <= 0)
                {
                    _logger.Info("检验同步: 无检验数据需要同步 || 开始时间:" + startDateTime.ToString() + "结束时间: " + endDateTime.ToString());
                    continue;
                }
                //平台数据正式入库
                if (!await SynchronizationDetail(hisData, testItemToAssessList, hospitalID))
                {
                    _logger.Error("检验同步: 检验同步失败 || 开始时间:" + startDateTime.ToString() + "结束时间: " + endDateTime.ToString());
                    continue;
                }
                //同步成功，更新最后同步时间
                syncAPIConfigInfo.LastSyncDateTime = lastDateTime;
                try
                {
                    _unitOfWorkOut.SaveChanges();
                }
                catch (Exception ex)
                {
                    _logger.Error("检验同步: 检验同步失败 || 开始时间:" + startDateTime.ToString() + "结束时间: " + endDateTime.ToString()+"原因: "+ ex.ToString());
                    return false;
                }
                _logger.Info("检验同步: 检验同步成功 || 开始时间:" + startDateTime.ToString() + "结束时间: " + endDateTime.ToString());
            }
            return true;
        }

        /// <summary>
        /// 根据CaseNumber同步检验数据
        /// </summary>
        /// <param name="CaseNumber"></param>
        /// <returns></returns>
        public async Task<bool> SyncTestReportByCaseNumber(string caseNumber)
        {
            //获取最后的同步时间
            var hospitalID = _config.Value.HospitalID;
            string apiStr = "";
            var testItemToAssessList = _ITestItemToAssessListRepository.GetByHospital();
            if (testItemToAssessList.Count == 0)
            {
                _logger.Error("TestItemToAssessList字典表无数据");
                return false;
            }
            //获取检验报告的记录对象
            var syncAPIConfigInfo = _syncAPIConfigRepository.GetSyncAPIConfigByApiID(13);
            if (syncAPIConfigInfo == null)
            {
                _logger.Error("没有找到ApiID为13(检验报告API)的配置");
                return false;
            }
            apiStr = syncAPIConfigInfo.APIAddress;
            if (string.IsNullOrEmpty(apiStr))
            {
                _logger.Error("没有找到ApiID为13(检验报告API)的配置 地址为空");
                return false;
            }
            
            _logger.Info("检验同步开始: CaseNumber" + caseNumber);
            var data = new Dictionary<string, string>
            {
                { "CaseNumber",caseNumber }
            };
            var hisData = GetTestApiData(apiStr, data, hospitalID);
            if (hisData == null || hisData.Count <= 0)
            {
                _logger.Info("检验同步: 无检验数据需要同步 || CaseNumber" + caseNumber);
                return false;
            }
            _logger.Info("检验同步: CaseNumber" + caseNumber + "转换Json数据完成，获得检验申请" + hisData.Count() + "条！");
            if ( !await SynchronizationDetail(hisData, testItemToAssessList, hospitalID))
            {
                _logger.Error("检验同步: 检验同步失败 || CaseNumber" + caseNumber);
                return false;
            }
            _logger.Info("检验同步: 检验同步成功 || CaseNumber" + caseNumber);
            return true;
        }


        private List<TestScheduleView> GetTestApiData(string api, Dictionary<string, string> dictionary, string hospitalID)
        {
            _logger.Info("呼叫API获取检验数据");
            var hisData = GetApiData(api, dictionary, hospitalID);
            if (string.IsNullOrEmpty(hisData))
            {
                return null;
            }
            var interconnect_Data = new List<TestScheduleView>();
            try
            {
                interconnect_Data = JsonConvert.DeserializeObject<List<TestScheduleView>>(hisData);
            }
            catch (Exception ex)
            {
                _logger.Error("Json格式化失败:" + ex.ToString());
                return null;
            }
            _logger.Info("转换Json数据完成，获得检验申请" + interconnect_Data.Count() + "条！");
            return interconnect_Data;
        }

        /// <summary>
        /// 同步检验
        /// </summary>
        /// <param name="originalList"></param>
        /// <param name="testItemToAssessList"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<bool> SynchronizationDetail(List<TestScheduleView> originalList, List<TestItemToAssessListInfo> testItemToAssessList, string hospitalID)
        {
            var testItemCodeList = testItemToAssessList.Select(m => m.TestItemCode).ToList();
            //过滤无检验转换字典的数据     
            originalList = originalList.Where(m => testItemCodeList.Contains(m.TestItemCode)).OrderBy(m=>m.TestDate).ToList();
            if (originalList.Count == 0)
            {
                _logger.Info("检验同步: 无符合同步的检验数据");
                return true;
            }
            //病人基本信息
            var caseNumbers = originalList.Select(m => m.CaseNumber).Distinct().ToList();
            var inPatientDatas =await _InPatientRepository.GetInpatientListByCaseNumberListAsync(caseNumbers,hospitalID);
            if (inPatientDatas.Count == 0)
            {
                _logger.Info("检验同步: 无符合同步的病人基本信息");
                return true;
            }
            //过滤已出院数据
            var sucCaseNumbers = inPatientDatas.Select(m => m.CaseNumber).Distinct().ToList();
            originalList = originalList.Where(m => sucCaseNumbers.Contains(m.CaseNumber)).OrderBy(m => m.TestDate).ToList();
            if (originalList.Count == 0)
            {
                _logger.Info("检验同步: 无符合同步的检验数据");
                return true;
            }
            //获取历史检验数据
            var caseNumbersArr = sucCaseNumbers.ToArray();
            var oldTestDatas =await _IPatientTestResultInfoRepository.GetAsync(caseNumbersArr, originalList[0].TestDate.AddMinutes(-1));
            //新增标记;
            var insertFalg = false;
            _logger.Info(" 检验同步: 开始进行数据同步，数据条数：" + originalList.Count);
            var number = 0;
            var patientProfiles = new List<PatientProfile>();
            foreach (var item in originalList)
            {
                number++;
                //基本信息
                var successInPatientInfo = inPatientDatas.Find(m => m.CaseNumber == item.CaseNumber.Trim());
                if (successInPatientInfo == null)
                {
                    _logger.Info("病人信息有误或者病人已出院 CaseNumber: "+ item.CaseNumber);
                    continue;
                }
                //获取检验项目对照
                var successTestSetting = testItemToAssessList.Where(m => m.TestItemCode == item.TestItemCode).FirstOrDefault();
                if (successTestSetting == null)
                {
                    _logger.Info("检验转换字段为null TestItemCode:"+ item.TestItemCode);
                    continue;
                }
                _logger.Info("TestItem:  " + ListToJson.ToJson(item));
                //获取旧检验
                var testItem = oldTestDatas.Find(m=>m.CaseNumber==item.CaseNumber&&m.TestNo== item.TestNO&&m.TestCode== item.TestItemCode&&m.TestDate== item.TestDate);
                //如果不存在进行新增
                if (testItem == null )
                {
                    testItem = NewInsertPatientTestResultInfo(item, successTestSetting.AssessListID, MODIFYPERSONID);
                    insertFalg = true;
                }
                else
                {
                    //修改
                    var upFlag = UPPatientTestResultInfo(testItem, item, successTestSetting.AssessListID, MODIFYPERSONID);
                    if (upFlag)
                    {
                        insertFalg = true;
                    }
                }
                //有异动添加profile
                if (insertFalg)
                {
                    patientProfiles.Add(CreateProfile(successInPatientInfo, testItem, hospitalID, MODIFYPERSONID));
                }
                //每100条保存一次
                if (number < 100)
                {
                    continue;
                }
                TestSave(patientProfiles);
                //重置初始条件
                number = 0;
                patientProfiles = new List<PatientProfile>();
            }
            //结束保存一次
            return TestSave(patientProfiles);
        }
        /// <summary>
        /// 检验结果保存
        /// </summary>
        /// <param name="patientProfiles"></param>
        /// <returns></returns>
        private bool TestSave(List<PatientProfile> patientProfiles)
        {
            try
            {
                _unitOfWork.SaveChanges();
                _commonHelper.AddProfile(patientProfiles);
            }
            catch (Exception ex)
            {
                _logger.Error("检验同步: 同步失败||" + ex.ToString());
                return false;
            }
            return true;
        }

        /// <summary>
        /// 修改检验项目
        /// </summary>
        /// <param name="item"></param>
        /// <param name="assessListID"></param>
        private bool UPPatientTestResultInfo(PatientTestResultInfo patientTestResultInfo, TestScheduleView item, int assessListID, string modifyPersonID)
        {
            var modifyFlag = false;
            if (patientTestResultInfo.TestValue != item.TestValue)
            {
                patientTestResultInfo.TestValue = item.TestValue;
                modifyFlag = true;
            }
            if (patientTestResultInfo.Unit != item.Unit)
            {
                patientTestResultInfo.Unit = item.Unit;
                modifyFlag = true;
            }
            if (patientTestResultInfo.NormalRange != item.NormalRange)
            {
                patientTestResultInfo.NormalRange = item.NormalRange;
                modifyFlag = true;
            }

            if (patientTestResultInfo.NormalAbnormal != item.NormalAbnormal)
            {
                patientTestResultInfo.NormalAbnormal = item.NormalAbnormal;
                modifyFlag = true;
            }

            if (patientTestResultInfo.Description != item.Description)
            {
                patientTestResultInfo.Description = item.Description;
                modifyFlag = true;
            }

            if (modifyFlag)
            {
                patientTestResultInfo.ModifyPersonID = modifyPersonID;  //异动人员
                patientTestResultInfo.ModifyDate = DateTime.Now; //异动时间
                patientTestResultInfo.DeleteFlag = "";  //删除标识
                patientTestResultInfo.AssessListID = assessListID;
            }
            return modifyFlag;

        }
        /// <summary>
        /// 新增检验数据
        /// </summary>
        /// <param name="item"></param>
        /// <param name="assessListID"></param>
        /// <returns></returns>
        private PatientTestResultInfo NewInsertPatientTestResultInfo(TestScheduleView item, int assessListID, string modifyPersonID)
        {
            var t = new PatientTestResultInfo
            {
                CaseNumber = item.CaseNumber,  //病历号
                TestNo = item.TestNO, //检验单号
                TestCode = item.TestItemCode,    //检验代码
                TestDate = item.TestDate,   //检验日期
                TestItem = item.TestItem,    //项目名称
                AssessListID = assessListID,    //系统评估码                         
                TestValue = item.TestValue,   //结果值
                Unit = item.Unit,    //单位
                NormalRange = item.NormalRange, //正常范围
                NormalAbnormal = item.NormalAbnormal,  //正常或异常
                Description = item.Description, //说明
                ModifyPersonID = modifyPersonID,  //异动人员
                ModifyDate = DateTime.Now, //异动时间
                DeleteFlag = ""  //删除标识
            };
            _unitOfWork.GetRepository<PatientTestResultInfo>().Insert(t);
            return t;
        }

        private PatientProfile CreateProfile(InpatientDataInfo inPatientData, PatientTestResultInfo testResult, string hospitalID, string modifyPersonID)
        {
            var patientProfile = new PatientProfile
            {
                HospitalID = hospitalID,
                InpatientID = inPatientData.ID,
                CaseNumber = inPatientData.CaseNumber,
                ChartNo = inPatientData.ChartNo,
                PatientID = inPatientData.PatientID,
                ModelName = "HIS.Lab",
                Source = "I",
                ProfileDate = testResult.TestDate.Date,
                ProfileTime = testResult.TestDate.TimeOfDay,
                AutoAddFlag = "",
                Note = "",
                ModifyPersonID = modifyPersonID,
            };
            patientProfile.SerialNumber = inPatientData.CaseNumber + "|"
                + testResult.TestNo + "|" + testResult.TestCode;
            //调用其他方法，传入原有assessListID
            patientProfile.AssessListID = testResult.AssessListID;
            //问题
            patientProfile.AssessValue = testResult.TestValue;
            patientProfile.Unit = testResult.Unit;
            patientProfile.AbnormalFlag = testResult.NormalAbnormal;
            patientProfile.NomalRange = testResult.NormalRange;
            return patientProfile;
        }

        /// <summary>
        /// 呼叫API获取数据
        /// </summary>
        /// <param name="api"></param>
        /// <param name="dictionarys"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private string GetApiData(string api, Dictionary<string, string> dictionarys, string hospitalID)
        {
            if (string.IsNullOrEmpty(api))
            {
                return "";
            }
            //获取环境 ,1 开发环境
            //var systemOperatingEnvironment = _appConfigSettingRepository.GetConfigSettingValue(hospitalID, APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "SystemOperatingEnvironment").Result;
            var  systemOperatingEnvironment = "2";
            var resultData = "";
            if (systemOperatingEnvironment == "1")
            {
                resultData = _ILogInfoServices.GetLog("18")[0].Logs;
            }
            else
            {
                //呼叫API获得数据             
                resultData = _commonHelper.GetInterconnectData(api, dictionarys);
            }
            var printInterfaceData = 0;
            var resultPrintDate = _appConfigSettingRepository.GetConfigSettingValue( APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "PrintInterfaceData").Result;
            if (StringCheck.IsNumeric(resultPrintDate))
            {
                printInterfaceData = int.Parse(resultPrintDate);
            }

            if (printInterfaceData == 1)
            {
                _logger.Info("Api:" + api + "获取的检验数据" + ListToJson.ToJson(dictionarys) + "检验数据：" + resultData);
            }

            try
            {
                var result = JsonConvert.DeserializeObject<ResponseResult>(resultData);
                var resultDataStr = result.Data.ToString();
                if (resultDataStr == "" || resultDataStr == "[]" || resultDataStr == "[{}]" || resultDataStr == "{}")
                {
                    return "";
                }
                return resultDataStr;
            }
            catch (Exception ex)
            {
                _logger.Error("Api: " + api + "获取的检验数据ResponseResult" + ex.ToString());
                return "";
            }
        }
    }
}