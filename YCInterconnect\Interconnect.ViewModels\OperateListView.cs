﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace Interconnect.ViewModels
{
   public class OperateListView
    {
        /// <summary>
        ///	手术日期	
        ///</summary>
        public DateTime? OperateDate { get; set; }        
        /// <summary>
        ///	手术预约时间	
        ///</summary>
        public DateTime? ScheduledDatetime { get; set; }
        /// <summary>
        /// 到达手术室时间
        /// </summary>
        public DateTime? ArrivedStationDateTime { get; set; }
        /// <summary>
        ///	入手术室时间	
        ///</summary>
        public DateTime? EntryRoomDateTime { get; set; }
        /// <summary>
        ///	手术开始时间	
        ///</summary>
        public DateTime? OperationStartDateTime { get; set; }
        /// <summary>
        ///	手术结束时间	
        ///</summary>
        public DateTime? OperationEndDateTime { get; set; }
        /// <summary>
        ///	出手术室时间	
        ///</summary>
        public DateTime? ExitRoomDateTime { get; set; }       
        /// <summary>
        ///	入恢复室时间	
        ///</summary>
        public DateTime? EntryRecoveryRoomDateTime { get; set; }
        /// <summary>
        ///	出恢复室时间	
        ///</summary>
        public DateTime? ExitRecoveryRoomDateTime { get; set; }
    }
}