﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class NursingGoalRepository : INursingGoalRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public NursingGoalRepository(
              MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService
            )
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        /// <summary>
        /// 根据ID查找记录
        /// </summary>
        /// <param name="id">序号</param>
        /// <param name="language">语言码</param>
        /// <returns></returns>
        public async Task<NursingGoalInfo> GetOneAsync(int id)
        {
            var data = (List<NursingGoalInfo>)await GetCacheAsync();
            if (data != null)
            {
                return data.Where(t => t.ID == id).SingleOrDefault();
            }
            return new NursingGoalInfo();
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<NursingGoalInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("language", out var language);
            return await _medicalDbContext.NursingGoals.Where(m => m.Language == (Int32)language && m.DeleteFlag != "*").ToListAsync();
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.NursingGoal.GetKey(_sessionCommonServer);
        }

        public async Task<List<NursingGoalInfo>> GetAsync()
        {
            var datas = (List<NursingGoalInfo>)await GetCacheAsync();

            if (datas != null)
            {
                return datas;
            }

            return new List<NursingGoalInfo>();
        }
    }
}