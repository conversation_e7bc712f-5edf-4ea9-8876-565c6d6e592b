﻿ using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;
using Interconnect.Data.Interface;

namespace Interconnect.Data.Repository
{
    public class SyncAPIConfigRepository: ISyncAPIConfigRepository
    {
        private DataOutConnection _DataOutConnection = null;
        public SyncAPIConfigRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }
        public SyncAPIConfigInfo GetSyncAPIConfigByApiID(int apiID)
        {
            return _DataOutConnection.SyncAPIConfigInfos.Where(m => m.APIID == apiID).FirstOrDefault();
        }
    }
}
