﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("NursingRecord_Fall")]
    public class NRFallInfo : ModifyReturnInfo
    {
        /// <summary>
        ///	自增长编号	
        ///</summary>
        [Key]
        [Column("SN")]
        public int SN { get; set; }
        /// <summary>
        ///	就诊序号	
        ///</summary>
        public int CureNo { get; set; }
        /// <summary>
        ///	评估日期	
        ///</summary>
        public DateTime ReportDate { get; set; }
        /// <summary>
        ///	最近一年内或住院中曾发生跌倒
        ///</summary>
        public short? Falled { get; set; }
        /// <summary>
        ///	意识欠清：0否，1是	
        ///</summary>
        public short? SanityNoGood { get; set; }
        /// <summary>
        ///	主诉视觉不佳：0否，1是	
        ///</summary>
        public short? ActionNoGood { get; set; }
        /// <summary>
        ///	使用特殊药物：0否，1是	
        ///</summary>
        public short? SpecailDrug { get; set; }
        /// <summary>
        ///	活动无耐力：0否，1是	
        ///</summary>
        public short? LowBP { get; set; }
        /// <summary>
        ///	特殊人群：儿童、孕妇、残疾或
        ///</summary>
        public short? AgeGT65 { get; set; }
        /// <summary>
        ///	护理措施，如：床栏使用/告知,
        ///</summary>
        public string NurseStep { get; set; }
        /// <summary>
        ///	录入人工号	
        ///</summary>
        public string InputerCode { get; set; }
        /// <summary>
        ///	录入人姓名	
        ///</summary>
        public string InputerName { get; set; }
        /// <summary>
        ///	录入时间	
        ///</summary>
        public DateTime? InputTime { get; set; }
        /// <summary>
        ///	病区代码	
        ///</summary>
        public string WardCode { get; set; }
        /// <summary>
        ///	科室代码	
        ///</summary>
        public string DeptCode { get; set; }
        /// <summary>
        ///	床号	
        ///</summary>
        public string BedNo { get; set; }
        /// <summary>
        ///	护士长审核人工号	
        ///</summary>
        public int? HeadNurseCode { get; set; }
        /// <summary>
        ///	护士长审时间	
        ///</summary>
        public DateTime? HeadNurseTime { get; set; }
        /// <summary>
        ///	删除人工号	
        ///</summary>
        public int? DelOpCode { get; set; }
        /// <summary>
        ///	删除时间	
        ///</summary>
        public DateTime? DelTime { get; set; }
        /// <summary>
        ///	状态：0 待审核，1护士长已审核
        ///</summary>
        public int? Status { get; set; }
        /// <summary>
        ///	最后更新时间	
        ///</summary>
        public DateTime? LastUpdateTime { get; set; }
    }
}