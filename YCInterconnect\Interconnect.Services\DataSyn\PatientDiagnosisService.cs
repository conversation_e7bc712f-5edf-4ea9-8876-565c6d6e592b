﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Interconnect.ViewModels;
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.Interface;
using MedicalExternalCommon.Service;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ViewModel;

namespace Interconnect.Services
{
    public class PatientDiagnosisService : IPatientDiagnosisService
    {
        //Mdeical
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IInpatientDataRepository _inpatientDataRepository;
        private readonly IPatientDiagnosisRepository _patientDiagnosisRepository;
        //Interconnect
        private readonly IOptions<SystemConfig> _config;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly INurseEMRFileListRepository _nurseEMRFileListRepository;
        private readonly DataTableEditListService _dataTableEditListService;
        private readonly ExternalCommonService _externalCommonService;
        private readonly ICommonHelper _commonHelper;

        public PatientDiagnosisService(
              IUnitOfWork<MedicalDbContext> UnitOfWork
            , IOptions<SystemConfig> config
            , IInpatientDataRepository inpatientDataRepository
            , IAppConfigSettingRepository appConfigSettingRepository
            , INurseEMRFileListRepository nurseEMRFileListRepository
            , DataTableEditListService dataTableEditListService
            , IPatientDiagnosisRepository patientDiagnosisRepository
            , ExternalCommonService externalCommonService
            , ICommonHelper commonHelper
            )
        {
            _unitOfWork = UnitOfWork;
            _config = config;
            _inpatientDataRepository = inpatientDataRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
            _nurseEMRFileListRepository = nurseEMRFileListRepository;
            _dataTableEditListService = dataTableEditListService;
            _patientDiagnosisRepository = patientDiagnosisRepository;
            _externalCommonService = externalCommonService;
            _commonHelper = commonHelper;
        }
        /// <summary>
        /// 根据住院号同步患者诊断
        /// </summary>
        /// <param name="caseNumber">住院号</param>
        /// <returns>同步结果信息</returns>
        public async Task<string> SyncPatientDiagnosisByCaseNumber(string caseNumber)
        {
            if (string.IsNullOrEmpty(caseNumber))
            {
                return "根据住院号同步诊断失败！住院号为空！";
            }

            // 创建诊断参数
            var diagnosisParams = CreateDiagnosisParams(new Dictionary<string, string>
            {
                 { "MAIN_FLAG", "1" },
                 { "PATIENT_NO", caseNumber }
            });

            // 获取his诊断数据
            var hisDiagnosis = await GetHisPatientDiagnosis(diagnosisParams);
            if (hisDiagnosis.Count == 0)
            {
                return $"根据住院号同步诊断失败！获取诊断为空! 入参||{ListToJson.ToJson(diagnosisParams)}";
            }

            // 同步诊断数据
            return await SyncPatientDiagnosis(hisDiagnosis);
        }

        /// <summary>
        /// 根据病区Code同步在院患者诊断
        /// </summary>
        /// <param name="stationCode">病区代码</param>
        /// <param name="saveCount">每次入数据库的数量，默认为1</param>
        /// <returns>同步结果信息</returns>
        public async Task<string> SyncPatientDiagnosisByStationCode(string stationCode, int? saveCount = 1)
        {
            // 创建诊断参数
            var diagnosisParamsDictionary = new Dictionary<string, string>
            {
                { "IN_STATE", "I" },
                 { "MAIN_FLAG", "1" }
            };
            if (!string.IsNullOrEmpty(stationCode))
            {
                diagnosisParamsDictionary.Add("Ward_Code", stationCode);
            }
            var diagnosisParams = CreateDiagnosisParams(diagnosisParamsDictionary);
            // 获取his诊断数据
            var hisDiagnosis = await GetHisPatientDiagnosis(diagnosisParams);
            if (hisDiagnosis.Count == 0)
            {
                return $"根据病区Code同步诊断失败！获取诊断为空! 入参||{ListToJson.ToJson(diagnosisParams)}";
            }

            // 同步诊断数据
            return await SyncPatientDiagnosis(hisDiagnosis, saveCount);
        }

        /// <summary>
        /// 打印his诊断数据
        /// </summary>
        /// <param name="stationCode">病区代码</param>
        /// <param name="caseNumber">住院号</param>
        /// <param name="inHosipitalStatus">在院状态</param>
        /// <returns>包含his诊断数据的字典</returns>
        public async Task<Dictionary<string, object>> PrintHisDiagnosis(string stationCode, string caseNumber, string inHosipitalStatus)
        {
            // 创建诊断参数
            var diagnosisParamsDictionary = new Dictionary<string, string>
            {
                 { "MAIN_FLAG", "1" }
            };
            if (!string.IsNullOrEmpty(inHosipitalStatus))
            {
                diagnosisParamsDictionary.Add("IN_STATE", "I");
            }
            if (!string.IsNullOrEmpty(stationCode))
            {
                diagnosisParamsDictionary.Add("Ward_Code", stationCode);
            }
            if (!string.IsNullOrEmpty(caseNumber))
            {
                diagnosisParamsDictionary.Add("PATIENT_NO", caseNumber);
            }
            var diagnosisParams = CreateDiagnosisParams(diagnosisParamsDictionary);
            // 获取his诊断数据
            var hisData = await GetHisPatientDiagnosis(diagnosisParams);
            var returnData = new Dictionary<string, object> { { "hisData", hisData } };

            if (hisData.Count == 0)
            {
                return returnData;
            }

            // 处理并转换his诊断数据
            var isConverHisData = hisData
                .GroupBy(item => item.PATIENT_NO)
                .Select(group => ProcessMainDiagnosis(group.ToList()))
                .ToList();

            returnData.Add("isConverHisData", isConverHisData);
            return returnData;
        }

        /// <summary>
        /// 创建诊断参数列表
        /// </summary>
        /// <param name="parameters">参数字典</param>
        /// <returns>诊断参数列表</returns>
        private static List<Dictionary<string, string>> CreateDiagnosisParams(Dictionary<string, string> parameters)
        {
            return parameters.Select(param => new Dictionary<string, string> { { "Param", param.Key }, { "Value", param.Value } }).ToList();
        }
        /// <summary>
        /// 同步患者诊断
        /// </summary>
        /// <param name="hisDiagnosis"></param>
        /// <param name="saveCount"></param>
        /// <returns></returns>
        private async Task<string> SyncPatientDiagnosis(List<DiagnosisItem> hisDiagnosis, int? saveCount = 1)
        {
            var caseNumbers = hisDiagnosis.Select(m => m.PATIENT_NO).Distinct().ToList();
            var inpatientDataInfos = _inpatientDataRepository.GetByCaseNumbersAll(caseNumbers);
            var oldDiagnosis = await _patientDiagnosisRepository.GetPatientDiagnosisByCaseNumber(caseNumbers);
            var needUpdateCaseNumberList = new List<string>();
            var syncErrCaseNumbers = new List<string>();

            foreach (var caseNumber in caseNumbers)
            {
                var sucHisDiagnosis = hisDiagnosis.Where(m => m.PATIENT_NO == caseNumber).OrderBy(m => m.DIAG_DATE).ToList();
                if (sucHisDiagnosis.Count == 0)
                {
                    LogError("his诊断为空", caseNumber);
                    syncErrCaseNumbers.Add(caseNumber);
                    continue;
                }

                var inpatientDataInfo = inpatientDataInfos.FirstOrDefault(m => m.CaseNumber == caseNumber);
                if (inpatientDataInfo == null)
                {
                    LogError("患者信息InpatientData找不到", caseNumber);
                    syncErrCaseNumbers.Add(caseNumber);
                    continue;
                }

                var sucOldDiagnosis = oldDiagnosis.Where(m => m.CaseNumber == caseNumber).ToList();
                var updateFlag = await SyncPatientDiagnosiItem(sucHisDiagnosis, inpatientDataInfo, sucOldDiagnosis);
                _logger.Info($"updateFlag||{updateFlag}CaseNumber||{caseNumber}");

                if (updateFlag)
                {
                    await UpdateEmrFileListAsync(inpatientDataInfo.ID);
                    needUpdateCaseNumberList.Add(inpatientDataInfo.CaseNumber);
                }

                if (needUpdateCaseNumberList.Count >= saveCount)
                {
                    SaveChangesAsync(needUpdateCaseNumberList);
                }
            }

            SaveChangesAsync(needUpdateCaseNumberList);
            return syncErrCaseNumbers.Count == 0 ? "同步成功" : $"同步失败住院号|| {ListToJson.ToJson(syncErrCaseNumbers)}";
        }

        /// <summary>
        /// 异动信息提交
        /// </summary>
        /// <param name="needUpdateCaseNumberList"></param>
        private void SaveChangesAsync(List<string> needUpdateCaseNumberList)
        {
            try
            {
                _unitOfWork.SaveChanges();
                needUpdateCaseNumberList.Clear();
            }
            catch (Exception ex)
            {
                _logger.Error($"诊断数据同步失败,needUpdateCaseNumberList||{ListToJson.ToJson(needUpdateCaseNumberList)} ex||{ex}");
            }
        }
        private static void LogError(string message, string caseNumber)
        {
            _logger.Error($"同步患者诊断失败！{message} CaseNumber||{caseNumber}");
        }

        /// <summary>
        /// 同步HIS诊断数据
        /// </summary>
        /// <param name="hisData"></param>
        /// <returns></returns>
        private async Task<bool> SyncPatientDiagnosiItem(List<DiagnosisItem> hisDiagnosis, InpatientDataInfo inpatient, List<PatientDiagnosisInfo> oldDiagnosis)
        {
            //组装his诊断View
            var hisData = ProcessMainDiagnosis(hisDiagnosis);
            _logger.Info("hisData||" + ListToJson.ToJson(hisData));
            hisData.CaseNumber = hisDiagnosis[0].PATIENT_NO;
            var diagnosisName = "";
            var diagnosisUPFlag = false;
            if (string.IsNullOrEmpty(hisData.InhospDiagnosisCode) && string.IsNullOrEmpty(hisData.OuthospDiagnosisCode))
            {
                return false;
            }
            //在院诊断
            if (!string.IsNullOrEmpty(hisData.InhospDiagnosisCode) && string.IsNullOrEmpty(hisData.OuthospDiagnosisCode))
            {
                diagnosisName = GetDiagnosisName(hisData.InhospDiagnosis, hisData.InhospDiagnosisDesc);
                if (inpatient.Diagnosis != diagnosisName)
                {
                    diagnosisUPFlag = true;
                    inpatient.Diagnosis = diagnosisName;
                    inpatient.ICDCode = hisData.InhospDiagnosisCode;
                    _logger.Info("住院号||" + inpatient.CaseNumber + "中更改后的在院诊断||" + inpatient.Diagnosis);
                    await SavePatientDiagnosisAsync(hisData, inpatient, "P");
                    await SavePatientDiagnosisProfile(inpatient);
                }
                return diagnosisUPFlag;
            }
            //出院诊断
            diagnosisName = GetDiagnosisName(hisData.OuthospDiagnosis, hisData.OuthospDiagnosisDesc);
            if (inpatient.Diagnosis != diagnosisName)
            {
                diagnosisUPFlag = true;
                inpatient.Diagnosis = diagnosisName;
                inpatient.ICDCode = hisData.OuthospDiagnosisCode;
                _logger.Info("住院号||" + inpatient.CaseNumber + "中更改后的出院诊断||" + inpatient.Diagnosis);
                await SavePatientDiagnosisAsync(hisData, inpatient, "D");
            }
            return diagnosisUPFlag;
        }

        /// <summary>
        /// 保存诊断profile
        /// </summary>
        /// <param name="inpatient"></param>
        /// <returns></returns>
        private async Task SavePatientDiagnosisProfile(InpatientDataInfo inpatient)
        {
            //无ICDCode 不需要添加Profile
            if (string.IsNullOrEmpty(inpatient.ICDCode))
            {
                return;
            }
            var patientProfiles = new List<PatientProfile>();
            var ids = await _externalCommonService.GetAssessListIDByICDCode(inpatient.ICDCode, inpatient.DepartmentListID);

            foreach (var item in ids)
            {
                var profile = _externalCommonService.CreateProfile(inpatient, "Diagnosis", item, "", _config.Value.HospitalID, "TongBu");
                patientProfiles.Add(profile);
            }
            _commonHelper.AddProfile(patientProfiles);
        }

        /// <summary>
        /// 保存病人诊断
        /// </summary>
        /// <param name="hisData"></param>
        /// <param name="inpatient"></param>
        /// <param name="diagnosisType"></param>
        /// <returns></returns>
        private async Task SavePatientDiagnosisAsync(HISDiagnosis hisData, InpatientDataInfo inpatient, string diagnosisType)
        {
            _logger.Info("保存InpatientData诊断");
            var diagnosisList = await _patientDiagnosisRepository.GetAsync(inpatient.ID);
            var oldDiagnosis = diagnosisList.FirstOrDefault(m => m.DiagnosisType == diagnosisType && m.DeleteFlag != "*");

            if (oldDiagnosis == null)
            {
                var newDiagnosis = CreateNewDiagnosis(hisData, inpatient, diagnosisType);
                _unitOfWork.GetRepository<PatientDiagnosisInfo>().Insert(newDiagnosis);
            }
            else
            {
                UpdateExistingDiagnosis(oldDiagnosis, hisData, diagnosisType);
            }
        }
        /// <summary>
        /// 新增诊断
        /// </summary>
        /// <param name="hisData"></param>
        /// <param name="inpatient"></param>
        /// <param name="diagnosisType"></param>
        /// <returns></returns>
        private static PatientDiagnosisInfo CreateNewDiagnosis(HISDiagnosis hisData, InpatientDataInfo inpatient, string diagnosisType)
        {
            var diagnosis = new Medical.Models.PatientDiagnosisInfo
            {
                InpatientID = inpatient.ID,
                PatientID = inpatient.PatientID,
                StationID = inpatient.StationID,
                DepartmentListID = inpatient.DepartmentListID,
                CaseNumber = inpatient.CaseNumber,
                ChartNo = inpatient.ChartNo,
                DiagnosisType = diagnosisType,
                ModifyDate = DateTime.Now,
                AddDate = DateTime.Now,
                AddEmployeeID = "System",
                DeleteFlag = ""
            };

            if (diagnosisType == "P")
            {
                diagnosis.DiagnosisCode = hisData.InhospDiagnosisCode;
                diagnosis.DiagnosisName = GetDiagnosisName(hisData.InhospDiagnosis, hisData.InhospDiagnosisDesc);
            }
            else if (diagnosisType == "D")
            {
                diagnosis.DiagnosisCode = hisData.OuthospDiagnosisCode;
                diagnosis.DiagnosisName = GetDiagnosisName(hisData.OuthospDiagnosis, hisData.OuthospDiagnosisDesc);
            }

            return diagnosis;
        }
        /// <summary>
        /// 修改诊断
        /// </summary>
        /// <param name="oldDiagnosis"></param>
        /// <param name="hisData"></param>
        /// <param name="diagnosisType"></param>
        private static void UpdateExistingDiagnosis(PatientDiagnosisInfo oldDiagnosis, HISDiagnosis hisData, string diagnosisType)
        {
            if (diagnosisType == "P")
            {
                oldDiagnosis.DiagnosisName = GetDiagnosisName(hisData.InhospDiagnosis, hisData.InhospDiagnosisDesc);
            }
            else if (diagnosisType == "D")
            {
                oldDiagnosis.DiagnosisName = GetDiagnosisName(hisData.OuthospDiagnosis, hisData.OuthospDiagnosisDesc);
            }
            oldDiagnosis.ModifyDate = DateTime.Now;
        }
        /// <summary>
        /// 筛选病人医嘱取用字段
        /// </summary>
        /// <param name="diagnosis"></param>
        /// <param name="diagnosisDesc"></param>
        /// <returns></returns>
        private static string GetDiagnosisName(string diagnosis, string diagnosisDesc)
        {
            if (string.IsNullOrEmpty(diagnosis) && string.IsNullOrEmpty(diagnosisDesc))
            {
                return null;
            }
            if (!string.IsNullOrEmpty(diagnosis) && string.IsNullOrEmpty(diagnosisDesc))
            {
                return diagnosis;
            }
            if (string.IsNullOrEmpty(diagnosis) && !string.IsNullOrEmpty(diagnosisDesc))
            {
                if (diagnosisDesc.Length > 24)
                {
                    return null;
                }
                return diagnosisDesc;
            }
            if (!string.IsNullOrEmpty(diagnosis) && !string.IsNullOrEmpty(diagnosisDesc))
            {
                if (diagnosisDesc.Length > 24)
                {
                    return diagnosis;
                }
                else
                {
                    return diagnosisDesc;
                }
            }
            return diagnosis;
        }

        /// <summary>
        /// 更新病人电子病历异动
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task UpdateEmrFileListAsync(string inpatientID)
        {
            var nurseEMRFileViews = await _nurseEMRFileListRepository.GetAllNurseEMRFileViews(inpatientID);
            if (nurseEMRFileViews.Count == 0)
            {
                return;
            }
            foreach (var item in nurseEMRFileViews)
            {
                await _dataTableEditListService.AddEditLog(item.InpatientID, item.StationID, "", item.FileClass, item.SerialNumber, item.RecordListID);
            }
        }
        /// <summary>
        /// 详细处理诊断
        /// </summary>
        /// <param name="diagnosis"></param>
        /// <param name="mainDiagnosis"></param>
        private static HISDiagnosis ProcessMainDiagnosis(List<DiagnosisItem> mainDiagnosis)
        {
            var diagnosis = new HISDiagnosis();
            foreach (var dia in mainDiagnosis)
            {
                if (string.IsNullOrEmpty(dia.DIAG_KIND_NAME))
                    continue;
                switch (dia.DIAG_KIND_NAME.Trim())
                {
                    case "初步诊断":
                        diagnosis.InhospDiagnosis = dia.DIAG_NAME;
                        diagnosis.InhospDiagnosisCode = dia.DIAG_CODE;
                        break;
                    case "最后诊断":
                        diagnosis.OuthospDiagnosis = dia.DIAG_NAME;
                        diagnosis.OuthospDiagnosisCode = dia.DIAG_CODE;
                        break;
                    case "修正诊断":
                        diagnosis.ModifyDiagnosis = dia.DIAG_NAME;
                        diagnosis.ModifyDiagnosisCode = dia.DIAG_CODE;
                        break;
                }
            }
            return diagnosis;
        }

        /// <summary>
        /// 根据获取患者his诊断信息
        /// </summary>
        /// <param name="dictionarys"></param>
        /// <returns></returns>
        private async Task<List<DiagnosisItem>> GetHisPatientDiagnosis(List<Dictionary<string, string>> dictionarys)
        {
            if (dictionarys == null)
            {
                _logger.Error("获取his诊断数据失败！接口入参为NULL");
                return [];
            }

            var hisDisgnosisDictionarys = new Dictionary<string, object>
            {
                 { "ViewName", "v_ccc_diag" },
                 { "Params", dictionarys }
            };

            var api = await _appConfigSettingRepository.GetConfigSettingValue("Configs", "GetPatientDiagnosisByCaseNumber");
            if (string.IsNullOrEmpty(api))
            {
                _logger.Error("获取诊断的配置失败，settingCode：GetPatientDiagnosisByCaseNumber");
                return [];
            }

            var postData = JsonConvert.SerializeObject(hisDisgnosisDictionarys);
            string hisData;

            try
            {
                hisData = await HttpHelper.HttpPostAsync(api, postData);
            }
            catch (Exception ex)
            {
                LogError("获取诊断数据失败", api, dictionarys, ex);
                return [];
            }

            _logger.Info(hisData);

            if (hisData == "{}")
            {
                _logger.Error($"获取诊断数据为{{}} 地址||{api} 入参||{ListToJson.ToJson(dictionarys)}");
                return [];
            }

            _logger.Info($"序列化获取后的数据 {hisData}");

            NewHISDiagnosis diagnosisData;
            try
            {
                diagnosisData = JsonConvert.DeserializeObject<NewHISDiagnosis>(hisData);
            }
            catch (Exception ex)
            {
                LogError("患者诊断信息转对象失败", api, dictionarys, ex);
                return [];
            }

            _logger.Info("患者诊断信息由JSON转换完成");

            if (diagnosisData?.data == null)
            {
                _logger.Error($"获取诊断数据为空！地址||{api} 入参||{ListToJson.ToJson(dictionarys)}");
            }

            return diagnosisData?.data ?? [];
        }

        private static void LogError(string message, string api, List<Dictionary<string, string>> dictionarys, Exception ex)
        {
            _logger.Error($"{message}！地址||{api} 入参||{ListToJson.ToJson(dictionarys)}");
            _logger.Error(ex.ToString());
        }

    }
}