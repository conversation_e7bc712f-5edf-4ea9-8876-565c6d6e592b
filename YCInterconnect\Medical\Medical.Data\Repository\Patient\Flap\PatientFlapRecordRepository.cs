﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientFlapRecordRepository : IPatientFlapRecordRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;

        public PatientFlapRecordRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 根据主记录ID获取主记录
        /// </summary>
        /// <param name="recordID">主记录ID</param>
        /// <returns></returns>
        public async Task<PatientFlapRecordInfo> GetRecordInfoByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientFlapRecordInfos.FirstOrDefaultAsync(m =>
            m.PatientFlapRecordID == recordID && m.DeleteFlag != "*");
        }
        /// <summary>
        /// 获取主记录ByInpatientID
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <returns></returns>
        public async Task<List<FlapRecordView>> GetRecordViewsByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientFlapRecordInfos.Where(m =>
            m.InpatientID == inpatientID && m.DeleteFlag != "*").Select(m => new FlapRecordView
            {
                PatientFlapRecordID = m.PatientFlapRecordID,
                StationID = m.StationID,
                DepartmentListID = m.DepartmentListID,
                StartDate = m.StartDate,
                StartTime = m.StartTime,
                EndDate = m.EndDate,
                EndTime = m.EndTime,
                BodyPartJson = m.BodyPartJson,
                BodyPartName = m.BodyPartName,
                SameBodyPartSort = m.SameBodyPartSort,
                UserID = m.AddEmployeeID,
            }).ToListAsync();
        }
        /// <summary>
        /// 获取拼接后的部位名称
        /// </summary>
        /// <param name="recordView">当前主记录</param>
        /// <param name="recordViews">全部主记录</param>
        /// <returns></returns>
        public string GetBodyShowName(FlapRecordView recordView, List<FlapRecordView> recordViews)
        {
            // 存在多个同部位主记录，部位才拼接显示序号
            var samePartCount = recordViews.Count(m => m.BodyPartJson == recordView.BodyPartJson);
            return recordView.BodyPartName +
                $"{(samePartCount > 1 ? recordView.SameBodyPartSort.ToString() : "")}";
        }
        /// <summary>
        /// 获取病人所有的主记录ID
        /// </summary>
        /// <param name="inpatientID">住院序号</param>
        /// <returns></returns>
        public async Task<List<string>> GetRecordIDsByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientFlapRecordInfos.Where(m =>
            m.InpatientID == inpatientID && m.DeleteFlag != "*").OrderBy(m => m.StartDate)
            .ThenBy(m => m.StartTime).Select(m => m.PatientFlapRecordID).ToListAsync();
        }
        /// <summary>
        /// 获取主记录对应的部位信息
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <returns>
        /// Key：RecordID
        /// Value：身体部位View
        /// </returns>
        public async Task<Dictionary<string, PatientBodyPartView>> GetRecordBodyPartViewByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientFlapRecordInfos.Where(m =>
           m.InpatientID == inpatientID && m.DeleteFlag != "*")
                .ToDictionaryAsync(k => k.PatientFlapRecordID, v => new PatientBodyPartView
                {
                    BodyPartJson = v.BodyPartJson,
                    BodyPartName = v.BodyPartName,
                    SameBodyPartSort = v.SameBodyPartSort
                });
        }
    }
}
