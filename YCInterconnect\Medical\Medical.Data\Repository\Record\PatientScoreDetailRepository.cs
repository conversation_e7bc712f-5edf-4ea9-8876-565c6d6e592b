﻿/**
 * 新增GetScoreValueByRecordListIDs方法， 用于获取相同recordListID最后一次记录  2022-04-26 苏军志
 */
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.Data;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientScoreDetailRepository : IPatientScoreDetailRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientScoreDetailRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<List<PatientScoreDetailInfo>> GetScoreDetial(string patientScoreMainID)
        {
            return await _medicalDbContext.PatientScoreDetailInfos.Where(m => m.PatientScoreMainID == patientScoreMainID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientScoreDetailInfo>> GetScoreDetialByScoreID(string patientScoreMainID)
        {
            return await _medicalDbContext.PatientScoreDetailInfos.Where(m => m.PatientScoreMainID == patientScoreMainID).ToListAsync();
        }

        public async Task<List<PatientScoreDetailInfo>> GetScoreDetailByScoreMainID(string patientScoreMainID)
        {
            return await _medicalDbContext.PatientScoreDetailInfos.Where(m => m.PatientScoreMainID == patientScoreMainID)
                .Select(m => new PatientScoreDetailInfo
                {
                    RecordFormatID = m.RecordFormatID,
                    DeleteFlag = m.DeleteFlag
                }).ToListAsync();
        }

        public async Task<List<PatientScoreDetailInfo>> GetScoreDetialBySourceID(string inpatientID, string sourceID)
        {
            return await _medicalDbContext.PatientScoreDetailInfos.Where(m => m.InpatientID == inpatientID && m.SourceID == sourceID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<int>> GetScoreDetialRecordFormatIDBySourceID(string inpatientID, string sourceID)
        {
            return await _medicalDbContext.PatientScoreDetailInfos.Where(m => m.InpatientID == inpatientID
            && m.SourceID == sourceID && m.DeleteFlag != "*").Select(m => m.RecordFormatID).ToListAsync();
        }

        public async Task<List<PatientScoreDetailInfo>> GetScoreDetialByID(string[] patientScoreMainID)
        {
            return await _medicalDbContext.PatientScoreDetailInfos.Where(m => patientScoreMainID.Contains(m.PatientScoreMainID)
          ).ToListAsync();

        }

        public async Task<List<PatientNowTube>> GetPatientTubeRisk(string scoreMainID, int language, string hospitalID)
        {
            var query = await (from a in _medicalDbContext.PatientScoreDetailInfos
                               join b in _medicalDbContext.PatientTubeRecordInfos on a.PatientTubeRecordID equals b.PatientTubeRecordID
                               join c in _medicalDbContext.BodyPartListInfos on new { ID = b.BodyPartID, Language = language } equals new { c.ID, c.Language }
                               join d in _medicalDbContext.TubeListInfos on new { ID = b.TubeID, Language = language, HospitalID = hospitalID } equals new { d.ID, d.Language, d.HospitalID }
                               where a.PatientScoreMainID == scoreMainID
                               select new PatientNowTube
                               {
                                   PatientTubeRecordID = a.PatientTubeRecordID,
                                   TubeAssessListID = d.TubeAssessListID,
                                   BodyPartName = c.BodyPartName,
                                   TubeName = d.TubeShortName,
                                   TubeNumber = b.TubeNumber
                               }).ToListAsync();
            return query;
        }

        public async Task<List<ScoreData>> GetScoreDatas(string scoreMainID)
        {
            return await _medicalDbContext.PatientScoreDetailInfos
                .Where(m => m.PatientScoreMainID == scoreMainID && m.DeleteFlag != "*")
                .Select(m => new ScoreData
                {
                    InpatientID = m.InpatientID,
                    RecordListID = m.RecordListID,
                    PatientScoreMainID = m.PatientScoreMainID,
                    RecordFormatID = m.RecordFormatID,
                    ModifyDate = m.ModifyDate,
                    SpecialListType = m.SpecialListType
                }).ToListAsync();
        }


        public async Task<List<ScoreData>> GetPatientScoreDetailByMainIDs(List<string> scoreMainIDs)
        {
            return await _medicalDbContext.PatientScoreDetailInfos
                .Where(m => scoreMainIDs.Contains(m.PatientScoreMainID) && m.DeleteFlag != "*")
                .Select(m => new ScoreData
                {
                    InpatientID = m.InpatientID,
                    RecordListID = m.RecordListID,
                    PatientScoreMainID = m.PatientScoreMainID,
                    RecordFormatID = m.RecordFormatID,
                    ModifyDate = m.ModifyDate
                }).ToListAsync();
        }


    }
}
