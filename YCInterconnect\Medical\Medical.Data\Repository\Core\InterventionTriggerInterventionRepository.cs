﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class InterventionTriggerInterventionRepository : IInterventionTriggerInterventionRepository
    {
        private MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public InterventionTriggerInterventionRepository(MedicalDbContext medicalDbContext, IMemoryCache memoryCache, SessionCommonServer sessionCommonServer, GetCacheService getCacheService)
        {
            _medicalDbContext = medicalDbContext;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<InterventionTriggerInterventionInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            if (int.TryParse(hospitalID?.ToString(), out int parsedHospitalID))
            {
                return await _medicalDbContext.InterventionTriggerInterventionInfos
                    .Where(m => m.HospitalID == parsedHospitalID && m.DeleteFlag != "*")
                    .ToListAsync();
            }
            else
            {
                // 如果 hospitalID 无法转换为 int，处理这个情况
                return new List<InterventionTriggerInterventionInfo>(); 
            }
        }

        public string GetCacheType()
        {
            return CacheType.InterventionTriggerIntervention.GetKey(_sessionCommonServer);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }
    }
}
