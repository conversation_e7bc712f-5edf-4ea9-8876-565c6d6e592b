﻿/*
 2021-12-30 2291 护理计划需要可以调整时间,重构时新增取得病人问题相关因素方法(GetRelatedFactorIDByPatientProblemID) -正元
 */
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientRelatedFactorRepository : IPatientRelatedFactorRepository
    {
        private MedicalDbContext _dbContext = null;

        public PatientRelatedFactorRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }

        /// <summary>
        /// 获取病人问题相关因素
        /// </summary>
        /// <param name="patientProblemID">病人护理问题序号</param>
        /// <returns></returns>
        public async Task<List<PatientRelatedFactorInfo>> GetAsync(string patientProblemID)
        {
            return await _dbContext.PatientRelatedFactors.Where(m => m.PatientProblemID == patientProblemID && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 获取病人相关因素
        /// </summary>        
        /// <param name="patientAssessMainID">病人评估序号</param>
        /// <returns></returns>
        public async Task<List<PatientRelatedFactorInfo>> GetByPatientAssessID(string patientAssessMainID)
        {
            return await _dbContext.PatientRelatedFactors.Where(m => m.PatientAssessMainID == patientAssessMainID && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 获取病人问题相关因素
        /// </summary>
        /// <param name="patientRelatedFactorID">病人护理问题相关因素序号</param>
        /// <returns></returns>
        public async Task<PatientRelatedFactorInfo> GetByIDAsync(string patientRelatedFactorID)
        {
            return await _dbContext.PatientRelatedFactors.Where(m => m.ID == patientRelatedFactorID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        //2021-12-28 取得病人问题相关因素
        public async Task<List<int>> GetRelatedFactorIDByPatientProblemID(string patientProblemID)
        {
            return await _dbContext.PatientRelatedFactors.Where(m => m.PatientProblemID == patientProblemID && m.DeleteFlag != "*")
                .Select(m => m.RelatedFactorID)
                .ToListAsync();
        }

        public async Task<List<PatientRelatedFactorInfo>> GetByIDAsync(List<string> patientRelatedFactorIDs)
        {
            return await _dbContext.PatientRelatedFactors.Where(m => patientRelatedFactorIDs.Contains(m.ID) && m.DeleteFlag != "*").ToListAsync();
        }
    }
}