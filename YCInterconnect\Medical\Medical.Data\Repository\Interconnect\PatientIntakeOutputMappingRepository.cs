﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.Models.Interconnect;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository.Interconnect
{
    public class PatientIntakeOutputMappingRepository : IPatientIntakeOutputMappingRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;

        public PatientIntakeOutputMappingRepository(MedicalDbContext medicalDbContext)
        {
            _medicalDbContext = medicalDbContext;
        }

        public async Task<List<PatientIntakeOutputMappingInfo>> GetMappingDatasAsync(string caseNumber)
        {
            return await _medicalDbContext.PatientIntakeOutputMappingInfos.Where(m => m.CaseNumber == caseNumber && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientIntakeOutputMappingInfo>> GetByIODateTime(DateTime startTime, DateTime endTime, string inpatientID)
        {
            var list = await _medicalDbContext.PatientIntakeOutputMappingInfos.Where(m =>
             m.InpatientID == inpatientID && m.IODate >= startTime.Date && m.IODate <= endTime.Date && m.DeleteFlag != "*").ToListAsync();

            return list.Where(m => m.IODate.Add(m.IOTime) >= startTime && m.IODate.Add(m.IOTime) <= endTime).ToList();
        }

        public async Task<List<PatientIntakeOutputInfo>> GetViewByInpatientIDAsync(string inpatientID)
        {
            return await _medicalDbContext.PatientIntakeOutputMappingInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*")
                           .Select(m => new PatientIntakeOutputInfo
                           {
                               InpatientID = m.InpatientID,
                               StationID = m.StationID,
                               IODate = m.IODate,
                               IOTime = m.IOTime,
                               IntakeOutputKind = m.IntakeOutputKind,
                               IntakeOuputNote = m.IntakeOuputNote,
                               Color = "",
                               Characteristic = "",
                               Smell = "",
                               PatientTubeRecordID = "",
                               PatientTubeCareMainID = "",
                               IntakeOutputVolume = m.IntakeOutputVolume,
                               IntakeOutputSettingID = m.IntakeOutputSettingID ?? 0,
                               BedNumber = m.BedNumber,
                               SourceType = "ICCA",
                               //借用字段显示内容
                               InPutContent = m.ItemName
                           }).ToListAsync();
        }
        /// <summary>
        /// 根据日期范围获取病人的仪器出入量数据
        /// </summary>
        /// <param name="inpatientID">病人在ccc唯一ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns></returns>
        public async Task<List<PatientIntakeOutputMappingInfo>> GetByIODateAsync(string inpatientID, DateTime startDate, DateTime endDate)
        {
            var list = await _medicalDbContext.PatientIntakeOutputMappingInfos.Where(m =>
                m.InpatientID == inpatientID && m.IODate >= startDate.Date && m.IODate <= endDate.Date && m.DeleteFlag != "*").ToListAsync();
            return list;
        }
    }
}
