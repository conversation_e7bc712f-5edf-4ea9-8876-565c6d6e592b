﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientOperationRecordMainRepository : IPatientOperationRecordMainRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;

        public PatientOperationRecordMainRepository(MedicalDbContext medicalDbContext)
        {
            _medicalDbContext = medicalDbContext;
        }

        /// <summary>
        /// 根据手术记录主表ID获取记录
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        public async Task<PatientOperationRecordMainInfo> GetByMainIDAsync(string mainID)
        {
            return await _medicalDbContext.PatientOperationRecordMainInfos.Where(m => m.PatientOperationRecordMainID == mainID
            && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据手术表ID获取手术记录表数据
        /// </summary>
        /// <param name="patientOperationID"></param>
        /// <returns></returns>
        public async Task<PatientOperationRecordMainInfo> GetByOperationIDAsync(string patientOperationID)
        {
            return await _medicalDbContext.PatientOperationRecordMainInfos.Where(m => m.PatientOperationID == patientOperationID
            && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据InpatientID查找患者所有的手术
        /// </summary>
        /// <param name="InpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientOperationRecordMainInfo>> GetByInpatientID(string InpatientID)
        {
            return await _medicalDbContext.PatientOperationRecordMainInfos.Where(m => m.InpatientID == InpatientID
            && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<PatientOperationRecordMainInfo> GetByHISOpreRationNo(string HISOpreRationNo)
        {
            return await _medicalDbContext.PatientOperationRecordMainInfos.Where(m => m.PatientOperationID == HISOpreRationNo
            && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
    }
}