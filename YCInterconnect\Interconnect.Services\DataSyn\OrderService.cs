﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Interconnect.Data.Interface;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using NLog;
using System.Linq;
using Microsoft.Extensions.Options;
using Medical.Data.Context;
using Interconnect.Data.Context;
using Medical.Common;
using Newtonsoft.Json;
using Arch.EntityFrameworkCore.UnitOfWork;

namespace Interconnect.Services
{
    public class OrderService : IOrderService
    {
        //Mdeical
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IPhysicianOrderRepository _IPhysicianOrderRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        //Interconnect
        private readonly IUnitOfWork<DataOutConnection> _unitOfWorkOut;
        private readonly IOptions<SystemConfig> _config;
        private readonly IOrderRepository _IOrderRepository;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ILogInfoServices _ILogInfoServices;
        private readonly ICommonHelper _commonHelper;
        private readonly ISettingDescRepository _ICSettingDescriptionRepository;

      
        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";
        /// <summary>
        /// 系统默认操作人
        /// </summary>
        private string MODIFYPERSONID = "TongBu";

        public OrderService(
              IUnitOfWork<MedicalDbContext> UnitOfWork
            , IUnitOfWork<DataOutConnection> UnitOfWorkOut
            , IPhysicianOrderRepository PhysicianOrderRepository
            , IOrderRepository OrderRepository
            , IOptions<SystemConfig> config
            , ILogInfoServices LogInfoServices
            , ICommonHelper commonHelper
            , ISettingDescRepository settingDescriptionRepository
            , IAppConfigSettingRepository  appConfigSettingRepository
            )
        {
            _unitOfWork = UnitOfWork;
            _unitOfWorkOut = UnitOfWorkOut;
            _IPhysicianOrderRepository = PhysicianOrderRepository;
            _IOrderRepository = OrderRepository;
            _config = config;
            _ILogInfoServices = LogInfoServices;
            _commonHelper = commonHelper;
            _ICSettingDescriptionRepository = settingDescriptionRepository;
            _appConfigSettingRepository = appConfigSettingRepository;           
        }
        

        /// <summary>
        /// 如果发现有没有同步的数据，则一直获取，直至完全同步
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SynchronizationMain()
        {
            _logger.Info("开始获取医嘱字典信息api");
            string apiStr = "";
            var apiStrList = _ICSettingDescriptionRepository.GetAsync(1, "11");

            if (apiStrList.Count > 0)
            {
                apiStr = apiStrList[0].Description;
            }
            else
            {
                _logger.Error("获取医嘱字典API失败");
                return false;
            }
            _logger.Info("获取医嘱字典数据");
            var data = new Dictionary<string, string>();
            //呼叫API获得数据
            var resultData = _commonHelper.GetInterconnectData(apiStr, data);

            //从配置当中获取数据 梁宝华 2020-04-29
            var printInterfaceData = 0;
            var resultPrintDate = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "PrintInterfaceData");
            if (StringCheck.IsNumeric(resultPrintDate))
            {
                printInterfaceData = int.Parse(resultPrintDate);
            }

            if (printInterfaceData == 1)
            {
                _logger.Info("获得数据如下" + resultData);
            }
            var Interconnect_Data = new List<OrderInfo>();
            var result = new ResponseResult();
            result = JsonConvert.DeserializeObject<ResponseResult>(resultData);
            try
            {
                Interconnect_Data = JsonConvert.DeserializeObject<List<OrderInfo>>(result.Data.ToString());
            }
            catch (Exception ex)
            {
                _logger.Error("Json格式化失败:" + ex.ToString());
                return false;
            }
            _logger.Info("获得" + Interconnect_Data.Count() + "条数据");

            if (Interconnect_Data.Count > 0) //如果没有同步完成，则继续同步
            {
                if (!await SynchronizationDetail(Interconnect_Data))
                {
                    return false;
                }
            }
            return true;
        }

        private async Task<bool> SynchronizationDetail(List<OrderInfo> OriginalList)
        {
            var MedicalOrderList = await _IPhysicianOrderRepository.GetAllAsync<PhysicianOrderInfo>();
            int MaxID = await _IPhysicianOrderRepository.GetMaxAsync();
            string tablename = "Order";
            int Failcount = 0;

            List<PhysicianOrderInfo> Insertlist = new List<PhysicianOrderInfo>();
            PhysicianOrderInfo t = null;
            _logger.Info(tablename + " 开始进行数据同步，数据条数：" + OriginalList.Count);


            #region "数据同步"
            foreach (var item in OriginalList)
            {
                item.Counts = item.Counts ?? 0;
                item.Counts = item.Counts + 1;
                try
                {
                    //获取Medical中的医嘱信息
                    var TempMedicalOrderList = MedicalOrderList.Where(m => m.OrderCode == item.OrderCode).ToList();

                    if (TempMedicalOrderList.Count > 1)
                    {
                        _logger.Info(tablename, "[" + item.OrderCode + "]查询医嘱信息错误!");
                        Failcount++;
                        continue;
                    }

                    //获取插入的数据,避免数据重复插入
                    var TempInsertlist = Insertlist.Where(m => m.OrderCode == item.OrderCode).ToList();

                    //如果不存在进行新增
                    if (TempMedicalOrderList.Count < 1 && TempInsertlist.Count < 1)
                    {
                        t = new PhysicianOrderInfo
                        {
                            ID = MaxID,
                            HospitalID = _config.Value.HospitalID,
                            OrderType = item.OrderType,    //医嘱类别
                            TypeName = item.TypeName ?? "",    //类别名称
                            OrderCode = item.OrderCode,   //医嘱代码
                            OrderName = item.OrderName ?? "",   //医嘱名称
                            ModifyPersonID = MODIFYPERSONID,
                            ModifyDate = DateTime.Now,
                            DeleteFlag = ""
                        };
                        item.DataPumpFlag = "*";
                        item.DataPumpDate = DateTime.Now;
                        Insertlist.Add(t);
                        MaxID++;
                    }
                    if (TempMedicalOrderList.Count == 1)
                    {
                        TempMedicalOrderList[0].OrderType = item.OrderType;    //医嘱类别
                        TempMedicalOrderList[0].TypeName = item.TypeName ?? "";    //类别名称
                        TempMedicalOrderList[0].OrderCode = item.OrderCode;   //医嘱代码
                        TempMedicalOrderList[0].OrderName = item.OrderName ?? "";   //医嘱名称
                        TempMedicalOrderList[0].ModifyPersonID = MODIFYPERSONID;
                        TempMedicalOrderList[0].ModifyDate = DateTime.Now;
                    };
                    item.DataPumpFlag = "*";
                    item.DataPumpDate = DateTime.Now;
                }
                catch (Exception ex)
                {
                    _logger.Info(tablename, ":同步错误：" + item.OrderCode + ":" + ex.ToString());
                    return false;
                }
            }
            #endregion
            try
            {
                _unitOfWork.GetRepository<PhysicianOrderInfo>().Insert(Insertlist);
                _unitOfWork.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Info(tablename + "||同步失败||" + ex.ToString());
                return false;
            }
            _logger.Info(tablename + " 同步结束 成功：" + (OriginalList.Count - Failcount).ToString() + "条！ 失败：" + Failcount.ToString() + "条！");
            return true;
        }
    }
}