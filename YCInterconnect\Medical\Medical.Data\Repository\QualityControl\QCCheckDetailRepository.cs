﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class QCCheckDetailRepository : IQCCheckDetailRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public QCCheckDetailRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }
        /// <summary>
        /// 获取所有数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<QCCheckDetailInfo>> GetAllAsync()
        {
            return await _medicalDbContext.QCCheckDetailInfos.Where(t => t.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        ///根据主表ID获取明细表数据
        /// </summary>
        /// <param name="qualityControlMainID"></param>
        /// <returns></returns>
        public async Task<List<QCCheckDetailInfo>> GetByMainIDAsync(string qCCheckMainID)
        {
            return await _medicalDbContext.QCCheckDetailInfos.Where(t => t.QCCheckMainID == qCCheckMainID && t.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据主ID获取数据
        /// </summary>
        /// <param name="qCCheckDetailID"></param>
        /// <returns></returns>
        public async Task<List<QCCheckDetailInfo>> GetByIDAsync(string qCCheckDetailID)
        {
            return await _medicalDbContext.QCCheckDetailInfos.Where(t => t.QCCheckDetailID == qCCheckDetailID && t.DeleteFlag != "*").ToListAsync();
        }
    }
}
