﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Interconnect.Models;

namespace Interconnect.Data.Interface
{
    public interface ITestScheduleRepository
    {
        /// <summary>
        /// 获取中介库检验信息
        /// </summary>
        /// <param name="tongbuCount"></param>
        /// <param name="takeRows"></param>
        /// <returns></returns>
        List<TestScheduleInfo> GetAsync(int tongbuCount, int takeRows);

    }
}
