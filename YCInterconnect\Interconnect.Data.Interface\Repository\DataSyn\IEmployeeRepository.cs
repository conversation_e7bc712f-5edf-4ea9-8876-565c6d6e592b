﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Interconnect.Models;

namespace Interconnect.Data.Interface
{
    public interface IEmployeeRepository
    {
        /// <summary>
        /// 获取员工基本信息
        /// </summary>
        /// <returns></returns>
        List<EmployeeInfo> GetAsync();
        /// <summary>
        /// 获取需要抽取的数据
        /// </summary>
        /// <param name="tongbuCount"></param>
        /// <param name="takeRows"></param>
        /// <returns></returns>
        List<EmployeeInfo> GetDataPump(int tongbuCount, int takeRows);

    }
}
