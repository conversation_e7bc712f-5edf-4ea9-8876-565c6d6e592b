﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class AssessListIDToColorRepository : IAssessListIDToColorRepository
    {
        private MedicalDbContext _dbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public AssessListIDToColorRepository(MedicalDbContext dbContext, IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer, GetCacheService getCacheService)
        {
            _dbContext = dbContext;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        /// <summary>
        /// 通过缓存获取数据
        /// </summary>
        /// <param name="language">语言</param>
        /// <returns></returns>
        public async Task<List<AssessListIDToColorInfo>> GetAsync()
        {
            var data = (List<AssessListIDToColorInfo>)await GetCacheAsync();
            if (data != null)
            {
                return data;
            }
            return new List<AssessListIDToColorInfo>();
        }

        public async Task<List<AssessListIDToColorInfo>> GetAsync(int assessListID)
        {
            var data = (List<AssessListIDToColorInfo>)await GetCacheAsync();

            if (data != null)
            {
                return data.Where(t => t.AssessListID == assessListID).ToList();
            }
            return new List<AssessListIDToColorInfo>();
        }
        public async Task<List<AssessListIDToColorInfo>> GetAsyncByAssessListIDArr(List<int> assessListIDArr)
        {
            var data = (List<AssessListIDToColorInfo>)await GetCacheAsync();
            return data.Where(t => assessListIDArr.Contains(t.AssessListID)).ToList();
        }

        /// <summary>
        /// 根据评估项目序号获取描述
        /// </summary>
        /// <param name="assessListID">评估项目序号</param>
        /// <param name="language">语言</param>
        /// <returns></returns>
        public async Task<List<AssessListIDToColorView>> GetColorByAssessList(int assessListID)
        {
            var data = (List<AssessListIDToColorInfo>)await GetCacheAsync();

            if (data != null)
            {
                return data.Where(t => t.AssessListID == assessListID)
                           .Select(m => new AssessListIDToColorView
                           {
                               Color16bit = m.Color16bit,
                               ColorName = m.ColorName
                           }).ToList();
            }
            return new List<AssessListIDToColorView>();
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<AssessListIDToColorInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            dict.TryGetValue("language", out var language);
            return await _dbContext.AssessListIDToColorInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.Language == (Int32)language && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<string> GetColorNameByBit(string bit, int assesslistID)
        {
            var data = (List<AssessListIDToColorInfo>)await GetCacheAsync();

            if (data != null)
            {
                var result = data.Where(t => (t.Color16bit ?? "").Trim() == bit && t.AssessListID == assesslistID).FirstOrDefault();
                return result.ColorName;
            }
            return "";
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.AssessListIDToColor.GetKey(_sessionCommonServer);
        }
        /// <summary>
        /// 获取颜色名称
        /// </summary>
        /// <param name="color16bit"></param>
        /// <returns></returns>
        public async Task<string> GetColorNameByColor16bit(string color16bit)
        {
            var data = (List<AssessListIDToColorInfo>)await GetCacheAsync();
            if (data != null)
            {
                return data.Where(m => m.Color16bit.Trim() == color16bit.Trim()).Select(m => m.ColorName).FirstOrDefault();
            }
            return "";
        }
    }
}
