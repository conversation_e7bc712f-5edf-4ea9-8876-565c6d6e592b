﻿/*
 * 2022-05-22 2643 导管反比逻辑detail获取过滤DeleteFlag -En
 */

using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.Data;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientTubeRecordRepository : IPatientTubeRecordRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;

        public PatientTubeRecordRepository(
            MedicalDbContext db,
            SessionCommonServer sessionCommonServer
            )
        {
            _medicalDbContext = db;
            _sessionCommonServer = sessionCommonServer;
        }

        /// <summary>
        ///  获取导管记录，isCurrent=true获取当前导管，isCurrent=false获取历史导管，不传则取所有
        /// </summary>
        /// <param name="inPatientID"></param>
        /// <param name="isCurrent"></param>
        /// <returns></returns>
        public async Task<List<PatientTubeRecordInfo>> GetAsync(string inPatientID, bool? isCurrent = null)
        {
            var list = await _medicalDbContext.PatientTubeRecordInfos.Where(t =>
             t.InpatientID == inPatientID &&
             t.DeleteFlag != "*").ToListAsync();

            if (isCurrent != null)
            {
                if (isCurrent.Value)
                {
                    list = list.Where(t => t.RemoveDate == null).ToList();
                }
                else
                {
                    list = list.Where(t => t.RemoveDate != null).ToList();
                }
            }
            return list;
        }

        /// <summary>
        ///  获取导管记录，不传则取所有
        /// </summary>
        /// <param name="inPatientID"></param>
        /// <param name="isCurrent"></param>
        /// <returns></returns>
        public async Task<List<PatientTubeRecordInfo>> GetPatientTubeByInpatientIDsAsync(List<string> inPatientIDs)
        {
            var list = await _medicalDbContext.PatientTubeRecordInfos.Where(t =>
             inPatientIDs.Contains(t.InpatientID) &&
             t.DeleteFlag != "*" && t.RemoveDate == null)
                .Select(m => new PatientTubeRecordInfo
                {
                    PatientTubeRecordID = m.PatientTubeRecordID,
                    TubeNumber = m.TubeNumber,
                    InpatientID = m.InpatientID,
                    TubeID = m.TubeID,
                    BodyPartID = m.BodyPartID,
                }).ToListAsync();
            return list;
        }

        /// <summary>
        ///  根据ID获取导管记录
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<PatientTubeRecordInfo> GetByIDAsync(string recordID)
        {
            return await _medicalDbContext.PatientTubeRecordInfos.Where(t => t.PatientTubeRecordID == recordID && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<List<PatientTubeRecordInfo>> GetPatientTubeByNumAsync(string inpatient, string num)
        {
            return await _medicalDbContext.PatientTubeRecordInfos.Where(t => t.InpatientID == inpatient && t.AssessMainID == num && t.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<HandoverTubeCareIntervention>> GetTubeHandoverByAssessNum(string inpatientID, string num)
        {
            var datas = await (from a in _medicalDbContext.PatientTubeCareMainInfos
                               join b in _medicalDbContext.PatientTubeRecordInfos on a.PatientTubeRecordID equals b.PatientTubeRecordID
                               where a.InpatientID == inpatientID && b.AssessMainID == num && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                               select new HandoverTubeCareIntervention
                               {
                                   InpatientID = a.InpatientID,
                                   AssessDate = a.AssessDate,
                                   AssessTime = a.AssessTime,
                                   TubeID = b.TubeID,
                                   BodyPartID = b.BodyPartID,
                                   CareIntervention = a.CareIntervention,
                                   Sort = 0,
                                   BodyPartName = "",
                                   TubeNumber = b.TubeNumber,
                                   PatientTubeRecordID = a.PatientTubeRecordID,
                                   ExtendItem = a.ExtendItem,
                                   AssessMainID = b.AssessMainID,
                                   NumberOfAssessment = a.NumberOfAssessment
                               }).ToListAsync();

            if (datas.Count == 0)
            {
                return datas;
            }

            datas = datas.GroupBy(m => m.PatientTubeRecordID)
                .Select(m => m.OrderByDescending(n => n.AssessDate.Date.Add(n.AssessTime)).ThenByDescending(n => n.NumberOfAssessment).FirstOrDefault()).ToList();

            return datas;
        }

        /// <summary>
        ///  根据身体部位和导管ID获取导管编号
        /// </summary>
        /// <param name="inpatient"></param>
        /// <param name="tubeID"></param>
        /// <param name="bodyPartID"></param>
        /// <returns></returns>
        public async Task<byte> GetNewTubeNumberAsync(string inpatient, int tubeID, int bodyPartID)
        {
            var number = 1;
            var tubeList = await _medicalDbContext.PatientTubeRecordInfos.Where(t => t.InpatientID == inpatient && t.DeleteFlag != "*").ToListAsync();
            if (tubeList != null && tubeList.Count > 0)
            {
                tubeList = tubeList.Where(m => m.TubeID == tubeID && m.BodyPartID == bodyPartID).ToList();
                if (tubeList != null && tubeList.Count > 0)
                {
                    number = tubeList.Max(m=>m.TubeNumber) + 1;
                }
            }
            return Convert.ToByte(number);
        }

        /// <summary>
        ///  获取病人所有的导管
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientTubeRecordInfo>> GetListByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientTubeRecordInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据病人序号和交班ID获取伤口记录
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="handoverID"></param>
        /// <returns></returns>
        public async Task<List<PatientTubeRecordInfo>> GetByHandoverAsync(string inpatientID, string handoverID)
        {
            return await _medicalDbContext.PatientTubeRecordInfos.Where(t => t.InpatientID == inpatientID && t.HandoverID == handoverID && t.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据统计周期获取非计划性拔管
        /// </summary>
        /// <param name="statisticalStartTime"></param>
        /// <param name="statisticalEndTime"></param>
        /// <returns></returns>
        public async Task<List<PatientTubeRecordInfo>> GetUEXByTimeAsync()
        {
            var infos = await _medicalDbContext.PatientTubeRecordInfos.Where(m => m.DeleteFlag != "*"
            && m.RemoveDate != null && m.RemoveTime != null).ToListAsync();
            return infos;
        }

        /// <summary>
        /// 获取时间段内非计划拔管的导管数据
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public async Task<List<PatientTubeRecordInfo>> GetUEXTubeByDate(DateTime startDate, DateTime endDate)
        {
            // 拔管原因数据非计划的id集合
            var removeReasonIDs = new string[] { "210", "220", "230", "235", "240", "245" };
            return await _medicalDbContext.PatientTubeRecordInfos.Where(m => m.RemoveDate >= startDate && m.RemoveDate <= endDate
                    && !string.IsNullOrEmpty(m.RemoveReason) && removeReasonIDs.Contains(m.RemoveReason) && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据时间阶段获取导管信息
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public async Task<List<PatientTubeRecordInfo>> GetListByDateAsync(DateTime? startDate, DateTime? endDate)
        {
            return await _medicalDbContext.PatientTubeRecordInfos.Where(m => m.DeleteFlag != "*"
            && m.StartDate >= startDate && m.StartDate <= endDate).ToListAsync();
        }

        /// <summary>
        /// 获取当前日期导管信息
        /// </summary>
        /// <returns></returns>
        public async Task<List<PatientTubeRecordInfo>> GetListByNowAsync()
        {
            return await _medicalDbContext.PatientTubeRecordInfos.Where(m => m.DeleteFlag != "*"
            && m.StartDate == DateTime.Now.Date.AddDays(-1)).ToListAsync();
        }

        /// <summary>
        /// 获取有创通气数据列表
        /// </summary>
        /// <returns></returns>
        public async Task<List<PatientTubeRecordInfo>> GetIMVListAsync()
        {
            return await _medicalDbContext.PatientTubeRecordInfos.Where(m => m.DeleteFlag != "*" && m.TubeID == 48).ToListAsync();
        }

        public async Task<List<PatientNowTube>> GetPatientNowTube(string inpatientID, int language, string hospitalID, bool unRemoveFlag)
        {
            var query = await (from a in _medicalDbContext.PatientTubeRecordInfos
                               join b in _medicalDbContext.BodyPartListInfos on new { ID = a.BodyPartID, Language = language } equals new { b.ID, b.Language }
                               join c in _medicalDbContext.TubeListInfos on new { ID = a.TubeID, Language = language, HospitalID = hospitalID } equals new { c.ID, c.Language, c.HospitalID }
                               where a.InpatientID == inpatientID && a.DeleteFlag == "" && b.DeleteFlag == "" && c.DeleteFlag == ""
                                    && (unRemoveFlag ? a.RemoveDate == null : true)
                               orderby b.Sort
                               select new PatientNowTube
                               {
                                   PatientTubeRecordID = a.PatientTubeRecordID,
                                   TubeAssessListID = c.TubeAssessListID,
                                   BodyPartName = b.BodyPartName,
                                   TubeName = c.TubeShortName,
                                   TubeNumber = a.TubeNumber,
                                   RecordsFormatID = c.RecordsFormatID
                               }).ToListAsync();
            return query;
        }

        public async Task<List<PatientNowTube>> GetPatientTubeByDateTime(string inpatientID, int language, string hospitalID, DateTime startDate, TimeSpan startTime)
        {
            var query = await (from a in _medicalDbContext.PatientTubeRecordInfos
                               join b in _medicalDbContext.BodyPartListInfos on new { ID = a.BodyPartID, Language = language } equals new { b.ID, b.Language }
                               join c in _medicalDbContext.TubeListInfos on new { ID = a.TubeID, Language = language, HospitalID = hospitalID } equals new { c.ID, c.Language, c.HospitalID }
                               where a.InpatientID == inpatientID && a.DeleteFlag == "" && b.DeleteFlag == "" && c.DeleteFlag == ""
                                    && ((a.StartDate < startDate) || (a.StartDate == startDate && a.StartTime <= startTime))
                                    && ((a.RemoveDate.HasValue && ((a.RemoveDate > startDate) || (a.RemoveDate == startDate && a.RemoveTime >= startTime))) || a.RemoveDate == null)
                               orderby b.Sort
                               select new PatientNowTube
                               {
                                   PatientTubeRecordID = a.PatientTubeRecordID,
                                   TubeAssessListID = c.TubeAssessListID,
                                   BodyPartName = b.BodyPartName,
                                   TubeName = c.TubeShortName,
                                   TubeNumber = a.TubeNumber,
                                   RecordsFormatID = c.RecordsFormatID
                               }).ToListAsync();
            return query;
        }

        public async Task<List<PatientTubeList>> GetPatientTubeList(string inpatientID, int departmentListID)
        {
            var (hospitalIDStr, language) = await _sessionCommonServer.GetSession();
            var hospitalID = int.Parse(hospitalIDStr);
            var query = await (from a in _medicalDbContext.PatientTubeRecordInfos
                               join b in _medicalDbContext.BodyPartListInfos on new { ID = a.BodyPartID, Language = language } equals new { b.ID, b.Language }
                               join c in _medicalDbContext.TubeListInfos on new { ID = a.TubeID, Language = language, HospitalID = hospitalIDStr } equals new { c.ID, c.Language, c.HospitalID }
                               join d in _medicalDbContext.IntakeOutputSettings on new { FullCode = c.ID.ToString(), Language = language, HospitalID = hospitalID } equals new { d.FullCode, d.Language, d.HospitalID }
                               join e in _medicalDbContext.SettingDescriptions on new { SettingTypeCode = "OutputCategory", TypeValue = d.Kind, d.IOType, d.Language, HospitalID = hospitalIDStr } equals new { e.SettingTypeCode, e.TypeValue, IOType = "O", e.Language, e.HospitalID }
                               join f in _medicalDbContext.DepartmentToAssessInfos on new { DepartmentListID = departmentListID, MappingType = "TubeMaintain", RecordsCode = c.TubeMaintain, HospitalID = hospitalIDStr } equals new { f.DepartmentListID, f.MappingType, f.RecordsCode, f.HospitalID }
                               where a.InpatientID == inpatientID && a.DeleteFlag == "" && b.DeleteFlag == "" && c.DeleteFlag == "" && d.DeleteFlag == "" && f.DeleteFlag == "" && f.HospitalID == hospitalIDStr
                               select new PatientTubeList
                               {
                                   PatientTubeRecordID = a.PatientTubeRecordID,
                                   TubeListID = c.ID,
                                   TubeCategoryID = c.TubeCategoryID,
                                   TubeAssessListID = c.TubeAssessListID,
                                   DrainageColorAssessList = c.DrainageColorAssessList,
                                   BodyPartName = b.BodyPartName,
                                   TubeName = d.IntakeOutput ?? "",
                                   TubeNumber = a.TubeNumber,
                                   StartDate = a.StartDate,
                                   StartTime = a.StartTime,
                                   RemoveDate = a.RemoveDate,
                                   RemoveTime = a.RemoveTime,
                                   IOKind = e.Description,
                                   IntakeOutputKind = e.TypeValue,
                                   RecordsCode = c.TubeMaintain,
                                   InterventionMainID = f.InterventionMainID,
                                   IntakeOutputSettingID = d.ID,
                                   TubeContent = a.TubeContent
                               }).ToListAsync();
            return query;
        }

        public async Task<List<PatientTubeRecordInfo>> GetListNotHistoryAsync()
        {
            return await _medicalDbContext.PatientTubeRecordInfos.Where(m => m.DeleteFlag != "*" && m.RemoveDate == null && m.RemoveTime == null).ToListAsync();
        }

        public async Task<List<HandoverTubeCareIntervention>> GetPatientTubeCareIntervention(string inpatientID, DateTime startDate, TimeSpan startTime, DateTime endDate, TimeSpan endTime)
        {
            var datas = await (from a in _medicalDbContext.PatientTubeCareMainInfos
                               join b in _medicalDbContext.PatientTubeRecordInfos on a.PatientTubeRecordID equals b.PatientTubeRecordID
                               where a.InpatientID == inpatientID
                                  && a.AssessDate >= startDate
                                  && a.AssessDate <= endDate
                                  && a.BringToShift == true
                                  && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                               select new HandoverTubeCareIntervention
                               {
                                   InpatientID = a.InpatientID,
                                   AssessDate = a.AssessDate,
                                   AssessTime = a.AssessTime,
                                   TubeID = b.TubeID,
                                   BodyPartID = b.BodyPartID,
                                   CareIntervention = a.CareIntervention,
                                   Sort = 0,
                                   BodyPartName = "",
                                   TubeNumber = b.TubeNumber,
                                   PatientTubeRecordID = a.PatientTubeRecordID,
                                   ExtendItem = a.ExtendItem,
                                   AssessMainID = b.AssessMainID,
                                   NumberOfAssessment = a.NumberOfAssessment,
                                   RemoveReason = b.RemoveReason,
                                   InsideLength = a.InsideLength,
                                   DrainageColor = a.DrainageColor,
                                   DrainageVolume = a.DrainageVolume
                               }).ToListAsync();

            if (datas.Count == 0)
            {
                return datas;
            }

            datas = datas.Where(m => m.AssessDate.Add(m.AssessTime) >= startDate.Date.Add(startTime) && m.AssessDate.Add(m.AssessTime) <= endDate.Date.Add(endTime))
                .GroupBy(m => m.PatientTubeRecordID)
                .Select(m => m.OrderByDescending(n => n.AssessDate.Date.Add(n.AssessTime)).ThenByDescending(n => n.NumberOfAssessment).FirstOrDefault()).ToList();

            return datas;
        }

        /// <summary>
        /// 取得所有未结束导管
        /// </summary>
        /// <param name="inpatientID">病人住院序號</param>
        /// <returns></returns>
        public async Task<List<HandoverTubeCareIntervention>> GetPatientTubeAllCareIntervention(string inpatientID)
        {
            var datas = await (from a in _medicalDbContext.PatientTubeCareMainInfos
                               join b in _medicalDbContext.PatientTubeRecordInfos on a.PatientTubeRecordID equals b.PatientTubeRecordID
                               where a.InpatientID == inpatientID
                               && b.RemoveDate == null
                                  && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                               select new HandoverTubeCareIntervention
                               {
                                   InpatientID = a.InpatientID,
                                   AssessDate = a.AssessDate,
                                   AssessTime = a.AssessTime,
                                   TubeID = b.TubeID,
                                   SourceDataID = b.SourceDataID,
                                   BodyPartID = b.BodyPartID,
                                   CareIntervention = a.CareIntervention,
                                   Sort = 0,
                                   BodyPartName = "",
                                   TubeNumber = b.TubeNumber,
                                   PatientTubeRecordID = a.PatientTubeRecordID,
                                   ExtendItem = a.ExtendItem,
                                   AssessMainID = b.AssessMainID,
                                   NumberOfAssessment = a.NumberOfAssessment,
                                   RemoveReason = b.RemoveReason,
                                   InsideLength = a.InsideLength,
                                   DrainageColor = a.DrainageColor,
                                   DrainageVolume = a.DrainageVolume
                               }).ToListAsync();

            if (datas.Count == 0)
            {
                return datas;
            }

            datas = datas.GroupBy(m => m.PatientTubeRecordID)
                .Select(m => m.OrderByDescending(n => n.AssessDate.Date.Add(n.AssessTime)).ThenByDescending(n => n.NumberOfAssessment).FirstOrDefault()).ToList();

            return datas;
        }

        public async Task<List<PatientTubeRecordInfo>> GetPatientTubeListByTubeIDAsync(int tubeID)
        {
            return await _medicalDbContext.PatientTubeRecordInfos.Where(m => m.DeleteFlag != "*"
            && m.TubeID == tubeID).ToListAsync();
        }
        public List<PatientTubeRecordInfo> GetByAssessListID(List<PatientTubeRecordInfo> patientTubes, int assessListID, string inpatientID)
        {
            var patientTubeListTemp = patientTubes.Where(m => m.DeleteFlag != "*").ToList();
            var result = (from a in _medicalDbContext.PatientTubeCareMainInfos
                          join b in _medicalDbContext.PatientTubeCareDetailInfos.Where(m => m.DeleteFlag != "*") on new { a.InpatientID, a.PatientTubeCareMainID } equals new { InpatientID = inpatientID, b.PatientTubeCareMainID }
                          where a.DeleteFlag != "*" && b.AssessListID == assessListID
                          select a.PatientTubeRecordID).ToArray();
            return patientTubeListTemp.Where(m => result.Contains(m.PatientTubeRecordID)).ToList();
        }

        public async Task<List<TubeFirstAssess>> GetUnEndTubeFirstAssess(string inpatientID, int stationID)
        {
            var result = await (from a in _medicalDbContext.PatientTubeRecordInfos
                                join b in _medicalDbContext.PatientTubeCareMainInfos on a.PatientTubeRecordID equals b.PatientTubeRecordID
                                where a.InpatientID == inpatientID && a.RemoveDate == null && a.DeleteFlag != "*"
                                && b.StationID == stationID
                                && b.NumberOfAssessment == 1 && b.DeleteFlag != "*"
                                select new TubeFirstAssess
                                {
                                    TubeID = a.TubeID,
                                    PatientTubeCareMainID = b.PatientTubeCareMainID,
                                    PatientTubeRecordID = a.PatientTubeRecordID,
                                }).ToListAsync();
            return result;
        }

        public async Task<List<PatientNowTube>> GetPatientTube(string inpatientID, int language, string hospitalID)
        {
            var query = await (from a in _medicalDbContext.PatientTubeRecordInfos
                               join b in _medicalDbContext.BodyPartListInfos on new { ID = a.BodyPartID, Language = language } equals new { b.ID, b.Language }
                               join c in _medicalDbContext.TubeListInfos on new { ID = a.TubeID, Language = language, HospitalID = hospitalID } equals new { c.ID, c.Language, c.HospitalID }
                               where a.InpatientID == inpatientID
                                    && a.DeleteFlag == "" && b.DeleteFlag == "" && c.DeleteFlag == ""
                               orderby b.Sort
                               select new PatientNowTube
                               {
                                   PatientTubeRecordID = a.PatientTubeRecordID,
                                   TubeAssessListID = c.TubeAssessListID,
                                   BodyPartName = b.BodyPartName,
                                   TubeName = c.TubeShortName,
                                   TubeNumber = a.TubeNumber,
                                   StartDate = a.StartDate,
                                   StartTime = a.StartTime,
                                   RemoveDate = a.RemoveDate,
                                   RemoveTime = a.RemoveTime,
                                   TubeCategoryID = c.TubeCategoryID,
                                   ExpectedChangeID = a.ExpectedChangeID,
                                   ShiftDate = a.StartDate,
                                   TubeListID = a.TubeID
                               }).ToListAsync();
            return query;
        }

        public async Task<string> GetPatientTubeRisk(string inpatientID)
        {
            var query = await _medicalDbContext.PatientTubeRecordInfos
                .Where(m => m.InpatientID == inpatientID && m.RemoveDate == null && m.RemoveTime == null && m.DeleteFlag != "*")
                .Join(
                    _medicalDbContext.TubeListInfos,
                    m => m.TubeID,
                    n => n.ID,
                    (m, n) => n.RiskLevel
                ).OrderBy(m => m).FirstOrDefaultAsync();
            return query.ToString();
        }

        public async Task<List<TubeLink>> GetPatientTubeForLink(string inpatientID)
        {
            return await _medicalDbContext.PatientTubeRecordInfos.Where(m =>
                                  m.InpatientID == inpatientID
                               && m.RemoveDate == null
                               && m.DeleteFlag != "*")
                 .Select(m => new TubeLink
                 {
                     TubeID = m.TubeID,
                     BodyPartID = m.BodyPartID,
                     PatientTubeRecordID = m.PatientTubeRecordID
                 }).ToListAsync();
        }

        /// <summary>
        ///  根据身体部位和导管ID获取导管
        /// </summary>
        /// <param name="inpatient"></param>
        /// <param name="tubeID"></param>
        /// <param name="bodyPartID"></param>
        /// <returns></returns>
        public async Task<PatientTubeRecordInfo> GetTheSameTubeAsync(string inpatient, int tubeID, int bodyPartID)
        {
            return await _medicalDbContext.PatientTubeRecordInfos.Where(t => t.InpatientID == inpatient && t.DeleteFlag != "*" && t.RemoveDate == null && t.TubeID == tubeID
            && t.BodyPartID == bodyPartID).FirstOrDefaultAsync();
        }

        public async Task<int> GetPatientTubeCount(string inpatient, int tubeID, int bodyPartID)
        {
            return await _medicalDbContext.PatientTubeRecordInfos.Where(t => t.InpatientID == inpatient && t.DeleteFlag != "*"
            && t.RemoveDate == null && t.TubeID == tubeID
            && t.BodyPartID == bodyPartID).CountAsync();
        }

        /// <summary>
        /// 获取患者导管数据View
        /// </summary>
        /// <param name="inpatient"></param>
        /// <returns></returns>
        public async Task<List<PatientTubeRecordInfo>> GetPatientAllTubeView(string inpatient)
        {
            return await _medicalDbContext.PatientTubeRecordInfos.Where(t => t.InpatientID == inpatient && t.DeleteFlag != "*"
           ).Select(m => new PatientTubeRecordInfo
           {
               InpatientID = m.InpatientID,
               BodyPartID = m.BodyPartID,
               TubeID = m.TubeID,
           }).ToListAsync();
        }

        /// <summary>
        /// 获取指定在院病人的某种导管
        /// </summary>
        /// <param name="inpatientIDs"></param>
        /// <param name="tubeID"></param>
        /// <returns></returns>
        public async Task<List<PatientTubeRecordInfo>> GetTubeRecordByStationID(List<string> inpatientIDs, int tubeID)
        {
            return await _medicalDbContext.PatientTubeRecordInfos.Where(m => inpatientIDs.Contains(m.InpatientID)
                           && m.TubeID == tubeID && m.DeleteFlag != "*").ToListAsync();
        }
        public async Task<AssessTimeView> GetAssessTimeByID(string assessMainID)
        {
            var assessMain = await _medicalDbContext.PatientTubeRecordInfos.Where(m => m.PatientTubeRecordID == assessMainID && m.DeleteFlag != "*")
                     .Select(m => new AssessTimeView { StartDate = m.StartDate, StartTime = m.StartTime })
                     .FirstOrDefaultAsync();
            return assessMain;
        }

        /// <summary>
        ///  根据ID获取导管部分信息
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<PatientTubeRecordInfo> GetTubePartialInfoByIDAsync(string recordID)
        {
            return await _medicalDbContext.PatientTubeRecordInfos.Where(t => t.PatientTubeRecordID == recordID && t.DeleteFlag != "*")
                .Select(m => new PatientTubeRecordInfo
                {
                    StartDate = m.StartDate,
                    StartTime = m.StartTime,
                    TubeNumber = m.TubeNumber,
                    BodyPartID = m.BodyPartID
                }).FirstOrDefaultAsync();
        }

        public async Task<List<PatientTubeRecordInfo>> GetAsyncNOTracking(string inPatientID)
        {
            return await _medicalDbContext.PatientTubeRecordInfos.Where(t =>
             t.InpatientID == inPatientID && t.DeleteFlag != "*").AsNoTracking().Select(m => new PatientTubeRecordInfo
             {
                 PatientTubeRecordID = m.PatientTubeRecordID,
                 InpatientID = m.InpatientID,
                 StationID = m.StationID,
                 DepartmentListID = m.DepartmentListID,
                 TubeID = m.TubeID,
                 RemoveDate = m.RemoveDate,
                 RemoveTime = m.RemoveTime,
                 StartDate = m.StartDate,
                 StartTime = m.StartTime,
                 BedNumber = m.BedNumber,
                 ExpectedChangeID = m.ExpectedChangeID,
                 RemoveReason = m.RemoveReason,
             }).ToListAsync();
        }
        //获取病人当前导管数据
        public async Task<List<PatientTubeRecordView>> GetPatientTubeRecordViewByCaseNumber(string caseNumber, int language)
        {
            var result = await (from a in _medicalDbContext.PatientTubeRecordInfos
                                join b in _medicalDbContext.TubeListInfos
                                on new { a.CaseNumber, a.TubeID, Language = language } equals new { CaseNumber = caseNumber, TubeID = b.ID, b.Language }
                                join c in _medicalDbContext.BodyPartListInfos
                                on new { a.BodyPartID, Language = language } equals new { BodyPartID = c.ID, c.Language }
                                where a.DeleteFlag != "*"
                                && a.RemoveDate == null
                                select new PatientTubeRecordView
                                {
                                    PatientTubeRecordID = a.PatientTubeRecordID,
                                    CaseNumber = a.CaseNumber,
                                    DepartmentListID = a.DepartmentListID,
                                    StationID = a.StationID,
                                    TubeID = a.TubeID,
                                    TubeName = b.TubeShortName,
                                    Tubetype = b.TubeType,
                                    BodyPartID = a.BodyPartID,
                                    BodyPartName = c.BodyPartName,
                                    StartDateTime = a.StartDate.Add(a.StartTime),
                                    IntubationEmployeeID = a.IntubationEmployeeID,
                                    OccuredDepartmentID = a.OccuredDepartmentID,
                                    OccuredStationID = a.OccuredStationID,
                                    RemoveDate = a.RemoveDate,
                                    RemoveTime = a.RemoveTime,
                                    RemoveTubePersonID = a.RemoveTubePersonID,
                                    RemoveStationID = a.RemoveStationID,
                                    RemoveDepartmentListID = a.RemoveDepartmentListID,
                                    RemoveReason = a.RemoveReason,
                                    UseDay = a.UseDay,
                                    AddPersonID = a.AddEmployeeID,
                                    AddDateTime = a.AddDate
                                }).ToListAsync();
            return result;
        }

        //根据ChartNO获取病人历史导管数据
        public async Task<List<PatientTubeRecordView>> GetPatientHistoryTubeRecordViewByChartNO(string chartNO, int language)
        {
            var result = await (from a in _medicalDbContext.PatientTubeRecordInfos
                                join b in _medicalDbContext.TubeListInfos
                                on new { a.ChartNo, a.TubeID, Language = language } equals new { ChartNo = chartNO, TubeID = b.ID, b.Language }
                                join c in _medicalDbContext.BodyPartListInfos
                                on new { a.BodyPartID, Language = language } equals new { BodyPartID = c.ID, c.Language }
                                where a.DeleteFlag != "*"
                                && a.RemoveDate != null
                                select new PatientTubeRecordView
                                {
                                    PatientTubeRecordID = a.PatientTubeRecordID,
                                    CaseNumber = a.CaseNumber,
                                    DepartmentListID = a.DepartmentListID,
                                    StationID = a.StationID,
                                    TubeID = a.TubeID,
                                    TubeName = b.TubeShortName,
                                    Tubetype = b.TubeType,
                                    BodyPartID = a.BodyPartID,
                                    BodyPartName = c.BodyPartName,
                                    StartDateTime = a.StartDate.Add(a.StartTime),
                                    IntubationEmployeeID = a.IntubationEmployeeID,
                                    OccuredDepartmentID = a.OccuredDepartmentID,
                                    OccuredStationID = a.OccuredStationID,
                                    RemoveDate = a.RemoveDate,
                                    RemoveTime = a.RemoveTime,
                                    RemoveTubePersonID = a.RemoveTubePersonID,
                                    RemoveStationID = a.RemoveStationID,
                                    RemoveDepartmentListID = a.RemoveDepartmentListID,
                                    RemoveReason = a.RemoveReason,
                                    UseDay = a.UseDay,
                                    AddPersonID = a.AddEmployeeID,
                                    AddDateTime = a.AddDate
                                }).ToListAsync();
            return result;
        }

        //根据caseNumber获取病人历史导管数据
        public async Task<List<PatientTubeRecordView>> GetPatientHistoryTubeRecordViewByCaseNumber(string caseNumber, int language)
        {
            var result = await (from a in _medicalDbContext.PatientTubeRecordInfos
                                join b in _medicalDbContext.TubeListInfos
                                on new { a.CaseNumber, a.TubeID, Language = language } equals new { CaseNumber = caseNumber, TubeID = b.ID, b.Language }
                                join c in _medicalDbContext.BodyPartListInfos
                                on new { a.BodyPartID, Language = language } equals new { BodyPartID = c.ID, c.Language }
                                where a.DeleteFlag != "*"
                                && a.RemoveDate != null
                                select new PatientTubeRecordView
                                {
                                    PatientTubeRecordID = a.PatientTubeRecordID,
                                    CaseNumber = a.CaseNumber,
                                    DepartmentListID = a.DepartmentListID,
                                    StationID = a.StationID,
                                    TubeID = a.TubeID,
                                    TubeName = b.TubeShortName,
                                    Tubetype = b.TubeType,
                                    BodyPartID = a.BodyPartID,
                                    BodyPartName = c.BodyPartName,
                                    StartDateTime = a.StartDate.Add(a.StartTime),
                                    IntubationEmployeeID = a.IntubationEmployeeID,
                                    OccuredDepartmentID = a.OccuredDepartmentID,
                                    OccuredStationID = a.OccuredStationID,
                                    RemoveDate = a.RemoveDate,
                                    RemoveTime = a.RemoveTime,
                                    RemoveTubePersonID = a.RemoveTubePersonID,
                                    RemoveStationID = a.RemoveStationID,
                                    RemoveDepartmentListID = a.RemoveDepartmentListID,
                                    RemoveReason = a.RemoveReason,
                                    UseDay = a.UseDay,
                                    AddPersonID = a.AddEmployeeID,
                                    AddDateTime = a.AddDate
                                }).ToListAsync();
            return result;
        }

        public async Task<List<PatientTubeRecordInfo>> GetTube(string inPatientID, string PatientTubeRecordID)
        {
            var list = await _medicalDbContext.PatientTubeRecordInfos.Where(t =>
              t.InpatientID == inPatientID &&
              t.DeleteFlag != "*" && t.PatientTubeRecordID == PatientTubeRecordID).ToListAsync();
            return list;
        }

        public List<TubeListInfo> GetByTubeID(int tubeID)
        {
            return _medicalDbContext.TubeListInfos.Where(t => t.ID == tubeID && t.DeleteFlag != "*").ToList();
        }

        public List<PatientTubeRecordInfo> GetPatientTubes(string inPatientID)
        {
            return _medicalDbContext.PatientTubeRecordInfos.Where(t =>
             t.InpatientID == inPatientID &&
             t.DeleteFlag != "*").ToList();
        }

        public async Task<List<PatientNowTube>> GetPatientTube(string inpatientID, int language)
        {
            var query = await (from a in _medicalDbContext.PatientTubeRecordInfos
                               join b in _medicalDbContext.BodyPartListInfos on new { ID = a.BodyPartID, Language = language } equals new { b.ID, b.Language }
                               join c in _medicalDbContext.TubeListInfos on new { ID = a.TubeID, Language = language } equals new { c.ID, c.Language }
                               where a.InpatientID == inpatientID
                                    && a.DeleteFlag == "" && b.DeleteFlag == "" && c.DeleteFlag == ""
                               orderby b.Sort
                               select new PatientNowTube
                               {
                                   PatientTubeRecordID = a.PatientTubeRecordID,
                                   TubeAssessListID = c.TubeAssessListID,
                                   BodyPartName = b.BodyPartName,
                                   TubeName = c.TubeShortName,
                                   TubeNumber = a.TubeNumber,
                                   StartDate = a.StartDate,
                                   StartTime = a.StartTime,
                                   RemoveDate = a.RemoveDate,
                                   RemoveTime = a.RemoveTime,
                                   TubeCategoryID = c.TubeCategoryID,
                                   ExpectedChangeID = a.ExpectedChangeID,
                                   ShiftDate = a.StartDate,
                                   TubeListID = a.TubeID
                               }).ToListAsync();
            return query;
        }

        public async Task<List<KeyValue>> GetAsyncByInpatientIDs(List<string> inpatientIDs)
        {
            var data = await _medicalDbContext.PatientTubeRecordInfos
                .Where(m => inpatientIDs.Contains(m.InpatientID) && m.DeleteFlag != "*" && m.RemoveDate == null)
                .Select(m => new KeyValue
                {
                    ID = m.TubeID,
                    Value = m.InpatientID
                }).ToListAsync();
            return data;
        }

        public async Task<List<string>> GetPatientTubeRecordIDByTubeIDAndBodyPartAsync(string inpatientID, int tubeID, int bodyPartID)
        {
            return await _medicalDbContext.PatientTubeRecordInfos.AsNoTracking()
                .Where(t => t.InpatientID == inpatientID && t.TubeID == tubeID && t.BodyPartID == bodyPartID && t.DeleteFlag != "*")
                .Select(m => m.PatientTubeRecordID).ToListAsync();
        }

        public async Task<List<string>> GetPatientTubeRecordIDByTubeIDAsync(string inpatientID, List<int> tubeIDs)
        {
            return await _medicalDbContext.PatientTubeRecordInfos.AsNoTracking()
                .Where(t => t.InpatientID == inpatientID && t.DeleteFlag != "*" && tubeIDs.Contains(t.TubeID))
                .Select(m => m.PatientTubeRecordID).ToListAsync();
        }

        public async Task<List<ProfileAssessListView>> GetExpectedChangeDay(string inpatientID)
        {
            return await _medicalDbContext.PatientTubeRecordInfos.AsNoTracking()
                .Where(t => t.InpatientID == inpatientID && t.DeleteFlag != "*" && t.ExpectedChangeDay.HasValue && !t.RemoveDate.HasValue)
                .Select(m => new ProfileAssessListView { AssessListID = m.TubeID, ModifyDate = m.ExpectedChangeDay.Value }).ToListAsync();
        }
        /// <summary>
        /// 根据主记录ID获取InpatientID
        /// </summary>
        /// <param name="recordID">主记录ID</param>
        /// <returns></returns>
        public async Task<string> GetChartNoByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientTubeRecordInfos.Where(m => m.PatientTubeRecordID == recordID)
                .Select(m => m.ChartNo).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据患者主键集合获取数据
        /// </summary>
        /// <param name="inpatientIDs">ID集合</param>
        /// <returns></returns>
        public async Task<List<PatientTubeRecordInfo>> GetPatientTubeRecordInfoByInpatientIDs(List<string> inpatientIDs)
        {
            return await _medicalDbContext.PatientTubeRecordInfos.Where(m => inpatientIDs.Contains(m.InpatientID) && m.DeleteFlag != "*" && m.RemoveDate == null)
                .ToListAsync();
        }
        /// <summary>
        /// 获取患者时间内存在的导管
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="date"></param>
        /// <param name="time"></param>
        /// <returns></returns>
        public async Task<List<PatientTubeRecordInfo>> GetTubeListByTime(string inpatientID, DateTime date, TimeSpan time)
        {
            var list = await _medicalDbContext.PatientTubeRecordInfos.Where(t => t.InpatientID == inpatientID && t.DeleteFlag != "*").ToListAsync();
            var dateTime = date.Add(time);
            list = list.Where(m => m.StartDate.Add(m.StartTime) <= dateTime
            && (m.RemoveDate == null || (m.RemoveDate != null && m.RemoveTime != null && m.RemoveDate.Value.Add(m.RemoveTime.Value) > dateTime))).ToList();
            return list;
        }
        /// <summary>
        /// 取出患者的现有导管，拔管时间在时间区间之内和之后的导管
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public async Task<List<PatientTubeRecordInfo>> GetTubeListByDate(string inpatientID, DateTime startDate, DateTime endDate)
        {
            return await _medicalDbContext.PatientTubeRecordInfos.Where(t =>
             (t.InpatientID == inpatientID && t.RemoveDate == null) || (t.InpatientID == inpatientID && t.RemoveDate.Value.Date >= startDate.Date && t.RemoveDate.Value.Date <= endDate.Date)
             || (t.InpatientID == inpatientID && t.RemoveDate.Value.Date >= endDate.Date) && t.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        ///  根据recordID和拔管原因获取数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="removeResoneList"></param>
        /// <returns></returns>
        public async Task<PatientTubeRecordInfo> GetByIDRemoveresonAsync(string recordID, List<string> removeResoneList)
        {
            return await _medicalDbContext.PatientTubeRecordInfos.Where(t => t.PatientTubeRecordID == recordID && removeResoneList.Contains(t.RemoveReason) && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<List<PatientTubeRecordInfo>> GetListByInpatientIDAndTime(string inpatientID, DateTime date)
        {
            return await _medicalDbContext.PatientTubeRecordInfos.Where(t => (t.StartDate == date.Date && t.StartTime < date.TimeOfDay && t.InpatientID == inpatientID && t.DeleteFlag != "*") || t.StartDate < date.Date && t.InpatientID == inpatientID && t.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取患者导管数据
        /// </summary>
        /// <param name="inpatientIDs">患者住院号集合</param>
        /// <returns></returns>
        public async Task<List<PatientTubeRecordInfo>> GetListByInpatientIDs(string[] inpatientIDs)
        {
            return await _medicalDbContext.PatientTubeRecordInfos.Where(m => inpatientIDs.Contains(m.InpatientID) && m.DeleteFlag != "*")
                .Select(m => new PatientTubeRecordInfo
                {
                    PatientTubeRecordID = m.PatientTubeRecordID,
                    StationID = m.StationID,
                    InpatientID = m.InpatientID,
                    TubeContent = m.TubeContent,
                    RemoveTime = m.RemoveTime,
                    RemoveDate = m.RemoveDate,
                    StartTime = m.StartTime,
                    StartDate = m.StartDate,
                    TubeNumber = m.TubeNumber,
                    BodyPartID = m.BodyPartID,
                    TubeID = m.TubeID,
                    RemoveReason = m.RemoveReason,
                }).ToListAsync();
        }
    }
}