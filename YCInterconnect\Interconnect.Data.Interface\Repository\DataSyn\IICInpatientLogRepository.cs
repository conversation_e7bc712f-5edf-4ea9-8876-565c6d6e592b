﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Interconnect.Models;

namespace Interconnect.Data.Interface
{
    public interface IICInpatientLogRepository
    {
        /// <summary>
        /// 获取未同步的信息
        /// </summary>
        /// <param name="tongbuCount"></param>
        /// <param name="takeRows"></param>
        /// <returns></returns>
        Task<List<ICInpatientLogInfo>> GetAsync(int tongbuCount, int takeRows);
    }
}
