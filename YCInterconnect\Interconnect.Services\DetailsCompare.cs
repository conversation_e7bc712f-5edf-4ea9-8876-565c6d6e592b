﻿
using Medical.Common;
using Medical.Models;
using Medical.ViewModels.View;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Interconnect.Services
{
    /// <summary>
    /// Detail表比较工具
    /// </summary>
    /// <remarks>
    /// 
    /// </remarks>
    public class DetailsCompare
    {
        /// <summary>
        /// 获取DetailsViw
        /// </summary>
        /// <param name="newDetials">前端传来的新的Detail</param>
        /// <param name="oldDetials">旧有的Detail</param>
        /// <returns></returns>
        public static DetailsView GetNewDetailsView<T>(List<Detail> newDetials, List<T> oldDetials,DateTime? oldAssessTime = null)
        {
            #region 数据初始化
            var view = new DetailsView()
            {
                AddList = new List<Detail>(),
                ModifyList = new List<Detail>(),
                DeleteList = new List<Detail>(),
            };
            var formatDetailList = new List<Detail>();

            if ((newDetials == null || newDetials.Count ==0) && (oldDetials == null || oldDetials.Count == 0))
            {
                return view;
            }
            // B类不进行分类、写入DetailLog
            newDetials = newDetials.Where(m => m.ControlerType != "B").ToList();
            #endregion

            #region 反比Detail数据预处理
            if (oldDetials != null)
            {
                var detailType = typeof(T);
                //数据库来源Details格式化
                foreach (var oldDetail in oldDetials)
                {
                    //从实例获取属性值
                    int id = int.Parse(ReflexUtil.GetProperty(oldDetail, "AssessListID", detailType));
                    string value = ReflexUtil.GetProperty(oldDetail, "AssessValue", detailType);
                    int? groupID = null;
                    if (int.TryParse(ReflexUtil.GetProperty(oldDetail, "AssessListGroupID", detailType), out int groupIDValue))
                    {
                        groupID = groupIDValue;
                    }
                    string valueJson = ReflexUtil.GetProperty(oldDetail, "AssessValueJson", detailType);
                    string bookMarkID = ReflexUtil.GetProperty(oldDetail, "BookMarkID", detailType);
                    string primaryKeyID = "";
                    if (detailType == typeof(Detail))
                    {
                        primaryKeyID = ReflexUtil.GetProperty(oldDetail, "ID", detailType);
                    }
                    else
                    {
                        primaryKeyID = ReflexUtil.GetPrimaryKeyValue(oldDetail);
                    }
                    var formatDetail = new Detail()
                    {
                        ID = primaryKeyID,
                        AssessListID = id,
                        AssessValue = value,
                        AssessListGroupID = groupID,
                        AssessValueJson = valueJson,
                        BookMarkID = bookMarkID,
                    };
                    formatDetail.AssessDateTime = oldAssessTime;

                    formatDetailList.Add(formatDetail);
                }
            }
            #endregion

            //反比,获取新增、修改、删除三个List
            return ClassifyDetails<T>(newDetials, formatDetailList);
        }

        /// <summary>
        /// 将数据分成新增、修改、删除三个集合
        /// </summary>
        /// <param name="newDetailList">前端传来的Details</param>
        /// <param name="oldDetailList">数据库保存的旧Details</param>
        /// <returns></returns>
        private static DetailsView ClassifyDetails<T>(List<Detail> newDetailList, List<Detail> oldDetailList)
        {
            #region 数据初始化&数据检核
            var view = new DetailsView()
            {
                AddList = new List<Detail>(),
                ModifyList = new List<Detail>(),
                DeleteList = new List<Detail>(),
            };
            if (newDetailList?.Count == 0 && oldDetailList?.Count == 0)
            {
                return view;
            }
            #endregion
            //全部新增
            if (newDetailList?.Count > 0 && oldDetailList.Count == 0)
            {
                view.AddList = newDetailList;
                return view;
            }
            //全部删除
            if (newDetailList?.Count == 0 && oldDetailList.Count > 0)
            {
                view.DeleteList = oldDetailList;
                return view;
            }

            //new - old ,得出新增的
            view.AddList = newDetailList.Except(oldDetailList).ToList();

            //old - new ,得出删除的
            view.DeleteList = oldDetailList.Except(newDetailList).ToList();
            
            var delAssessListIDs = view.DeleteList.Select(m => m.AssessListID).ToList();
            
            //将删除的数据从旧数据中剔除
            oldDetailList.RemoveAll(m => delAssessListIDs.Contains(m.AssessListID));

            view.ModifyList = FillModifyList<T>(newDetailList, oldDetailList).ToList();

            return view;
        }

        /// <summary>
        /// 填充ModifyList
        /// </summary>
        /// <param name="newDetailList">前端传递的集合</param>
        /// <param name="oldDetailList">后端查询出的集合</param>
        private static IEnumerable<Detail> FillModifyList<T>(List<Detail> newDetailList, List<Detail> oldDetailList)
        {
            var modifyList = new List<Detail>();
            foreach (var newDetail in newDetailList)
            {
                //根据旧集合的ID在新集合中Find
                var oldDetail = oldDetailList.Find(m => m.Equals(newDetail));

                //将新的数据放入修改集合
                if (oldDetail != null &&
                    (oldDetail.AssessValue != newDetail.AssessValue || oldDetail.AssessValueJson != newDetail.AssessValueJson || oldDetail.AssessDateTime!=newDetail.AssessDateTime))
                {
                    newDetail.ID = oldDetail.ID;
                    yield return newDetail;
                }
            }
            yield break;
        }

        /// <summary>
        /// 更新明细数据时使用
        /// </summary>
        /// <typeparam name="D">CareDetail类型</typeparam>
        /// <typeparam name="M">CareMain类型</typeparam>
        /// <param name="oldDetailInfos">明细数据，方法会根据传入的DetailsView对其进行处理</param>
        /// <param name="detailsView">分类数据</param>
        /// <param name="CreateDetailInfo">方法，创建一个CareDetailInfo</param>
        /// <param name="mainInfo">CareMain数据，传入的委托方法使用</param>
        /// <param name="userID">异动人员</param>
        /// <returns>新增的明细集合</returns>
        public static List<D> UpdateDetails<M, D>(List<D> oldDetailInfos, DetailsView detailsView,Func<M, List<Detail>, List<D>> CreateDetailInfo,
            M mainInfo, string userID) where D : ModifyInfo, IBaseCareDetailInfo, new()
        {
            // 要新插入到数据库的数据
            var newDetailInfoList = new List<D>();

            // 新增集合处理
            var newDetails = CreateDetailInfo(mainInfo, detailsView.AddList);
            newDetailInfoList.AddRange(newDetails);

            // 修改集合处理
            detailsView.ModifyList.ForEach(m =>
            {
                // TODO: 部分专项需要额外的判断条件
                var oldDetailInfo = oldDetailInfos.Find(n => n.AssessListID == m.AssessListID);
                var newDetailInfo = CloneData.CloneObj(oldDetailInfo);
                newDetailInfo.AssessValue = m.AssessValue;
                newDetailInfo.SetId();
                newDetailInfoList.Add(newDetailInfo);

                oldDetailInfo.Delete(userID);
            });

            // 删除集合处理
            detailsView.DeleteList.ForEach(m =>
            {
                var oldDetailInfo = oldDetailInfos.Find(n => n.AssessListID == m.AssessListID);
                oldDetailInfo.Delete(userID);
            });

            return newDetailInfoList;
        }

        /// <summary>
        /// 重载，以支持明细表带明细来源的方法
        /// </summary>
        /// <returns></returns>
        public static List<D> UpdateDetails<M, D>(List<D> oldDetailInfos, DetailsView detailsView, Func<M, List<Detail>,
            List<AssessContentSourceView>, List<D>> CreateDetailInfo, M mainInfo, string userID, List<AssessContentSourceView> assessContentSources)
            where D : ModifyInfo, IBaseCareDetailInfo, new()
        {
            // 要新插入到数据库的数据
            var newDetailInfoList = new List<D>();

            // 新增集合处理
            var newDetails = CreateDetailInfo(mainInfo, detailsView.AddList, assessContentSources);
            newDetailInfoList.AddRange(newDetails);

            // 修改集合处理
            detailsView.ModifyList.ForEach(m =>
            {
                var oldDetailInfo = oldDetailInfos.Find(n => n.AssessListID == m.AssessListID);
                var newDetailInfo = CloneData.CloneObj(oldDetailInfo);
                newDetailInfo.AssessValue = m.AssessValue;
                newDetailInfo.SetId();
                newDetailInfoList.Add(newDetailInfo);

                oldDetailInfo.Delete(userID);
            });

            // 删除集合处理
            detailsView.DeleteList.ForEach(m =>
            {
                var oldDetailInfo = oldDetailInfos.Find(n => n.AssessListID == m.AssessListID);
                oldDetailInfo?.Delete(userID);
            });

            return newDetailInfoList;
        }

        /// <summary>
        /// 删除明细表数据,并返回一个DetailsView
        /// </summary>
        /// <typeparam name="D">明细表类</typeparam>
        /// <param name="detailInfos">明细表数据集合</param>
        /// <param name="userID">护士工号</param>
        /// <returns></returns>
        public static DetailsView DeleteDetails<D>(List<D> detailInfos, string userID) where D : ModifyInfo, IBaseCareDetailInfo, new()
        {
            var detailsView = new DetailsView
            {
                DeleteList = new List<Detail>()
            };
            if (detailInfos.Count == 0)
            {
                return detailsView;
            }
            // 获取D类的类型
            var type = detailInfos.GetType();

            foreach (var detailInfo in detailInfos)
            {
                // 先逻辑删除
                detailInfo.Delete(userID);
                type.GetProperty("DataPumpDate")?.SetValue(detailInfo, null);
                type.GetProperty("DataPumpFlag")?.SetValue(detailInfo, null);

                // 组装DetailsView，供之后写入DetailLog使用
                var detail = new Detail
                {
                    AssessListID = detailInfo.AssessListID,
                    AssessValue = detailInfo.AssessValue
                };
                detail.ID = ReflexUtil.GetProperty(detailInfo, "ID", type);

                detailsView.DeleteList.Add(detail);
            }
            return detailsView;
        }
    }
}
