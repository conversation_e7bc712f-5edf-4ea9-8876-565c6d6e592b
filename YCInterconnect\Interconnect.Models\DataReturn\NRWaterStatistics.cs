﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("NursingRecord_WaterStatistics")]
    public class NRWaterStatisticsInfo : ModifyReturnInfo
    {
        /// <summary>
        ///	自增长序号	
        ///</summary>
        [Key]
        [Column("SN")]
        public int SN { get; set; }
        /// <summary>
        ///	住院就诊序号	
        ///</summary>
        public string CureNo { get; set; }
        /// <summary>
		///	"1 进食2 补液3 输血4 非 ///</summary>
        public int? Category { get; set; }
        /// <summary>
        ///	分类名称	
        ///</summary>
        public string CategoryName { get; set; }
        /// <summary>
        ///	用法	
        ///</summary>
        public string Usage { get; set; }
        /// <summary>
        ///	说明（内容）	
        ///</summary>
        public string WaterDesc { get; set; }
        /// <summary>
        ///	入/出水量	
        ///</summary>
        [Column(TypeName = "decimal(10,2)")]
        public decimal Volume { get; set; }

        /// <summary>
        ///	单位
        ///</summary>
        public string Unit { get; set; }
        
        /// <summary>
        ///		
        ///</summary>
        public string Remark { get; set; }
        /// <summary>
        ///	原记录ID	
        ///</summary>
        public string SrcKey { get; set; }
        /// <summary>
        ///	出入水时间	
        ///</summary>
        public DateTime? WaterTime { get; set; }
        /// <summary>
        ///	病区代码	
        ///</summary>
        public string WardCode { get; set; }
    }
}