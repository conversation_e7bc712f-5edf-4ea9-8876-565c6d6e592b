﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Interconnect.Data.Interface;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using NLog;
using System.Linq;
using Microsoft.Extensions.Options;
using Medical.Data.Context;
using Interconnect.Data.Context;
using Medical.Common;
using Arch.EntityFrameworkCore.UnitOfWork;

namespace Interconnect.Services
{
    public class DrugAllergyService : IDrugAllergyService
    {
        //Mdeical   
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWorkMedical;
        private readonly IInpatientDataRepository _IInpatientDataRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly ICommonHelper _commonHelper;

        //Interconnect
        private readonly IOptions<SystemConfig> _config;
        private readonly IDrugAllergyRepository _IDrugAllergyRepository;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ILogInfoServices _ILogInfoServices;
        private readonly IUnitOfWork<DataOutConnection> _unitOfWorkOut;
        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";
      
        /// <summary>
        /// 系统默认操作人
        /// </summary>
        private string MODIFYPERSONID = "TongBu";

        public DrugAllergyService(
             IInpatientDataRepository InPatientRepository
            , IDrugAllergyRepository DrugAllergyRepository
            , IOptions<SystemConfig> config
            , ILogInfoServices LogInfoServicess           
            , IUnitOfWork<MedicalDbContext> UnitOfWorkMedical
            , IUnitOfWork<DataOutConnection> UnitOfWorkOut
            , IAppConfigSettingRepository  appConfigSettingRepository
            , ICommonHelper commonHelper
            )
        {
            _IInpatientDataRepository = InPatientRepository;
            _IDrugAllergyRepository = DrugAllergyRepository;
            _config = config;
            _ILogInfoServices = LogInfoServicess;         
            _unitOfWorkMedical = UnitOfWorkMedical;
            _unitOfWorkOut = UnitOfWorkOut;
            _appConfigSettingRepository = appConfigSettingRepository;
            _commonHelper = commonHelper;
        }        

        /// <summary>
        /// 如果发现有没有同步的数据，则一直获取，直至完全同步
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SynchronizationMain()
        {
            var tongBuCount = await GetAppConfigSetting("TongbuCount");
            var OriginalList = _IDrugAllergyRepository.GetAsync(tongBuCount);
            while (OriginalList.Count > 0) //如果没有同步完成，则继续同步
            {
                if (!await SynchronizationDetail(OriginalList))
                {
                    return false;
                }
                OriginalList = _IDrugAllergyRepository.GetAsync(tongBuCount);
            }
            return true;
        }

        private async Task<int> GetAppConfigSetting(string settingCode)
        {
            int settingValue = 0;
            var result = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, settingCode);
            if (StringCheck.IsNumeric(result))
            {
                settingValue = int.Parse(result);
            }
            return settingValue;
        }

        private async Task<bool> SynchronizationDetail(List<DrugAllergyInfo> OriginalList)
        {
            List<Medical.ViewModels.Interface.PatientProfile> patientProfile = new List<Medical.ViewModels.Interface.PatientProfile>();
            Medical.ViewModels.Interface.PatientProfile t = null;
            string Tablename = "DrugAllergy";
            int Failcount = 0;
            List<LogInfo> LogList = new List<LogInfo>();
            LogInfo TempLog = null;

            //从配置档中获取数据 梁宝华 2020-04-28
            var alllogSetFlag = await GetAppConfigSetting("AllLogSet");
            var drugAllergyID = await GetAppConfigSetting("DrugAllergyID");
            var language = await GetAppConfigSetting("Language");


            TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, " 开始进行数据同步，数据条数：" + OriginalList.Count);
            LogList.Add(TempLog);

            #region "数据同步"
            foreach (var item in OriginalList)
            {
                item.Counts = item.Counts ?? 0;
                item.Counts = item.Counts + 1;
                try
                {
                    //获取Medical中的病人基本信息
                    InpatientDataInfo TempInpatientData = await _IInpatientDataRepository.GetAsyncByCaseNumber(item.ChartNo,_config.Value.HospitalID);

                    if (TempInpatientData == null)
                    {
                        if (alllogSetFlag == 1)
                        {
                            TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, "表: InpatientData ChartNo[" + item.ChartNo + "]没有查询到病人的信息!");
                            LogList.Add(TempLog);
                        }
                        Failcount++;
                        continue;
                    }

                    t = new Medical.ViewModels.Interface.PatientProfile
                    {
                        HospitalID = _config.Value.HospitalID,
                        InpatientID = TempInpatientData.ID,
                        CaseNumber = TempInpatientData.CaseNumber,
                        ChartNo= TempInpatientData.ChartNo,
                        PatientID = TempInpatientData.PatientID,
                        ModelName = "HIS",
                        Source = "I",
                        ProfileDate = TempInpatientData.AdmissionDate,
                        ProfileTime = TempInpatientData.AdmissionTime,

                        SerialNumber = item.ID.ToString(), //中间表的主键
                        AssessListID = drugAllergyID,//药物过敏ID
                        AssessValue = item.DrugAllergy, //药物过敏内容
                        AutoAddFlag = "",
                        Note = "",
                        ModifyPersonID = MODIFYPERSONID,
                        ModifyDate = DateTime.Now,
                    };
                    item.DataPumpFlag = "*";
                    item.DataPumpDate = DateTime.Now;
                    patientProfile.Add(t);
                }
                catch (Exception ex)
                {
                    _logger.Error("同步错误：item.ID " + item.ID + ":" + ex.ToString());
                    return false;
                }
            }
            #endregion

            #region "数据更新"
            //调用接口
            if (patientProfile.Count > 0)
            {
                //呼叫Profile     
                _commonHelper.AddProfile(patientProfile);              
            }
            
            //if (OriginalList.Count >= 1)
            //{
            //    try
            //    {
            //        _unitOfWorkOut.GetRepository<DrugAllergyInfo>().Update(OriginalList);
            //        _unitOfWorkOut.SaveChanges();
            //    }
            //    catch (Exception ex)
            //    {
            //        _logger.Error("同步失败" + ex.ToString());
            //        return false;
            //    }

            //}
            TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, " 同步结束 成功：" + (OriginalList.Count - Failcount).ToString() + "条！ 失败：" + Failcount.ToString() + "条！");

            LogList.Add(TempLog);
            int ItemNo = 0;
            string Guid = "";
            Guid = System.Guid.NewGuid().ToString("N");
            foreach (var item in LogList)
            {
                item.Guid = Guid;
                item.ItemNo = ItemNo;
                ItemNo++;
            }
            try
            {
                _unitOfWorkOut.GetRepository<LogInfo>().Insert(LogList);
                _unitOfWorkOut.SaveChanges();
            }
            catch (Exception ex)
            {

                _logger.Error(Tablename + "同步成功，但写同步日志失败||" + ex.ToString());
            }
            _logger.Info(Tablename + "    同步完成!" + DateTime.Now);
            return true;
            #endregion
        }

    }
}


