﻿using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Interconnect.ViewModels;
using Medical.Common;
using Medical.Data.Interface;
using NLog;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Interconnect.Services
{
    public class TPRService : ITPRService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private static DateTime? LastTime = null;
        private readonly ITPRscheduleRepository _tPRscheduleRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;

        private const string APPCONFIGSETTING_SETTINGCODE_FREQUENCYSECONDS = "FrequencySeconds";
        private const string APPCONFIGSETTING_SETTINGCODE_BIFREQUESTTIME = "BigRequestTime";

        public TPRService(
            ITPRscheduleRepository tPRscheduleRepository
            , IAppConfigSettingRepository appConfigSettingRepository
            )
        {
            _tPRscheduleRepository = tPRscheduleRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
        }
        /// <summary>
        /// 获取TPRSchedule生命体征数据
        /// </summary>
        /// <param name="StartDate"></param>
        /// <param name="EndDate"></param>
        /// <param name="HospitalID"></param>
        /// <returns></returns>
        public async Task<Dictionary<string, List<TPRschedule>>> GetTprscheduleListAsync(DateTime StartDate, DateTime EndDate, string HospitalID)
        {
            Dictionary<string, List<TPRschedule>> data = new Dictionary<string, List<TPRschedule>>();
            List<TPRscheduleInfo> tprList = new List<TPRscheduleInfo>();
            List<TPRschedule> tprs = new List<TPRschedule>();
            string message = "";
            DateTime nowDate = DateTime.Now;
            var seconds = await _appConfigSettingRepository.GetBySettingCode(APPCONFIGSETTING_SETTINGCODE_FREQUENCYSECONDS);
            var minutes = await _appConfigSettingRepository.GetBySettingCode(APPCONFIGSETTING_SETTINGCODE_BIFREQUESTTIME);
            if (seconds == null || string.IsNullOrEmpty(seconds.SettingValue))
            {
                _logger.Error("数据库未配置最大访问秒数数据");
                return null;
            }
            if (!StringCheck.IsNumeric(seconds.SettingValue))
            {
                _logger.Error("最大访问秒数数据类型配置错误");
                return null;
            }
            if (minutes == null || string.IsNullOrEmpty(minutes.SettingValue))
            {
                _logger.Error("数据库未配置最大访问间隔时间数据");
                return null;
            }
            if (!StringCheck.IsNumeric(minutes.SettingValue))
            {
                _logger.Error("数据库最大访问间隔时间数据类型配置错误");
                return null;
            }
            if (EndDate.Subtract(StartDate).TotalMinutes > int.Parse(minutes.SettingValue))
            {
                message = "请求时间间隔超过两个小时";
                data.Add(message, tprs);
                return data;
            }
            tprList = await _tPRscheduleRepository.GetTPRscheduleListAsync(StartDate, EndDate);
            if (tprList.Count == 0)
            {
                message = "此段时间内，未获取到数据";
                data.Add(message, tprs);
                return data;
            }
            if (LastTime == null)
            {
                LastTime = nowDate;                
                tprs = GetTPRscheduleList(tprList, tprs);
                data.Add(message, tprs);
                return data;
            }
            else
            {
                if (nowDate.Subtract(LastTime.Value).TotalSeconds <= int.Parse(seconds.SettingValue))
                {
                    message = "请求时间过于频繁";
                    data.Add(message, tprs);
                }
                else
                {
                    LastTime = nowDate;                    
                    tprs = GetTPRscheduleList(tprList, tprs);
                    data.Add(message, tprs);
                }
                return data;
            }
          
        }
        /// <summary>
        /// 组装传出生命体征数据
        /// </summary>
        /// <param name="tprList"></param>
        /// <param name="tprs"></param>
        /// <returns></returns>
        private List<TPRschedule> GetTPRscheduleList(List<TPRscheduleInfo> tprList, List<TPRschedule> tprs)
        {
            foreach (var tprInfo in tprList)
            {
                var tpr = new TPRschedule
                {
                    CaseNumber = tprInfo.CaseNumber,
                    ChartNo = tprInfo.ChartNo,
                    EMRField = tprInfo.EMRField,
                    ScheduleDate = tprInfo.ScheduleDate,
                    ScheduleTime = tprInfo.ScheduleTime
                };
                tprs.Add(tpr);
            }
            return tprs;
        }
    }
}