﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Interconnect.Data.Context;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Interconnect.Services
{
    public class InterconnectNurseShiftService : IInterconnectNurseShiftService
    {
        //Mdeical
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IStationListRepository _IStationListRepository;
        private readonly ILogInfoServices _ILogInfoServices;
        private readonly INurseShiftRepository _MedicalNurseShiftRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;

        //Interconnect
        private readonly IUnitOfWork<DataOutConnection> _unitOfWorkOut;
        private readonly IOptions<SystemConfig> _config;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IInterconnectNurseShiftRepository _nurseShiftRepository;
        private readonly Medical.Data.Interface.IStationShiftRepository _MedicalStationShiftRepository;


        public InterconnectNurseShiftService(IUnitOfWork<MedicalDbContext> unitOfWork
            , IStationListRepository stationListRepository
            , IUnitOfWork<DataOutConnection> unitOfWorkOut
            , IOptions<SystemConfig> options
            , IInterconnectNurseShiftRepository nurseShiftRepository
            , ILogInfoServices logInfoServices
            , INurseShiftRepository MedicalNurseShiftRepository
            , Medical.Data.Interface.IStationShiftRepository stationShiftRepository
            , IAppConfigSettingRepository  appConfigSettingRepository)
        {
            _unitOfWork = unitOfWork;
            _IStationListRepository = stationListRepository;
            _unitOfWorkOut = unitOfWorkOut;
            _config = options;
            _nurseShiftRepository = nurseShiftRepository;
            _ILogInfoServices = logInfoServices;
            _MedicalNurseShiftRepository = MedicalNurseShiftRepository;
            _MedicalStationShiftRepository = stationShiftRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
        }

        /// <summary>
        /// 如果发现有没有同步的数据，则一直获取，直至完全同步
        /// </summary>
        /// <returns></returns>
        public   bool SynchronizationMain()
        {
            //_logger.Info("开始获取科室未同步数据");
            ////待抽档数据
            //var pumpDatas =  _nurseShiftRepository.GetDataPump();
            //var nurseShift=_MedicalNurseShiftRepository.GetNurseShiftByDateTime(DateTime.Now.Date);
            //var StationList = _IStationListRepository.GetStationList(_config.Value.HospitalID);
            //var medicalStationShiftList =   _MedicalStationShiftRepository.GetShiftList();

            //StationList = StationList.Where(m => m.HospitalID == _config.Value.HospitalID).ToList();
            //while (pumpDatas.Count > 0) //如果没有同步完成，则继续同步
            //{
            //    if (! SynchronizationDetail(pumpDatas, StationList, nurseShift, medicalStationShiftList))
            //    {
            //        return false;
            //    }
            //    pumpDatas =  _nurseShiftRepository.GetDataPump();
            //}
            return true;
        }

    

        private  bool SynchronizationDetail(List<Models.NurseShiftInfo> OriginalList, List<StationListInfo> StationList
            , List<Medical.Models.NurseShiftInfo> NurseShift,List<Medical.Models.StationShiftInfo> medicalStationShiftList)
        {
            _logger.Info("开始运行SynchronizationDetail");
            int MaxID =  _IStationListRepository.GetMaxID();
            List<Medical.Models.NurseShiftInfo> Insertlist = new List<Medical.Models.NurseShiftInfo>();           

            int Failcount = 0;
            string Tablename = "NurseShift";
            List<LogInfo> LogList = new List<LogInfo>();
            LogInfo TempLog = null;

            TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, " 开始进行数据同步，数据条数：" + OriginalList.Count);
            LogList.Add(TempLog);

            //从配置档中获取数据 梁宝华 2020-04-29
            var modifyPersonID = _appConfigSettingRepository.GetConfigSettingValue( "Configs", "ModifyPersonID").Result;

            #region "数据同步"
            foreach (var item in OriginalList)
            {

                item.Counts = item.Counts ?? 0;
                item.Counts = item.Counts + 1;
                try
                {
                    //获取Medical中的病区信息 
                    var TempStation = StationList.Where(m => m.StationCode.Trim() == item.StationCode.Trim()).ToList();
                    if (TempStation.Count != 1)
                    {
                        TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, "StationCode:[" + item.StationCode + "]查询病区对照信息错误!");
                        LogList.Add(TempLog);
                        Failcount++;
                        continue;
                    }

                    //获取班别对应名称
                    var stationShiftInfos = medicalStationShiftList.Where(m => m.StationID == TempStation[0].ID && m.ShiftName == item.HRShiftClass).ToList();

                    if (stationShiftInfos.Count<1)
                    {
                        TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, "查找表：Stationshift，StationCode:[" + item.StationCode + "] 班别：" + item.HRShiftName + "获取失败");
                        LogList.Add(TempLog);
                        Failcount++;
                        continue;
                    }

                    //判断一个人，在一个病区的班别信息，如果没有，进行新增
                    var TempNurseShift = NurseShift.Where(m => m.ShiftDate == item.ShiftDate && m.EmployeeID == item.EmployeeID
                    && m.StationID == TempStation[0].ID && m.StationShiftID== stationShiftInfos[0].ID).ToList();

                    //获取插入的数据
                    var TempInsertlist = Insertlist.Where(m => m.ShiftDate == item.ShiftDate && m.EmployeeID == item.EmployeeID
                    && m.StationID == TempStation[0].ID && m.StationShiftID == stationShiftInfos[0].ID).ToList();
                    //如果不存在进行新增
                    if (TempNurseShift.Count < 1 && TempInsertlist.Count < 1)
                    {
                        var t = new Medical.Models.NurseShiftInfo
                        {
                            ShiftDate = item.ShiftDate,
                            StationID = TempStation[0].ID,
                            HRShift =item.HRShiftClass,
                            ManPower = Convert.ToDecimal(1.0),
                            StationShiftID = stationShiftInfos[0].ID,
                            SystemShift = stationShiftInfos[0].Shift,
                            EmployeeID = item.EmployeeID,
                            ModifyPersonID = item.ModifyPersonID,
                            ModifyDate = DateTime.Now,
                            DeleteFlag = ""
                        };
                        Insertlist.Add(t);
                        MaxID++;
                    }
                    else //如果根据病区查询到这条记录，则判断其他的记录是否发生了改变
                    {
                        if (TempNurseShift.Count() == 1)
                        {
                            TempNurseShift[0].DeleteFlag = item.DeleteFlag;
                            TempNurseShift[0].ModifyPersonID = modifyPersonID;
                            TempNurseShift[0].ModifyDate = DateTime.Now;
                        }
                        _unitOfWork.GetRepository<Medical.Models.NurseShiftInfo>().Update(TempNurseShift);
                    }
                    item.DataPumpFlag = "*";
                    item.DataPumpDate = DateTime.Now;
                }

                catch (Exception ex)
                {
                    _logger.Error(Tablename + "  同步错误：" + item.StationCode + ":" + ex.ToString());
                    return false;
                }
            }

            #endregion
            #region "数据更新"
            if (OriginalList.Count >= 1)
            {
                try
                {
                    _unitOfWork.GetRepository<Medical.Models.NurseShiftInfo>().Insert(Insertlist);
                 //   _unitOfWorkOut.GetRepository<Models.NurseShiftInfo>().Update(OriginalList);
                    _unitOfWork.SaveChanges();
                   // _unitOfWorkOut.SaveChanges();
                }
                catch (Exception ex)
                {
                    _logger.Error(Tablename + "||同步失败||" + ex.ToString());
                    return false;
                }
            }
            TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, " 同步结束 成功：" + (OriginalList.Count - Failcount).ToString() + "条！ 失败：" + Failcount.ToString() + "条！");
            LogList.Add(TempLog);
            int ItemNo = 0;
            string Guid = "";
            Guid = System.Guid.NewGuid().ToString("N");
            foreach (var item in LogList)
            {
                item.Guid = Guid;
                item.ItemNo = ItemNo;
                ItemNo++;
            }
            try
            {
                _unitOfWorkOut.GetRepository<LogInfo>().Insert(LogList);
                _unitOfWorkOut.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error(Tablename + "同步成功，但写同步日志失败||" + ex.ToString());
            }
            _logger.Info(Tablename + "||同步完成!");
            return true;
            #endregion
        }
    }
}