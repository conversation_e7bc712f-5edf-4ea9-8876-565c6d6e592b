﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.Document;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace Medical.Data.Repository
{
    public class PatientTubeCareMainRepository : IPatientTubeCareMainRepository
    {
        private MedicalDbContext _dbContext = null;

        public PatientTubeCareMainRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }
        /// <summary>
        /// 根据导管维护主档ID获取导管维护主表
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<PatientTubeCareMainInfo> GetByCareMainIDAsync(string careMainID)
        {
            return await _dbContext.PatientTubeCareMainInfos.Where(t => t.PatientTubeCareMainID == careMainID && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<bool> GetExistByCareMainIDAsync(string careMainID)
        {
            var count = await _dbContext.PatientTubeCareMainInfos.Where(t => t.PatientTubeCareMainID == careMainID && t.DeleteFlag != "*").CountAsync();
            if (count <= 0)
            {
                return false;
            }
            else
            {
                return true;
            }
        }
        /// <summary>
        /// 根据导管记录ID获取导管维护主表集合
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<List<PatientTubeCareMainInfo>> GetByRecordIDAsync(string recordID)
        {
            return await _dbContext.PatientTubeCareMainInfos.Where(t => t.PatientTubeRecordID == recordID && t.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据导管记录ID获取按评估时间的最后一次导管维护主表
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<PatientTubeCareMainInfo> GetLastByTimeAsync(string recordID)
        {
            var careMain = await _dbContext.PatientTubeCareMainInfos.Where(t => t.PatientTubeRecordID == recordID && t.DeleteFlag != "*")
                .OrderByDescending(m => m.AssessDate)
                .ThenByDescending(m => m.AssessTime)
                .ThenByDescending(t => t.NumberOfAssessment)
                .FirstOrDefaultAsync();

            return careMain;
        }
        /// <summary>
        /// 根据导管记录ID获取按评估次数的最后一次导管维护主表
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<PatientTubeCareMainInfo> GetLastByNumAsync(string recordID)
        {
            var careMains = await _dbContext.PatientTubeCareMainInfos.Where(t => t.PatientTubeRecordID == recordID && t.DeleteFlag != "*").OrderByDescending(t => t.NumberOfAssessment).ToListAsync();
            if (careMains != null && careMains.Count > 0)
            {
                return careMains[0];
            }
            return null;
        }
        /// <summary>
        ///  根据措施码获取导管维护主表集合
        /// </summary>
        /// <param name="scheduleID"></param>
        /// <returns></returns>
        public async Task<List<PatientTubeCareMainInfo>> GetTubeCareByScheduleID(string scheduleID)
        {
            return await _dbContext.PatientTubeCareMainInfos.Where(t => t.PatientScheduleMainID == scheduleID && t.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据RecordsCode获取导管记录
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="recordsCode"></param>
        /// <returns></returns>
        public async Task<PatientTubeCareMainInfo> GetCareByRecordsCode(string recordID, string recordsCode)
        {
            return await _dbContext.PatientTubeCareMainInfos.Where(t => t.PatientTubeRecordID == recordID && t.RecordsCode == recordsCode && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<List<PatientTubeCareMainInfo>> GetByHandoverID(string hadnoverID)
        {
            var list = await _dbContext.PatientTubeCareMainInfos.Where(t => t.HandoverID == hadnoverID).ToListAsync();

            return list;
        }

        public async Task<List<PatientTubeCareMainInfo>> GetByAssessMainID(string assessMainID)
        {
            return await _dbContext.PatientTubeCareMainInfos.Where(m => m.PatientAssessMainID == assessMainID
                              && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientTubeCareMainInfo>> GetNoAssessMainIDData(string inpatientID)
        {
            return await _dbContext.PatientTubeCareMainInfos.Where(m => m.InpatientID == inpatientID
                            && (m.PatientAssessMainID == null || m.PatientAssessMainID == "") && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取病人所有维护记录
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientTubeCareMainInfo>> GetCareMianByInpatientID(string inpatientID)
        {
            return await _dbContext.PatientTubeCareMainInfos.Where(m => m.InpatientID == inpatientID
                            && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<KeyValue>> GetVentilationTubeCare(string patientTubeCareRecordID)
        {
            var query = await (from m in _dbContext.PatientTubeCareMainInfos
                               join n in _dbContext.PatientTubeCareDetailInfos.Where(m => m.DeleteFlag != "*") on m.PatientTubeCareMainID equals n.PatientTubeCareMainID
                               where m.PatientTubeRecordID == patientTubeCareRecordID && m.NumberOfAssessment == 1
                               select new KeyValue
                               {
                                   ID = n.AssessListID,
                                   Value = n.AssessValue
                               }).ToListAsync();
            return query;
        }
        public async Task<List<SchedulePerformDetail>> GetDayPerformDetail(string inpatientID, int stationID, DateTime shiftDate)
        {
            var datas = await (from a in _dbContext.PatientTubeCareMainInfos
                               join b in _dbContext.PatientTubeCareDetailInfos.Where(m => m.DeleteFlag != "*") on a.PatientTubeCareMainID equals b.PatientTubeCareMainID
                               where a.InpatientID == inpatientID && a.StationID == stationID && a.ShiftDate == shiftDate && a.DeleteFlag != "*"
                               select new SchedulePerformDetail
                               {
                                   PerformDate = a.AssessDate,
                                   PerformTime = a.AssessTime,
                                   AssessListID = b.AssessListID,
                                   ScheduleData = b.AssessValue,
                                   InterventionDetailID = b.AssessListID
                               }).ToListAsync();

            return datas;
        }
        public async Task<List<TubeInsideLengthView>> GetPatientTubeInsideLength(string inpatientID, DateTime shiftDate)
        {
            var query = await _dbContext.PatientTubeCareMainInfos.Where(m => m.InpatientID == inpatientID && m.ShiftDate == shiftDate && m.DeleteFlag != "*")
                              .Join(_dbContext.PatientTubeRecordInfos
                              , m => m.PatientTubeRecordID
                              , n => n.PatientTubeRecordID
                              , (m, n) => new TubeInsideLengthView
                              {
                                  PatientTubeRecordID = m.PatientTubeRecordID,
                                  AssessDate = m.AssessDate,
                                  AssessTime = m.AssessTime,
                                  InsideLength = m.InsideLength,
                                  Shift = m.Shift,
                                  TubeID = n.TubeID,
                                  RemoveDate = n.RemoveDate,
                                  RemoveTime = n.RemoveTime
                              }).ToListAsync();

            return query;
        }

        /// <summary>
        /// 获取为胃肠减压导管的出入量记录内容键值对
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="tubeID_15"></param>
        /// <returns></returns>
        public async Task<List<DecompressionTubeView>> GetDrainageVolumeByTubeListIDAndInpatientIDAsync(string inpatientID, int tubeID_15, DateTime startDateTime, DateTime endDateTime)
        {
            var records = await (from a in _dbContext.PatientTubeRecordInfos.Where(m => m.InpatientID == inpatientID
                                     && m.DeleteFlag != "*" && m.TubeID == tubeID_15)
                                 join b in _dbContext.PatientTubeCareMainInfos.Where(m => m.InpatientID == inpatientID
                                 && m.DeleteFlag != "*" && m.TubeID == tubeID_15 && m.DrainageVolume.HasValue
                                 && m.ShiftDate >= startDateTime.Date && m.ShiftDate <= endDateTime.Date)
                                 on a.PatientTubeRecordID equals b.PatientTubeRecordID
                                 select new DecompressionTubeView
                                 {
                                     PatientTubeCareMainID = b.PatientTubeCareMainID,
                                     RecordDate = b.AssessDate.Add(b.AssessTime),
                                     DrainageVolume = b.DrainageVolume.Value,
                                 }
                                ).ToListAsync();

            return records.OrderBy(m => m.RecordDate).ToList();
        }

        /// <summary>
        /// 获取起止时间内指定导管数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="tubeIDs"></param>
        /// <param name="startDateTime"></param>
        /// <param name="endDateTime"></param>
        /// <returns></returns>
        public async Task<List<TubeCareMainAndDetailView>> GetTubeDatasByIDAndShiftDateAsync(string inpatientID, List<int> tubeIDs, DateTime startDateTime, DateTime endDateTime)
        {
            var datas = await (from m in _dbContext.PatientTubeRecordInfos.Where(m => m.InpatientID == inpatientID && tubeIDs.Contains(m.TubeID) && m.DeleteFlag != "*")
                               join a in _dbContext.PatientTubeCareMainInfos.Where(m => m.AssessDate >= startDateTime.Date && m.AssessDate <= endDateTime.Date && m.DeleteFlag != "*")
                                    on m.PatientTubeRecordID equals a.PatientTubeRecordID
                               join b in _dbContext.PatientTubeCareDetailInfos.Where(m => m.DeleteFlag != "*") on a.PatientTubeCareMainID equals b.PatientTubeCareMainID
                               select new TubeCareMainAndDetailView
                               {
                                   PatientTubeRecordID = m.PatientTubeRecordID,
                                   PatientTubeCareMainID = a.PatientTubeCareMainID,
                                   AssessDate = a.AssessDate,
                                   AssessTime = a.AssessTime,
                                   AssessListAndInterventionDetailID = b.AssessListID,
                                   AssessValue = b.AssessValue,
                                   InsideLength = a.InsideLength,
                                   BodyPartID = m.BodyPartID,
                                   AssessListGroupID = b.AssessListGroupID,
                                   TubeID = a.TubeID,
                                   PerformPersonID = a.ModifyPersonID
                               }).ToListAsync();
            datas = datas.Where(m => m.AssessDate.Add(m.AssessTime) >= startDateTime && m.AssessDate.Add(m.AssessTime) <= endDateTime).ToList();
            return datas;
        }
        /// <summary>
        /// 获取每条主记录的最后一条维护记录
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientTubeCareMainInfo>> GetLastCareMainByRecordIDs(List<string> recordIDs)
        {
            var lists = new List<PatientTubeCareMainInfo>();
            var careMains = await _dbContext.PatientTubeCareMainInfos.Where(m =>
               m.DeleteFlag != "*" && recordIDs.Contains(m.PatientTubeRecordID)).ToListAsync();
            if (careMains.Count == 0)
            {
                return lists;
            }
            foreach (var recordID in recordIDs)
            {
                var sucCareMain = careMains.Where(m => m.PatientTubeRecordID == recordID).OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).LastOrDefault();
                if (sucCareMain != null)
                {
                    lists.Add(sucCareMain);
                }
            }
            return lists;
        }

        public async Task<List<NursingRecordTubeView>> GetNursingRecordData(string inpatientID, List<int> tubeIDs, List<int> assessListIDs = null)
        {
            var datas = new List<NursingRecordTubeView>();
            if (assessListIDs == null)
            {
                datas = await _dbContext.PatientTubeCareMainInfos.Where(m => m.InpatientID == inpatientID && tubeIDs.Contains(m.TubeID) && m.DeleteFlag != "*")
                    .Select(m => new NursingRecordTubeView
                    {
                        StationID = m.StationID,
                        TubeID = m.TubeID,
                        AssessDate = m.AssessDate,
                        AssessTime = m.AssessTime,
                        EmployeeID = m.AddEmployeeID,
                        NursingLevel = m.NursingLevel,
                    }).ToListAsync();
            }
            else
            {
                datas = await (from m in _dbContext.PatientTubeCareMainInfos.Where(m => m.InpatientID == inpatientID && tubeIDs.Contains(m.TubeID) && m.DeleteFlag != "*")
                               join n in _dbContext.PatientTubeCareDetailInfos.Where(m => assessListIDs.Contains(m.AssessListID) && m.DeleteFlag != "*")
                               on m.PatientTubeCareMainID equals n.PatientTubeCareMainID
                               select new NursingRecordTubeView
                               {
                                   StationID = m.StationID,
                                   TubeID = m.TubeID,
                                   AssessListID = n.AssessListID,
                                   AssessValue = n.AssessValue,
                                   AssessDate = m.AssessDate,
                                   AssessTime = m.AssessTime,
                                   EmployeeID = m.AddEmployeeID,
                                   NursingLevel = m.NursingLevel
                               }).ToListAsync();
            }
            return datas;
        }
        public async Task<List<PatientTubeCareMainInfo>> GetRecordsBySourceID(string sourceID, string sourceType)
        {
            var data = await _dbContext.PatientTubeCareMainInfos.Where(m => m.SourceID == sourceID && m.DeleteFlag != "*").ToListAsync();
            if (!string.IsNullOrEmpty(sourceType))
            {
                data = data.Where(m => !string.IsNullOrEmpty(m.SourceType) && m.SourceType.Trim() == sourceType.Trim()).ToList();
            }
            return data;
        }
        public async Task<List<TubeNameView>> GetTubeName(List<string> careMainIDs)
        {
            var data = await (from m in _dbContext.PatientTubeCareMainInfos.Where(m => careMainIDs.Contains(m.PatientTubeCareMainID) && m.DeleteFlag != "*")
                              join n in _dbContext.PatientTubeRecordInfos.Where(m => m.DeleteFlag != "*") on m.PatientTubeRecordID equals n.PatientTubeRecordID
                              select new TubeNameView
                              {
                                  PatientTubeRecordID = m.PatientTubeRecordID,
                                  PatientTubeCareMainID = m.PatientTubeCareMainID,
                                  TubeID = n.TubeID,
                                  BodyPartID = n.BodyPartID,
                                  TubeNumber = n.TubeNumber
                              }).ToListAsync();
            return data;
        }
        /// <summary>
        /// 获取每个导管最近的一次维护
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <returns></returns>
        public async Task<List<TubeHandoverView>> GetTubeLastMaintain(string inpatientID)
        {
            var datas = await (from a in _dbContext.PatientTubeRecordInfos.Where(m => m.DeleteFlag != "*")
                               join b in _dbContext.PatientTubeCareMainInfos.Where(m => m.DeleteFlag != "*")
                               on a.PatientTubeRecordID equals b.PatientTubeRecordID
                               where a.InpatientID == inpatientID
                               select new TubeHandoverView
                               {
                                   PatientTubeRecordID = a.PatientTubeRecordID,
                                   InpatientID = a.InpatientID,
                                   TubeListID = a.TubeID,
                                   TubeNumber = a.TubeNumber,
                                   BodyPartID = a.BodyPartID,
                                   PatientTubeCareMainID = b.PatientTubeCareMainID,
                                   StationID = b.StationID,
                                   RecordsCode = b.RecordsCode,
                                   AssessDate = b.AssessDate,
                                   AssessTime = b.AssessTime,
                                   ShiftDate = b.ShiftDate ?? b.AssessDate,
                                   Shift = b.Shift,
                                   DrainageColor = b.DrainageColor,
                                   Characteristic = b.Characteristic,
                                   BringToShift = b.BringToShift ?? false,
                                   RemoveDate = a.RemoveDate,
                                   RemoveTime = a.RemoveTime,
                                   RemoveReason = a.RemoveReason,
                                   ExtendItem = b.ExtendItem
                               }).OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime).ToListAsync();
            return datas.GroupBy(m => m.PatientTubeRecordID).Select(m => m.First()).ToList();
        }
 
        public async Task<List<TubeHandoverView>> GetTubeMaintainByDateTime(string inpatientID, DateTime startDateTime, DateTime endDateTime)
        {
            return await (from a in _dbContext.PatientTubeRecordInfos.Where(m => m.DeleteFlag != "*")
                          join b in _dbContext.PatientTubeCareMainInfos.Where(m => m.DeleteFlag != "*")
                          on a.PatientTubeRecordID equals b.PatientTubeRecordID
                          where a.InpatientID == inpatientID
                           && (a.StartDate < endDateTime.Date || (a.StartDate == endDateTime.Date && a.StartTime <= endDateTime.TimeOfDay))
                           && (!a.RemoveDate.HasValue || (a.RemoveDate.HasValue && (a.RemoveDate.Value > startDateTime.Date || (a.RemoveDate == startDateTime.Date && a.RemoveTime >= startDateTime.TimeOfDay))))
                          select new TubeHandoverView
                          {
                              PatientTubeRecordID = a.PatientTubeRecordID,
                              InpatientID = a.InpatientID,
                              TubeListID = a.TubeID,
                              TubeNumber = a.TubeNumber,
                              BodyPartID = a.BodyPartID,
                              PatientTubeCareMainID = b.PatientTubeCareMainID,
                              StationID = b.StationID,
                              RecordsCode = b.RecordsCode,
                              AssessDate = b.AssessDate,
                              AssessTime = b.AssessTime,
                              ShiftDate = b.ShiftDate ?? b.AssessDate,
                              Shift = b.Shift,
                              DrainageColor = b.DrainageColor,
                              Characteristic = b.Characteristic,
                              BringToShift = b.BringToShift ?? false,
                              RemoveDate = a.RemoveDate,
                              RemoveTime = a.RemoveTime,
                              RemoveReason = a.RemoveReason,
                              ExtendItem = b.ExtendItem
                          }).OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).ToListAsync();
        }
        /// <summary>
        /// 获取指定时间内，导管最近的一次维护，且不是补录
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <returns></returns>
        public async Task<List<TubeHandoverView>> GetTubeLastMaintainByDateTime(string inpatientID, DateTime removeDate)
        {
            var datas = await (from a in _dbContext.PatientTubeRecordInfos.Where(m => m.DeleteFlag != "*")
                               join b in _dbContext.PatientTubeCareMainInfos.Where(m => m.DeleteFlag != "*")
                               on a.PatientTubeRecordID equals b.PatientTubeRecordID
                               where a.InpatientID == inpatientID
                                     && (!a.RemoveDate.HasValue || (a.RemoveDate.HasValue && a.RemoveDate.Value > removeDate))
                               select new TubeHandoverView
                               {
                                   PatientTubeRecordID = a.PatientTubeRecordID,
                                   InpatientID = a.InpatientID,
                                   TubeListID = a.TubeID,
                                   TubeNumber = a.TubeNumber,
                                   BodyPartID = a.BodyPartID,
                                   PatientTubeCareMainID = b.PatientTubeCareMainID,
                                   StationID = b.StationID,
                                   RecordsCode = b.RecordsCode,
                                   AssessDate = b.AssessDate,
                                   AssessTime = b.AssessTime,
                                   ShiftDate = b.ShiftDate ?? b.AssessDate,
                                   Shift = b.Shift,
                                   DrainageColor = b.DrainageColor,
                                   Characteristic = b.Characteristic,
                                   BringToShift = b.BringToShift ?? false,
                                   RemoveDate = a.RemoveDate,
                                   RemoveTime = a.RemoveTime,
                                   RemoveReason = a.RemoveReason,
                                   ExtendItem = b.ExtendItem
                               }).OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime).ToListAsync();
            return datas.GroupBy(m => m.PatientTubeRecordID).Select(m => m.First()).ToList(); 
        }

        /// <summary>
        /// 根据导管记录ID获取导管维护主表ID集合
        /// </summary>
        /// <param name="recordID">主记录ID</param>
        /// <param name="shiftDate">班别日期</param>
        /// <param name="shift">班别</param>
        /// <returns></returns>
        public async Task<List<string>> GetByRecordIDAndShift(string recordID, DateTime shiftDate, string shift)
        {
            return await _dbContext.PatientTubeCareMainInfos.Where(m => m.PatientTubeRecordID == recordID && m.ShiftDate == shiftDate && m.Shift == shift)
                .Select(m => m.PatientTubeCareMainID).ToListAsync();
        }
        /// <summary>
        /// 根据sourceID和SourceType获取数据
        /// </summary>
        /// <param name="sourceID">来源序号</param>
        /// <param name="sourceType">来源类别</param>
        /// <returns></returns>
        public async Task<List<string>> GetRecordIDsBySourceID(string sourceID, string sourceType)
        {
            return await _dbContext.PatientTubeCareMainInfos.Where(m => m.SourceID == sourceID && m.SourceType == sourceType && m.DeleteFlag != "*")
                .Select(m => m.PatientTubeRecordID).ToListAsync();
        }

        /// <summary>
        /// 获取特定导管的特定节点(Record)记录
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="tubeID"></param>
        /// <returns></returns>
        public async Task<List<PatientTubeCareMainInfo>> GetCareMainsByInpatientID(string inpatientID, int tubeID, string recordCode)
        {
            return await _dbContext.PatientTubeCareMainInfos.Where(m => m.InpatientID == inpatientID && m.TubeID == tubeID && m.RecordsCode == recordCode && m.DeleteFlag != "*").ToListAsync();
        }
    }
}