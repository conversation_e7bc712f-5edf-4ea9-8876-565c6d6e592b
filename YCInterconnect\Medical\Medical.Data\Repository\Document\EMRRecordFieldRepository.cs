﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
namespace Medical.Data.Repository
{
    public class EMRRecordFieldRepository : IEMRRecordFieldRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IEMRSourceRepository _eMRSourceRepository;
        private readonly GetCacheService _getCacheService;

        public EMRRecordFieldRepository(
              MedicalDbContext medicalDb
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , IEMRSourceRepository eMRSourceRepository
            , GetCacheService getCacheService)
        {
            _medicalDbContext = medicalDb;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _eMRSourceRepository = eMRSourceRepository;
            _getCacheService = getCacheService;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<EMRRecordFieldInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            return await _medicalDbContext.EMRRecordFieldInfos.Where(m => m.HospitalID == hospitalID.ToString()  && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<EMRRecordFieldInfo>> GetByClassIDAsync(int fieldClassID)
        {
            var data = (await GetCacheAsync()) as List<EMRRecordFieldInfo>;

            return data.Where(m => m.FileClassID == fieldClassID && m.DeleteFlag != "*").ToList();

        }

        /// <summary>
        /// 获取病历和前端表头公用方法
        /// </summary>
        /// <param name="fieldClassID"></param>
        /// <param name="fileClassSub"></param>
        /// <param name="useDescription"></param>
        /// <param name="emrUseFlag"></param>
        /// <returns></returns>
        public async Task<List<EMRRecordFieldInfo>> GetDataByFileClassSub(int fieldClassID, string fileClassSub, string useDescription, bool? emrUseFlag)
        {
            var data = (await GetCacheAsync()) as List<EMRRecordFieldInfo>;
            var returnData = new List<EMRRecordFieldInfo>();
            data = data.Where(m => m.FileClassID == fieldClassID).ToList();
            if (!string.IsNullOrEmpty(fileClassSub))
            {
                data = data.Where(m => m.FileClassSub == fileClassSub.Trim()).ToList();
            }
            //获取前端表格表头
            if (!string.IsNullOrEmpty(useDescription))
            {
                returnData = (from m in data
                              where m.UseDescription == useDescription
                              orderby m.Sort
                              select new EMRRecordFieldInfo
                              {
                                  FileClassSub = m.FileClassSub,
                                  EMRFieldID = m.EMRFieldID,
                                  FieldLevel = m.FieldLevel,
                                  GroupID = m.GroupID,
                                  Sort = m.Sort,
                                  Prop = m.Prop,
                                  TableColumnWidth = m.TableColumnWidth,
                                  TableMinWidthFlag = m.TableMinWidthFlag,
                                  Width = m.Width,
                                  FixedPosition = m.FixedPosition,
                                  Align = m.Align,
                                  HeaderAlign = m.HeaderAlign,
                                  SlotName = m.SlotName,
                                  ColumnClassName = m.ColumnClassName,
                                  ColumnStyle = m.ColumnStyle,
                                  ColumnMergeFlag = m.ColumnMergeFlag,
                                  PlacementLocation = m.PlacementLocation,
                              }).ToList();
            }
            //获取病历使用表头
            if (emrUseFlag.HasValue)
            {
                returnData = (from m in data
                              where m.EMRUseFlag == emrUseFlag.Value
                              orderby m.Sort
                              select new EMRRecordFieldInfo
                              {
                                  EMRFieldID = m.EMRFieldID,
                                  FileClassID = m.FileClassID,
                                  FieldLevel = m.FieldLevel,
                                  GroupID = m.GroupID,
                                  Width = m.Width,
                                  Sort = m.Sort,
                                  Prop = m.Prop,
                                  SlotName = m.SlotName
                              }).ToList();
            }
            return returnData;
        }
        /// <summary>
        /// 获取表中setting配置
        /// </summary>
        /// <param name="fieldClassID"></param>
        /// <param name="fileClassSub"></param>
        /// <returns></returns>
        public async Task<List<EMRRecordFieldInfo>> GetUseDescriptionIsSettingEMRRecordField(int fieldClassID, string fileClassSub)
        {
            var data = (await GetCacheAsync()) as List<EMRRecordFieldInfo>;
            return data.Where(m => m.FileClassID == fieldClassID && m.FileClassSub == fileClassSub && m.UseDescription == "2||Setting")
                .Select(m => new EMRRecordFieldInfo
                {
                    FileClassSub = m.FileClassSub,
                    Prop = m.Prop,
                    EMRFieldID = m.EMRFieldID
                }
            ).ToList();
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.EMRRecordField.GetKey(_sessionCommonServer);
        }
        /// <summary>
        /// 根据fileClassID获取配置数据
        /// </summary>
        /// <param name="fileClassID"></param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public async Task<List<KeyValueString>> GetKeyValueAsync(int fileClassID, string hospitalID, int language)
        {
            var emrSourceInfos = await _eMRSourceRepository.GetCacheAsync() as List<EMRSourceInfo>;
            var eMRRecordFieldInfo = (await GetCacheAsync()) as List<EMRRecordFieldInfo>;
            return (from a in emrSourceInfos
                    join b in eMRRecordFieldInfo on a.ID equals b.EMRFieldID
                    where b.DeleteFlag != "*" && b.FileClassID == fileClassID
                    select new KeyValueString
                    {
                        Key = a.SubCode[1..],
                        Value = a.ShowName,
                        Type = b.SlotName
                    }).ToList();
        }
    }
}
