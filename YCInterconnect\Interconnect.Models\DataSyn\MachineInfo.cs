﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("Machine")]
    public class MachineInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///住院号
        ///</summary>
        public string CaseNumber { get; set; }
        /// <summary>
        ///仪器日期
        ///</summary>
        public DateTime MonitorDate { get; set; }
        /// <summary>
        ///
        ///</summary>
        public TimeSpan? MonitorTime { get; set; }
        /// <summary>
        ///仪器监测项目
        ///</summary>
        public string MonitorItem { get; set; }
        /// <summary>
        ///仪器数据
        ///</summary>
        public string MonitorValue { get; set; }
    }
}