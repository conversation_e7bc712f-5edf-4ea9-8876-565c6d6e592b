﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientDeliriumCareDetailRepository : IPatientDeliriumCareDetailRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientDeliriumCareDetailRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<List<AssessContentValue>> GetAssessValueByCareMainID(string mainID)
        {
            return await _medicalDbContext.PatientDeliriumCareDetailInfos
                .Where(m => m.PatientDeliriumCareMainID == mainID && m.DeleteFlag != "*")
                .Select(m => new AssessContentValue
                {
                    AssessListID = m.AssessListID,
                    AssessValue = m.AssessValue
                })
                .ToListAsync();
        }

        public async Task<List<PatientDeliriumCareDetailInfo>> GetByCareMainID(string mainID)
        {
            return await _medicalDbContext.PatientDeliriumCareDetailInfos
                .Where(m => m.PatientDeliriumCareMainID == mainID && m.DeleteFlag != "*").ToListAsync();
        }

    }
}
