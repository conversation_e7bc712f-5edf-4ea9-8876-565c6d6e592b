﻿ 
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("TPRschedule")]
    public class TPRscheduleInfo
    {
        [Key]
        public int TPRScheduleID { get; set; }
        /// <summary>
        /// 住院号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 唯一号
        /// </summary>
        public string  ChartNo { get; set; }        
        /// <summary>
        /// 体征数据
        /// </summary>
        public string EMRField { get; set; }
        /// <summary>
        /// 排程日期
        /// </summary>
        public DateTime ScheduleDate { get; set; }
        /// <summary>
        /// 排程时间
        /// </summary>
        public TimeSpan ScheduleTime { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyDate { get; set; }
    }
}
