﻿using Interconnect.Models;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Interconnect.Data.Interface
{
    public interface IInterconnectPatientScoreMainRepository
    {
        /// <summary>
        /// 获取没有同步的数据
        /// </summary>
        /// <returns></returns>
        Task<List<InterconnectPatientScoreMainInfo>> GetPatientScoreMainNotSync();
    }
}
