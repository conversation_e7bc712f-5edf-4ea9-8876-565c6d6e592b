﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("Dialysis")]
    public class DialysisInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///病案号
        ///</summary>
        public string ChartNo { get; set; }
        /// <summary>
        ///透析日期
        ///</summary>
        public DateTime DialysisDate { get; set; }
        /// <summary>
        ///透析时间
        ///</summary>
        public TimeSpan? DialysisTime { get; set; }
        /// <summary>
        ///脱水量
        ///</summary>
        public short UltrafiltrationVolume { get; set; }
    }
}