﻿/*
 * 添加获取指定日期的危急值列表接口、依据CaseNumber获取未确认危急值接口，苏军志 2022-05-12
 */
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientTestResultInfoRepository : IPatientTestResultInfoRepository
    {
        private MedicalDbContext _dbContext = null;

        public PatientTestResultInfoRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }

        public async Task<List<PatientTestResultInfo>> GetAsync()
        {
            return await _dbContext.PatientTestResultInfos.ToListAsync();
        }

        /// <summary>
        /// 根据病历号获取检验结果
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        public async Task<List<PatientTestResultInfo>> GetAsync(string caseNumber)
        {
            return await _dbContext.PatientTestResultInfos.Where(m => m.CaseNumber.Trim() == caseNumber).ToListAsync();
        }

        /// <summary>
        /// 获取时间段内的检验结果
        /// </summary>
        /// <param name="caseNumber">病历号</param>
        /// <param name="startTime">开始时间</param>
        /// <returns></returns>
        public async Task<List<PatientTestResultInfo>> GetAsync(string caseNumber, DateTime startTime)
        {
            return await _dbContext.PatientTestResultInfos.AsNoTracking().Where(m => m.CaseNumber.Trim() == caseNumber
            && m.TestDate > startTime && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取时间段内检验结果(包括Delete为*的，同步检验结果用)
        /// </summary>
        /// <param name="startTime"></param>
        /// <returns></returns>
        public async Task<List<PatientTestResultInfo>> GetByTimeAsync(DateTime startTime)
        {
            return await _dbContext.PatientTestResultInfos.Where(m => m.TestDate > startTime
            //&& m.DeleteFlag != "*"
            ).ToListAsync();
        }

        /// <summary>
        /// 根据病历号获取检验结果
        /// </summary>
        /// <param name="chatNO"></param>
        /// <returns></returns>
        public async Task<List<PatientTestResultInfo>> GetByChatNO(string chatNO)
        {
            return await _dbContext.PatientTestResultInfos.Where(t => t.CaseNumber.Trim() == chatNO && t.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 获取检验结果
        /// </summary>
        /// <param name="caseNumber">病历号</param>
        /// <param name="dateTime">检验时间</param>
        /// <param name="itemCode">项目代码</param>
        /// <returns></returns>
        public async Task<PatientTestResultInfo> Get(string caseNumber, string testNO, string itemCode)
        {
            return await _dbContext.PatientTestResultInfos.Where(t => t.CaseNumber.Trim() == caseNumber
            && t.TestNo == testNO && t.TestCode.Trim() == itemCode
            && t.DeleteFlag != "*").SingleOrDefaultAsync();
        }

        /// <summary>
        /// 增加检验结果
        /// </summary>
        /// <param name="t"></param>
        /// <returns></returns>
        public async Task<bool> AddAsync(PatientTestResultInfo t)
        {
            if (t == null)
            {
                return false;
            }
            _dbContext.PatientTestResultInfos.Add(t);
            return await _dbContext.SaveChangesAsync() > 0;
        }
        /// <summary>
        /// 修改检验结果
        /// </summary>
        /// <param name="t"></param>
        /// <returns></returns>
        public async Task<bool> UpdateAsync(PatientTestResultInfo t)
        {
            _dbContext.PatientTestResultInfos.Update(t);
            return await _dbContext.SaveChangesAsync() > 0;
        }

        public async Task<List<PatientTestResultInfo>> GetAsync(string[] caseNumbers, DateTime startTime)
        {
            var pratientTestResultList = new List<PatientTestResultInfo>();
            for (int i = 0; i < caseNumbers.Length; i++)
            {
                var tempList = await _dbContext.PatientTestResultInfos.Where(t => t.CaseNumber == caseNumbers[i]
              && t.DeleteFlag != "*").ToListAsync();
                pratientTestResultList = pratientTestResultList.Union(tempList).ToList();
            }
            return pratientTestResultList.Where(t => t.TestDate > startTime).ToList();
        }
        /// <summary>
        /// 获取一条检验数据
        /// </summary>
        /// <param name="CaseNumber"></param>
        /// <param name="TestNo"></param>
        /// <param name="TestCode"></param>
        /// <param name="startTime"></param>
        /// <returns></returns>
        public PatientTestResultInfo GetOneTest(string CaseNumber, string TestNo, string TestCode, DateTime TestDate)
        {
            return _dbContext.PatientTestResultInfos.Where
                (t => t.CaseNumber == CaseNumber && t.TestNo == TestNo && t.TestCode == TestCode
                && t.TestDate == TestDate && t.DeleteFlag != "*").FirstOrDefault();
        }

        /// <summary>
        /// 获取一组检验数据
        /// </summary>
        /// <param name="CaseNumber"></param>
        /// <param name="TestNo"></param>
        /// <param name="TestCode"></param>
        /// <param name="startTime"></param>
        /// <returns></returns>
        public async Task<List<PatientTestResultInfo>> GetTestResultByTestNo(string CaseNumber, string TestNo)
        {
            return await _dbContext.PatientTestResultInfos.Where(t => t.CaseNumber == CaseNumber && t.TestNo == TestNo).ToListAsync();
        }

        /// <summary>
        /// 获取一组检验数据
        /// </summary>
        /// <param name="CaseNumber"></param>
        /// <param name="TestNo"></param>
        /// <param name="TestCode"></param>
        /// <param name="startTime"></param>
        /// <returns></returns>
        public async Task<List<PatientTestResultInfo>> GetTestByTestNo(string TestNo)
        {
            return await _dbContext.PatientTestResultInfos.Where(t => t.TestNo == TestNo).ToListAsync();
        }


        public async Task<PatientTestResultInfo> GetLastData()
        {
            return await _dbContext.PatientTestResultInfos.OrderByDescending(m => m.ModifyDate).FirstOrDefaultAsync();
        }

        public async Task<List<PatientTestView>> GetTestResult(string caseNumber, DateTime startDate, DateTime endDate)
        {
            var query = await _dbContext.PatientTestResultInfos.Where(m => m.CaseNumber == caseNumber && m.TestDate >= startDate && m.TestDate <= endDate)
                         .Select(m => new PatientTestView
                         {
                             TestDate = m.TestDate,
                             TestItem = m.TestItem,
                             AssessListID = m.AssessListID,
                             TestValue = m.TestValue,
                             Unit = m.Unit,
                             NormalAbnormal = m.NormalAbnormal,
                             NormalRange = m.NormalRange,
                         }).ToListAsync();

            query = query.GroupBy(m => m.AssessListID)
                       .Select(m => m.First()).OrderByDescending(m => m.TestDate)
                       .ToList();

            return query;
        }

        public async Task<List<PatientTestResultInfo>> GetPatientTestAsync(string caseNumber)
        {
            return await _dbContext.PatientTestResultInfos.Where(m => m.CaseNumber == caseNumber && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<string>> GetCriticalValuesByDateTime(DateTime start, DateTime end, string caseNumber)
        {
            return await _dbContext.PatientTestResultInfos.Where(m => m.CaseNumber == caseNumber && m.DeleteFlag != "*"
            && m.NormalAbnormal == "CriticalValue" && m.TestDate > start && m.TestDate < end)
                .OrderBy(m => m.TestDate).Select(m => m.Description).ToListAsync();
        }
        public async Task<List<PatientTestResultInfo>> GetCriticalValuesByDate(DateTime date)
        {
            return await _dbContext.PatientTestResultInfos.Where(m => m.NormalAbnormal == "CriticalValue" && m.TestDate.Date == date.Date
            && m.DeleteFlag != "*").OrderBy(m => m.TestDate).ToListAsync();
        }
        public async Task<List<PatientTestResultInfo>> GetUnconfirmedCriticalByCaseNumber(string caseNumber)
        {
            return await _dbContext.PatientTestResultInfos.Where(m => m.CaseNumber == caseNumber
            && !string.IsNullOrEmpty(m.TestGroupCode) && m.TestGroupCode == "1" && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据检验码获取病人一段时间内的检验结果
        /// </summary>
        /// <param name="caseNumber">住院号</param>
        /// <param name="assessListID">检验对应的AssessListID</param>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <returns></returns>
        public async Task<List<PatientTestView>> GetTestResultViewByAssessListAndDate(string caseNumber, int assessListID, DateTime startDateTime, DateTime endDateTime)
        {
            return await _dbContext.PatientTestResultInfos.Where(m => m.CaseNumber == caseNumber && m.AssessListID == assessListID &&
            m.TestDate >= startDateTime && m.TestDate <= endDateTime && m.DeleteFlag != "*")
                .Select(m => new PatientTestView
                {
                    TestItem = m.TestItem,
                    TestDate = m.TestDate,
                    TestValue = m.TestValue,
                    Unit = m.Unit
                }).ToListAsync();
        }
        /// <summary>
        /// 根据检验码获取病人的检验结果
        /// </summary>
        /// <param name="caseNumber">住院号</param>
        /// <param name="testCodes">检验码集合</param>
        /// <returns></returns>
        public async Task<List<string>> GetTestResultByCodes(string caseNumber, List<string> testCodes)
        {
            return await _dbContext.PatientTestResultInfos.Where(m => m.CaseNumber == caseNumber && testCodes.Contains(m.TestCode)
            && m.DeleteFlag != "*").Select(m => m.TestValue).ToListAsync();
        }

        public async Task<List<PatientTestResultInfo>> GetTestResultByAssessListIDs(string caseNumber, List<int> assessListID, DateTime date)
        {
            var datas = await _dbContext.PatientTestResultInfos.Where(m => m.CaseNumber == caseNumber && assessListID.Contains(m.AssessListID)
            && m.TestDate < date && m.DeleteFlag != "*").ToListAsync();
            if (datas.Count == 0)
            {
                return null;
            }
            return datas.GroupBy(s => s.AssessListID).Select(g => g.OrderByDescending(x => x.TestDate).FirstOrDefault()).ToList();
        }
    }
}
