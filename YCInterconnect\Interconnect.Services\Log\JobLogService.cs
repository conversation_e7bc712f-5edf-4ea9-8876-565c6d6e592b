﻿using System;
using Interconnect.Models;
using Interconnect.ViewModels;
using Interconnect.Services.Interface;
using NLog;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Linq;
using Medical.Common;
using Medical.Data.Interface;

namespace Interconnect.Services
{
    public class JobLogService : IJobLogService
    {
        public static List<SyncJob> SyncJobList = new List<SyncJob>();
        private object lockobj = new object();
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IOptions<SystemConfig> _config;
        private readonly IErrorLogService _errorLogService;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly ILogInfoServices _logInfoServices;

        public JobLogService(
              IOptions<SystemConfig> options
            , IErrorLogService errorLogService
            , ILogInfoServices logInfoServices
            , IAppConfigSettingRepository appConfigSettingRepository
            )
        {
            _config = options;
            _errorLogService = errorLogService;
            _logInfoServices = logInfoServices;
            _appConfigSettingRepository = appConfigSettingRepository;
        }

        //判断作业状态
        public bool GetJobStatus(string jobId, string jobName, string subJobId)
        {
            if (string.IsNullOrEmpty(jobId))
            {
                _logger.Error("判断作业状态,jobId为空，返回");
                return false;
            }
            //从配置档中获取数据
            int jobRunTime = 0;
            _logger.Info("获取作业最长执行时间【JobRunTime】");
            var resultRunTime = _appConfigSettingRepository.GetConfigSettingValue("Configs", "JobRunTime").Result;
            if (StringCheck.IsNumeric(resultRunTime))
            {
                jobRunTime = int.Parse(resultRunTime);
            }
            _logger.Info("判断作业是否正在执行," + "JobId:" + jobId + "||JobName:" + jobName + "||SubJobID:[" + subJobId + "]");
            var syncJobListTemp = SyncJobList.Where(m => m == null).ToList();

            foreach (var item in syncJobListTemp)
            {
                _logger.Error("发现为空的数据，移除");
                SyncJobList.Remove(item);
            }

            var syncJob = new SyncJob();

            syncJob = SyncJobList.Where(m => m.JobId == jobId.Trim() && m.JobName == jobName.Trim() && m.SubJobID == subJobId.Trim()).FirstOrDefault();

            if (syncJob == null)
            {
                var t = new SyncJob()
                {
                    JobId = jobId,
                    JobName = jobName,
                    SubJobID = subJobId,
                    StartDateTime = DateTime.Now,
                    JobStatus = 1,
                };

                SyncJobList.Add(t);

                return true;
            }
            if (syncJob.JobStatus == 0)
            {
                syncJob.JobStatus = 1;
                syncJob.StartDateTime = DateTime.Now;
                return true;
            }
            if (jobRunTime <= 0)
            {
                jobRunTime = 10;
            }

            //作业启动时间过长，清除正在执行的作业，重新开始一个作业
            _logger.Info("判断作业是否超时，超时时间(分钟)" + jobRunTime.ToString());
            if (syncJob.StartDateTime.AddMinutes(jobRunTime) < DateTime.Now)
            {
                _logger.Error("作业超时，重启启动作业！,超时时间" + jobRunTime.ToString() + "||" + ListToJson.ToJson(syncJob));
                syncJob.StartDateTime = DateTime.Now;
                return true;
            }
            _logger.Info("作业正在执行！" + ListToJson.ToJson(syncJob));
            return false;

        }
        //移除作业
        public void RemoveJob(string jobId, string jobName, string subJobID)
        {
            if (SyncJobList == null)
            {
                return;
            }
            var syncJobList = SyncJobList.Where(m => m.JobId == jobId && m.JobName == jobName && m.SubJobID == subJobID).ToList();
            _logger.Info("移除作业" + ListToJson.ToJson(syncJobList));
            if (syncJobList.Count == 0)
            {
                return;
            }

            foreach (var item in syncJobList)
            {
                item.JobStatus = 0;
            }
        }

        public List<SyncJob> GetJobByID(string jobId, string jobName, string subJobId)
        {
            if (jobId == "all")
            {
                return SyncJobList;
            }
            var syncJobList = SyncJobList.Where(m => m.JobId == jobId.Trim() && m.JobName == jobName.Trim() && m.SubJobID == subJobId.Trim()).ToList();
            return syncJobList;
        }
    }
}