﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class TestItemToAssessListRepository : ITestItemToAssessListRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public TestItemToAssessListRepository(MedicalDbContext medicalDbContext, IMemoryCache memoryCache, SessionCommonServer sessionCommonServer, GetCacheService getCacheService)
        {
            _medicalDbContext = medicalDbContext;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        /// <summary>
        /// 获取
        /// </summary>
        /// <returns></returns>
        public async Task<List<TestItemToAssessListInfo>> GetAsync()
        {
            string key = GetCacheType();

            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);

            return await _medicalDbContext.TestItemToAssessListInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<TestItemToAssessListInfo>> GetByHospitalAsync()
        {
            string key = GetCacheType();

            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);

            return await _medicalDbContext.TestItemToAssessListInfos.Where(m => m.HospitalID == hospitalID
            && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 获取字典，同步使用
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public List<TestItemToAssessListInfo> GetByHospital()
        {
            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(GetCacheType());

            return _medicalDbContext.TestItemToAssessListInfos.Where(m => m.HospitalID == hospitalID
                && m.DeleteFlag != "*").ToList();
        }

        /// <summary>
        /// 根据检验项目获取对应
        /// </summary>
        /// <param name="testItemCode"></param>
        /// <returns></returns>
        public async Task<List<TestItemToAssessListInfo>> GetAsync(string testItemCode)
        {
            string key = GetCacheType();

            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);

            return await _medicalDbContext.TestItemToAssessListInfos.Where(m => m.HospitalID == hospitalID && m.TestItemCode == testItemCode
            && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据检验项目获取对应
        /// </summary>
        /// <param name="testItemCode"></param>
        /// <returns></returns>
        public async Task<List<TestItemToAssessListInfo>> GetAsyncByCache(string testItemCode)
        {
            var data = await GetCacheAsync() as List<TestItemToAssessListInfo>;

            return data.Where(m => m.TestItemCode == testItemCode).ToList();
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<TestItemToAssessListInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            return await _medicalDbContext.TestItemToAssessListInfos.Where(m => m.HospitalID == hospitalID.ToString()&& m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.TestItemToAssessList.GetKey(_sessionCommonServer);
        }

        public async Task<bool> AddTestItem(TestItemToAssessListInfo testItem)
        {
            await _medicalDbContext.TestItemToAssessListInfos.AddAsync(testItem);
            return await _medicalDbContext.SaveChangesAsync() > 0;
        }
    }
}