﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class NursingRecordRepository : INursingRecordRepository
    {
        private MedicalDbContext _medicalDbContext;
        private IDepartmentListRepository _departmentListRepository;
        private IUserRepository _userRepository;
        private IStationListRepository _stationListRepository;

        public NursingRecordRepository(MedicalDbContext db
            , IDepartmentListRepository departmentListRepository
            , IUserRepository userRepository
            , IStationListRepository stationListRepository
            )
        {
            _departmentListRepository = departmentListRepository;
            _userRepository = userRepository;
            _stationListRepository = stationListRepository;
            _medicalDbContext = db;
        }

        public async Task<List<NursingRecordInfoView>> GetNursingRecordInfosByCaseNumberAsync(string caseNumber, DateTime nursingRecordDate, string hospitalID)
        {
            if (string.IsNullOrEmpty(caseNumber))
            {
                return null;
            }
            var stationList = await _stationListRepository.GetAllAsync();
            var departmentList = await _departmentListRepository.GetDepartmentListAsync();
            var query = await (from a in _medicalDbContext.NursingRecordInfos
                               where a.CaseNumber == caseNumber && a.DeleteFlag != "*" && a.PerformDate == nursingRecordDate
                               select new NursingRecordInfoView
                               {
                                   NursingRecordID = a.NursingRecordID,
                                   InpatientID = a.InpatientID,
                                   PatientID = a.PatientID,
                                   CaseNumber = a.CaseNumber,
                                   ChartNo = a.ChartNo,
                                   DepartmentListID = a.DepartmentListID,
                                   StationID = a.StationID,
                                   BedID = a.BedID,
                                   BedNumber = a.BedNumber,
                                   PerformDate = a.PerformDate,
                                   PerformTime = a.PerformTime,
                                   BodyTemperature = a.BodyTemperature,
                                   Pulse = a.Pulse,
                                   Respire = a.Respire,
                                   SystolicPressure = a.SystolicPressure,
                                   DiastolicPressure = a.DiastolicPressure,
                                   PaintPoint = a.PaintPoint,
                                   HeartRate = a.HeartRate,
                                   SPO2 = a.SPO2,
                                   PerformIntervention = a.PerformIntervention,
                                   PerformEmployeeID = a.PerformEmployeeID,
                                   AddEmployeeID = a.AddEmployeeID,
                                   AddDate = a.AddDate,
                                   SupplyFlag = a.SupplyFlag,
                                   EmrFieldList = a.EmrFieldList,
                                   DataSources = a.DataSources,
                                   StationName = "",
                                   DepartmentListName = "",
                                   EmployeeName = "",
                                   BringToNursingRecords = a.BringToNursingRecords,
                                   InformPhysician = a.InformPhysician,
                                   OriginalEmrFieldList = a.OriginalEmrFieldList,
                                   InterventionID = a.InterventionID
                               }).ToListAsync();
            foreach (var item in query)
            {
                var station = stationList.Find(m => m.ID == item.StationID);
                var department = departmentList.Find(m => m.ID == item.DepartmentListID);
                var userName = await _userRepository.GetUserNameByEmployeeID(item.PerformEmployeeID);
                if (station != null)
                {
                    item.StationName = station.StationName;
                }
                if (department != null)
                {
                    item.DepartmentListName = department.Department;
                }
                item.EmployeeName = userName;
            }
            query = query.OrderByDescending(m => m.PerformDate).ThenByDescending(m => m.PerformTime).ToList();
            return query;
        }

        public async Task<NursingRecordInfoView> GetNursingRecordViewByID(string nursingRecordID, string hospitalID)
        {
            if (string.IsNullOrEmpty(nursingRecordID))
            {
                return null;
            }
            var stationList = await _stationListRepository.GetAllAsync();
            var departmentList = await _departmentListRepository.GetDepartmentListAsync();
            var query = await (from a in _medicalDbContext.NursingRecordInfos
                               where a.NursingRecordID == nursingRecordID && a.DeleteFlag != "*"
                               select new NursingRecordInfoView
                               {
                                   NursingRecordID = a.NursingRecordID,
                                   InpatientID = a.InpatientID,
                                   PatientID = a.PatientID,
                                   CaseNumber = a.CaseNumber,
                                   ChartNo = a.ChartNo,
                                   DepartmentListID = a.DepartmentListID,
                                   StationID = a.StationID,
                                   BedID = a.BedID,
                                   BedNumber = a.BedNumber,
                                   PerformDate = a.PerformDate,
                                   PerformTime = a.PerformTime,
                                   BodyTemperature = a.BodyTemperature,
                                   Pulse = a.Pulse,
                                   Respire = a.Respire,
                                   SystolicPressure = a.SystolicPressure,
                                   DiastolicPressure = a.DiastolicPressure,
                                   PaintPoint = a.PaintPoint,
                                   HeartRate = a.HeartRate,
                                   SPO2 = a.SPO2,
                                   PerformIntervention = a.PerformIntervention,
                                   OriginalBodyTemperature = a.OriginalBodyTemperature,
                                   OriginalEmrFieldList = a.OriginalEmrFieldList,
                                   PerformEmployeeID = a.PerformEmployeeID.Trim(),
                                   AddEmployeeID = a.AddEmployeeID.Trim(),
                                   AddDate = a.AddDate,
                                   SupplyFlag = a.SupplyFlag,
                                   EmrFieldList = a.EmrFieldList,
                                   DataSources = a.DataSources,
                                   StationName = "",
                                   DepartmentListName = "",
                                   EmployeeName = ""
                               }).FirstOrDefaultAsync();
            if (query == null)
            {
                return null;
            }
            var station = stationList.Find(m => m.ID == query.StationID);
            var department = departmentList.Find(m => m.ID == query.DepartmentListID);
            var userName = await _userRepository.GetUserNameByEmployeeID(query.PerformEmployeeID);
            if (station != null)
            {
                query.StationName = station.StationName;
            }
            if (department != null)
            {
                query.DepartmentListName = department.Department;
            }
            query.EmployeeName = userName;
            return query;
        }

        public async Task<NursingRecordInfo> GetNursingRecordInfoByIdAsync(string nursingRecordId)
        {
            if (string.IsNullOrEmpty(nursingRecordId))
            {
                return null;
            }
            return await _medicalDbContext.NursingRecordInfos.Where(m => m.NursingRecordID == nursingRecordId && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<NursingRecordSealView> GetSealViewByID(string nursingRecordId)
        {
            return await _medicalDbContext.NursingRecordInfos.Where(m => m.NursingRecordID == nursingRecordId && m.DeleteFlag != "*")
                .Select(m => new NursingRecordSealView
                {
                    NursingRecordID = m.NursingRecordID,
                    InpatientID = m.InpatientID,
                    CaseNumber = m.CaseNumber,
                    PerformDate = m.PerformDate,
                    PerformTime = m.PerformTime,
                    EmrFieldList = m.EmrFieldList,
                    PerformIntervention = m.PerformIntervention,
                }).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据id集查询相应的护理记录
        /// </summary>
        /// <param name="ids">护理记录的id集合</param>
        /// <param name="dataSources"></param>
        /// <returns>没有查询到数据或者参数不正确：返回空集合</returns>
        public async Task<List<NursingRecordInfo>> GetNursingRecordInfosByIdsAsync(List<string> ids, string dataSources)
        {
            if (ids.Count <= 0)
            {
                return new List<NursingRecordInfo>();
            }
            return await _medicalDbContext.NursingRecordInfos.Where(m => ids.Contains(m.NursingRecordID) && m.DeleteFlag != "*" && m.DataSources == dataSources).ToListAsync();
        }

        //获得一段执行时间内病人的护理记录
        public async Task<List<NursingRecordInfo>> GetByPerformDate(string inpatientID, DateTime startDate, DateTime endDate, string bringToNR = null)
        {
            DateTime tempStartDate = startDate.Date;
            DateTime tempEndDate = endDate.Date;
            var nursingRecordList = new List<NursingRecordInfo>();
            if (string.IsNullOrEmpty(bringToNR))
            {
                nursingRecordList = await _medicalDbContext.NursingRecordInfos.Where(m => m.InpatientID == inpatientID
                && m.PerformDate >= tempStartDate
                && m.PerformDate <= tempEndDate
                && m.DeleteFlag != "*").AsNoTracking().ToListAsync();
            }
            else
            {
                nursingRecordList = await _medicalDbContext.NursingRecordInfos.Where(m => m.InpatientID == inpatientID && m.BringToNursingRecords == bringToNR
               && m.PerformDate >= tempStartDate
               && m.PerformDate <= tempEndDate
               && m.DeleteFlag != "*").AsNoTracking().ToListAsync();
            }

            nursingRecordList = nursingRecordList.Where(m => m.PerformDate.Date.Add(m.PerformTime) >= startDate && m.PerformDate.Date.Add(m.PerformTime) <= endDate).ToList();
            nursingRecordList = nursingRecordList.Where(m => (m.PerformIntervention ?? "") != "" || (m.EmrFieldList ?? "") != "").OrderBy(m => m.PerformDate).ThenBy(m => m.PerformTime).ToList();
            return nursingRecordList;
        }

        public async Task<List<NursingRecordInfo>> GetByPerformDateAndStation(string inpatientID, DateTime startDate, DateTime endDate, int stationID)
        {
            DateTime tempStartDate = startDate.Date;
            DateTime tempEndDate = endDate.Date;
            var nursingRecordList = await _medicalDbContext.NursingRecordInfos.Where(m => m.InpatientID == inpatientID
                && m.StationID == stationID
                && m.BringToNursingRecords == "1"
                && m.PerformDate >= tempStartDate
                && m.PerformDate <= tempEndDate
                && m.DeleteFlag != "*").AsNoTracking().ToListAsync();

            nursingRecordList = nursingRecordList.Where(m => m.PerformDate.Date.Add(m.PerformTime) >= startDate && m.PerformDate.Date.Add(m.PerformTime) <= endDate).ToList();
            nursingRecordList = nursingRecordList.Where(m => (m.PerformIntervention ?? "") != "" || (m.EmrFieldList ?? "") != "").OrderBy(m => m.PerformDate).ThenBy(m => m.PerformTime).ToList();
            return nursingRecordList;
        }

        public async Task<List<NursingRecordInfo>> GetByInpatientId(string inpatientId)
        {
            return await _medicalDbContext.NursingRecordInfos.Where(m => m.InpatientID == inpatientId
            && m.DeleteFlag != "*"
            ).ToListAsync();
        }

        public async Task<List<NursingRecordInfo>> GetSimpleListByPerformDate(string inpatientID, DateTime startDate, DateTime endDate)
        {
            var nursingRecordList = await (from nursingRecord in _medicalDbContext.NursingRecordInfos
                                           where nursingRecord.InpatientID == inpatientID && nursingRecord.DeleteFlag != "*"
                                           select nursingRecord).ToListAsync();

            return nursingRecordList.Where(m => m.PerformDate.Date.Add(m.PerformTime) >= startDate && m.PerformDate.Date.Add(m.PerformTime) <= endDate
          ).ToList();
        }
        public async Task<List<CDA_NursingRecoreView>> GetDataByModifyDateNew(DateTime dateTimes, string hospitalID, int language)
        {
            var result = (from nursingRecord in _medicalDbContext.NursingRecordInfos
                          join inpatient in _medicalDbContext.InpatientDatas
                          on nursingRecord.InpatientID equals inpatient.ID
                          join patientBasic in _medicalDbContext.PatientBasicDatas on new { inpatient.PatientID } equals new { patientBasic.PatientID }
                          join station in _medicalDbContext.StationList on new { ID = nursingRecord.StationID, hospitalID } equals new { station.ID, hospitalID = station.HospitalID }
                          join departmnt in _medicalDbContext.DepartmentListInfos on nursingRecord.DepartmentListID equals departmnt.ID
                          join employee in _medicalDbContext.Users on nursingRecord.PerformEmployeeID equals employee.UserID
                          join interventtionMain in _medicalDbContext.NursingInterventionMain
                          on new { nursingRecord.InterventionID, Language = language } equals new { InterventionID = interventtionMain.ID, interventtionMain.Language }
                          into temp1
                          from mm in temp1.DefaultIfEmpty()
                          where nursingRecord.ModifyDate > dateTimes && nursingRecord.ModifyDate <= dateTimes.AddDays(1)
                          && departmnt.HospitalID == hospitalID && employee.HospitalID == hospitalID
                          && inpatient.HospitalID == hospitalID && patientBasic.HospitalID == hospitalID
                          select new CDA_NursingRecoreView
                          {
                              DCID = nursingRecord.NursingRecordID,
                              InpatientID = nursingRecord.InpatientID,
                              ChartNo = nursingRecord.ChartNo,
                              LocalChartNO = patientBasic.LocalChartNO,
                              CaseNumber = nursingRecord.CaseNumber,
                              DeptCode = departmnt.DepartmentCode,
                              DeptName = departmnt.Department,
                              WardAreaCode = station.StationCode,
                              WardAreaName = station.StationName,
                              SickbedId = nursingRecord.BedNumber,
                              Age = inpatient.Age.ToString(),
                              MonthAge = inpatient.AgeDetail,
                              Sex = patientBasic.Gender,
                              Name = patientBasic.PatientName,
                              IdCard = patientBasic.HospitalID == "5" ? EncryptionAndDecryption.DecryptStr(patientBasic.IdentityID) : patientBasic.IdentityID,
                              AdmissionDate = inpatient.AdmissionDate,
                              AdmissionTime = inpatient.AdmissionTime,
                              ModifyDate = nursingRecord.ModifyDate.Value,
                              NursingDateTime = nursingRecord.PerformDate.Add(nursingRecord.PerformTime),
                              RecordingNurse = nursingRecord.PerformEmployeeID,
                              DiagnoseCode = inpatient.ICDCode,
                              DiagnoseName = inpatient.Diagnosis,
                              AdmissionDateTime = inpatient.AdmissionDate.Add(inpatient.AdmissionTime),
                              NurseSign = employee.Name,
                              SignDateTime = nursingRecord.PerformDate.Add(nursingRecord.PerformTime),
                              NursingLevel = inpatient.NursingLevel,
                              EmrFieldList = nursingRecord.EmrFieldList,
                              InterventionID = nursingRecord.InterventionID,
                              RemarkInfo = nursingRecord.PerformIntervention,
                              Intervention = (mm == null) ? "" : mm.Intervention,
                              DateOfBirth = patientBasic.DateOfBirth,
                              TimeOfBirth = patientBasic.TimeOfBirth,
                              PatientID = inpatient.PatientID,
                              VisitDateTime = inpatient.AdmissionDate.Add(inpatient.AdmissionTime),
                              AuditNurseSign = employee.Name,
                              AuditSignDateTime = nursingRecord.PerformDate.Add(nursingRecord.PerformTime),
                              VisitID = inpatient.CaseNumber,
                              DeleteFlag = nursingRecord.DeleteFlag,
                              DataPumpFlag = "",
                              DataPumpDate = null,
                          });
            return await result.ToListAsync();
        }
        public async Task<List<CDA_NursingRecoreView>> GetDataByModifyDateNewAsNoTrackAsync(DateTime startDate, DateTime endDate, string hospitalID, int language)
        {
            var result = (from nursingRecord in _medicalDbContext.NursingRecordInfos.AsNoTracking()
                          join inpatient in _medicalDbContext.InpatientDatas.AsNoTracking()
                          on nursingRecord.InpatientID equals inpatient.ID
                          join patientBasic in _medicalDbContext.PatientBasicDatas.AsNoTracking() on new { inpatient.PatientID } equals new { patientBasic.PatientID }
                          join station in _medicalDbContext.StationList.AsNoTracking() on new { ID = nursingRecord.StationID, hospitalID } equals new { station.ID, hospitalID = station.HospitalID }
                          join departmnt in _medicalDbContext.DepartmentListInfos.AsNoTracking() on nursingRecord.DepartmentListID equals departmnt.ID
                          join employee in _medicalDbContext.Users.AsNoTracking() on nursingRecord.PerformEmployeeID equals employee.UserID
                          join interventtionMain in _medicalDbContext.NursingInterventionMain.AsNoTracking()
                          on new { nursingRecord.InterventionID, Language = language } equals new { InterventionID = interventtionMain.ID, interventtionMain.Language }
                          into temp1
                          from mm in temp1.DefaultIfEmpty()
                          where nursingRecord.ModifyDate >= startDate && nursingRecord.ModifyDate < endDate
                          && departmnt.HospitalID == hospitalID && employee.HospitalID == hospitalID
                          && inpatient.HospitalID == hospitalID && patientBasic.HospitalID == hospitalID

                          select new CDA_NursingRecoreView
                          {
                              DCID = nursingRecord.NursingRecordID,
                              InpatientID = nursingRecord.InpatientID,
                              ChartNo = nursingRecord.ChartNo,
                              LocalChartNO = patientBasic.LocalChartNO,
                              CaseNumber = nursingRecord.CaseNumber,
                              DeptCode = departmnt.DepartmentCode,
                              DeptName = departmnt.Department,
                              WardAreaCode = station.StationCode,
                              WardAreaName = station.StationName,
                              SickbedId = nursingRecord.BedNumber,
                              Age = inpatient.Age.ToString(),
                              MonthAge = inpatient.AgeDetail,
                              Sex = patientBasic.Gender,
                              Name = patientBasic.PatientName,
                              IdCard = patientBasic.HospitalID == "5" ? EncryptionAndDecryption.DecryptStr(patientBasic.IdentityID) : patientBasic.IdentityID,
                              AdmissionDate = inpatient.AdmissionDate,
                              AdmissionTime = inpatient.AdmissionTime,
                              ModifyDate = nursingRecord.ModifyDate.Value,
                              NursingDateTime = nursingRecord.PerformDate.Add(nursingRecord.PerformTime),
                              RecordingNurse = nursingRecord.PerformEmployeeID,
                              DiagnoseCode = inpatient.ICDCode,
                              DiagnoseName = inpatient.Diagnosis,
                              AdmissionDateTime = inpatient.AdmissionDate.Add(inpatient.AdmissionTime),
                              NurseSign = employee.Name,
                              SignDateTime = nursingRecord.PerformDate.Add(nursingRecord.PerformTime),
                              NursingLevel = inpatient.NursingLevel,
                              EmrFieldList = nursingRecord.EmrFieldList,
                              InterventionID = nursingRecord.InterventionID,
                              RemarkInfo = nursingRecord.PerformIntervention,
                              Intervention = (mm == null) ? "" : mm.Intervention,
                              DateOfBirth = patientBasic.DateOfBirth,
                              TimeOfBirth = patientBasic.TimeOfBirth,
                              PatientID = inpatient.PatientID,
                              VisitDateTime = inpatient.AdmissionDate.Add(inpatient.AdmissionTime),
                              AuditNurseSign = employee.Name,
                              AuditSignDateTime = nursingRecord.PerformDate.Add(nursingRecord.PerformTime),
                              VisitID = inpatient.NumberOfAdmissions.ToString(),
                              DeleteFlag = nursingRecord.DeleteFlag,
                              DataPumpFlag = "",
                              DataPumpDate = null
                          });
            return await result.ToListAsync();
        }
        public async Task<List<CDA_NursingRecoreView>> GetCriticalDataByModifyDateNew(DateTime dateTimes, string hospitalID, int language)
        {
            var result = (from nursingRecord in _medicalDbContext.NursingRecordInfos
                          join inpatient in _medicalDbContext.InpatientDatas
                          on nursingRecord.InpatientID equals inpatient.ID
                          join patientBasic in _medicalDbContext.PatientBasicDatas on new { inpatient.PatientID } equals new { patientBasic.PatientID }
                          join station in _medicalDbContext.StationList on new { ID = nursingRecord.StationID, hospitalID } equals new { station.ID, hospitalID = station.HospitalID }
                          join departmnt in _medicalDbContext.DepartmentListInfos on nursingRecord.DepartmentListID equals departmnt.ID
                          join employee in _medicalDbContext.Users on nursingRecord.PerformEmployeeID equals employee.UserID
                          join interventtionMain in _medicalDbContext.NursingInterventionMain
                          on new { nursingRecord.InterventionID, Language = language } equals new { InterventionID = interventtionMain.ID, interventtionMain.Language }
                          into temp1
                          from mm in temp1.DefaultIfEmpty()
                          where nursingRecord.NursingLevel == "0" && nursingRecord.ModifyDate >= dateTimes && nursingRecord.ModifyDate <= dateTimes.AddDays(1)
                          && departmnt.HospitalID == hospitalID && employee.HospitalID == hospitalID
                          && inpatient.HospitalID == hospitalID && patientBasic.HospitalID == hospitalID
                          select new CDA_NursingRecoreView
                          {
                              DCID = nursingRecord.NursingRecordID,
                              InpatientID = nursingRecord.InpatientID,
                              ChartNo = nursingRecord.ChartNo,
                              CaseNumber = nursingRecord.CaseNumber,
                              DeptCode = departmnt.DepartmentCode,
                              DeptName = departmnt.Department,
                              WardAreaCode = station.StationCode,
                              WardAreaName = station.StationName,
                              SickbedId = nursingRecord.BedNumber,
                              Age = inpatient.Age.ToString(),
                              MonthAge = inpatient.AgeDetail,
                              Sex = patientBasic.Gender,
                              Name = patientBasic.PatientName,
                              AdmissionDate = inpatient.AdmissionDate,
                              AdmissionTime = inpatient.AdmissionTime,
                              ModifyDate = nursingRecord.ModifyDate.Value,
                              NursingDateTime = nursingRecord.PerformDate.Add(nursingRecord.PerformTime),
                              RecordingNurse = nursingRecord.PerformEmployeeID,
                              DiagnoseCode = inpatient.ICDCode,
                              DiagnoseName = inpatient.Diagnosis,
                              AdmissionDateTime = inpatient.AdmissionDate.Add(inpatient.AdmissionTime),
                              NurseSign = employee.Name,
                              SignDateTime = nursingRecord.PerformDate.Add(nursingRecord.PerformTime),
                              NursingLevel = inpatient.NursingLevel,
                              EmrFieldList = nursingRecord.EmrFieldList,
                              InterventionID = nursingRecord.InterventionID,
                              RemarkInfo = nursingRecord.PerformIntervention,
                              Intervention = (mm == null) ? "" : mm.Intervention,
                              DateOfBirth = patientBasic.DateOfBirth,
                              TimeOfBirth = patientBasic.TimeOfBirth,
                              PatientID = inpatient.PatientID,
                              VisitDateTime = inpatient.AdmissionDate.Add(inpatient.AdmissionTime),
                              AuditNurseSign = employee.Name,
                              AuditSignDateTime = nursingRecord.PerformDate.Add(nursingRecord.PerformTime),
                              VisitID = inpatient.NumberOfAdmissions.ToString(),
                              DeleteFlag = nursingRecord.DeleteFlag,
                              DataPumpDate = null,
                              DataPumpFlag = "",
                          });
            return await result.ToListAsync();
        }
        public async Task<List<CDA_NursingRecoreView>> GetCriticalDataByModifyDateNew(DateTime startDate, DateTime endDate, string hospitalID, int language)
        {
            var result = (from nursingRecord in _medicalDbContext.NursingRecordInfos
                          join inpatient in _medicalDbContext.InpatientDatas
                          on nursingRecord.InpatientID equals inpatient.ID
                          join patientBasic in _medicalDbContext.PatientBasicDatas on new { inpatient.PatientID } equals new { patientBasic.PatientID }
                          join station in _medicalDbContext.StationList on new { ID = nursingRecord.StationID, hospitalID } equals new { station.ID, hospitalID = station.HospitalID }
                          join departmnt in _medicalDbContext.DepartmentListInfos on nursingRecord.DepartmentListID equals departmnt.ID
                          join employee in _medicalDbContext.Users on nursingRecord.PerformEmployeeID equals employee.UserID
                          join interventtionMain in _medicalDbContext.NursingInterventionMain
                          on new { nursingRecord.InterventionID, Language = language } equals new { InterventionID = interventtionMain.ID, interventtionMain.Language }
                          into temp1
                          from mm in temp1.DefaultIfEmpty()
                          where nursingRecord.NursingLevel == "0" && nursingRecord.ModifyDate >= startDate && nursingRecord.ModifyDate < endDate
                          && departmnt.HospitalID == hospitalID && employee.HospitalID == hospitalID
                          && inpatient.HospitalID == hospitalID && patientBasic.HospitalID == hospitalID
                          select new CDA_NursingRecoreView
                          {
                              DCID = nursingRecord.NursingRecordID,
                              InpatientID = nursingRecord.InpatientID,
                              ChartNo = nursingRecord.ChartNo,
                              CaseNumber = nursingRecord.CaseNumber,
                              DeptCode = departmnt.DepartmentCode,
                              DeptName = departmnt.Department,
                              WardAreaCode = station.StationCode,
                              WardAreaName = station.StationName,
                              SickbedId = nursingRecord.BedNumber,
                              Age = inpatient.Age.ToString(),
                              MonthAge = inpatient.AgeDetail,
                              Sex = patientBasic.Gender,
                              Name = patientBasic.PatientName,
                              AdmissionDate = inpatient.AdmissionDate,
                              AdmissionTime = inpatient.AdmissionTime,
                              ModifyDate = nursingRecord.ModifyDate.Value,
                              NursingDateTime = nursingRecord.PerformDate.Add(nursingRecord.PerformTime),
                              RecordingNurse = nursingRecord.PerformEmployeeID,
                              DiagnoseCode = inpatient.ICDCode,
                              DiagnoseName = inpatient.Diagnosis,
                              AdmissionDateTime = inpatient.AdmissionDate.Add(inpatient.AdmissionTime),
                              NurseSign = employee.Name,
                              SignDateTime = nursingRecord.PerformDate.Add(nursingRecord.PerformTime),
                              NursingLevel = inpatient.NursingLevel,
                              EmrFieldList = nursingRecord.EmrFieldList,
                              InterventionID = nursingRecord.InterventionID,
                              RemarkInfo = nursingRecord.PerformIntervention,
                              Intervention = (mm == null) ? "" : mm.Intervention,
                              DateOfBirth = patientBasic.DateOfBirth,
                              TimeOfBirth = patientBasic.TimeOfBirth,
                              PatientID = inpatient.PatientID,
                              VisitDateTime = inpatient.AdmissionDate.Add(inpatient.AdmissionTime),
                              AuditNurseSign = employee.Name,
                              AuditSignDateTime = nursingRecord.PerformDate.Add(nursingRecord.PerformTime),
                              VisitID = inpatient.NumberOfAdmissions.ToString(),
                              DeleteFlag = nursingRecord.DeleteFlag,
                              DataPumpFlag = "",
                              DataPumpDate = null,
                          });
            return await result.ToListAsync();
        }
        public async Task<List<NursingRecordInfo>> GetCDData()
        {
            return await _medicalDbContext.NursingRecordInfos.Where(m => m.CDFlag == false && m.DeleteFlag != "*")
                           .OrderBy(m => m.ModifyPersonID).ThenBy(m => m.InpatientID).ThenBy(m => m.DepartmentListID).ToListAsync();
        }
        /// <summary>
        /// 根据ID集合获取数据
        /// </summary>
        /// <param name="inpatientIds"></param>
        /// <returns></returns>
        public async Task<List<NursingRecordInfo>> GetByInpatientIds(string[] inpatientIds)
        {
            return await _medicalDbContext.NursingRecordInfos.Where(m => inpatientIds.Contains(m.InpatientID)
            && m.DeleteFlag != "*"
            ).ToListAsync();
        }

        public async Task<List<NursingRecordInfo>> GetNeedDataByPerformDate(string inpatientID, DateTime startDate, DateTime endDate, string bringToNR = null)
        {
            DateTime tempStartDate = startDate.Date;
            DateTime tempEndDate = endDate.Date;
            var nursingRecordList = new List<NursingRecordInfo>();
            if (string.IsNullOrEmpty(bringToNR))
            {
                nursingRecordList = await _medicalDbContext.NursingRecordInfos.Where(m => m.InpatientID == inpatientID
                        && m.PerformDate >= tempStartDate
                        && m.PerformDate <= tempEndDate
                        && m.DeleteFlag != "*").Select(m =>
                        new NursingRecordInfo
                        {
                            PerformDate = m.PerformDate,
                            PerformTime = m.PerformTime,
                            PerformIntervention = m.PerformIntervention,
                            EmrFieldList = m.EmrFieldList
                        }).ToListAsync();
            }
            else
            {
                nursingRecordList = await _medicalDbContext.NursingRecordInfos.Where(m => m.InpatientID == inpatientID && m.BringToNursingRecords == bringToNR
                        && m.PerformDate >= tempStartDate
                        && m.PerformDate <= tempEndDate
                        && m.DeleteFlag != "*").Select(m =>
                        new NursingRecordInfo
                        {
                            PerformDate = m.PerformDate,
                            PerformTime = m.PerformTime,
                            PerformIntervention = m.PerformIntervention,
                            EmrFieldList = m.EmrFieldList
                        }).ToListAsync();
            }

            nursingRecordList = nursingRecordList.Where(m => m.PerformDate.Date.Add(m.PerformTime) >= startDate && m.PerformDate.Date.Add(m.PerformTime) <= endDate).ToList();
            nursingRecordList = nursingRecordList.Where(m => (m.PerformIntervention ?? "") != "" || (m.EmrFieldList ?? "") != "").OrderBy(m => m.PerformDate).ThenBy(m => m.PerformTime).ToList();
            return nursingRecordList;
        }

        /// <summary>
        /// 获取需回写EmrFieldList的内容
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="performDate"></param>
        /// <param name="performTime"></param>
        /// <param name="performEmployeeID"></param>
        /// <param name="interventionID"></param>
        /// <returns></returns>
        public async Task<List<NursingRecordInfo>> GetNeedWriteBack(string inpatientID, DateTime performDate
            , TimeSpan performTime, string performEmployeeID, int interventionID)
        {
            return await _medicalDbContext.NursingRecordInfos.Where(m => m.InpatientID == inpatientID
            && m.PerformDate == performDate && m.PerformTime == performTime && m.PerformEmployeeID == performEmployeeID
            && m.InterventionID == interventionID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<NursingRecordInfo> GetNRByIdAsync(string nrID)
        {
            if (string.IsNullOrEmpty(nrID))
            {
                return null;
            }
            return await _medicalDbContext.NursingRecordInfos.Where(m => m.NursingRecordID == nrID).FirstOrDefaultAsync();
        }

        public async Task<List<NursingRecordInfo>> GetEmrDataByPerformDate(string inpatientID, DateTime startDate, DateTime endDate)
        {
            var nursingRecordList = await _medicalDbContext.NursingRecordInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*"
                    && m.EmrFieldList != "" && m.PerformDate >= startDate.Date && m.PerformDate <= endDate.Date).ToListAsync();

            return nursingRecordList.Where(m => m.PerformDate.Date.Add(m.PerformTime) >= startDate
                && m.PerformDate.Date.Add(m.PerformTime) <= endDate).ToList();
        }
        /// <summary>
        /// 获取护理记录单异动人ID
        /// </summary>
        /// <param name="dcidList"></param>
        /// <returns></returns>
        public async Task<List<Dictionary<string, string>>> GetDataByDcidList(List<string> dcidList)
        {
            return await _medicalDbContext.NursingRecordInfos.Where(m => dcidList.Contains(m.NursingRecordID)).Select(m =>
            new Dictionary<string, string>
            {
                {"id",m.NursingRecordID },
                {"modifyPersonID",m.PerformEmployeeID },
                {"interventionID",m.InterventionID.ToString() },
                {"sourceType",m.DataSources }
            }).ToListAsync();
        }

        public async Task<NursingRecordInfo> GetNursingRecordInfosByOriginalEmrFieldList(string inpatientID, string nursingRecordId)
        {
            return await _medicalDbContext.NursingRecordInfos.Where(m => m.InpatientID == inpatientID && m.OriginalEmrFieldList == nursingRecordId && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<List<NursingRecordSealView>> GetSealUpDataByHandOver(DateTime startDate, DateTime endDate, string employeeId, string bringToNR = null)
        {
            DateTime tempStartDate = startDate.Date;
            DateTime tempEndDate = endDate.Date;
            var data = await _medicalDbContext.NursingRecordInfos.Where(m => m.PerformDate >= tempStartDate && m.PerformDate <= tempEndDate && m.PerformEmployeeID == employeeId
            && m.DeleteFlag != "*" && (bringToNR == null || m.BringToNursingRecords == bringToNR))
                .Select(m => new NursingRecordSealView
                {
                    NursingRecordID = m.NursingRecordID,
                    InpatientID = m.InpatientID,
                    CaseNumber = m.CaseNumber,
                    PerformDate = m.PerformDate,
                    PerformTime = m.PerformTime,
                    EmrFieldList = m.EmrFieldList,
                    PerformIntervention = m.PerformIntervention,
                }).ToListAsync();

            return data.Where(m => m.PerformDate.Date.Add(m.PerformTime) >= startDate && m.PerformDate.Date.Add(m.PerformTime) <= endDate).ToList();
        }
    }
}