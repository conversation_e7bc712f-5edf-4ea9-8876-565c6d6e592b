﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class CDA_DiscrepancyRecord_ItemRepository : ICDA_DiscrepancyRecord_ItemRepository
    {
        private readonly CDADBContext _cDADBContext = null;
        private readonly static Logger _logger = LogManager.GetCurrentClassLogger();

        public CDA_DiscrepancyRecord_ItemRepository(CDADBContext cDADBContext)
        {
            _cDADBContext = cDADBContext;
        }

        public async Task<bool> SaveAsync(List<CDA_DiscrepancyRecord_ItemInfo> translateDetaildatas)
        {
            try
            {
                await _cDADBContext.AddRangeAsync(translateDetaildatas);
                return await _cDADBContext.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {
                _logger.Error("CDA出入量明细数据写入失败,异常数据:" + Common.ListToJson.ToJson(translateDetaildatas) + "异常信息:" + ex);
                return false;
            }
        }

        public async Task<bool> SaveAsync(CDA_DiscrepancyRecord_ItemInfo translateDetaildata, bool saveFlag = true)
        {
            if (translateDetaildata == null)
            {
                return false;
            }
            try
            {
                await _cDADBContext.AddAsync(translateDetaildata);
                if (saveFlag)
                {
                    return await _cDADBContext.SaveChangesAsync() > 0;
                }
            }
            catch (Exception ex)
            {
                _logger.Error("CDA出入量明细数据写入失败,异常数据:" + Common.ListToJson.ToJson(translateDetaildata) + "异常信息:" + ex);
                return false;
            }
            return true;
        }

        public async Task<CDA_DiscrepancyRecord_ItemInfo> GetByIDAsync(string DCID)
        {
            return await _cDADBContext.CDA_DiscrepancyRecord_ItemInfos
                .Where(m => m.PatientIntakeOutputID == DCID).FirstOrDefaultAsync();
        }

        public async Task<bool> UpdateAsync(CDA_DiscrepancyRecord_ItemInfo detailData)
        {
            var old = await _cDADBContext.CDA_DiscrepancyRecord_ItemInfos.Where(m => m.DocID == detailData.DocID).FirstOrDefaultAsync();
            if (old == null)
            {
                return await SaveAsync(detailData, true);
            }
            try
            {
                _cDADBContext.Entry(old).CurrentValues.SetValues(detailData);
                return await _cDADBContext.SaveChangesAsync() >= 0;
            }
            catch (Exception ex)
            {
                _logger.Error("CDA出入量明细数据更新失败,异常数据:" + Common.ListToJson.ToJson(detailData) + "异常信息:" + ex);
                return false;
            }
        }

        public async Task<List<CDA_DiscrepancyRecord_ItemInfo>> GetListByIDAsync(string dCID)
        {
            return await _cDADBContext.CDA_DiscrepancyRecord_ItemInfos.Where(m => m.PatientIntakeOutputID == dCID).ToListAsync();
        }
    }
}