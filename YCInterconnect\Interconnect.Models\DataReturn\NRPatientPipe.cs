﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("NursingRecord_PatientPipe")]
    public class NRPatientPipeInfo : ModifyReturnInfo
    {
         
        [Key]
        [Column("ID")]
        public int ID { get; set; }

        /// <summary>
        /// 就诊序号
        /// </summary>
        public int CureNo { get; set; }
        /// <summary>
        ///	导管分类ID，关联ConvientCureI
        ///</summary>
        public int? PipeTypeID { get; set; }
        /// <summary>
        ///	导管分类名称	
        ///</summary>
        public string PipeType { get; set; }
        /// <summary>
        ///	导管名称	
        ///</summary>
        public string NursingType { get; set; }
        /// <summary>
        ///	导管位置、部位	
        ///</summary>
        public string NursingContent { get; set; }
        /// <summary>
        ///	录入内容的xml	
        ///</summary>
        public string  NursingContentXml { get; set; }
        /// <summary>
        ///	创建人工号	
        ///</summary>
        public string InputerCode { get; set; }
        /// <summary>
        ///	创建人姓名	
        ///</summary>
        public string InputerName { get; set; }
        /// <summary>
        ///	录入时间	
        ///</summary>
        public DateTime? InputTime { get; set; }
        /// <summary>
        ///	0 有效，1 已停止，-1 无效	
        ///</summary>
        public int? Status { get; set; }
        /// <summary>
        ///	停止人工号	
        ///</summary>
        public string StopOper { get; set; }
        /// <summary>
        ///	停止时间	
        ///</summary>
        public DateTime? StopTime { get; set; }
        /// <summary>
        ///	删除人	
        ///</summary>
        public string DelOper { get; set; }
        /// <summary>
        ///	删除时间	
        ///</summary>
        public DateTime? DelTime { get; set; }
    }
}