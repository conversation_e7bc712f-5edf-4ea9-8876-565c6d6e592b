﻿using System;
using System.Collections.Generic;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Interconnect.Data.Interface;
using Medical.Data.Interface;
using Medical.Models;
using NLog;
using System.Linq;
using Medical.Data.Context;
using Interconnect.Data.Context;
using Newtonsoft.Json;
using Medical.Common;
using Medical.ViewModels.Query;
using Arch.EntityFrameworkCore.UnitOfWork;
using Microsoft.Extensions.Options;

namespace Interconnect.Services
{
    public class BedService : IBedService
    {
        //Mdeical
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IBedListRepository _iBedListRepository;
        private readonly IStationListRepository _iStationListRepository;
        private readonly IVirtualBedListRepository _iVirtualBedListRepository;
        //Interconnect
        private readonly IUnitOfWork<DataOutConnection> _unitOfWorkOut;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ILogInfoServices _iLogInfoServices;
        private readonly ICommonHelper _commonHelper;
        private readonly ISettingDescRepository _iCSettingDescriptionRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly IOptions<SystemConfig> _config;

        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";
        /// <summary>
        /// 系统默认操作人
        /// </summary>
        private string MODIFYPERSONID = "TongBu";
        /// <summary>
        /// 构造方法
        /// </summary>
        /// <param name="unitOfWork"></param>
        /// <param name="unitOfWorkOut"></param>
        /// <param name="bedListRepository"></param>
        /// <param name="stationListRepository"></param>
        /// <param name="logInfoServices"></param>
        /// <param name="commonHelper"></param>
        /// <param name="settingDescriptionRepository"></param>
        /// <param name="virtualBedListRepository"></param>
        public BedService(
              IUnitOfWork<MedicalDbContext> unitOfWork
            , IUnitOfWork<DataOutConnection> unitOfWorkOut
            , IBedListRepository bedListRepository
            , IStationListRepository stationListRepository
            , ILogInfoServices logInfoServices
            , ICommonHelper commonHelper
            , ISettingDescRepository settingDescriptionRepository
            , IVirtualBedListRepository virtualBedListRepository
            , IAppConfigSettingRepository appConfigSettingRepository
            , IOptions<SystemConfig> config
            )
        {
            _unitOfWork = unitOfWork;
            _unitOfWorkOut = unitOfWorkOut;
            _iBedListRepository = bedListRepository;
            _iStationListRepository = stationListRepository;
            _iLogInfoServices = logInfoServices;
            _commonHelper = commonHelper;
            _iCSettingDescriptionRepository = settingDescriptionRepository;
            _iVirtualBedListRepository = virtualBedListRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
            _config = config;
        }

        /// <summary>
        /// 获取床位信息
        /// </summary>
        /// <returns></returns>
        public bool SynchronizationMain()
        {
            _logger.Info("开始获取床位信息api");
            string apiStr = "";
            var apiStrList = _iCSettingDescriptionRepository.GetAsync(1, "3");
            if (apiStrList.Count > 0)
            {
                apiStr = apiStrList[0].Description;
            }
            else
            {
                _logger.Error("获取床位信息API失败");
                return false;
            }
            _logger.Info("获取床位信息数据");
            var data = new Dictionary<string, string>();
            //呼叫API获得数据
            var resultData = _commonHelper.GetInterconnectData(apiStr, data);

            //从配置当中获取数据 梁宝华 2020-04-29
            var printInterfaceData = 0;
            var resultPrintDate = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "PrintInterfaceData").Result;
            if (StringCheck.IsNumeric(resultPrintDate))
            {
                printInterfaceData = int.Parse(resultPrintDate);
            }

            if (printInterfaceData == 1)
            {
                _logger.Info("获得数据如下" + resultData);
            }

            var Interconnect_Data = new List<BedInfo>();
            //获取数据
            var result = JsonConvert.DeserializeObject<ResponseResult>(resultData);
            if (result == null || result.Data == null)
            {
                _logger.Error("获取resultData失败:" + resultData);
                return false;
            }
            try
            {
                Interconnect_Data = JsonConvert.DeserializeObject<List<BedInfo>>(result.Data.ToString());
            }
            catch (Exception ex)
            {
                _logger.Error("Json格式化失败:" + ex.ToString());
                return false;
            }
            _logger.Info("获得" + Interconnect_Data.Count() + "条数据");
            if (!SynchronizationDetail(Interconnect_Data))
            {
                return false;
            }

            try
            {
                ////更新床位缓存
                CacheQuery query = new CacheQuery
                {
                    Type = CacheType.Bed
                };
                _commonHelper.UpdateCache(query);
            }
            catch (Exception ex)
            {
                _logger.Info("床位缓存更新失败" + ex.Message);
            }
            return true;
        }

        private bool SynchronizationDetail(List<BedInfo> originalList)
        {
            var mdicalBedList = _iBedListRepository.GetBedListNoCacheSynchronize();
            var medicalStationList = _iStationListRepository.GetAllStation();
            var virtualBedList = _iVirtualBedListRepository.GetAllVirtualBedList();
            int maxID = _iBedListRepository.GetMaxID();
            List<BedListInfo> Insertlist = new List<BedListInfo>();
            List<BedListInfo> Updatelist = new List<BedListInfo>();
            BedListInfo t = null;
            string tablename = "bed";
            List<LogInfo> logList = new List<LogInfo>();
            LogInfo tempLog = null;
            int failcount = 0;
            tempLog = _iLogInfoServices.InnsertLogAsync(tablename, " 开始进行数据同步，数据条数：" + originalList.Count);
            logList.Add(tempLog);

            #region "数据同步"
            foreach (var item in originalList)
            {
                List<BedListInfo> tempMedicalBedInfo, tempInsertlist, tempUpdatelist = null;

                var tempVirtualBedList = virtualBedList.Where(m => m.HisBedNumber == item.BedNumber
                 && m.HISStationCode == item.StationCode).ToList();

                if (tempVirtualBedList.Count > 1)
                {
                    tempLog = _iLogInfoServices.InnsertLogAsync(tablename, "StationCode:[" + item.StationCode + "] BedNumber:[" + item.BedNumber + "]查询虚拟床位信息错误!");
                    logList.Add(tempLog);
                    failcount++;
                    continue;
                }
                int stationID = 0;
                if (tempVirtualBedList.Count == 1)
                {
                    stationID = tempVirtualBedList[0].VirtualStationID;
                    _logger.Info("根据StationCode:[" + item.StationCode + "] && BedNumber:[" + item.BedNumber + "] 在虚拟床位中查找数据查找到[" + tempVirtualBedList.Count + "]条，并且该数据的stationID为:[" + stationID + "]");
                }
                else
                {
                    var tempStation = medicalStationList.Where(m => m.StationCode.Trim() == item.StationCode.Trim()).ToList();
                    if (tempStation.Count != 1)
                    {
                        tempLog = _iLogInfoServices.InnsertLogAsync(tablename, "StationCode:[" + item.StationCode + "] BedNumber:[" + item.BedNumber + "]查询病区对照信息错误!");
                        logList.Add(tempLog);
                        failcount++;
                        continue;
                    }
                    stationID = tempStation[0].ID;
                    _logger.Info("根据StationCode:[" + item.StationCode + "] && BedNumber:[" + item.BedNumber + "] 在虚拟床位中查找数据查找到[" + tempVirtualBedList.Count + "]条，该数据的stationID为真实病区:[" + stationID + "]");
                }
                tempMedicalBedInfo = mdicalBedList.Where(m => m.StationID == stationID && m.BedNumber == item.BedNumber).ToList();
                if (tempMedicalBedInfo.Count > 1)
                {
                    tempLog = _iLogInfoServices.InnsertLogAsync(tablename, "StationCode:[" + item.StationCode + "] BedNumber:[" + item.BedNumber + "]查询床位信息错误!");
                    logList.Add(tempLog);
                    failcount++;
                    continue;
                }
                tempInsertlist = Insertlist.Where(m => m.StationID == stationID && m.BedNumber == item.BedNumber).ToList();
                var stationList = medicalStationList.Where(m => m.ID == stationID).ToList();
                if (tempMedicalBedInfo.Count < 1 && tempInsertlist.Count < 1)
                {
                    t = new BedListInfo
                    {
                        ID = maxID,
                        BedNumber = item.BedNumber,
                        StationID = stationID,
                        DepartmentListID = 0,//没有对照
                        ICUFlag = stationList.Count > 0 ? stationList[0].ICUFlag : "",
                        DisableFlag = "",
                        ModifyPersonID = MODIFYPERSONID,
                        ModifyDate = DateTime.Now,
                        DeleteFlag = "",
                        HospitalID = _config.Value.HospitalID
                    };
                    item.DataPumpFlag = "*";
                    item.DataPumpDate = DateTime.Now;
                    Insertlist.Add(t);
                    maxID++;
                    _logger.Info("StationCode:[" + item.StationCode + "] && BedNumber:[" + item.BedNumber + "]&&stationID:[" + stationID + "]的床位新增完成");
                }
                tempUpdatelist = Updatelist.Where(m => m.StationID == stationID && m.BedNumber == item.BedNumber).ToList();

                if (tempUpdatelist.Count > 0)
                {
                    tempLog = _iLogInfoServices.InnsertLogAsync(tablename, "StationCode:[" + item.StationCode + "] && BedNumber:[" + item.BedNumber + "] 的床位需要进行更新操作,但是在用来存储更新操作的集合中已经存在该条数据");
                    logList.Add(tempLog);
                    failcount++;
                    continue;
                }
                if (tempMedicalBedInfo.Count == 1 && tempUpdatelist.Count < 1)
                {
                    tempMedicalBedInfo[0].StationID = stationID;
                    tempMedicalBedInfo[0].DeleteFlag = item.DeleteFlag;
                    tempMedicalBedInfo[0].ModifyPersonID = MODIFYPERSONID;
                    tempMedicalBedInfo[0].ModifyDate = DateTime.Now;
                    item.DataPumpFlag = "*";
                    item.DataPumpDate = DateTime.Now;
                    Updatelist.Add(tempMedicalBedInfo[0]);
                    mdicalBedList.Remove(tempMedicalBedInfo[0]);
                    _logger.Info("StationCode:[" + item.StationCode + "] && BedNumber:[" + item.BedNumber + "]&&stationID:[" + stationID + "]的床位修改完成");
                }
            }
            #endregion
            #region "床位反比"
            if (originalList.Count >= 1)
            {
                var modifydate = DateTime.Now;
                _logger.Info("剩下" + mdicalBedList.Count() + "条床位信息数据，开始进行遍历");
                foreach (var item in mdicalBedList)
                {
                    var station = medicalStationList.Where(m => m.ID == item.StationID).FirstOrDefault();
                    if (station == null)
                    {
                        continue;
                    }
                    var bed = originalList.Where(m => m.StationCode == station.StationCode && m.BedNumber == item.BedNumber).FirstOrDefault();
                    if (bed == null)
                    {
                        _logger.Info("未在HIS中找到床号为:"+item.BedNumber+"的数据，medical库中需要删除");
                        item.DeleteFlag = "*";
                        item.ModifyDate = modifydate;
                    }
                    else
                    {
                        _logger.Info("在HIS中找到床号为:" + item.BedNumber+";"+bed.BedNumber+";"+bed.StationCode + "的数据，medical库中不需要删除");
                    }
                }
                _logger.Info("剩下" + mdicalBedList.Count() + "条床位信息数据，遍历结束");
            }
            #endregion
            #region "数据更新"
            if (originalList.Count >= 1)
            {
                try
                {
                    _unitOfWork.GetRepository<BedListInfo>().Insert(Insertlist);
                    _unitOfWork.SaveChanges();
                }
                catch (Exception ex)
                {
                    _logger.Error(tablename + "||保存失败||" + ex.ToString());
                    return false;
                }
            }

            tempLog = _iLogInfoServices.InnsertLogAsync(tablename, " 同步结束 成功：" + (originalList.Count - failcount).ToString() + "条！ 失败：" + failcount.ToString() + "条！");
            logList.Add(tempLog);
            int ItemNo = 0;
            string Guid = "";
            Guid = System.Guid.NewGuid().ToString("N");
            foreach (var item in logList)
            {
                item.Guid = Guid;
                item.ItemNo = ItemNo;
                ItemNo++;
            }
            try
            {
                _unitOfWorkOut.GetRepository<LogInfo>().Insert(logList);
                _unitOfWorkOut.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error(tablename + "同步成功，但写同步日志失败||" + ex.ToString());
            }
            _logger.Info(tablename + "  同步完成!");
            return true;
            #endregion
        }
    }
}