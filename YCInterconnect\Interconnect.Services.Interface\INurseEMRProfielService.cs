﻿using System.Threading.Tasks;

namespace Interconnect.Services.Interface
{
  public  interface INurseEMRProfielService
    { /// <summary>
      ///  生成病上传护理电子病历
      /// </summary>
      /// <param name="hospitalId"></param>
      /// <param name="language"></param>
      /// <param name="fileClass">传入0，生成所有的</param>
      /// <returns></returns>
        Task<bool> CreateNurseEMRMain(string hospitalId, int language, string inPatientId, int fileClass);
        /// <summary>
        /// 下载电子病历文件
        /// </summary>
        /// <param name="path"></param>
        /// <param name="fileName"></param>
        /// <param name="objectID"></param>
        /// <returns></returns>
        bool DownLoadNurseEMRPdf(string path, string fileName, string objectID);
        /// <summary>
        /// 下载一下病人的电子病历
        /// </summary>
        /// <param name="path"></param>
        /// <param name="fileName"></param>
        /// <param name="objectID"></param>
        /// <returns></returns>
        Task<bool> DownLoadPatientNurseEMRPdf(string inPatientId);

    }
}
