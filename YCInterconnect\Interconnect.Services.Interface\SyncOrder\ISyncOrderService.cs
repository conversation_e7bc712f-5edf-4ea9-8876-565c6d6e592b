﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Models;

namespace Interconnect.Services.Interface
{
    public interface ISyncOrderService
    {
        /// <summary>
        /// 根据住院号同步病人医嘱
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        Task<bool> SyncHisOrderByCaseNumber(string caseNumber);
        /// <summary>
        /// 根据病区码同步医嘱
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        Task<bool> SyncHisOrderByStationCode(string stationCode);

    }
}