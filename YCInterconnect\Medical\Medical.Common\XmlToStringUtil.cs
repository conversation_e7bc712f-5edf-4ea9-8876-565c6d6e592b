﻿using NLog;
using System;
using System.IO;
using System.Text;
using System.Xml;

namespace Medical.Common
{
    public class XmlToStringUtil
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// xml字符串转字符串
        /// </summary>
        /// <param name="xml">xml字符串</param>
        /// <returns></returns>
        public static string XmlToString(XmlDocument xml)
        {
            var result = "";
            try
            {
                MemoryStream streamXml = new MemoryStream();
                XmlTextWriter writer = new XmlTextWriter(streamXml, Encoding.UTF8)
                {
                    Formatting = System.Xml.Formatting.Indented
                };
                xml.Save(writer);
                StreamReader reader = new StreamReader(streamXml, Encoding.UTF8);
                streamXml.Position = 0;
                result = reader.ReadToEnd();
                reader.Close();
                streamXml.Close();
            }
            catch (Exception ex)
            {
                _logger.Error("XmlToStringTtil.XmlToString转换失败" + ex.ToString());
                return "";
            }
            return result;
        }
    }
}
