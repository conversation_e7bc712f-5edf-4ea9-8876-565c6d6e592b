﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("MailUserDict")]
    public  class MailUserDictInfo
    {
        /// <summary>
        /// 邮件地址
        /// </summary>
        [Key]
        [Column("MailAddress")]
        public string MailAddress { get; set; }

        /// <summary>
        /// 接收者姓名
        /// </summary>
        public string UserName { get; set; }

        public string DeleteFlag { get; set; }
        public DateTime ModifyDate { get; set; }
    }
}
