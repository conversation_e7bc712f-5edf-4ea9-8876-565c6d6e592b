﻿using System;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;

using Microsoft.Extensions.Options;
using System.Collections.Generic;

namespace Interconnect.Data.Repository
{
    public class LogInfoRepository : ILogInfoRepository
    {
        private DataOutConnection _DataOutConnection = null;


        public LogInfoRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }

        /// <summary>
        /// 删除历史同步Log
        /// </summary>
        /// <returns></returns>
        public bool DelAsync(int logSaveDays)
        {
            try
            {
                //删除N天前的数据
                var Logdata = _DataOutConnection.LogInfos.Where(m => m.Datetimes < m.Datetimes.AddDays(-logSaveDays)).ToList();
                _DataOutConnection.LogInfos.RemoveRange(Logdata);
                _DataOutConnection.SaveChanges();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 获取测试数据
        /// </summary>
        /// <returns></returns>
        public List<LogInfo> GetLog(string guid)
        {
            var Logdata = _DataOutConnection.LogInfos.Where(m => m.Guid == guid).ToList();
            return Logdata;
        }
    }
}

