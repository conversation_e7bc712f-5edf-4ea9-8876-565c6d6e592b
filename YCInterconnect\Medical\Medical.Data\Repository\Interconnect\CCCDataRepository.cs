﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data
{
    public class CCCDataRepository : ICCCDataRepository
    {
        private readonly FilesDBContext _filesDBContext = null;
        private readonly static Logger _logger = LogManager.GetCurrentClassLogger();

        public CCCDataRepository(FilesDBContext filesDBContext)
        {
            _filesDBContext = filesDBContext;
        }

        public async Task<List<CCCDataInfo>> GetAsync(string casenumber, string dataType)
        {
            return await _filesDBContext.CCCDataInfos.Where(m => m.CaseNumber == casenumber && m.DataType == dataType && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<CCCDataInfo>> GetUnAsync(string dataType)
        {
            return await _filesDBContext.CCCDataInfos.Where(m => m.DataType == dataType && m.DataPumpFlag != "*" && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<bool> Save(List<CCCDataInfo> datas)
        {
            try
            {
                await _filesDBContext.AddRangeAsync(datas);

                return await _filesDBContext.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {
                _logger.Error("CCCDataInfo写入异常,异常数据:" + ex.Message);
                return false;
            }
        }

        public async Task<bool> Update(List<CCCDataInfo> datas)
        {
            try
            {
                _filesDBContext.UpdateRange(datas);

                return await _filesDBContext.SaveChangesAsync() >= 0;
            }
            catch (Exception ex)
            {

                _logger.Error("CCCDataInfo更新异常,异常数据:" + ex.Message);

                return false;
            }
        }
    }

}
