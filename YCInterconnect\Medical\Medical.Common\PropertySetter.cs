﻿using NLog;
using System;
using System.Collections.Concurrent;
using System.Linq.Expressions;
using System.Reflection;

namespace Medical.Common
{
    public static class PropertySetter<T>
    {
        private readonly static Logger _logger = LogManager.GetCurrentClassLogger();
        /// <summary>
        /// 缓存[对象.属性]的赋值表达式
        /// </summary>
        private static readonly ConcurrentDictionary<string, Action<T, string>> cache = new ConcurrentDictionary<string, Action<T, string>>();

        /// <summary>
        /// 对象属性赋值
        /// </summary>
        /// <param name="instance">对象实例</param>
        /// <param name="propertyInfo">属性类型</param>
        /// <param name="data">值</param>
        public static void Set(T instance, PropertyInfo propertyInfo, string data)
        {
            if (instance == null || propertyInfo == null)
            {
                _logger.Warn($"关键参数不可为 null");
                return;
            }
            var instanceType = typeof(T);
            string cacheKey = $"{instanceType.FullName}.{propertyInfo.Name}";
            try
            {
                if (cache.TryGetValue(cacheKey, out var func))
                {
                    func(instance, data);
                    return;
                }
                var propertySetter = CreateExpression(instanceType, propertyInfo);
                // 缓存该表达式，下次直接调用
                cache.TryAdd(cacheKey, propertySetter);
                // 调用表达式进行赋值
                propertySetter(instance, data);
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message, $"使用ClassOperationEx.SetValue对{cacheKey}进行赋值调用发生异常；类型为{propertyInfo.PropertyType.Name}，值为{data}");
                throw;
            }
        }
        /// <summary>
        /// 创建赋值表达式（形如：(target, data) => target.Property = data;）
        /// </summary>
        /// <param name="instanceType">实例对象类型</param>
        /// <param name="propertyInfo">属性类型</param>
        /// <returns></returns>
        private static Action<T, string> CreateExpression(Type instanceType, PropertyInfo propertyInfo)
        {
            // 参数：对象 (obj)
            var targetExp = Expression.Parameter(instanceType, "target");
            var propertyExp = Expression.Property(targetExp, propertyInfo);
            // 参数：值 (data)
            var valueExp = Expression.Parameter(typeof(string), "data");

            // 需要将字符串转换为属性的类型
            var convertedValueExp = CreateConvertValueExp(propertyInfo.PropertyType, valueExp);
            // 赋值
            var assignmentExp = Expression.Assign(propertyExp, convertedValueExp);

            // 编译表达式树
            var lambda = Expression.Lambda<Action<T, string>>(assignmentExp, targetExp, valueExp);
            var setter = lambda.Compile();

            return setter;
        }

        /// <summary>
        /// 创建类型转换表达式
        /// </summary>
        /// <param name="propertyType">属性类型</param>
        /// <param name="valueExp">参数值表达式</param>
        /// <returns></returns>
        /// <exception cref="NotSupportedException"></exception>
        private static Expression CreateConvertValueExp(Type propertyType, ParameterExpression valueExp)
        {
            // 如果目标属性类型就是字符串，直接返回
            if (propertyType == typeof(string))
            {
                return valueExp;
            }

            // 尝试直接获取目标属性类型的Parse方法
            var methodInfo = (propertyType?.GetMethod("Parse", new[] { typeof(string) })) ?? throw new NotSupportedException($"当前属性类型 {propertyType.Name} 暂不支持转换.");
            return Expression.Call(methodInfo, valueExp);
        }
    }
}
