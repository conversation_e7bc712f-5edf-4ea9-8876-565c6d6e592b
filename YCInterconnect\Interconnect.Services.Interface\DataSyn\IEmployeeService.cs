﻿using System.Threading.Tasks;
namespace Interconnect.Services.Interface
{
   public  interface IEmployeeService
    {
        /// <summary>
        /// 同步人员信息 
        /// </summary>
        /// <returns></returns>
        Task<bool> Synchronization();

        /// <summary>
        /// 同步人员对应的病区信息
        /// </summary>
        /// <param name="employeeID"></param>
        /// <param name="stationCode"></param>
        /// <param name="addUserID"></param>
        /// <returns></returns>
        Task<bool> UpEmployeeDepartmentSwitch(string employeeID, string stationCode, string addUserID);
    }
}