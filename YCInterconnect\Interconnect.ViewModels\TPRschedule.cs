﻿ 
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.ViewModels
{
    public class TPRschedule
    {    
        /// <summary>
        /// 住院号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 病案号
        /// </summary>
        public string  ChartNo { get; set; }        
        /// <summary>
        /// 体征数据
        /// </summary>
        public string EMRField { get; set; }
        /// <summary>
        /// 排程日期
        /// </summary>
        public DateTime ScheduleDate { get; set; }
        /// <summary>
        /// 排程时间
        /// </summary>
        public TimeSpan ScheduleTime { get; set; }
    }
}
