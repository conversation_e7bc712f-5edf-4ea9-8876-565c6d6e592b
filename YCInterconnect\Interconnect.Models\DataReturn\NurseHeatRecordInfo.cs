﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("Nurse_Heat_Record")]
    public class NurseHeatRecordInfo
    {
        /// <summary>
        ///	治疗号	
        ///</summary>        
        public string CureNo { get; set; }
        /// <summary>
        ///	日期	
        ///</summary>
        public DateTime Date { get; set; }
        /// <summary>
        ///	时刻(时刻0:午夜2,1:上午6,2:上
        ///</summary>
        public int Time { get; set; }
        /// <summary>
        ///	体温类型：体温类型：1: 口表2:
        ///</summary>
        public short? TemperatureType { get; set; }
        /// <summary>
        ///	体温温度	
        ///</summary>
        public float? Temperature { get; set; }
        /// <summary>
        ///	脉搏	
        ///</summary>
        public float? Pulse { get; set; }
        /// <summary>
        ///	呼吸	
        ///</summary>
        public float? Breathe { get; set; }
        /// <summary>
        ///	物理降温	
        ///</summary>
        public float? TemperatureReduction { get; set; }
        /// <summary>
        ///	呼吸机	
        ///</summary>
        public float? BreathingMachines { get; set; }
        /// <summary>
        ///	起搏器	
        ///</summary>
        public float? Pacemaker { get; set; }
        /// <summary>
        ///	记录日期	
        ///</summary>
        public DateTime? Recdate { get; set; }
        /// <summary>
        ///	记录人工号	
        ///</summary>
        public string RecCode { get; set; }
        /// <summary>
        ///	疼痛程度评分	
        ///</summary>
        public int? PainLevel { get; set; }
    }
}