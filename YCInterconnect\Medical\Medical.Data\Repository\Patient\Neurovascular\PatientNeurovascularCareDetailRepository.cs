﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientNeurovascularCareDetailRepository : IPatientNeurovascularCareDetailRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientNeurovascularCareDetailRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }
        /// <summary>
        /// 根据维护主记录ID获取明细数据
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        public async Task<List<PatientNeurovascularCareDetailInfo>> GetByMainID(string mainID)
        {
            return await _medicalDbContext.PatientNeurovascularCareDetailInfos.Where(t => t.PatientNeurovascularAssessCareMainID == mainID && t.DeleteFlag != "*").ToListAsync();
        }

    }
}
