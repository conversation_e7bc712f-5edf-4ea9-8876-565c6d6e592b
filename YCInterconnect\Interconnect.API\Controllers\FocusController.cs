﻿using Interconnect.Models;
using Interconnect.Services.Interface;
using Medical.Common;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using NLog;
using System;
using System.Threading.Tasks;

namespace Interconnect.API.Controllers
{
    /// <summary>
    /// 信息同步
    /// </summary>
    [Produces("application/json")]
    [Route("api/Focus")]
    [EnableCors("any")]
    public class FocusController : Controller
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IFocusService _focusService;
        private readonly IJobLogService _jobLogService;
        private readonly IOptions<SystemConfig> _config;

        /// <summary>
        ///
        /// </summary>
        /// <param name="focusService"></param>
        /// <param name="jobLogService"></param>
        /// <param name="config"></param>
        public FocusController(
             IFocusService focusService
            , IJobLogService jobLogService
            , IOptions<SystemConfig> config
            )
        {
            _focusService = focusService;
            _jobLogService = jobLogService;
            _config = config;
        }

        /// <summary>
        /// 同步医院字典数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncInpatientBaseDict")]
        public IActionResult SyncInpatientBaseDict()
        {
            var resultSrt = _focusService.SyncInpatientBaseDict();
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 同步新入院病人数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncNewInPatient")]
        public async Task<IActionResult> SyncNewInPatient()
        {
            var resultSrt = await _focusService.SyncNewInPatient();
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 根据病区分组同步在院病人数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncInPatientByStationGroup")]
        public async Task<IActionResult> SyncInPatientByStationGroup(int StationGroup)
        {
            var resultSrt = await _focusService.SyncInPatientByStationGroup(StationGroup);
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 根据病区Code同步在院病人数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncInPatientByStatioCode")]
        public async Task<IActionResult> SyncInPatientByStatioCode(string stationCode)
        {
            var resultSrt = await _focusService.SyncInPatientByStationCode(stationCode);
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 同步人员病区权限信息同步
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("EmployeeStationSwitch")]
        public async Task<IActionResult> EmployeeStationSwitch()
        {
            var resultSrt = await _focusService.SyncEmployeeStationSwitch();
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 同步员工基本信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncEmployee")]
        public async Task<IActionResult> SyncEmployee()
        {
            var resultSrt = await _focusService.SyncEmployee();
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 同步床位信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncBed")]
        public IActionResult SyncBed()
        {
            var resultSrt = _focusService.SyncBed();
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 同步病区信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncStation")]
        public IActionResult SyncStation()
        {
            var resultSrt = _focusService.SyncStation();
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 同步科室信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncDepartmen")]
        public IActionResult SyncDepartmen()
        {
            var resultSrt = _focusService.SyncDepartmen();
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 同步科室病区对照
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncWardDept")]
        public IActionResult SyncWardDept()
        {
            var resultSrt = _focusService.SyncWardDept();
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 同步医嘱字典
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncOrderDict")]
        public async Task<IActionResult> SyncOrderDict()
        {
            var resultSrt = await _focusService.SyncOrderDict();
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 同步检验数据，caseNumber如果为空，则同步所有在院病人的
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncTestReportByCaseNumber")]
        public async Task<IActionResult> SyncTestReportByCaseNumber(string caseNumber)
        {
            var resultSrt = await _focusService.SyncTestReportByCaseNumber(caseNumber);
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 同步检验数据，同步一段时间内的检验数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncTestReportByDateTime")]
        public async Task<IActionResult> SyncTestReportByDateTime()
        {
            var resultSrt = await _focusService.SyncTestReportByDateTime();
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 同步医嘱数据
        /// </summary>SyncTestReportByDateTime
        /// <returns></returns>
        [HttpGet]
        [Route("SyncPatientOrderByDateTime")]
        public async Task<IActionResult> SyncPatientOrderByDateTime()
        {
            var resultSrt = await _focusService.SyncPatientOrderByDateTime();
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 同步历史停止医嘱数据
        /// </summary>SyncTestReportByDateTime
        /// <returns></returns>
        [HttpGet]
        [Route("SyncHistoryPatientOrder")]
        public async Task<IActionResult> SyncHistoryPatientOrder()
        {
            var resultSrt = await _focusService.SyncHistoryPatientOrder();

            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 同步历史医嘱数据(手动)
        /// </summary>
        /// <param name="startDateTime"></param>
        /// <param name="endDateTime"></param>
        /// <param name="apiAddress"></param>
        /// <param name="hospitalID"></param>
        /// <param name="syncStopFlag">是否只同步停止医嘱</param>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncHisOrderByManual")]
        public async Task<IActionResult> SyncHisOrderByManual(DateTime startDateTime, DateTime endDateTime
             , bool syncStopFlag, string caseNumber
             , string apiAddress = "http://**********:8093/api/Transaction/SyncOrderByDateTime")
        {
            var hospitalID = _config.Value.HospitalID;
            await _focusService.SyncHisOrderByManual(startDateTime, endDateTime, apiAddress, hospitalID, syncStopFlag, caseNumber);
            var result = new ResponseResult
            {
                Data = true,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 同步病人主诉,stationID 不传入，或者传入0，同步所有的
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncPatientsChiefComplaint")]
        public async Task<IActionResult> SyncPatientsChiefComplaint(int? stationID)
        {
            var result = new ResponseResult();

            result.Data = await _focusService.SyncPatientChiefComplaint(stationID);
            result.Code = 1;
            return result.ToJson();
        }

        /// <summary>
        /// 根据ID获取作业
        /// </summary>
        /// <param name="jobId">all 返回所有</param>
        /// <param name="jobName"></param>
        /// <param name="subJobId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetJobByID")]
        public IActionResult GetJobByID(string jobId, string jobName, string subJobId)
        {
            var result = new ResponseResult();

            result.Data = _jobLogService.GetJobByID(jobId, jobName, subJobId);
            result.Code = 1;
            return result.ToJson();
        }

        /// <summary>
        /// 移除作业
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("RemoveJobByID")]
        public IActionResult RemoveJob(string jobId, string jobName, string subJobId)
        {
            var result = new ResponseResult();

            _jobLogService.RemoveJob(jobId, jobName, subJobId);
            result.Code = 1;
            return result.ToJson();
        }

        /// <summary>
        /// 读取本地测试文件
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("ReadFileTest")]
        public IActionResult ReadFileTest()
        {
            var result = new ResponseResult();

            var files = ReadFile.ReadTxt(@"D:\1.json");
            result.Data = files;
            result.Code = 1;
            return result.ToJson();
        }

        /// <summary>
        /// 同步一段时间内的给药记录数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncMedicineRecordByDateTime")]
        public async Task<IActionResult> SyncMedicineRecordByDateTimeAsync()
        {
            var resultSrt = await _focusService.SyncMedicineRecordByDateTimeAsync();
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 出院病人同步,同步一段时间内的出院病人数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncDischargedPatientsByDateTime")]
        public async Task<IActionResult> SyncDischargedPatientsByDateTime()
        {
            var result = new ResponseResult();

            result.Data = await _focusService.SyncDischargedPatientsByDateTime();
            result.Code = 1;
            return result.ToJson();
        }

        /// <summary>
        /// 病人出院病人数据补救
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncDisChargeErrorInpatientSupplement")]
        public async Task<IActionResult> SyncDisChargeErrorInpatientSupplement()
        {
            var resultSrt = await _focusService.SyncDisChargeInPatientByDatetimeSupplement();
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 同步手术记录,同步一段时间内的手术数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncPatientOperation")]
        public async Task<IActionResult> SyncPatientOperation()
        {
            var resultSrt = await _focusService.SyncOperateDataByDateTime();

            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 根据时间段同步出院患者（刷新用）
        /// </summary>
        /// <param name="startDateTime"></param>
        /// <param name="endDateTime"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncDischargedByDateTime")]
        public async Task<IActionResult> SyncDischargedByDateTime(DateTime? startDateTime, DateTime? endDateTime)
        {
            var result = new ResponseResult();

            result.Data = await _focusService.SyncDischargedByDateTime(startDateTime, endDateTime);
            result.Code = 1;
            return result.ToJson();
        }

        /// <summary>
        /// 同步母婴关系
        /// </summary>
        /// <param name="stationCode">有母婴关系的病区码</param>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncMotherAndChildList")]
        public async Task<IActionResult> SyncMotherAndChildList(string stationCode)
        {
            var resultSrt = false;

            resultSrt = await _focusService.SyncMotherAndChildList(stationCode);

            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }
    }
}