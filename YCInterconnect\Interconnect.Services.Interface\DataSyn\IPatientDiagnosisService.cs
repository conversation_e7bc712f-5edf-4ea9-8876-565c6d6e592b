﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Interconnect.Services.Interface
{
    public interface IPatientDiagnosisService
    {
        /// <summary>
        /// 根据住院号同步患者诊断
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        Task<string> SyncPatientDiagnosisByCaseNumber(string caseNumber);
        /// <summary>
        /// 根据病区Code同步在院桓
        /// </summary>
        /// <param name="stationCode"></param>
        /// <param name="saveCount">每次入数据库的数量 不传默认为1</param>
        /// <returns></returns>
        Task<string> SyncPatientDiagnosisByStationCode(string stationCode, int? saveCount);
        /// <summary>
        /// 打印his诊断数据
        /// </summary>
        /// <param name="stationCode"></param>
        /// <param name="caseNumber"></param>
        /// <param name="inHosipitalStatus"></param>
        /// <returns></returns>
        Task<Dictionary<string, Object>> PrintHisDiagnosis(string stationCode, string caseNumber, string inHosipitalStatus);
        /// <summary>
        /// 更新病人电子病历异动
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        Task UpdateEmrFileListAsync(string inpatientID);
    }
}