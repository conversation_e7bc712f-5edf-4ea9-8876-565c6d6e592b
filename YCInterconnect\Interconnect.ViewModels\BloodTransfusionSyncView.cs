using System;
using System.ComponentModel.DataAnnotations;

namespace Interconnect.ViewModels
{
    /// <summary>
    /// 输血数据同步视图模型
    /// </summary>
    public class BloodTransfusionSyncView
    {
        /// <summary>
        /// 输血单号
        /// </summary>
        public string TransfusionID { get; set; }
        /// <summary>
        /// 血袋码
        /// </summary>
        public string BloodDonorBagsCode { get; set; }
        /// <summary>
        /// 院内住院号
        /// </summary>
        public string LocalCaseNumber { get; set; }
        /// <summary>
        /// 病案号
        /// </summary>
        public string ChartNo { get; set; }
        /// <summary>
        /// 病人姓名
        /// </summary>
        public string PatientName { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public string Gender { get; set; }
        /// <summary>
        /// 年龄
        /// </summary>
        public string Age { get; set; }
        /// <summary>
        /// 失效时间
        /// </summary>
        public DateTime? ExpiryDate { get; set; }
        /// <summary>
        /// 血袋血型
        /// </summary>
        public string BloodDonorABO { get; set; }
        /// <summary>
        /// 血袋Rh
        /// </summary>
        public string BloodDonorRH { get; set; }
        /// <summary>
        /// 病人血型
        /// </summary>
        public string PatientABO { get; set; }
        /// <summary>
        /// 病人Rh
        /// </summary>
        public string PatientRH { get; set; }
        /// <summary>
        /// 血液制品号
        /// </summary>
        public string BloodNumber { get; set; }
        /// <summary>
        /// 外部血液制品代码
        /// </summary>
        public string OuterBloodCode { get; set; }
        /// <summary>
        /// 发血时间
        /// </summary>
        public DateTime? SendBloodDate { get; set; }
        /// <summary>
        /// 科室核收人员1
        /// </summary>
        public string StationVerificationEmployeeID1 { get; set; }
        /// <summary>
        /// 科室核收人员2
        /// </summary>
        public string StationVerificationEmployeeID2 { get; set; }
        /// <summary>
        /// 血袋回收核验人1
        /// </summary>
        public string StationRecycleEmployeeID1 { get; set; }
        /// <summary>
        /// 血袋回收核验人2
        /// </summary>
        public string StationRecycleEmployeeID2 { get; set; }
        /// <summary>
        /// 输血开始时间
        /// </summary>
        public DateTime? TransfusionStartDate { get; set; }
        /// <summary>
        /// 输血结束时间
        /// </summary>
        public DateTime? TransfusionEndDate { get; set; }
        /// <summary>
        /// 血液成分名称
        /// </summary>
        public string BloodName { get; set; }
        /// <summary>
        /// 交叉配血结果
        /// </summary>
        public string BloodMatchResult { get; set; }
        /// <summary>
        /// 发血数量
        /// </summary>
        public decimal? SendBloodNumber { get; set; }
        /// <summary>
        /// 输血单位
        /// </summary>
        public string BloodUnit { get; set; }
        /// <summary>
        /// 输血人员
        /// </summary>
        public string TransfusionEmployeeID { get; set; }
        /// <summary>
        /// 输血核对人员
        /// </summary>
        public string TransfusionCheckEmployeeID { get; set; }
        /// <summary>
        /// 输血方式
        /// </summary>
        public int? TransfusionType { get; set; }
        /// <summary>
        /// 输血反应
        /// </summary>
        public string TransfusionReaction { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateDate { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyDate { get; set; }
        /// <summary>
        /// 修改人员
        /// </summary>
        public string ModifyPersonID { get; set; }
        /// <summary>
        /// 删除标志
        /// </summary>
        public string DeleteFlag { get; set; }
    }
}
