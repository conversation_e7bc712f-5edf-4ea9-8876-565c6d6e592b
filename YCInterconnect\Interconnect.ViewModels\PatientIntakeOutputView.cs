﻿using System;

namespace Interconnect.ViewModels
{
    /// <summary>
    /// 出入量视图
    /// </summary>
    public class PatientHisIntakeOutputView
    {
        /// <summary>
        /// 来源类型
        /// </summary>
        public int SourceType { get; set; }
        /// <summary>
        /// 一条数据的唯一键
        /// </summary>
        public string RecordID { get; set; }
        /// <summary>
        /// 详细数据
        /// </summary>
        public IntakeOutputData Data { get; set; }
    }
    /// <summary>
    /// 出入量明细
    /// </summary>
    public class IntakeOutputData
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 患者编号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 住院号
        /// </summary>
        public string ChartNo { get; set; }
        /// <summary>
        /// 病区编码
        /// </summary>
        public string StationCode { get; set; }
        /// <summary>
        /// 病区名
        /// </summary>
        public string StationName { get; set; }
        /// <summary>
        /// 类型：I入O出
        /// </summary>
        public string IOType { get; set;}
        /// <summary>
        /// 类别编码
        /// </summary>
        public string IOKindCode { get; set; }
        /// <summary>
        /// 类别
        /// </summary>
        public string IOKingName { get; set; }
        /// <summary>
        /// 项目编码
        /// </summary>
        public string IntakeOutputSettingID { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string IntakeOutput { get; set; }
        /// <summary>
        /// 输入类型编码
        /// </summary>
        public string IOWayCode { get; set; }
        /// <summary>
        /// 输入类型
        /// </summary>
        public string IOWay { get; set; }
        /// <summary>
        /// 输入输出内容
        /// </summary>
        public string IONme { get; set; }
        /// <summary>
        /// 出入量值
        /// </summary>
        public string IntakeOutputVolume { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }
        /// <summary>
        /// 颜色
        /// </summary>
        public string Color { get; set; }
        /// <summary>
        /// 性状
        /// </summary>
        public string Characteristic { get; set; }
        /// <summary>
        /// 气味
        /// </summary>
        public string Smell { get; set; }
        /// <summary>
        /// 修改人
        /// </summary>
        public string ModifyPersonID { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyDate { get; set; }
        /// <summary>
        /// 删除标记
        /// </summary>
        public string DeleteFlag { get; set; }

    };
}
