﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PhysicianToInterventionRepository : IPhysicianToInterventionRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IMemoryCache _memoryCache;
        private readonly GetCacheService _getCacheService;
        public PhysicianToInterventionRepository(MedicalDbContext db, SessionCommonServer sessionCommonServer, IMemoryCache memoryCache, GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _sessionCommonServer = sessionCommonServer;
            _memoryCache = memoryCache;
            _getCacheService = getCacheService;
        }

        /// <summary>
        /// 获取措施对应的医嘱内容
        /// </summary>
        /// <param name="orderCode">医嘱代码</param>
        /// <returns></returns>
        public async Task<List<PhysicianToInterventionInfo>> GetAsync(string orderCode)
        {
            var data = await GetAsync();
            return data.Where(m => m.OrderCode == orderCode).ToList();
        }

        /// <summary>
        /// 获取措施对应的医嘱内容(同步方式)
        /// </summary> 
        /// <returns></returns>
        public async Task<List<PhysicianToInterventionInfo>> GetAllData()
        {
            return await GetAsync();
        }
        /// <summary>
        /// 根据TPRFlag获取医嘱对应措施数据
        /// </summary>
        /// <param name="tprFlag"></param>
        /// <returns></returns>
        public async Task<List<PhysicianToInterventionInfo>> GetByTPRFlag(bool tprFlag)
        {
            var data = await GetAsync();
            return data.Where(m => m.TPRFlag == tprFlag).ToList();
        }
        /// <summary>
        /// 获取医嘱对措施
        /// </summary>
        /// <returns></returns>
        public async Task<List<PhysicianToInterventionInfo>> GetAsync()
        {
            return await GetCacheAsync() as List<PhysicianToInterventionInfo>;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<PhysicianToInterventionInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            return await _medicalDbContext.PhysicianToInterventions.Where(m => m.HospitalID == hospitalID.ToString() && m.DeleteFlag != "*").ToListAsync();
        }
        public string GetCacheType()
        {
            return CacheType.PhysicianToInterventions.GetKey(_sessionCommonServer);
        }

        public async Task<List<PhysicianToInterventionInfo>> GetByOrderStatus(int orderStatus)
        {
            var data = await GetAsync();
            return data.Where(m => m.OrderStatus == orderStatus).ToList();
        }
    }
}
