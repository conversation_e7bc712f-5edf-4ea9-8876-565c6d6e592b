﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Interconnect.Models;

namespace Interconnect.Data.Interface
{
    public interface INRPressureScoreInfoRepository
    {
        /// <summary>
        ///根据PatientScoreMainID获取PressureScoreID
        /// </summary>
        /// <param name="PatientScoreMainID"></param>
        /// <returns></returns>     
        List<NRPressureScoreInfo> GetPressureScore(string PatientScoreMainID);
    }
}
