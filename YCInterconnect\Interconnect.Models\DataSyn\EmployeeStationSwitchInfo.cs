﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("EmployeeStationSwitch")]
    public class EmployeeStationSwitchInfo : ModifyReturnInfo
    {
        /// <summary>
        ///	自增ID	
        ///</summary>
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///	工号	
        ///</summary>
        public string EmployeeID { get; set; }
        /// <summary>
        ///	病区(护理单元)代码	
        ///</summary>
        public string StationCode { get; set; }
        /// <summary>
        ///	异动人员	
        ///</summary>
        public string ModifyPersonID { get; set; }
        /// <summary>
        ///	异动日期	
        ///</summary>
        public DateTime? ModifyDate { get; set; }
        /// <summary>
        ///	删除注记 *表示删除	
        ///</summary>
        public string DeleteFlag { get; set; } 
        /// <summary>
        ///	同步次数	
        ///</summary>
        public int? Counts { get; set; }
    }
}