﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Medical.Common;
using Medical.Data.Context;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using static Medical.Common.Enums;

namespace Interconnect.API
{
    /// <summary>
    /// 记录请求和响应日志中间件
    /// </summary>
    /// <param name="next"></param>
    /// <param name="scopeFactory">作用域创建工厂</param>
    public class ReqResLogMiddle(RequestDelegate next, IServiceScopeFactory scopeFactory, IOptions<SystemConfig> config)
    {
        private readonly RequestDelegate _next = next;
        private readonly IServiceScopeFactory _scopeFactory = scopeFactory;
        private readonly string IgnoreApis = "";
        private readonly IOptions<SystemConfig> _config = config;

        /// <summary>
        /// 中间件调用方法
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <param name="serviceProvider">服务提供程序</param>
        /// <returns>异步任务</returns>
        public async Task InvokeAsync(HttpContext context, IServiceProvider serviceProvider)
        {
            if (!_config.Value.RecordLog)
            {
                await _next(context);
                return;
            }
            var sessionCommonServer = serviceProvider.GetRequiredService<SessionCommonServer>();

            HttpRequest request = context.Request;
            var api = context.Request.Path.ToString().TrimEnd('/');

            // 过滤，只有接口
            if (!api.Contains("api") || IgnoreApis.Contains(api) || request.Method == "OPTIONS")
            {
                await _next(context);
                return;
            }

            var session = await sessionCommonServer.GetSession();
            var eventLog = new EventLogInfo
            {
                CID = "",
                Target = api,
                EventType = api switch
                {
                    var x when x.Contains(EventLogType.NursingAssess.ToString()) => EventLogType.NursingAssess.ToString("d"),
                    var x when x.Contains(EventLogType.NursingLevelChange.ToString()) => EventLogType.NursingLevelChange.ToString("d"),
                    var x when x.Contains(EventLogType.RiskAssess.ToString()) => EventLogType.RiskAssess.ToString("d"),
                    var x when x.Contains(EventLogType.NursingDiagnosis.ToString()) => EventLogType.NursingDiagnosis.ToString("d"),
                    var x when x.Contains(EventLogType.NursingProblemChange.ToString()) => EventLogType.NursingProblemChange.ToString("d"),
                    var x when x.Contains(EventLogType.InterventionSave.ToString()) => EventLogType.InterventionSave.ToString("d"),
                    var x when x.Contains(EventLogType.ClusterProblem.ToString()) => EventLogType.ClusterProblem.ToString("d"),
                    var x when x.Contains(EventLogType.ScheduleExecution.ToString()) => EventLogType.ScheduleExecution.ToString("d"),
                    var x when x.Contains(EventLogType.NursingRecord.ToString()) => EventLogType.NursingRecord.ToString("d"),
                    _ => "0"
                },
                CreateTime = DateTime.Now,
                IPAddress = GetClientIp(context),
                ServerIPAddress = request.Host.HasValue ? request.Host.Value : "",
                EmployeeID = session?.EmployeeID == 0 ? null : session.EmployeeID.ToString(),
                HospitalID = session?.HospitalID,
                ClientType = session?.ClientType,
                EmployeeName = session?.UserName
            };

            // 获取请求body内容
            if (request.Method.ToLower().Equals("post") || request.Method.ToLower().Equals("put"))
            {
                request.EnableBuffering();
                Stream stream = request.Body;
                byte[] buffer = new byte[request.ContentLength.Value];
                int bytesRead = await stream.ReadAsync(buffer);
                if (bytesRead > 0)
                {
                    eventLog.Content = $"入参：{Encoding.UTF8.GetString(buffer, 0, bytesRead)}";
                }
                request.Body.Position = 0;
            }
            else if (request.Method.ToLower().Equals("get") || request.Method.ToLower().Equals("delete"))
            {
                var reqContent = request.QueryString.ToString();
                if (!string.IsNullOrEmpty(reqContent))
                {
                    eventLog.Content = $"入参：{HttpUtility.UrlDecode(reqContent[1..], Encoding.UTF8)}";
                }
            }

            eventLog.ID = eventLog.GetId();

            // 获取Response.Body内容
            var originalBodyStream = context.Response.Body;
            using var responseBody = new MemoryStream();
            context.Response.Body = responseBody;

            // 保存原始的CORS头
            var originalCorsHeaders = context.Response.Headers.AccessControlAllowOrigin;

            await _next(context);

            // 恢复CORS头
            context.Response.Headers.AccessControlAllowOrigin = originalCorsHeaders;

            var responseBodyData = await GetResponse(context.Response);
            eventLog.Content += $"出参：{responseBodyData}";

            _ = Task.Run(async () =>
            {
                using var scope = _scopeFactory.CreateAsyncScope();
                var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
                await unitOfWork.GetRepository<EventLogInfo>().InsertAsync(eventLog);
                await unitOfWork.SaveChangesAsync();
            });

            await responseBody.CopyToAsync(originalBodyStream);
        }

        /// <summary>
        /// 获取响应
        /// </summary>
        /// <param name="response">http响应</param>
        /// <returns></returns>
        public async Task<string> GetResponse(HttpResponse response)
        {
            response.Body.Seek(0, SeekOrigin.Begin);
            var text = await new StreamReader(response.Body).ReadToEndAsync();
            response.Body.Seek(0, SeekOrigin.Begin);
            return text;
        }
        /// <summary>
        /// 获取客户端IP地址
        /// </summary>
        /// <param name="context">http上下文</param>
        /// <returns></returns>
        public string GetClientIp(HttpContext context)
        {
            var ip = context.Request.Headers["X-Forwarded-For"].ToString();
            if (string.IsNullOrEmpty(ip))
            {
                if (context.Connection.RemoteIpAddress != null)
                {
                    ip = context.Connection.RemoteIpAddress.MapToIPv4().ToString();
                }
            }
            return ip;
        }
    }
}
