﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Interconnect.ViewModels
{
    /// <summary>
    /// 出院电子病历
    /// </summary>
    public class DischargeView
    {
        /// <summary>
        /// 页签编码
        /// </summary>
        public string BookMarkID { get; set; }
        /// <summary>
        /// 评估项目序号
        /// </summary>
        public int AssessListID { get; set; }
        /// <summary>
        /// 评估结果
        /// </summary>
        public string AssessValue { get; set; }
        /// <summary>
        /// 专项护理注记 
        /// </summary>
        public string SpecialListType { get; set; }
        /// <summary>
        /// 标签ID（数据对应真正含义）
        /// </summary>
        public int? AssessListGroupID { get; set; }
    }
}
