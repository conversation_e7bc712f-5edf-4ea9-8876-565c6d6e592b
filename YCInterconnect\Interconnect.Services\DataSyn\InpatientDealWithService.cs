﻿/*
 * 2022-01-12 2352 病人信息同步添加转床、转科时写患者事件 -杨欣欣
 * 2022-05-15   新增母婴关系同步，可通过母亲信息补全婴儿信息    ——孟昭永
 */
using Arch.EntityFrameworkCore.UnitOfWork;
using Hangfire;
using Interconnect.Data.Context;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Interconnect.ViewModels;
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Data.Repository;
using Medical.Models;
using Medical.ViewModels.Interface;
using Medical.ViewModels.View;
using MedicalExternalCommon.Service;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Interconnect.Services
{
    public class InpatientDealWithService : IInpatientDealWithService
    {
        //Mdeical
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;

        private readonly IStationListRepository _IStationListRepository;
        private readonly IDepartmentListRepository _IDepartmentListRepository;
        private readonly IBedListRepository _IBedListRepository;
        private readonly IInpatientDataRepository _IInpatientDataRepository;
        private readonly IPatientBasicService _IPatientBasicService;
        private readonly IPatientListIconRepository _patientListIconRepository;
        private readonly IVirtualStationListRepository _virtualStationListRepository;
        private readonly IEventSettingRepository _eventSettingRepository;
        private readonly IUserRepository _userRepository;
        private readonly IHospitalListRepository _hospitalListRepository;
        private readonly IUnitOfWork<DataOutConnection> _unitOfWorkOut;
        private readonly IOptions<SystemConfig> _config;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        public readonly IExecTableDictRepository _execTableDictRepository;
        private readonly ICommonHelper _commonHelper;
        private readonly ISyncAPIConfigRepository _syncAPIConfigRepository;
        private readonly IPatientEventRepository _patientEventRepository;
        private readonly PatientProfileMarkService _patientProfileMarkService;
        private readonly ExternalCommonService _externalCommonService;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly IInpatientLogRepository _inpatientLogRepository;
        private readonly PatientEventCommonService _patientEventCommonService;
        private readonly ISyncLogService _syncLogService;
        private readonly MQCommonService _mQCommonService;
        private readonly DataTableEditListService _dataTableEditListService;
        private readonly GetBirthDateService _getBirthDateService;
        private readonly IFocusService _focusService;
        private readonly ExternalProfileCommonService _externalProfileCommonService;
        private readonly IAPISettingRepository _aPISettingRepository;
        private readonly IPatientDiagnosisService _patientDiagnosisService;
        /// <summary>
        /// 系统默认操作人
        /// </summary>
        private const string MODIFYPERSONID = "TongBu";

        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";

        private List<MarkView> MarkViewList = new List<MarkView>();
        private List<PatientProfile> PatientProfileList = new List<PatientProfile>();
        private List<MessageModel> SendMessageList = new List<MessageModel>();
        private List<InPatientChangeViewInfo> InPatientChangeList = new List<InPatientChangeViewInfo>();//记录更新或新增的病人数据
        #region --常量配置
        /// <summary>
        /// 转出床事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_TURNOUTBED = 5317;
        /// <summary>
        /// 转入床事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_TURNINBED = 5318;
        /// <summary>
        /// 转出科事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_TRANSOUTDEPT = 5319;
        /// <summary>
        /// 转入科事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_TRANSINDEPT = 5320;
        /// <summary>
        /// 入院事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_ADMISSION = 2872;
        /// <summary>
        /// 入科事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_UNITARRIVALTIME = 4700;
        /// <summary>
        /// 转入病区事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_TRANSIN = 2874;
        /// <summary>
        /// 转病区事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_TRANSOUT = 2875;
        /// <summary>
        /// 出院事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_DISCHARGE = 2873;
        /// <summary>
        /// 死亡
        /// </summary>
        private const int DeathAssessListId = 2876;
        #endregion

        public InpatientDealWithService(
              IUnitOfWork<MedicalDbContext> UnitOfWork
            , IUnitOfWork<DataOutConnection> UnitOfWorkOut
            , IStationListRepository StationListRepository
            , IDepartmentListRepository DepartmentListRepository
            , IBedListRepository BedListRepository
            , IInpatientDataRepository inpatientDataRepository
            , IOptions<SystemConfig> config
            , IExecTableDictRepository execTableDictRepository
            , IPatientBasicService patientBasicService
            , ICommonHelper commonHelper
            , IPatientListIconRepository patientListIconRepository
            , IVirtualStationListRepository virtualStationListRepository
            , IEventSettingRepository eventSettingRepository
            , ISyncAPIConfigRepository syncAPIConfigRepository
            , IUserRepository userRepository
            , IHospitalListRepository hospitalListRepository
            , IPatientEventRepository patientEventRepository
            , PatientProfileMarkService patientProfileMarkService
            , ExternalCommonService externalCommonService
            , IAppConfigSettingRepository appConfigSettingRepository
            , IInpatientLogRepository inpatientLogRepository
            , PatientEventCommonService patientEventCommonService
            , ISyncLogService syncLogService
            , MQCommonService mQCommonService
            , DataTableEditListService dataTableEditListService
            , GetBirthDateService getBirthDateService
            , IFocusService focusService
            , ExternalProfileCommonService externalProfileCommonService
            , IAPISettingRepository aPISettingRepository
            ,IPatientDiagnosisService patientDiagnosisService

            )
        {
            _unitOfWork = UnitOfWork;
            _unitOfWorkOut = UnitOfWorkOut;
            _IDepartmentListRepository = DepartmentListRepository;
            _IStationListRepository = StationListRepository;
            _IBedListRepository = BedListRepository;
            _IInpatientDataRepository = inpatientDataRepository;
            _config = config;
            _execTableDictRepository = execTableDictRepository;
            _IPatientBasicService = patientBasicService;
            _commonHelper = commonHelper;
            _patientListIconRepository = patientListIconRepository;
            _virtualStationListRepository = virtualStationListRepository;
            _eventSettingRepository = eventSettingRepository;
            _syncAPIConfigRepository = syncAPIConfigRepository;
            _userRepository = userRepository;
            _hospitalListRepository = hospitalListRepository;
            _patientEventRepository = patientEventRepository;
            _patientProfileMarkService = patientProfileMarkService;
            _externalCommonService = externalCommonService;
            _appConfigSettingRepository = appConfigSettingRepository;
            _inpatientLogRepository = inpatientLogRepository;
            _patientEventCommonService = patientEventCommonService;
            _syncLogService = syncLogService;
            _mQCommonService = mQCommonService;
            _dataTableEditListService = dataTableEditListService;
            _getBirthDateService = getBirthDateService;
            _focusService = focusService;
            _externalProfileCommonService = externalProfileCommonService;
            _aPISettingRepository = aPISettingRepository;
            _patientDiagnosisService = patientDiagnosisService;
        }
        //同步一个病区的数据
        public async Task<bool> SyncInPatientByStationCode(string statinoCode)
        {
            var hospitalInfo = _hospitalListRepository.GetHospitalInfo();
            if (hospitalInfo == null)
            {
                return false;
            }
            var hospitalBaseDict = await GetHospitalBaseDict();
            //获取API接口数据
            var inpatientData = await GetInpateintDataByStationCode(statinoCode, hospitalInfo.HospitalID);
            if (inpatientData == null)
            {
                //_logger.Error("inpatientData||" + ListToJson.ToJson(inpatientData));
                _logger.Error("程序报错||没有获得住院数据，statinoCode=" + statinoCode);
                return false;
            }
            if (inpatientData.Count == 0)
            {
                _logger.Error("本病区所有病人都出院StationCode||" + statinoCode);
                //本病区没有的病人，把BedNumber改为空，BedID为0，使清单不呈现
                return await SyncClearStationInPatient(statinoCode, inpatientData, hospitalInfo.HospitalID);
            }
            //将数据进行同步

            var resultFlag = await SyncInPatientData(hospitalBaseDict, inpatientData, hospitalInfo.HospitalID);
            if (!resultFlag)
            {
                _logger.Error("根据病区同步病人失败 病区Code||" + statinoCode);
                return false;
            }
            resultFlag = await SyncClearStationInPatient(statinoCode, inpatientData, hospitalInfo.HospitalID);
            if (!resultFlag)
            {
                _logger.Error("根据病区同步出院病人失败 StationCode||" + statinoCode);
                return false;
            }
            return true;
        }
        /// <summary>
        /// 同步时间段内获取的病人数据（入院、出院）
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncNewInPatient()
        {
            var hospitalInfo = _hospitalListRepository.GetHospitalInfo();
            if (hospitalInfo == null)
            {
                return false;
            }
            //获取API接口数据
            var inpatientData = await GetInpateintDataByDateTime("N", hospitalInfo.HospitalID);
            if (inpatientData == null || inpatientData.Count < 1)
            {
                return false;
            }
            //获取字典信息
            var hospitalBaseDict = await GetHospitalBaseDict();
            //数据同步
            return await SyncInPatientData(hospitalBaseDict, inpatientData, hospitalInfo.HospitalID);
        }
        /// <summary>
        /// 同步在院数据
        /// </summary>
        /// <param name="hospitalBaseDict"></param>
        /// <param name="inpatientData">从中介库中获取数据</param>
        /// <returns></returns>
        private async Task<bool> SyncInPatientData(HospitalBaseDictView hospitalBaseDict, List<InPatientDataView> inpatientData, string hospitalID)
        {
            #region "配置初始化"
            var language = _config.Value.Language;
            var result = false;
            //配置加载
            var patientListIcon = await _patientListIconRepository.GetAllAsync<PatientListIconInfo>();
            patientListIcon = patientListIcon.Where(m => m.IdentifyCategory.Contains("NursingLevel") && m.DeleteFlag != "*").ToList();
            var modifyPersonID = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "ModifyPersonID");
            var eventSettings = await _eventSettingRepository.GetAllAsync<EventSettingInfo>();
            var userList = await _userRepository.GetAllAsync<UserInfo>();
            #endregion "配置初始化"
            _logger.Info("同步在院病人[" + inpatientData.Count() + "]人");
            //开始同步
            foreach (var item in inpatientData) //记录作业正在执行日志
            {
                _logger.Info("同步患者住院信息 CaseNumber:" + item.CaseNumber + " ChartNo:" + item.ChartNo);
                if (item.DischargeDate == null)
                {
                    if (string.IsNullOrEmpty(item.StationCode) || string.IsNullOrEmpty(item.DepartmentCode) || string.IsNullOrEmpty(item.BedNumber))
                    {
                        _logger.Error("住院信息 CaseNumber: " + item.CaseNumber + " ChartNo: " + item.ChartNo + "科室、病区、床位信息个别为空");
                        continue;
                    }
                }
                //记录同步在院病人开始，写入作业
                //  inpatientJobStatus = _jobLogService.GetJobStatus(item.CaseNumber, inpatientJobName, item.ChartNo);
                //表示不需要同步
                //if (!inpatientJobStatus) 
                //{
                //    continue;
                //}
                //同步在院病人数据
                try
                {
                    result = await SyncInpatientDetail(item, hospitalBaseDict, patientListIcon, eventSettings, userList, hospitalID, modifyPersonID);
                }
                catch (Exception ex)
                {
                    _logger.Error("病人CaseNumber" + item.CaseNumber + " ChartNo:" + item.ChartNo + "同步失败！" + ex.ToString());
                }
                //记录同步完成，移除作业
                // _jobLogService.RemoveJob(item.CaseNumber, inpatientJobName, item.ChartNo);
                _logger.Info("病人CaseNumber" + item.CaseNumber + " ChartNo:" + item.ChartNo + "同步完成，移除作业");
            }
            return result;
        }

        //数据同步明细
        private async Task<bool> SyncInpatientDetail(InPatientDataView hisInpatientData, HospitalBaseDictView hospitalBaseDictView
            , List<PatientListIconInfo> patientListIconList
            , List<EventSettingInfo> eventSettings
            , List<UserInfo> userList
            , string hospitalID, string modifyPersonID)
        {
            #region "初始化"
            //获取年龄对应的AssessListID
            var ageAssessListID = 0;
            var upDataDBFlag = false;
            //写日志用
            string tableName = "Inpatient";
            var patientString = " CaseNumber[" + hisInpatientData.CaseNumber + "]  ChartNo [" + hisInpatientData.ChartNo + "]";
            MarkViewList = new List<MarkView>();
            InPatientChangeList = new List<InPatientChangeViewInfo>();
            PatientProfileList = new List<PatientProfile>();
            SendMessageList = new List<MessageModel>();
            SyncInpatientDataView syncInpatientDataView = new SyncInpatientDataView();
            #endregion "初始化"
            //获取年龄配置
            var resultAgeAssessListID = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "AgeAssessListID");
            if (!StringCheck.IsNumeric(resultAgeAssessListID))
            {
                _logger.Error("年龄AssessListID在表appConfigSetting里面配置错误，settingCode(AgeAssessListID)");
            }
            ageAssessListID = int.Parse(resultAgeAssessListID);

            // 判断获取的病人病区、科室、床位信息是否正常
            var hospitalBaseDict = GetinPatientBaseDict(hisInpatientData, hospitalBaseDictView);
            if (hospitalBaseDict == null)
            {
                _logger.Error(patientString + "||病人病区 科室 床位信息异常");
                return false;
            }
            if (!CheckPatientBaseDict(hospitalBaseDict))
            {
                _logger.Info(patientString + "||病人病区、科室、床位信息异常");
                return false;
            }
            //同步病人基本信息 PatientBasecData  新增||修改
            var patientBasicDatas = await SyncPatientBase(hisInpatientData);
            if (patientBasicDatas == null || patientBasicDatas.Count == 0)
            {
                _logger.Error(patientString + "||病人基本信息同步失败");
                return false;
            }
            //获取病人基本信息，没有基本信息，不同步数据
            var patientBasicData = patientBasicDatas.FirstOrDefault();
            //获取病人InPatientData数据
            var tempPatient = _IInpatientDataRepository.GetInpatientIDByCaseNumber(hisInpatientData.CaseNumber);
            //计算年龄Detail
            hisInpatientData = GetInpatientAgeDetail(hisInpatientData);
            //转换医生的工号，为登录账号
            var userInfo = userList.Where(m => m.PhysicianID == hisInpatientData.AttendingPhysicianID).FirstOrDefault();
            if (userInfo != null)
            {
                hisInpatientData.AttendingPhysicianID = userInfo.UserID.ToString();
            }
            //病人无InPatientData信息 需新增
            if (tempPatient == null)
            {
                return await InPatientDataAdd(tempPatient, hisInpatientData, hospitalBaseDict, patientBasicData, eventSettings, patientListIconList, tableName, hospitalID, ageAssessListID, modifyPersonID);
            }
            //接口数据有出院时间  CCC库病人为在院状态  走预出院流程
            if (hisInpatientData.DischargeDate.HasValue && hisInpatientData.DischargeTime.HasValue && tempPatient.InHospitalStatus.HasValue 
                && InHospitalStatus.INHOSPITALLIST.Contains(tempPatient.InHospitalStatus.Value))
            {
                //出院时间必须小于当下时间才进行出院操作
                if (hisInpatientData.DischargeDate.Value.Add(hisInpatientData.DischargeTime.Value) < DateTime.Now)
                {
                    _logger.Info("患者预出院||CaseNumber||hisDischargeTime||" + hisInpatientData.DischargeTime.Value + "||tempPatientDischargeTime||" + tempPatient.DischargeTime);
                    if (hisInpatientData.DischargeDate != tempPatient.DischargeDate || hisInpatientData.DischargeTime != tempPatient.DischargeTime)
                    {
                        //出院时间发生改变 更新出院时间 重写出院患者事件
                        if (tempPatient.DischargeDate.HasValue && tempPatient.DischargeTime.HasValue)
                        {
                            _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "Dischange", tempPatient.CaseNumber + "||出院日期发生改变，从 [" + tempPatient.DischargeDate.Value.Add(tempPatient.DischargeTime.Value) + "]更新为[" + hisInpatientData.DischargeDate.Value.Add(hisInpatientData.DischargeTime.Value), "TongBu", false);
                        }
                        syncInpatientDataView = await SyncInpatientDischarge(tempPatient, hisInpatientData, eventSettings, hospitalID);
                        upDataDBFlag = syncInpatientDataView.RetureFlag;
                        MarkViewList = syncInpatientDataView.MarkViewList;
                        InPatientChangeList = syncInpatientDataView.InPatientChangeViewList;
                    }
                }
                else
                {
                    _logger.Error("患者出院时间大于当下时间||CaseNumber||" + hisInpatientData.CaseNumber + "||出院时间：" + hisInpatientData.DischargeDate.Value.Add(hisInpatientData.DischargeTime.Value));
                }
            }

            //住院信息修改
            _logger.Info("修改住院病人 ,CaseNumber=" + hisInpatientData.CaseNumber);
            var result = await UpdateInpatientData(tempPatient, hisInpatientData, hospitalBaseDict, patientListIconList, patientBasicData.PatientID, tableName, eventSettings, hospitalID, modifyPersonID);
            upDataDBFlag = syncInpatientDataView.RetureFlag;
            upDataDBFlag = result.Item1;
            PatientProfileList = result.Item2;
            SendMessageList = result.Item3;

            //确认之前是否有PatientProfileMark,如果有，确认病区等基本信息
            var upProfileResult = await _patientProfileMarkService.UPDataPatientProfileMark(hisInpatientData.ChartNo, hisInpatientData.BedNumber, hospitalBaseDict.StationList[0].StationCode, hospitalBaseDict.DepartmentList[0].DepartmentCode);
            if (upProfileResult)
            {
                upDataDBFlag = true;
            }

            if (upDataDBFlag)
            {
                return await InPatientSaveAsync(tempPatient);
            }
            return true;
        }

        private async Task<bool> InPatientSaveAsync(InpatientDataInfo tempPatient)
        {
            try
            {
                _unitOfWork.SaveChanges();
                _unitOfWorkOut.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error("CareNumber:" + tempPatient.CaseNumber + "||病人信息同步失败||" + ex.ToString());
                return false;
            }

            //异动Mark
            CallMarkAPI(MarkViewList);
            //写PatientProfile
            _logger.Info("写PatientProfile");
            //呼叫Profile
            _commonHelper.AddProfile(PatientProfileList);
            _logger.Info("调用病人信息发生变化,调用对应的API");
            //病人信息变化，调用相应的APi进行处理
            CheckInPatient(InPatientChangeList);
            //发送信息
            if (tempPatient != null)
            {
                _logger.Info("发送MQ");
                BackgroundJob.Enqueue(() => _mQCommonService.SendingMessage(SendMessageList, "2", tempPatient.ID, tempPatient.StationID));
            }
            //再次入院患者同步历史过敏数据
            await SyncInPatientHistoryAllergy(tempPatient.ChartNo, tempPatient.ID, tempPatient.CaseNumber);
            return true;
        }
        /// <summary>
        /// 同步病人-新增病人信息 从无到有
        /// </summary>
        /// <param name="tempPatient"></param>
        /// <param name="hisInpatientData"></param>
        /// <param name="hospitalBaseDict"></param>
        /// <param name="patientBasicData"></param>
        /// <param name="eventSettings"></param>
        /// <param name="patientListIconList"></param>
        /// <param name="tableName"></param>
        /// <param name="hospitalID"></param>
        /// <param name="ageAssessListID"></param>
        /// <param name="modifyPersonID"></param>
        /// <returns></returns>
        private async Task<bool> InPatientDataAdd(InpatientDataInfo tempPatient, InPatientDataView hisInpatientData, HospitalBaseDictView hospitalBaseDict, PatientBasicDataInfo patientBasicData, List<EventSettingInfo> eventSettings, List<PatientListIconInfo> patientListIconList, string tableName, string hospitalID, int ageAssessListID, string modifyPersonID)
        {
            var cccInpatientData = await NewInpatientData(hisInpatientData, hospitalBaseDict, patientBasicData, hospitalID);
            var inpatientLogs = await _inpatientLogRepository.GetByCaseNumberAsync(cccInpatientData.CaseNumber);
            //确认之前是否有PatientProfileMark,如果有，确认病区等基本信息
            await _patientProfileMarkService.UPDataPatientProfileMark(hisInpatientData.ChartNo, cccInpatientData.BedNumber, hospitalBaseDict.StationList[0].StationCode, hospitalBaseDict.DepartmentList[0].DepartmentCode);
            await _externalProfileCommonService.DeletePatientProfileOwnDueDayAsync(cccInpatientData.ChartNo, cccInpatientData.ID);
            //新增住院并人增加Mark异动记录
            var markView = AddInpatientToProfileMark(cccInpatientData, hisInpatientData, patientBasicData);
            MarkViewList.Add(markView);
            var result = await CreateAddPatientProfile(cccInpatientData, patientListIconList, ageAssessListID, modifyPersonID);
            PatientProfileList = result.Item1; //profile数据
            SendMessageList = result.Item2;//发送消息通知
                                           //写入院事件
            await SetInpatientLogAndEvent(inpatientLogs, cccInpatientData, cccInpatientData.StationID, cccInpatientData.DepartmentListID
               , cccInpatientData.BedID
              , cccInpatientData.BedNumber, cccInpatientData.AdmissionDate.Add(cccInpatientData.AdmissionTime), EVENTSETTING_ASSESSLISTID_ADMISSION, eventSettings);

            return await InPatientSaveAsync(cccInpatientData);
        }
        /// <summary>
        /// 再次入院患者同步历史过敏数据
        /// </summary>
        /// <param name="chartNo"></param>
        /// <param name="inpatientID"></param>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        private async Task SyncInPatientHistoryAllergy(string chartNo, string inpatientID, string caseNumber)
        {
            if (string.IsNullOrEmpty(chartNo) || string.IsNullOrEmpty(inpatientID) || string.IsNullOrEmpty(caseNumber))
            {
                return;
            }
            var urlSetting = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "SyncInPatientHistoryAllergyByChartNoAPI");
            //urlSetting = "http://localhost:56194/api/Allergy/SyncInPatientHistoryAllergyByChartNo";
            if (string.IsNullOrEmpty(urlSetting))
            {
                return;
            }
            try
            {
                var url = $"{urlSetting}?chartNo={chartNo}&inpatientID={inpatientID}";
                //写同步日志
                var syncLog = _commonHelper.SaveLog(url, "", inpatientID, caseNumber, _unitOfWork, true, true);
                //呼叫API
                WebRequestSugar wrs = new WebRequestSugar();
                string result = wrs.SendObjectAsJsonInBody(url, null);
                //更新同步日志状态
                _commonHelper.GetAPIExecResult(result, syncLog, _unitOfWork);
            }
            catch (Exception ex)
            {
                _logger.Error($"ChartNo={chartNo}的患者同步历史过敏数据失败" + ex.ToString());
                return;
            }
        }

        private InPatientDataView GetInpatientAgeDetail(InPatientDataView hisInpatientData)
        {
            if (hisInpatientData.DateOfBirth == null)
            {
                return hisInpatientData;
            }
            var birthDate = hisInpatientData.DateOfBirth.Date;
            if (hisInpatientData.TimeOfBirth.HasValue)
            {
                birthDate = birthDate.Add(hisInpatientData.TimeOfBirth.Value);
            }
            var ageDetail = _getBirthDateService.GetAgeDetail(birthDate);
            hisInpatientData.AgeDetail = ageDetail;
            return hisInpatientData;
        }
        /// <summary>
        ///本病区没有的病人，住院状态置为50
        /// </summary>
        /// <param name="statinoCode"></param>
        /// <param name="hisInpatientDataViews"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<bool> SyncClearStationInPatient(string statinoCode, List<InPatientDataView> hisInpatientDataViews, string hospitalID)
        {
            if (hisInpatientDataViews == null || hisInpatientDataViews.Count <= 0)
            {
                //return false;
            }
            var stationLists = await _IStationListRepository.GetSimpleList();
            var stationInfo = stationLists.Find(m => m.Code == statinoCode);
            if (stationInfo == null)
            {
                _logger.Error("病区信息找不到  根据病区同步出院病人停止 StationCode||" + statinoCode);
                return false;
            }

            var inpatientDatas = await _IInpatientDataRepository.GetInpatientListByStationID(stationInfo.ID, hospitalID);
            if (inpatientDatas == null || inpatientDatas.Count <= 0)
            {
                _logger.Error("本病区无病人 根据病区同步出院病人停止 StationCode||" + statinoCode);
                return true;
            }
            inpatientDatas = inpatientDatas.Where(m => m.BedID != 0 || (m.InHospitalStatus.HasValue && Medical.Common.InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus.Value))).ToList();
            foreach (var item in inpatientDatas)
            {
                var hisInpatient = hisInpatientDataViews.Find(m => m.CaseNumber == item.CaseNumber);
                if (hisInpatient != null)
                {
                    continue;
                }
                //正式出院无出院时间  调用单病人接口进行预出院作业 由30=》40
                if (!item.DischargeDate.HasValue || !item.DischargeTime.HasValue)
                {
                    await SyncDischangeByCaseNumber(item.CaseNumber);
                    _syncLogService.InsertSyncLog(3, "1", "SyncClearStationInPatient", "inHospitalStatus||40", "正式出院补走预出院流程"+item.CaseNumber + "||在院状态，从 [" + item.InHospitalStatus + "]更新为[40] ", "TongBu", false);
                }
                //日志写入
                _syncLogService.InsertSyncLog(3, "1", "SyncClearStationInPatient", "inHospitalStatus||60", item.CaseNumber + "||在院状态，从 [" + item.InHospitalStatus + "]更新为[60] ", "TongBu", false);
                //病人清单不呈现-有出院时间调整为出院60
                item.InHospitalStatus = 60;
                item.Modify("TongBu");
                try
                {
                    _unitOfWorkOut.SaveChanges();
                    _unitOfWork.SaveChanges();
                }
                catch (Exception ex)
                {
                    _logger.Error("根据病区同步出院病人 保存失败StationCode||" + statinoCode + ex.ToString());
                    return false;
                }
            }
            return true;
        }

        //同步病人基本信息
        private async Task<List<PatientBasicDataInfo>> SyncPatientBase(InPatientDataView inPatientData)
        {
            var patientBasicList = new List<PatientBasicInfo>();
            var patientBasic = new PatientBasicInfo
            {
                ChartNo = inPatientData.ChartNo,
                PatientName = inPatientData.PatientName,
                Gender = inPatientData.Gender,
                DateOfBirth = inPatientData.DateOfBirth,
                IdentityID = inPatientData.IdentityID,
                NativePlace = inPatientData.NativePlace,
                NativePlaceCode = inPatientData.NativePlaceCode,
                BloodType = inPatientData.BloodType,
                TimeOfBirth = inPatientData.TimeOfBirth
            };
            patientBasicList.Add(patientBasic);
            return await _IPatientBasicService.SyncPatientBaseDetail(patientBasicList);
        }
        #region 获得接口数据

        /// <summary>
        /// 根据stationCode获取数据
        /// </summary>
        /// <param name="stationCode"></param>
        /// <returns></returns>
        private async Task<List<InPatientDataView>> GetInpateintDataByStationCode(string stationCode, string hospitalID)
        {
            //呼叫APi获得数据
            var apiStr = _config.Value.Status == "2" ? (_config.Value.DataInterfaceAPI + "/api/Transaction/GetInpatientByStationCode") : _commonHelper.GetApiStr(1, "17");
            if (string.IsNullOrEmpty(apiStr))
            {
                _logger.Info(",获取API地址失败,接口编号-17");
                return null;
            }
            var data = new Dictionary<string, string>
            {
               { "StationCode",stationCode}
            };
            return await GetInpatientApiData(apiStr, data);
        }

        /// <summary>
        /// 获取指定时间的在院病人数据
        /// </summary>
        /// <param name="allDataFlag"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<List<InPatientDataView>> GetInpateintDataByDateTime(string allDataFlag, string hospitalID)
        {
            //呼叫APi获得数据
            var syncAPIConfigInfo = _syncAPIConfigRepository.GetSyncAPIConfigByApiID(1);
            var apiStr = _config.Value.Status == "2" ? (_config.Value.DataInterfaceAPI + "/api/Transaction/SyncInpatient") : syncAPIConfigInfo.APIAddress;
            if (string.IsNullOrEmpty(apiStr))
            {
                _logger.Info(",获取API地址失败,接口编号-1");
                return null;
            }
            //同步指定时间
            var resultSyncHour = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "SyncHour");
            var syncHour = 1;
            if (!StringCheck.IsNumeric(resultSyncHour) && !int.TryParse(resultSyncHour, out syncHour))
            {
                _logger.Error("小时数配置错误");
                return null;
            }
            var data = new Dictionary<string, string>
            {
                { "startDataTime", DateTime.Now.AddHours(-syncHour).ToString() },
                { "endDateTime", DateTime.Now.ToString() },
                { "allDataFlag", allDataFlag }
            };
            return await GetInpatientApiData(apiStr, data);
        }

        /// <summary>
        /// 获取在院病人
        /// </summary>
        /// <param name="apiStr"></param>
        /// <param name="data"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<List<InPatientDataView>> GetInpatientApiData(string apiStr, Dictionary<string, string> data)
        {
            //创建集合
            var interconnect_Data = new List<InPatientDataView>();
            if (string.IsNullOrEmpty(apiStr))
            {
                _logger.Info("获取API地址失败");
                return null;
            }
            _logger.Info("获取在院病人同步 API:[" + apiStr + "]参数:" + ListToJson.ToJson(data));
            var resultData = "";
            //获取环境 ,1 开发环境
            var systemOperatingEnvironment = await _appConfigSettingRepository.GetConfigSettingValue("Configs", "SystemOperatingEnvironment");
            if (systemOperatingEnvironment != "1")
            {
                //呼叫API获得数据
                try
                {
                    resultData = _commonHelper.GetInterconnectData(apiStr, data);
                }
                catch (Exception ex)
                {
                    _logger.Error("呼叫API,失败 ，API:[" + apiStr + "]参数: " + ListToJson.ToJson(data) + ex.ToString());
                    return null;
                }
            }
            else
            {
                //resultData = _ILogInfoServices.GetLog("19")[0].Logs;
                //resultData = ReadFile.ReadTxt(@"D:\SyncInpatient.json");
            }

            if (string.IsNullOrEmpty(resultData))
            {
                return null;
            }
            //打印接口
            var resultPringData = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "PrintInterfaceData").Result;

            if (resultPringData == "1")
            {
                _logger.Info("获取在院病人数据:" + resultData);
            }
            //将数据装化对象
            var responseResult = new ResponseResult();
            try
            {
                responseResult = JsonConvert.DeserializeObject<ResponseResult>(resultData);
            }
            catch (Exception ex)
            {
                _logger.Info("获取在院病人数据:" + resultData + "转换失败" + ex.ToString());
                return null;
            }
            if (responseResult.Data == null)
            {
                return null;
            }
            var responseResulData = responseResult.Data.ToString();
            if (responseResulData == "[]")
            {
                return interconnect_Data;
            }
            try
            {   //数据转化成InpatientDataView集合
                interconnect_Data = JsonConvert.DeserializeObject<List<InPatientDataView>>(responseResulData);
            }
            catch (Exception ex)
            {
                _logger.Error("Json格式化失败:" + ex.ToString());
                return null;
            }
            _logger.Info("转换Json数据完成，获得在院病人" + interconnect_Data.Count() + "条！");
            //过滤返回数据
            var interconnect_DataTempCount = interconnect_Data.Where(m => !string.IsNullOrEmpty(m.DepartmentCode) && !string.IsNullOrEmpty(m.StationCode) && !string.IsNullOrEmpty(m.BedNumber)).Count();
            _logger.Info("住院病人,有" + (interconnect_Data.Count() - interconnect_DataTempCount).ToString() + "条数据不符合要求！");
            //将数据返回
            return interconnect_Data;
        }

        #endregion 获得接口数据
        /// <summary>
        /// 获取医院科室 病区 床位
        /// </summary>
        /// <returns></returns>
        private async Task<HospitalBaseDictView> GetHospitalBaseDict()
        {
            var hospitalDictView = new HospitalBaseDictView
            {
                StationList = await _IStationListRepository.GetAllAsync<StationListInfo>(),
                BedList = await _IBedListRepository.GetAllAsync<BedListInfo>(),//床位信息
                DepartmentList = await _IDepartmentListRepository.GetAllAsync<DepartmentListInfo>() //科室信息
            };
            return hospitalDictView;
        }

        //获取同步需要的基本字典信息
        private HospitalBaseDictView GetinPatientBaseDict(InPatientDataView inPatientData, HospitalBaseDictView hospitalDictView)
        {
            var hospitalDict = new HospitalBaseDictView();
            var medicalStationList = hospitalDictView.StationList; //病区字典
            var medicalDepartmentList = hospitalDictView.DepartmentList; //科室字典
            var medicalBedList = hospitalDictView.BedList;//床位字典

            //获取Medical中的科室信息
            var tempMedicalDepartmentList = medicalDepartmentList.Where(m => m.DepartmentCode == inPatientData.DepartmentCode).ToList();
            if (tempMedicalDepartmentList.Count != 1)
            {
                return null;
            }

            //获取Medical中的病区信息
            var tempMedicalStationListInfo = medicalStationList.Where(m => m.StationCode == inPatientData.StationCode).ToList();
            if (tempMedicalStationListInfo.Count != 1)
            {
                return null;
            }

            var tempMedicalBedList = new List<BedListInfo>();
            //先在获取Medical中的床位信息
            tempMedicalBedList = medicalBedList.Where(m => m.BedNumber == inPatientData.BedNumber && m.StationID == tempMedicalStationListInfo[0].ID).ToList();
            if (tempMedicalBedList.Count <= 0)
            {
                //确认是否有虚拟病区
                var tempVirtualStationList = _virtualStationListRepository.GetVirtualStationInfoByStationID(tempMedicalStationListInfo[0].ID);
                if (tempVirtualStationList.Count > 0)
                {
                    //获取病区对应虚拟病区列表
                    foreach (var item in tempVirtualStationList)
                    {
                        tempMedicalBedList = medicalBedList.Where(m => m.BedNumber == inPatientData.BedNumber && item.VirtualStationID == m.StationID).ToList();
                        //找到虚拟病区床位
                        if (tempMedicalBedList.Count > 0)
                        {
                            //重新赋值真实病区
                            //获取虚拟病区
                            tempMedicalStationListInfo = medicalStationList.Where(m => m.ID == item.VirtualStationID).ToList();
                            break;
                        }
                    }
                }
            }

            //复制对象
            if (tempMedicalBedList.Count != 1)
            {
                return null;
            }
            hospitalDict.StationList = tempMedicalStationListInfo;
            hospitalDict.DepartmentList = tempMedicalDepartmentList;
            hospitalDict.BedList = tempMedicalBedList;
            return hospitalDict;
        }

        /// <summary>
        /// 判断获取的病人病区、科室、床位信息是否正常
        /// </summary>
        /// <param name="hospitalBaseDict"></param>
        /// <returns></returns>
        private bool CheckPatientBaseDict(HospitalBaseDictView hospitalBaseDict)
        {
            if (hospitalBaseDict == null)
            {
                return false;
            }

            if (hospitalBaseDict.DepartmentList == null || hospitalBaseDict.DepartmentList.Count() <= 0)
            {
                return false;
            }
            if (hospitalBaseDict.StationList == null || hospitalBaseDict.StationList.Count <= 0)
            {
                return false;
            }
            if (hospitalBaseDict.BedList == null || hospitalBaseDict.BedList.Count <= 0)
            {
                return false;
            }
            return true;
        }

        /// <summary>
        /// 新增住院病人逻辑
        /// </summary>
        private async Task<InpatientDataInfo> NewInpatientData(InPatientDataView inPatientData, HospitalBaseDictView hospitalBaseDict, PatientBasicDataInfo patient, string hospitalID)
        {
            var inPatientChangeList = new List<InPatientChangeViewInfo>();
            var medicalDepartmentList = hospitalBaseDict.DepartmentList;
            var medicalStationList = hospitalBaseDict.StationList;
            var medicalBedList = hospitalBaseDict.BedList;
            var t = new InpatientDataInfo();
            t.ID = t.GetId();
            t.PatientID = patient.PatientID;
            t.HospitalID = hospitalID;
            t.CaseNumber = inPatientData.CaseNumber ?? "";
            t.LocalCaseNumber = t.CaseNumber;
            t.ChartNo = inPatientData.ChartNo ?? "";
            t.NumberOfAdmissions = 0; //住院次数
            t.DepartmentListID = medicalDepartmentList[0].ID;
            t.StationID = medicalStationList[0].ID;
            t.BedID = medicalBedList[0].ID;
            t.BedNumber = inPatientData.BedNumber ?? "";
            t.ICUFlag = medicalBedList[0].ICUFlag ?? "";
            t.ICDCode = "";//新增病人数据，不写ICDCode ，为了区分数据是否从诊断接口过来
            t.Diagnosis = inPatientData.Diagnosis ?? "";
            t.AttendingPhysicianID = inPatientData.AttendingPhysicianID ?? "";
            t.Age = inPatientData.Age ?? 0;
            t.AgeDetail = inPatientData.AgeDetail;//不到1岁的显示X月X天-LS
            t.BillingPattern = inPatientData.BillingPattern ?? "";
            t.AdmissionDate = inPatientData.AdmissionDate; //住院日期
            t.AdmissionTime = inPatientData.AdmissionTime; //住院时间
            t.DeleteFlag = "";
            t.DischargeDate = null;
            t.DischargeTime = null;
            if (string.IsNullOrEmpty(inPatientData.NursingLevel))
            {
                t.NursingLevel = "";
            }
            else
            {
                t.NursingLevel = inPatientData.NursingLevel;
            }
            if (string.IsNullOrEmpty(t.ChartNo))
            {
                _syncLogService.InsertSyncLog(3, "1", "患者chartNo有误", "患者chartNo有误", "chartNo||caseNumber" + inPatientData.ChartNo +"||"+inPatientData.CaseNumber, "TongBu", false);
            }
            //获取之前的住院次数
            var numberOfAdmissions = await _IInpatientDataRepository.GetPatientDataCountByChartNo(t.ChartNo);
            if (!numberOfAdmissions.HasValue)
            {
                _syncLogService.InsertSyncLog(3, "1", "患者chartNo有误", "患者chartNo有误", "chartNo||caseNumber" + inPatientData.ChartNo + "||" + inPatientData.CaseNumber, "TongBu", false);
            }
            else
            {
                t.NumberOfAdmissions = numberOfAdmissions.Value + 1;
            }
            //新入院病人-在院状态（30)
            //日志写入
            _syncLogService.InsertSyncLog(3, "1", "入院||NewInpatientData", "inHospitalStatus||30", inPatientData.CaseNumber + "||在院状态，从 []更新为[30] ", "TongBu", false);
            t.InHospitalStatus = 30;
            t.NursingProcedureCode = ""; //病人护理程序码
            t.ModifyPersonID = MODIFYPERSONID;
            t.ModifyDate = DateTime.Now;
            _unitOfWork.GetRepository<InpatientDataInfo>().Insert(t);
            inPatientChangeList.Add(InPatientChangeView(t.ID, t.CaseNumber, "新增", PatientType.PatientInsert));//存储病人变化信息
            return t;
        }

        /// <summary>
        /// 修改病人信息
        /// </summary>
        /// <param name="medicalInPatientData"></param>
        /// <param name="inPatientData"></param>
        /// <param name="hospitalBaseDict"></param>
        /// <param name="patientListIconList"></param>
        /// <param name="patientID"></param>
        /// <param name="tableName"></param>
        /// <param name="eventSettings"></param>
        /// <param name="hospitalID"></param>
        /// <param name="modifyPersonID"></param>
        /// <returns></returns>
        private async Task<Tuple<bool, List<PatientProfile>, List<MessageModel>>> UpdateInpatientData(InpatientDataInfo medicalInPatientData
            , InPatientDataView inPatientData, HospitalBaseDictView hospitalBaseDict
            , List<PatientListIconInfo> patientListIconList
            , string patientID, string tableName, List<EventSettingInfo> eventSettings
            , string hospitalID, string modifyPersonID)
        {
            var patientProfileList = new List<PatientProfile>();
            var messageModelList = new List<MessageModel>();
            var upDataDBFlag = false;
            bool otherFlag = false;
            //从字典中获取数据,外层已经判断了是否有数据
            var departmentInfo = hospitalBaseDict.DepartmentList[0];
            var stationInfo = hospitalBaseDict.StationList[0];
            var bedInfo = hospitalBaseDict.BedList[0];
            var oldSataionID = medicalInPatientData.StationID;
            var oldDepartmentID = medicalInPatientData.DepartmentListID;
            var oldBedID = medicalInPatientData.BedID;
            var oldBedNumber = medicalInPatientData.BedNumber;
            //获取病人异动日志
            var inpatientLog = await _inpatientLogRepository.GetByCaseNumberAsync(medicalInPatientData.CaseNumber);
            _logger.Info(tableName + " 更新住院病人CaseNumber[" + medicalInPatientData.CaseNumber + "]信息!");
            //入院时间不一致  删除旧入院事件  新增新入院事件 inpatientData入院事件调整 体温单异动添加
            if (medicalInPatientData.AdmissionDate != inPatientData.AdmissionDate || medicalInPatientData.AdmissionTime != inPatientData.AdmissionTime)
            {
                var tempOldAdmissionDate = medicalInPatientData.AdmissionDate;
                var tempOldAdmissionTime = medicalInPatientData.AdmissionTime;
                //日志写入
                _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "AdmissionDate", medicalInPatientData.CaseNumber + "||入院日期发生改变，从 [" + medicalInPatientData.AdmissionDate.Add(medicalInPatientData.AdmissionTime) + "]更新为[" + inPatientData.AdmissionDate.Add(inPatientData.AdmissionTime), "TongBu", false);
                upDataDBFlag = true;
                medicalInPatientData.AdmissionDate = inPatientData.AdmissionDate;
                medicalInPatientData.AdmissionTime = inPatientData.AdmissionTime;
                //  先删除 再写入
                await _patientEventCommonService.DelInpatientEvent(medicalInPatientData.ID, 2872, tempOldAdmissionDate, tempOldAdmissionTime, "TongBu");
                await SetInpatientLogAndEvent(inpatientLog, medicalInPatientData, medicalInPatientData.StationID, medicalInPatientData.DepartmentListID
                     , medicalInPatientData.BedID, medicalInPatientData.BedNumber, medicalInPatientData.AdmissionDate.Add(medicalInPatientData.AdmissionTime), EVENTSETTING_ASSESSLISTID_ADMISSION, eventSettings);
            }
            //转病区
            if (medicalInPatientData.StationID != stationInfo.ID)
            {
                //写入日志
                _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "Station", medicalInPatientData.CaseNumber + "||病区发生变化，从[" + medicalInPatientData.StationID + "]更新为[" + stationInfo.ID + "]", "TongBu", false);
                var markView = MondifyInpateintToProfileMark(medicalInPatientData, inPatientData);
                MarkViewList.Add(markView);
                upDataDBFlag = true;
                medicalInPatientData.StationID = stationInfo.ID;
                //转科 ，护理流程码置空
                medicalInPatientData.NursingProcedureCode = "";
                //存储病人转科信息  发送MQ使用
                InPatientChangeList.Add(InPatientChangeView(medicalInPatientData.ID, medicalInPatientData.CaseNumber, medicalInPatientData.NursingLevel, PatientType.PatientTransfer));
                var url = await _aPISettingRepository.GetSettingBySettingCode("MQSolutionServer", "PushMessage");
                if (!string.IsNullOrEmpty(url))
                {
                    // 发送患者转科消息
                    var sendMsgView = new SendMessageView
                    {
                        MessageType = MessageType.BROADCAST,
                        ExchangeName = "PatientTransfer",
                        Body = medicalInPatientData.ID
                    };
                    await HttpHelper.HttpPostAsync(url, ListToJson.ToJson(sendMsgView), "application/json");
                }
            }
            //转科
            if (medicalInPatientData.DepartmentListID != departmentInfo.ID)
            {
                //写入日志
                _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "DepartmentList", medicalInPatientData.CaseNumber + "||科室发生变化，从[" + medicalInPatientData.DepartmentListID + "]更新为[" + departmentInfo.ID + "]", "TongBu", false);
                upDataDBFlag = true;
                medicalInPatientData.DepartmentListID = departmentInfo.ID;
                //转科 ，护理流程码置空
                medicalInPatientData.NursingProcedureCode = "";
            }
            //转床
            if (medicalInPatientData.BedID != bedInfo.ID || medicalInPatientData.BedNumber != (inPatientData.BedNumber ?? ""))
            {
                _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "Bed", medicalInPatientData.CaseNumber + "||床位发生变化，床号从[" + medicalInPatientData.BedNumber + "]更新为[" + bedInfo.BedNumber + "]", "TongBu", false);
                upDataDBFlag = true;
                medicalInPatientData.BedID = bedInfo.ID;
                medicalInPatientData.BedNumber = inPatientData.BedNumber ?? "";
                if (hospitalBaseDict != null && hospitalBaseDict.BedList != null && hospitalBaseDict.BedList.Count > 0)
                {
                    _logger.Info("床位信息" + ListToJson.ToJson(hospitalBaseDict.BedList));
                    //判断床位上是否有其他在院病人，如果有，先移除
                    var inPatientByBedList = await _IInpatientDataRepository.GetInpatientListByBedIDAsync(hospitalBaseDict.BedList[0].ID, hospitalID);
                    var sameBed = inPatientByBedList.Find(m => m.CaseNumber == inPatientData.CaseNumber);
                    if (sameBed != null)
                    {
                        //日志写入
                        _syncLogService.InsertSyncLog(3, "1", "转床||bedNumberChange", "inHospitalStatus||50", inPatientData.CaseNumber + "||在院状态，从 [" + medicalInPatientData.InHospitalStatus + "]更新为[50] ", "TongBu", false);
                        //先让在床病人出科，让新病人入科
                        medicalInPatientData.InHospitalStatus = 50;
                    }
                }
            }
            if (medicalInPatientData.ChartNo != (inPatientData.ChartNo ?? ""))
            {
                _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "ChartNo", medicalInPatientData.CaseNumber + "||ChartNo[" + medicalInPatientData.ChartNo + "]更新为[" + inPatientData.ChartNo + "]", "TongBu", false);
                medicalInPatientData.ChartNo = inPatientData.ChartNo ?? "";
                otherFlag = true;
                upDataDBFlag = true;
            }
            if (medicalInPatientData.PatientID != (patientID ?? ""))
            {
                otherFlag = true;
                upDataDBFlag = true;
                medicalInPatientData.PatientID = patientID ?? "";
                _logger.Info("PatientID变化");
            }

            if (medicalInPatientData.ICUFlag != (bedInfo.ICUFlag ?? ""))
            {
                _logger.Info("ICUFlag变化");
                medicalInPatientData.ICUFlag = bedInfo.ICUFlag ?? "";
                otherFlag = true;
                upDataDBFlag = true;
            }
            //病人转科或者出院召回
            if (medicalInPatientData.InHospitalStatus > 40)
            {
                //日志写入
                _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "inHospitalStatus||30", inPatientData.CaseNumber + "||在院状态，从 [" + medicalInPatientData.InHospitalStatus + "]更新为[30] 病人转科或者出院召回 置空出院时间 删除出院患者事件", "TongBu", false);
                medicalInPatientData.InHospitalStatus = 30;
                medicalInPatientData.DischargeDate = null;
                medicalInPatientData.DischargeTime = null;
                await _patientEventCommonService.DeleteInpatientEventAndLog(medicalInPatientData.CaseNumber, 2873, "TongBu", 1, hospitalID);
                upDataDBFlag = true;
            }
            //接口数据无出院时间 CCC库为出院状态 走出院召回  在院状态为 30 删除出院患者事件
            if (!inPatientData.DischargeDate.HasValue && !inPatientData.DischargeTime.HasValue && 
                medicalInPatientData.DischargeDate.HasValue&& medicalInPatientData.DischargeTime.HasValue
                && medicalInPatientData.InHospitalStatus.Value==40)
            {
                _logger.Info("预出院召回 || caseNumber||"+ medicalInPatientData.CaseNumber);
                //日志写入
                _syncLogService.InsertSyncLog(3, "1", "预出院召回", "inHospitalStatus||30", inPatientData.CaseNumber + "||在院状态，从 [" + medicalInPatientData.InHospitalStatus + "]更新为[30] ", "TongBu", false);
                medicalInPatientData.InHospitalStatus = 30;
                medicalInPatientData.DischargeDate = null;
                medicalInPatientData.DischargeTime = null;
                await _patientEventCommonService.DeleteInpatientEventAndLog(medicalInPatientData.CaseNumber, 2873, "TongBu", 1, hospitalID);
                upDataDBFlag = true;
            }
            var iCDCode = (medicalInPatientData.ICDCode ?? "").Trim();
            //病人入院后，如果诊断编码为空，才进行诊断更新，说明可能没有从诊断接口获取诊断数据
            //如果ICDCode存在，说明已经从诊断接口获得了数据，以诊断接口的数据为准
            if (medicalInPatientData.Diagnosis != (inPatientData.Diagnosis ?? "") && string.IsNullOrEmpty(iCDCode))
            {
                medicalInPatientData.Diagnosis = inPatientData.Diagnosis ?? "";
                otherFlag = true;
                upDataDBFlag = true;
                //更新病历异动记录
                await _patientDiagnosisService.UpdateEmrFileListAsync(medicalInPatientData.ID);
            }
            //从新计算NumberOfAdmissions
            var numberOfAdmissions = await _IInpatientDataRepository.GetPatientDataCountByChartNo(inPatientData.ChartNo);
            if (!numberOfAdmissions.HasValue)
            {
                _syncLogService.InsertSyncLog(3, "1", "inPatientData.ChartNo有误", "numberOfAdmissions", inPatientData.CaseNumber + "||inPatientData.ChartNo|| "+ inPatientData.ChartNo, "TongBu", false);
            }
            if (numberOfAdmissions.HasValue && numberOfAdmissions != medicalInPatientData.NumberOfAdmissions)
            {
                medicalInPatientData.NumberOfAdmissions = numberOfAdmissions.Value;
            }
            if (medicalInPatientData.BillingPattern != (inPatientData.BillingPattern ?? ""))
            {
                medicalInPatientData.BillingPattern = inPatientData.BillingPattern ?? "";
                otherFlag = true;
                upDataDBFlag = true;
            }

            if (medicalInPatientData.Age != (inPatientData.Age ?? 0))
            {
                medicalInPatientData.Age = inPatientData.Age ?? 0;
                otherFlag = true;
                upDataDBFlag = true;
            }

            if (medicalInPatientData.AgeDetail != (inPatientData.AgeDetail ?? ""))
            {
                medicalInPatientData.AgeDetail = inPatientData.AgeDetail;
                otherFlag = true;
                upDataDBFlag = true;
            }

            if (!string.IsNullOrEmpty(inPatientData.AttendingPhysicianID) && medicalInPatientData.AttendingPhysicianID != inPatientData.AttendingPhysicianID)
            {
                medicalInPatientData.AttendingPhysicianID = inPatientData.AttendingPhysicianID;
                otherFlag = true;
                upDataDBFlag = true;
            }

            //护理级别进行转换
            if (!string.IsNullOrEmpty(inPatientData.NursingLevel) && medicalInPatientData.NursingLevel.Trim() != inPatientData.NursingLevel.Trim())
            {
                _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "NursingLevel", medicalInPatientData.CaseNumber + "|护理级别发生变化，从[" + medicalInPatientData.NursingLevel + "]更新为[" + inPatientData.NursingLevel + "]", "TongBu", false);
                upDataDBFlag = true;
                medicalInPatientData.NursingLevel = inPatientData.NursingLevel;
                //增加PatientProfiles数据(护理级别变化)
                patientProfileList.Add(CreateNursingLeveProfile(medicalInPatientData, patientListIconList, hospitalID));
                //如果护理级别发生变化，停止护理问题
                var inPatientChange = InPatientChangeView(medicalInPatientData.ID, medicalInPatientData.CaseNumber, medicalInPatientData.NursingLevel, PatientType.PatientLevelChange);
                InPatientChangeList.Add(inPatientChange);//存储病人变化信息
            }
            if (!upDataDBFlag)
            {
                return new Tuple<bool, List<PatientProfile>, List<MessageModel>>(false, null, null);
            }
            medicalInPatientData.ModifyPersonID = MODIFYPERSONID;
            medicalInPatientData.ModifyDate = DateTime.Now;
            _logger.Info("更新在院病人数据:ChartNo" + medicalInPatientData.ChartNo);
            if (otherFlag)
            {
                InPatientChangeList.Add(InPatientChangeView(medicalInPatientData.ID, medicalInPatientData.CaseNumber, medicalInPatientData.NursingLevel, PatientType.PatientOtherChange)); //存储更新的病人信息
            }
            //写病人事件
            await SetInPatientEvent(inpatientLog, medicalInPatientData, oldSataionID, oldDepartmentID, oldBedID, oldBedNumber, eventSettings);
            return new Tuple<bool, List<PatientProfile>, List<MessageModel>>(true, patientProfileList, messageModelList);
        }

        /// <summary>
        /// 处理病人正常住院逻辑
        /// </summary>
        /// <param name="medicalInPatientData"></param>
        /// <param name="inPatientData"></param>
        /// <param name="eventSettings"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<SyncInpatientDataView> SyncInpatientDischarge(InpatientDataInfo medicalInPatientData
            , InPatientDataView inPatientData, List<EventSettingInfo> eventSettings, string hospitalID)
        {
            var patientProfileList = new List<PatientProfile>();
            var inpatientChangeList = new List<InPatientChangeViewInfo>();
            var markViewList = new List<MarkView>();
            medicalInPatientData.DischargeDate = inPatientData.DischargeDate;//出院日期
            medicalInPatientData.DischargeTime = inPatientData.DischargeTime;//出院时间

            if (!medicalInPatientData.InHospitalStatus.HasValue || medicalInPatientData.InHospitalStatus.Value < 40)
            {
                //日志写入
                _syncLogService.InsertSyncLog(3, "1", "预出院||SyncInpatientDischarge", "inHospitalStatus||40", inPatientData.CaseNumber + "||在院状态，从 [" + medicalInPatientData.InHospitalStatus + "]更新为[40] ", "TongBu", false);
                medicalInPatientData.InHospitalStatus = 40;
                //出院时最新诊断
                await _patientDiagnosisService.SyncPatientDiagnosisByCaseNumber(medicalInPatientData.CaseNumber);
            }
            //病人出院，更新Mark
            var markView = DischargeToProfileMark(medicalInPatientData, hospitalID);
            markViewList.Add(markView);
            //患者出院，清除Profile
            markView = DoDischargeDealDischargeProfile(medicalInPatientData);
            markViewList.Add(markView);
            _logger.Info("出院病人信息：" + ListToJson.ToJson(medicalInPatientData));
            var dischargeDateTime = CreateDateTime(medicalInPatientData.DischargeDate, medicalInPatientData.DischargeTime);
            //写InpatientLog
            await InsertInpatientEventInfo(medicalInPatientData, "Discharge", dischargeDateTime, eventSettings);
            //处理病人出院，呼叫信息
            var inPatientChange = InPatientChangeView(medicalInPatientData.ID, medicalInPatientData.CaseNumber, medicalInPatientData.NursingLevel, PatientType.PatientDischarged);
            inpatientChangeList.Add(inPatientChange);
            var syncInpatientDataView = new SyncInpatientDataView
            {
                RetureFlag = true,
                PatientProfileList = patientProfileList,
                InPatientChangeViewList = inpatientChangeList,
                MarkViewList = markViewList
            };
            return syncInpatientDataView;
        }

        private InPatientChangeViewInfo InPatientChangeView(string inPatientId, string caseNumber, string nursingLevel, PatientType patientType)
        {
            var inpatientChangeViewInfo = new InPatientChangeViewInfo()
            {
                InPatientID = inPatientId,
                CaseNumber = caseNumber,
                PatientType = patientType,
                NursingLevel = nursingLevel
            };
            return inpatientChangeViewInfo;
        }

        /// <summary>
        /// 生成DateTime
        /// </summary>
        /// <param name="DischargeDate"></param>
        /// <param name="dateTime"></param>
        /// <returns></returns>
        private DateTime CreateDateTime(DateTime? date, TimeSpan? time)
        {
            var times = new TimeSpan(0, 0, 0);
            var dates = new DateTime(1900, 01, 01);
            if (time.HasValue)
            {
                _logger.Info("转换时间:" + time.ToString());
                times = new TimeSpan(time.Value.Hours, time.Value.Minutes, time.Value.Seconds);
            }
            else
            {
                _logger.Info("时间为空");
            }

            if (date.HasValue)
            {
                _logger.Info("转换日期" + date.ToString());
                dates = date.Value.Date.Add(times);
            }
            else
            {
                _logger.Info("日期为空");
            }

            return dates;
        }

        private PatientProfile CreateNursingLeveProfile(InpatientDataInfo inPatient, List<PatientListIconInfo> patientListIconList, string hospitalID)
        {
            var nursingLevel = "NursingLevel";
            if (!string.IsNullOrEmpty(inPatient.NursingLevel))
            {
                nursingLevel += inPatient.NursingLevel;
            }
            var patientListIcon = patientListIconList.Where(m => m.IdentifyCategory == nursingLevel).FirstOrDefault();
            if (patientListIcon == null)
            {
                return null;
            }
            return CreateProfile(inPatient, nursingLevel, patientListIcon.IdentifyID.Value, "", hospitalID);
        }

        private PatientProfile CreateProfile(InpatientDataInfo inPatient
            , string subSerialNumber, int assessListID, string assessValue, string hospitalID)
        {
            var patientProfile = new PatientProfile
            {
                HospitalID = hospitalID,
                InpatientID = inPatient.ID,
                CaseNumber = inPatient.CaseNumber,
                ChartNo = inPatient.ChartNo,
                PatientID = inPatient.PatientID,
                ModelName = "HIS",
                Source = "I",
                ProfileDate = inPatient.AdmissionDate,
                ProfileTime = inPatient.AdmissionTime,
                AutoAddFlag = "",
                Note = "",
                ModifyPersonID = MODIFYPERSONID,
                SerialNumber = inPatient.CaseNumber + "_" + subSerialNumber,
                AssessListID = assessListID,
                AssessValue = assessValue
            };
            return patientProfile;
        }

        //出院ProfileMark
        private MarkView DischargeToProfileMark(InpatientDataInfo inpatient, string hospitalID)
        {
            Dictionary<string, string> dict = new Dictionary<string, string>
            {
                { "chartNo", inpatient.ChartNo }
            };
            var dischargeProfileMarkAPI = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "DischargeProfileMarkAPI").Result;
            dischargeProfileMarkAPI = _config.Value.Status == "2" ? _config.Value.SyncMedicalApi + "/api/ProfileMark/Discharge" : dischargeProfileMarkAPI;
            _logger.Info("出院呼叫ProfileMar地址||" + dischargeProfileMarkAPI);
            var markData = GetKeyValue(dict);
            var markView = new MarkView
            {
                InpatientData = inpatient,
                Url = dischargeProfileMarkAPI,
                MarkDatas = markData,
                DataType = "出院"
            };
            return markView;
        }
        private MarkView DoDischargeDealDischargeProfile(InpatientDataInfo inpatient)
        {
            //更新PatientProfile
            Dictionary<string, string> dict = new Dictionary<string, string>
                {
                    { "chartNo", inpatient.ChartNo },
                    { "dischargeDate", inpatient.DischargeDate.Value.ToString("yyyy-MM-dd") },
                    { "inpatientID",inpatient.ID }
                };

            //从配置当中获取数据 梁宝华 2020-04-29
            var api = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "DealDischargeProfile").Result;
            var markData = GetKeyValue(dict);
            var markView = new MarkView
            {
                InpatientData = inpatient,
                Url = api,
                MarkDatas = markData,
                DataType = "出院"
            };
            return markView;
        }

        /// <summary>
        /// Mark数据呼叫API
        /// </summary>
        /// <param name="markViews"></param>
        private void CallMarkAPI(List<MarkView> markViews)
        {
            if (markViews == null || markViews.Count <= 0)
            {
                return;
            }
            foreach (var markView in markViews)
            {
                _logger.Info("异动Mark,CaseNumber=" + markView.InpatientData.CaseNumber + " 异动类型DataType:" + markView.DataType);
                if (markView.Url == "")
                {
                    _logger.Error("呼叫Mark，API为空,数据类型：" + markView.DataType);
                    return;
                }

                var url = markView.Url + "?" + markView.MarkDatas;
                _logger.Info("更新MarkUrl:" + url);
                try
                {
                    //写同步日志
                    var syncLog = _commonHelper.SaveLog(url,
                        "", markView.InpatientData.ID, markView.InpatientData.CaseNumber, _unitOfWork, true, true);
                    WebRequestSugar wrs = new WebRequestSugar();
                    string result = wrs.SendObjectAsJsonInBody(url, null);
                    //更新同步日志状态
                    _commonHelper.GetAPIExecResult(result, syncLog, _unitOfWork);
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "写ProfileMark失败，API" + url);
                    return;
                }
            }
        }

        private string GetKeyValue(Dictionary<string, string> dict)
        {
            string result = "";
            List<string> list = new List<string>();
            foreach (var item in dict)
            {
                list.Add(item.Key + "=" + item.Value);
            }
            result = string.Join("&", list.ToArray());
            return result;
        }
        /// <summary>
        /// 插入患者事件 --写患者事件的同时，会写一笔InpatientLog日志到表中
        /// </summary>
        /// <param name="inpatient"></param>
        /// <param name="logCode"></param>
        /// <param name="logDateTime"></param>
        /// <param name="eventSettings"></param>
        /// <returns></returns>
        private async Task InsertInpatientEventInfo(InpatientDataInfo inpatient, string logCode, DateTime logDateTime
            , List<EventSettingInfo> eventSettings)
        {
            var setting = eventSettings.Find(m => m.LogCode == logCode);
            if (setting == null)
            {
                _logger.Error("病人事件LogCode配置异常,LogCode:" + logCode + "请确认EventSetting是否已经配置");
                return;
            }
            var assessListIDs = new List<int>
            {
                2873,2876,2872
            };
            //判断出院事件是否存在
            if (assessListIDs.Contains(setting.AssessListID))
            {
                var patientEvents = await _patientEventRepository.GetEventByAssessListIDAsync(inpatient.ID, setting.AssessListID);
                foreach (var item in patientEvents)
                {
                    await _patientEventCommonService.DeleteInpatientEventAndLogAsync(item.InpatientID, item.PatientEventID, _config.Value.HospitalID, _config.Value.Language, "TongBu");
                }
            }
            //远程调用MedicalAPI中的保存患者事件的公共方法
            await _patientEventCommonService.CallEventAPI(inpatient, inpatient.StationID, inpatient.DepartmentListID, inpatient.BedID, inpatient.BedNumber
                , logDateTime, setting.AssessListID, logCode, "", inpatient.ID + logCode);
        }

        /// <summary>
        /// 同步出院病人，这个是银川医生下医嘱的出院时间
        /// </summary>
        /// <param name="inpatientData"></param>
        /// <returns></returns>
        private async Task<bool> SyncDischargedPatient(List<InpatientInfoView> inpatientData, string hospitalID)
        {
            _logger.Info("同步出院病人[" + inpatientData.Count() + "]人");
            inpatientData = inpatientData.Where(m => m.DischargeDateTime <= DateTime.Now).ToList();
            _logger.Info("预出院人数排除后出院病人[" + inpatientData.Count() + "]人");
            var dischargeFiag = false;
            var eventSettings = await _eventSettingRepository.GetAllAsync<EventSettingInfo>();
            var caseNumberArray = inpatientData.Select(m => m.CaseNumber).ToArray();
            //获取当前同步出院病人的患者事件日志集合
            var inpatientLogs = await _inpatientLogRepository.GetByCaseNumbersAsync(caseNumberArray);
            var inpatientDataInfoList = await _IInpatientDataRepository.GetInpatientDataByCaseNumberListAsync(caseNumberArray.ToList());
            var disChangeEvents = await _patientEventRepository.GetDataByCaseNumbers(caseNumberArray.ToList(), EVENTSETTING_ASSESSLISTID_DISCHARGE);
            //获得病人标签
            foreach (var item in inpatientData) //记录作业正在执行日志
            {
                _logger.Info("开始同步住院号为:" + item.CaseNumber + "的患者出院信息");
                var inpatientDataInfo = inpatientDataInfoList.Find(m => m.CaseNumber == item.CaseNumber);
                if (inpatientDataInfo == null)
                {
                    continue;
                }
                var hisDisChargeDate = item.DischargeDateTime.Date;
                var hisDisChargeTime = item.DischargeDateTime.TimeOfDay;
                if (hisDisChargeDate == inpatientDataInfo.DischargeDate && hisDisChargeTime == inpatientDataInfo.DischargeTime)
                {
                    continue;
                }
                if (inpatientDataInfo.InHospitalStatus == 60)
                {
                    _logger.Info("同步出院病人||item.CaseNumber||" + item.CaseNumber + "病人已正式出院||InHospitalStatus=60");
                }
                var disChangeEvent = disChangeEvents.Find(m =>m.CaseNumber== item.CaseNumber&& m.OccurDate == hisDisChargeDate && m.OccurTime == hisDisChargeTime && m.DeleteFlag == "*");
                if (disChangeEvent != null)
                {
                    //日志写入
                    _syncLogService.InsertSyncLog(3, "1", "出院重复同步||SyncInpatientDischarge", "inHospitalStatus||"+ inpatientDataInfo.InHospitalStatus, "CaseNumer||" + inpatientDataInfo.CaseNumber  + "hisDisChargeDate||" + hisDisChargeDate + "hisDisChargeTime||" + hisDisChargeTime, "TongBu", false);
                    continue;
                }
                //获取病人在院病人数据
                var inpatientCount = await _IInpatientDataRepository.GetInpatientCountByStationID(inpatientDataInfo.StationID);
                //如果只有一个病人，则判断这个病人是否在院
                if (inpatientCount == 1)
                {
                    //获取病区在院病人
                    var hisInpatientData = await GetInpateintDataByStationCode(item.StationCode, hospitalID);
                    if (inpatientData == null || inpatientData.Count < 1)
                    {
                        dischargeFiag = true;
                    }
                }
                if (!inpatientDataInfo.DischargeDate.HasValue)
                {
                    dischargeFiag = true;
                }
                if (dischargeFiag)
                {
                    var hisView = new InPatientDataView
                    {
                        CaseNumber = item.CaseNumber,
                        ChartNo = item.ChartNo,
                        DischargeDate = hisDisChargeDate,
                        DischargeTime = hisDisChargeTime
                    };
                    var syncInpatientDataView = await SyncInpatientDischarge(inpatientDataInfo, hisView, eventSettings, hospitalID);
                    //异动Mark
                    CallMarkAPI(syncInpatientDataView.MarkViewList);
                    CheckInPatient(syncInpatientDataView.InPatientChangeViewList);
                }
                //筛出当前病人的患者事件日志
                var ipLogs = inpatientLogs.Where(m => m.CaseNumber == item.CaseNumber).ToList();
                //开始同步的逻辑，所以需要更改这个循环
                await SyncInpatientDischargeEvent(ipLogs, item, inpatientDataInfo, eventSettings);
                try
                {
                    _unitOfWork.SaveChanges();
                }
                catch (Exception ex)
                {
                    _logger.Error("SyncDischargedPatient病人CaseNumber" + item.CaseNumber + "出院数据同步失败||" + ex.ToString());
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 出院,更新病人事件
        /// </summary>
        /// <param name="inpatientLogs"></param>
        /// <param name="item"></param>
        /// <param name="inpatientDataInfo"></param>
        /// <param name="eventSettings"></param>
        /// <returns></returns>
        private async Task<bool> SyncInpatientDischargeEvent(List<InpatientLogInfo> inpatientLogs, InpatientInfoView item, InpatientDataInfo inpatientDataInfo, List<EventSettingInfo> eventSettings)
        {
            _logger.Info("同步出院病人 CaseNumber:" + item.CaseNumber + " ChartNo:" + item.ChartNo);

            //如果有出院事件，或者死亡事件不再增加
            var assessListIds = new List<int>
            {
                EVENTSETTING_ASSESSLISTID_DISCHARGE,
                DeathAssessListId
            };
            var patientEvent = await _patientEventRepository.GetEventByAssessListIDs(inpatientDataInfo.ID, assessListIds);

            if (patientEvent != null && patientEvent.Count > 0)
            {
                var oldEvent = patientEvent.Find(m => m.OccurDate == item.DischargeDateTime.Date && m.OccurTime == item.DischargeDateTime.TimeOfDay);
                if (oldEvent != null)
                {
                    return true;
                }
                patientEvent.ForEach(m =>
                {
                    if (m.AssessListID == EVENTSETTING_ASSESSLISTID_DISCHARGE)
                    {
                        m.DeleteFlag = "*";
                        m.ModifyDate = DateTime.Now;
                    }
                });
            }
            //处理病人事件
            await SetInpatientLogAndEvent(inpatientLogs, inpatientDataInfo, inpatientDataInfo.StationID, inpatientDataInfo.DepartmentListID
                       , inpatientDataInfo.BedID
                      , inpatientDataInfo.BedNumber, item.DischargeDateTime, EVENTSETTING_ASSESSLISTID_DISCHARGE, eventSettings);

            //添加体温单异动
            await _dataTableEditListService.AddEditLog(inpatientDataInfo.ID, 0, "PatientEvent", 12, "", null);
            return true;
        }
        /// <summary>
        /// 出院病人事件同步
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncDischargedPatientsEventByDateTime()
        {
            //获取最后的同步时间
            var hospitalID = _config.Value.HospitalID;
            var nowDateTime = DateTime.Now;
            var startDateTime = DateTime.Now;
            var nowHour = nowDateTime.Hour;
            var hourArr = new int[] { 0, 6, 12, 18 };
            //获取出院数据API
            var syncAPIConfigInfo = _syncAPIConfigRepository.GetSyncAPIConfigByApiID(18);
            if (syncAPIConfigInfo == null)
            {
                _logger.Error("没有找到ApiID为18(出院数据API)的数据");
                return false;
            }
            var intervalminutes = syncAPIConfigInfo.IntervalMinutes;
            if (intervalminutes == 0)
            {
                intervalminutes = 120;
            }
            if (hourArr.Contains(nowHour))
            {
                startDateTime = nowDateTime.AddMinutes(-360);
            }
            else
            {
                startDateTime = nowDateTime.AddMinutes(-intervalminutes);
            }

            var dictionary = new Dictionary<string, string>
                 {
                    { "StartDateTime", startDateTime.ToString()},
                    { "EndDateTime", nowDateTime.ToString()},
                 };
            var hisData = new List<InpatientInfoView>();
            try
            {
                var address = _config.Value.Status == "2" ? (_config.Value.DataInterfaceAPI + "/api/Transaction/GetDischargedPatients") : syncAPIConfigInfo.APIAddress;
                hisData = await GetDischargedPatientsApiData(address, dictionary, hospitalID);
            }
            catch (Exception ex)
            {
                _logger.Error("获取出院病人数据失败" + ex.ToString());
                return false;
            }
            //所以在时间段内如果没有数据，则继续循环，直到获取数据。
            if (hisData != null && hisData.Count() > 0)
            {
                _logger.Info("出院同步||" + "开始时间:" + startDateTime.ToString() + " 结束时间:" + nowDateTime.ToString());
                if (!await SyncDischargedPatient(hisData, hospitalID))
                {
                    return false;
                }
            }
            try
            {
                _unitOfWorkOut.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error("更新最后同步时间失败" + ex.ToString());
                return false;
            }
            return true;
        }

        /// <summary>
        /// 获取出院数据后，json转model
        /// </summary>
        /// <param name="aPIAddress"></param>
        /// <param name="dictionary"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<List<InpatientInfoView>> GetDischargedPatientsApiData(string api, Dictionary<string, string> dictionary, string hospitalID)
        {
            _logger.Info("呼叫API获取出院数据");
            var interconnect_Data = new List<InpatientInfoView>();
            var hisData = await GetDischargeApiData(api, dictionary, hospitalID);
            _logger.Info("<问题验证>[出院数据时间段]" + JsonConvert.SerializeObject(dictionary) + "<问题验证>[出院数据内容]" + hisData);
            if (string.IsNullOrEmpty(hisData))
            {
                return interconnect_Data;
            }
            try
            {
                interconnect_Data = JsonConvert.DeserializeObject<List<InpatientInfoView>>(hisData);
            }
            catch (Exception ex)
            {
                _logger.Error("Json格式化失败:" + ex.ToString());
                return interconnect_Data;
            }
            _logger.Info("转换Json数据完成，获得出院数据" + interconnect_Data.Count() + "条！");
            return interconnect_Data;
        }

        /// <summary>
        /// 根据API获取出院数据
        /// </summary>
        /// <param name="api"></param>
        /// <param name="dictionary"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<string> GetDischargeApiData(string api, Dictionary<string, string> dictionarys, string hospitalID)
        {
            if (string.IsNullOrEmpty(api))
            {
                return "";
            }
            //获取环境 ,1 正式环境环境 2 测试环境
            var resultData = "";
            //获取环境 ,1 开发环境
            var systemOperatingEnvironment = _appConfigSettingRepository.GetConfigSettingValue("Configs", "SystemOperatingEnvironment").Result;
            if (systemOperatingEnvironment != "1")
            {
                //呼叫API获得数据
                resultData = _commonHelper.GetInterconnectData(api, dictionarys);
            }
            else
            {
                //resultData = _ILogInfoServices.GetLog("30")[0].Logs;
                resultData = ReadFile.ReadTxt(@"E:\MockData\银川出院.json");
            }

            var printInterfaceData = 0;
            //获取打印接口配置
            var resultPrintDate = await _appConfigSettingRepository.GetConfigSettingValue("Configs", "PrintInterfaceData");
            if (StringCheck.IsNumeric(resultPrintDate))
            {
                printInterfaceData = int.Parse(resultPrintDate);
            }

            if (printInterfaceData == 1)
            {
                _logger.Info("Api:" + api + "获取的出院数据" + ListToJson.ToJson(dictionarys) + "出院数据：" + resultData);
            }

            try
            {
                var result = JsonConvert.DeserializeObject<ResponseResult>(resultData);
                var resultDataStr = result.Data.ToString();
                if (resultDataStr == "" || resultDataStr == "[]" || resultDataStr == "[{}]" || resultDataStr == "{}")
                {
                    return "";
                }
                return resultDataStr;
            }
            catch (Exception ex)
            {
                _logger.Error("Api: " + api + "获取的出院数据ResponseResult" + ex.ToString());
                return "";
            }
        }
        //同步病人信息，呼叫Proflie
        private async Task<Tuple<List<PatientProfile>, List<MessageModel>>> CreateAddPatientProfile(InpatientDataInfo inpatientData
            , List<PatientListIconInfo> patientListIconList
            , int ageAssessListID
            , string modifyPersonID)
        {
            var hospitalID = _config.Value.HospitalID;
            var patientProfiles = new List<PatientProfile>();
            var sendMessageList = new List<MessageModel>();
            //增加PatientProfiles数据(年龄)
            if (inpatientData.Age != null)
            {
                patientProfiles.Add(_externalCommonService.CreateProfile(inpatientData, "Age", ageAssessListID
                    , inpatientData.Age.ToString(), hospitalID, modifyPersonID));
            }
            //增加PatientProfiles数据(护理级别)
            patientProfiles.Add(CreateNursingLeveProfile(inpatientData, patientListIconList, hospitalID));

            //增加主诊断集束护理
            if (!string.IsNullOrEmpty(inpatientData.ICDCode))
            {
                var ids = await _externalCommonService.GetAssessListIDByICDCode(inpatientData.ICDCode, inpatientData.DepartmentListID);

                foreach (var item in ids)
                {
                    var profile = _externalCommonService.CreateProfile(inpatientData, "Diagnosis", item, "", hospitalID, modifyPersonID);
                    patientProfiles.Add(profile);
                }
                if (patientProfiles.Count > 0)
                {
                    sendMessageList.Add(_externalCommonService.CreateDiagnosisMessage(inpatientData));
                }
            }
            return new Tuple<List<PatientProfile>, List<MessageModel>>(patientProfiles, sendMessageList);
        }

        //新增Mark数据组装
        private MarkView AddInpatientToProfileMark(InpatientDataInfo inpatient
            , InPatientDataView hisInPatient, PatientBasicDataInfo patient)
        {
            var hospitalID = _config.Value.HospitalID;
            Dictionary<string, string> dict = new Dictionary<string, string>
            {
                { "chartNo", inpatient.ChartNo },
                { "patientName", patient.PatientName },
                { "gender", patient.Gender },
                { "nursingLevel", inpatient.NursingLevel },
                { "bedNumber", inpatient.BedNumber },
                { "stationCode", hisInPatient.StationCode },
                { "departmenCode", hisInPatient.DepartmentCode },
                { "hospitalID", hospitalID },
                { "admissionDate", hisInPatient.AdmissionDate.Add(hisInPatient.AdmissionTime).ToString() }
            };
            var inpatientProfileMarkAPI = _config.Value.Status == "2" ? (_config.Value.SyncMedicalApi + "/api/ProfileMark/Inpatient") : _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "InpatientProfileMarkAPI").Result;
            var markData = GetKeyValue(dict);
            var markView = new MarkView
            {
                InpatientData = inpatient,
                Url = inpatientProfileMarkAPI,
                MarkDatas = markData,
                DataType = "新增住院病人"
            };
            return markView;
        }

        /// <summary>
        /// 写同步日志,及病人事件
        /// </summary>
        /// <param name="inpatientLogs">患者日志档</param>
        /// <param name="inpatientDataInfo">患者信息</param>
        /// <param name="stationID">病区ID</param>
        /// <param name="DepartmentID">科室ID</param>
        /// <param name="bedID">床位ID</param>
        /// <param name="bedNumber">床位号</param>
        /// <param name="eventDateTime">事件发生时间</param>
        /// <param name="assessListID">事件对应ID</param>
        /// <returns></returns>
        private async Task SetInpatientLogAndEvent(List<InpatientLogInfo> inpatientLogs, InpatientDataInfo inpatient
            , int stationID, int DepartmentID, int bedID, string bedNumber, DateTime eventDateTime
            , int assessListID, List<EventSettingInfo> eventSettings)
        {
            var eventSetting = eventSettings.Where(m => m.AssessListID == assessListID)
                                           .Select(t => new { t.ShowName, t.LogCode }).FirstOrDefault();
            if (eventSetting == null)
            {
                _logger.Warn("未能获取到EventSetting中AssessListID={0}的配置！", assessListID);
                return;
            }
            //判断记录是否存在
            inpatientLogs = inpatientLogs.Where(m => m.StationID == stationID && m.LogDateTime == eventDateTime
            && m.LogCode == eventSetting.LogCode).ToList();

            if (inpatientLogs.Count >= 1)
            {
                return;
            }
            //远程调用MedicalAPI中的保存患者事件的公共方法
            await _patientEventCommonService.CallEventAPI(inpatient, stationID, DepartmentID, bedID, bedNumber
                , eventDateTime, assessListID, eventSetting.LogCode, "", inpatient.ID + eventSetting.LogCode);
        }

        /// <summary>
        /// 写转病区，转科，转床事件
        /// </summary>
        /// <param name="medicalInPatientData">患者信息</param>
        /// <param name="oldSataionID">病区ID</param>
        /// <param name="oldDepartmentID">科室ID</param>
        /// <param name="oldBedID">床位ID</param>
        /// <param name="oldBedNumber">床位号</param>
        /// <param name="eventSettings">事件配置</param>
        /// <returns></returns>
        private async Task<bool> SetInPatientEvent(List<InpatientLogInfo> inpatientLogs, InpatientDataInfo medicalInPatientData, int oldSataionID, int oldDepartmentID, int oldBedID, string oldBedNumber, List<EventSettingInfo> eventSettings)
        {
            //写转科事件
            if (oldDepartmentID != medicalInPatientData.DepartmentListID)
            {
                await SetInpatientLogAndEvent(inpatientLogs, medicalInPatientData, oldSataionID, oldDepartmentID, oldBedID
                    , oldBedNumber, DateTime.Now, EVENTSETTING_ASSESSLISTID_TRANSOUTDEPT, eventSettings);

                await SetInpatientLogAndEvent(inpatientLogs, medicalInPatientData, medicalInPatientData.StationID, medicalInPatientData.DepartmentListID
                    , medicalInPatientData.BedID, medicalInPatientData.BedNumber, DateTime.Now, EVENTSETTING_ASSESSLISTID_TRANSINDEPT, eventSettings);
            }
            //写转病区事件
            if (oldSataionID != medicalInPatientData.StationID)
            {
                await SetInpatientLogAndEvent(inpatientLogs, medicalInPatientData, oldSataionID, oldDepartmentID, oldBedID
                    , oldBedNumber, DateTime.Now, EVENTSETTING_ASSESSLISTID_TRANSOUT, eventSettings);

                await SetInpatientLogAndEvent(inpatientLogs, medicalInPatientData, medicalInPatientData.StationID, medicalInPatientData.DepartmentListID
                     , medicalInPatientData.BedID, medicalInPatientData.BedNumber, DateTime.Now, EVENTSETTING_ASSESSLISTID_TRANSIN, eventSettings);
            }
            //写转床事件
            if (oldSataionID == medicalInPatientData.StationID && (oldBedNumber != medicalInPatientData.BedNumber || oldBedID != medicalInPatientData.BedID))
            {
                await SetInpatientLogAndEvent(inpatientLogs, medicalInPatientData, oldSataionID, oldDepartmentID
                       , oldBedID, oldBedNumber
                       , DateTime.Now, EVENTSETTING_ASSESSLISTID_TURNOUTBED, eventSettings);

                await SetInpatientLogAndEvent(inpatientLogs, medicalInPatientData, medicalInPatientData.StationID, medicalInPatientData.DepartmentListID
                , medicalInPatientData.BedID, medicalInPatientData.BedNumber, DateTime.Now, EVENTSETTING_ASSESSLISTID_TURNINBED, eventSettings);
            }
            return true;
        }

        //转科转床,Mark数据组装
        private MarkView MondifyInpateintToProfileMark(InpatientDataInfo inpatient
            , InPatientDataView hisInpatient)
        {
            var hospitalID = _config.Value.HospitalID;
            Dictionary<string, string> dict = new Dictionary<string, string>
            {
                { "chartNo", inpatient.ChartNo },
                { "nursingLevel", inpatient.NursingLevel },
                { "bedNumber", hisInpatient.BedNumber },
                { "stationCode", hisInpatient.StationCode },
                { "departmenCode", hisInpatient.DepartmentCode },
                { "hospitalID", hospitalID }
            };

            //从配置当中获取数据 梁宝华 2020-04-29
            var transferProfileMarkAPI = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "TransferProfileMarkAPI").Result;

            var markData = GetKeyValue(dict);
            var markView = new MarkView
            {
                InpatientData = inpatient,
                Url = transferProfileMarkAPI,
                MarkDatas = markData,
                DataType = "转科、转床、护理级别发生变化"
            };
            return markView;
        }

        //病人信息变化，调用相应的APi进行处理
        ////出院，或则护理级别发生变化，停止问题
        private void CheckInPatient(List<InPatientChangeViewInfo> inPatientChangeList)
        {
            foreach (var item in inPatientChangeList)
            {
                switch (item.PatientType)
                {
                    case PatientType.PatientInsert:
                    case PatientType.PatientOtherChange:
                        _commonHelper.CheckSchedulePatientInfo(item.InPatientID, item.CaseNumber, false); //病人信息发生变化，验证排程中的病人信息（延迟5分钟执行）
                        break;

                    case PatientType.PatientLevelChange:
                        _commonHelper.StopProblem(item.InPatientID, item.NursingLevel, true, MedicalEnumUtility.StopProblemType.NursingLevelChange);
                        break;

                    case PatientType.PatientTransfer:
                        _commonHelper.StopProblem(item.InPatientID, item.NursingLevel, true, MedicalEnumUtility.StopProblemType.Transfer);
                        _commonHelper.CheckSchedulePatientInfo(item.InPatientID, item.CaseNumber, true);
                        break;

                    case PatientType.PatientDischarged:
                        _commonHelper.StopProblem(item.InPatientID, item.NursingLevel, true, MedicalEnumUtility.StopProblemType.Discharge);
                        break;

                    default:
                        break;
                }
            }
        }

        /// <summary>
        /// 根据时间段同步出院患者（刷新用）
        /// </summary>
        /// <param name="startDateTime"></param>
        /// <param name="endDateTime"></param>
        /// <returns></returns>
        public async Task<bool> SyncDischargedByDateTime(DateTime? startDateTime, DateTime? endDateTime)
        {
            var hospitalID = _config.Value.HospitalID;
            var syncAPIConfigInfo = _syncAPIConfigRepository.GetSyncAPIConfigByApiID(18);
            if (syncAPIConfigInfo == null || string.IsNullOrEmpty(syncAPIConfigInfo.APIAddress))
            {
                return false;
            }
            var dictionary = new Dictionary<string, string>
                 {
                    { "StartDateTime", startDateTime.Value.ToString("yyyy-MM-dd HH:mm:ss")},
                    { "EndDateTime", endDateTime.Value.ToString("yyyy-MM-dd HH:mm:ss")},
                 };
            var hisData = new List<InpatientInfoView>();
            try
            {
                var address = _config.Value.Status == "2" ? (_config.Value.DataInterfaceAPI + "/api/Transaction/GetDischargedPatients") : syncAPIConfigInfo.APIAddress;
                hisData = await GetDischargedPatientsApiData(address, dictionary, hospitalID);
            }
            catch (Exception ex)
            {
                _logger.Error("刷新出院病人数据失败" + ex.ToString());
                return false;
            }
            if (hisData == null || hisData.Count() == 0)
            {
                return false;
            }
            return await SyncDischargedPatient(hisData, hospitalID);
        }
        /// <summary>
        /// 根据CaseNumber进行预出院同步
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncDischangeByCaseNumber(string caseNumber)
        {
            if (string.IsNullOrEmpty(caseNumber))
            {
                _logger.Error("根据CaseNumber同步出院病人方法 SyncDischangeByCaseNumber 参数为空");
                return false;
            };
            var hisData = await GetHisDischangeDataByCaseNumber(caseNumber);
            if (hisData == null)
            {
                return false;
            }
            var inpatientData = await _IInpatientDataRepository.GetAsyncByCaseNumber(caseNumber);
            if (inpatientData == null)
            {
                _logger.Error("inpatientData无该病人信息 caseNumber||" + caseNumber);
            };
            var eventSettings = await _eventSettingRepository.GetAllAsync<EventSettingInfo>();
            var hisView = new InPatientDataView
            {
                CaseNumber = hisData.CaseNumber,
                ChartNo = hisData.ChartNo,
                DischargeDate = hisData.DischargeDateTime.Date,
                DischargeTime = hisData.DischargeDateTime.TimeOfDay
            };
            if (inpatientData.DischargeDate != hisView.DischargeDate || inpatientData.DischargeTime != hisView.DischargeTime)
            {
                //出院时间发生改变 更新出院时间 重写出院患者事件
                if (inpatientData.DischargeDate.HasValue && inpatientData.DischargeTime.HasValue)
                {
                    _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "Dischange", inpatientData.CaseNumber + "||出院日期发生改变，从 [" + inpatientData.DischargeDate.Value.Add(inpatientData.DischargeTime.Value) + "]更新为[" + hisView.DischargeDate.Value.Add(hisView.DischargeTime.Value), "TongBu", false);
                }
                var syncInpatientDataView = await SyncInpatientDischarge(inpatientData, hisView, eventSettings, _config.Value.HospitalID);
                // 异动Mark
                CallMarkAPI(syncInpatientDataView.MarkViewList);
                CheckInPatient(syncInpatientDataView.InPatientChangeViewList);
            }
            try
            {
                return _unitOfWork.SaveChanges()>=0;
            }
            catch (Exception ex)
            {
                _logger.Error("SyncDischargedPatient病人CaseNumber" + hisView.CaseNumber + "出院数据同步失败||" + ex.ToString());
                return false;
            }
        }

        public async Task<InpatientInfoView> GetHisDischangeDataByCaseNumber(string caseNumber)
        {
            var url = await _appConfigSettingRepository.GetConfigSettingValue("Configs", "GetDischangeByCaseNumber");
            if (string.IsNullOrEmpty(url))
            {
                _logger.Error("根据CaseNumber同步出院病人方法 获取平台地址为空  appConfigSetting||settingCode=GetDischangeByCaseNumber");
                return null;
            }
            //将请求参数封装到双列集合中
            Dictionary<string, string> dictionarys = new Dictionary<string, string>
            {
                //向集合中添加参数
                { "CaseNumber", caseNumber }
            };
            var postData = JsonConvert.SerializeObject(dictionarys);
            string hisData;
            try
            {
                    hisData = await HttpHelper.HttpPostAsync(url, postData);
            }
            catch (Exception ex)
            {
                _logger.Error(ex.ToString());
                return null;
            }
            if (string.IsNullOrEmpty(hisData))
            {
                return null;
            }
            InpatientInfoView disChangeData;
            try
            {
                disChangeData = ListToJson.ToList<InpatientInfoView>(hisData);
            }
            catch (Exception ex)
            {
                _logger.Error("出院病人记录JSON转换败 ||caseNumber||" + caseNumber + ex.ToString());
                return null;
            }
            _logger.Info("出院病人记录由JSON转换完成||caseNumber||"+caseNumber);
            return disChangeData;
        }
    }
}