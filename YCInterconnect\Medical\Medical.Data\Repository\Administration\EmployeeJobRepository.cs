﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class EmployeeJobRepository : IEmployeeJobRepository
    {
        private readonly MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly GetCacheService _getCacheService;


        public EmployeeJobRepository(MedicalDbContext db, IMemoryCache memoryCache, GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _getCacheService = getCacheService;
        }

        public async Task<List<EmployeeJobInfo>> GetAsync(int deptmentJobID)
        {
            var data = (List<EmployeeJobInfo>)await GetCacheAsync();
            if (data == null)
            {
                return new List<EmployeeJobInfo>();
            }
            return data.Where(t => t.DepartmentJobID == deptmentJobID).ToList();
        }

        public async Task<List<EmployeeJobInfo>> GetList()
        {
            return await _medicalDbContext.EmployeeJobInfos.Where(
                m => m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<EmployeeJobInfo> GetList(int ID)
        {
            return await _medicalDbContext.EmployeeJobInfos.Where(
                m => m.DeleteFlag != "*" && m.EmployeeJobID == ID).SingleAsync();
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<EmployeeJobInfo>>(key, GetDataBaseListData);
        }
        /// <summary>
        /// 获取数据库数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            var data = await _medicalDbContext.EmployeeJobInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
            return data;
        }


        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.EmployeeJob.ToString();
        }

        public async Task<List<EmployeeJobInfo>> GetByUserIDAsync(int userID)
        {
            var data = (List<EmployeeJobInfo>)await GetCacheAsync();
            if (data == null)
            {
                return new List<EmployeeJobInfo>();
            }
            return data.Where(t => t.EmployeeBasicID == userID).ToList();
        }

        public async Task<List<EmployeeJobInfo>> GetAsync(int stationID, DateTime shiftDate, string shift)
        {
            return await _medicalDbContext.EmployeeJobInfos.Where(m => m.StationID == stationID
            && m.StartDate == shiftDate && m.Shift == shift && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<EmployeeJobInfo>> GetByJobAsync(int jobID, DateTime shiftDate, string shift)
        {
            return await _medicalDbContext.EmployeeJobInfos.Where(m => m.DepartmentJobID == jobID
            && m.StartDate == shiftDate && m.Shift == shift && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<EmployeeJobInfo>> GetByJobIDsAsync(int[] jobIDs, DateTime shiftDate, string shift)
        {
            return await _medicalDbContext.EmployeeJobInfos.Where(m => jobIDs.Contains(m.DepartmentJobID)
            && m.StartDate == shiftDate && m.Shift == shift && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<AttendanceNurseJobView>> GetAliasNameByNurseID(DateTime nowShiftDate, int stationID, string shift)
        {
            var result = await (from a in _medicalDbContext.EmployeeJobInfos
                                join b in _medicalDbContext.DepartmentJobInfos
                                on a.DepartmentJobID equals b.ID
                                where a.StartDate == nowShiftDate && a.Shift == shift && a.StationID == stationID
                                select new AttendanceNurseJobView
                                {
                                    EmployeeDatalID = a.EmployeeBasicID,
                                    AttendanceDate = a.StartDate,
                                    Shift = a.Shift,
                                    StationID = a.StationID,
                                    DepartmentJobID = a.DepartmentJobID,
                                    AliasName = b.AliasName
                                }).ToListAsync();
            return result;
        }

        public async Task<List<AttendanceNurseJobView>> GetAliasNameByNurseIDAndStationID(DateTime nowShiftDate, int ID)
        {
            var result = await (from a in _medicalDbContext.EmployeeJobInfos
                                join b in _medicalDbContext.DepartmentJobInfos
                                on a.DepartmentJobID equals b.ID
                                where a.StartDate == nowShiftDate && a.EmployeeBasicID == ID
                                select new AttendanceNurseJobView
                                {
                                    EmployeeDatalID = a.EmployeeBasicID,
                                    AttendanceDate = a.StartDate,
                                    Shift = a.Shift,
                                    StationID = a.StationID,
                                    DepartmentJobID = a.DepartmentJobID,
                                    AliasName = b.AliasName
                                }).ToListAsync();
            return result;
        }
    }
}