﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientPCCareMainRepository : IPatientPCCareMainRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientPCCareMainRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 根据末梢血运记录ID和评估次数获取末梢血运评估主表
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="num"></param>
        /// <returns></returns>
        public async Task<PatientPCCareMainInfo> GetPCCareAsync(string recordID, int num)
        {
            return await _medicalDbContext.PatientPCCareMainInfos.Where(t => t.PatientPCRecordID == recordID && t.NumberOfAssessment == num && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据末梢血运记录ID获取末梢血运评估列表
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<List<PatientPCCareMainInfo>> GetPCCareAsync(string recordID)
        {
            return await _medicalDbContext.PatientPCCareMainInfos.Where(t => t.PatientPCRecordID == recordID && t.DeleteFlag != "*")
                .OrderBy(t => t.NumberOfAssessment).ToListAsync();
        }
        /// <summary>
        ///  获取按时间排序的最后一次评估主记录
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<PatientPCCareMainInfo> GetLastByTimeAsync(string recordID)
        {
            var list = await _medicalDbContext.PatientPCCareMainInfos.Where(t => t.PatientPCRecordID == recordID && t.DeleteFlag != "*").ToListAsync();
            list = list.OrderByDescending(t => t.AssessDate.Add(t.AssessTime)).ThenByDescending(t => t.NumberOfAssessment).ToList();
            if (list != null && list.Count > 0)
            {
                return list[0];
            }
            return null;
        }
        /// <summary>
        ///  获取按次数排序的最后一次评估主记录
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<PatientPCCareMainInfo> GetLastByNumAsync(string recordID)
        {
            var list = await _medicalDbContext.PatientPCCareMainInfos.Where(t => t.PatientPCRecordID == recordID && t.DeleteFlag != "*").OrderByDescending(t => t.NumberOfAssessment).ToListAsync();
            if (list != null && list.Count > 0)
            {
                return list[0];
            }
            return null;
        }
        /// <summary>
        /// 根据末梢血运评估主表ID获取数据
        /// </summary>
        /// <param name="pcCareMainID"></param>
        /// <returns></returns>
        public async Task<PatientPCCareMainInfo> GetPCCareByIDAsync(string pcCareMainID)
        {
            return await _medicalDbContext.PatientPCCareMainInfos.Where(t => t.PatientPCCareMainID == pcCareMainID && t.DeleteFlag != "*").SingleOrDefaultAsync();
        }
        /// <summary>
        ///  获取措施码对应的所有维护记录
        /// </summary>
        /// <param name="scheduleID"></param>
        /// <returns></returns>
        public async Task<List<PatientPCCareMainInfo>> GetPCCareByScheduleID(string scheduleID)
        {
            return await _medicalDbContext.PatientPCCareMainInfos.Where(t => t.PatientScheduleMainID == scheduleID && t.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据RecordsCode获取末梢血运评估数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="recordsCode"></param>
        /// <returns></returns>
        public async Task<PatientPCCareMainInfo> GetCareByRecordsCode(string recordID, string recordsCode)
        {
            var list = await _medicalDbContext.PatientPCCareMainInfos.Where(t => t.PatientPCRecordID == recordID && t.RecordsCode == recordsCode && t.DeleteFlag != "*").ToListAsync();
            if (list != null && list.Count > 0)
            {
                return list[0];
            }
            return null;
        }

        public async Task<List<PatientPCCareMainInfo>> GetByAssessMainID(string assessMainID)
        {
            var list = await _medicalDbContext.PatientPCCareMainInfos.Where(m => m.PatientAssessMainID == assessMainID
                              && m.DeleteFlag != "*").ToListAsync();
            return list;
        }

        public async Task<List<PatientPCCareMainInfo>> GetNoAssessMainIDData(string inpatientID)
        {
            var list = await _medicalDbContext.PatientPCCareMainInfos.Where(m => m.InpatientID == inpatientID
                            && (m.PatientAssessMainID == null || m.PatientAssessMainID == "") && m.DeleteFlag != "*").ToListAsync();
            return list;
        }

        public async Task<List<HandoverPCCareIntervention>> GetPatientPCCareIntervention(string inpatientID, int stationID, DateTime startTime, DateTime endTime)
        {
            var query = await (from m in _medicalDbContext.PatientPCCareMainInfos
                               join n in _medicalDbContext.PatientPCRecordInfos on m.PatientPCRecordID equals n.PatientPCRecordID
                               where m.InpatientID == inpatientID
                                    && m.StationID == stationID
                                    && m.AssessDate.Add(m.AssessTime) >= startTime && m.AssessDate.Add(m.AssessTime) <= endTime
                                    && m.DeleteFlag != "*" && n.DeleteFlag != "*"
                               select new HandoverPCCareIntervention
                               {
                                   BodyShowName = n.BodyShowName,
                                   CareIntervention = m.CareIntervention,
                                   SkinTemperature = m.SkinTemperature,
                                   SubjectiveFeeling = m.SubjectiveFeeling,
                                   Hyperemia = m.Hyperemia,
                                   SkinColor = m.SkinColor,
                                   ArterialPulsation = m.ArterialPulsation,
                                   ArteryName = m.ArteryName,
                                   SwellingDegree = m.SwellingDegree,
                                   StretchPain = m.StretchPain
                               }).ToListAsync();
            return query;
        }

        public async Task<List<HandoverPCCareIntervention>> GetPatientPCCareIntervention(string inpatientID, string num)
        {
            var query = await (from m in _medicalDbContext.PatientPCCareMainInfos
                               join n in _medicalDbContext.PatientPCRecordInfos on m.PatientPCRecordID equals n.PatientPCRecordID
                               where m.InpatientID == inpatientID && n.AssessMainID == num
                                    && m.DeleteFlag != "*" && n.DeleteFlag != "*"
                               select new HandoverPCCareIntervention
                               {
                                   BodyShowName = n.BodyShowName,
                                   CareIntervention = m.CareIntervention,
                                   SkinTemperature = m.SkinTemperature,
                                   SubjectiveFeeling = m.SubjectiveFeeling,
                                   Hyperemia = m.Hyperemia,
                                   SkinColor = m.SkinColor,
                                   ArterialPulsation = m.ArterialPulsation,
                                   ArteryName = m.ArteryName,
                                   SwellingDegree = m.SwellingDegree,
                                   StretchPain = m.StretchPain,
                                   AssessTime = m.AssessTime
                               }).ToListAsync();
            return query;
        }

        public async Task<List<SchedulePerformDetail>> GetPCCareAssessData(string inpatientID, int stationID, DateTime shiftDate)
        {
            var query = await (from m in _medicalDbContext.PatientPCCareMainInfos
                               join n in _medicalDbContext.PatientPCCareDetailInfos.Where(m => m.DeleteFlag != "*") on m.PatientPCCareMainID equals n.PatientPCCareMainID
                               where m.InpatientID == inpatientID
                                    && m.StationID == stationID
                                    && m.ShiftDate == shiftDate
                                    && m.DeleteFlag != "*"
                               select new SchedulePerformDetail
                               {
                                   PerformDate = m.AssessDate,
                                   PerformTime = m.AssessTime,
                                   AssessListID = n.AssessListID,
                                   ScheduleData = n.AssessValue
                               }).ToListAsync();
            return query;
        }

        public async Task<List<PatientPCCareMainInfo>> GetRecordsBySourceID(string sourceID, string sourceType)
        {
            var data = await _medicalDbContext.PatientPCCareMainInfos.Where(m => m.SourceID == sourceID && m.DeleteFlag != "*").ToListAsync();
            if (!string.IsNullOrEmpty(sourceType))
            {
                data = data.Where(m => !string.IsNullOrEmpty(m.SourceType) && m.SourceType.Trim() == sourceType.Trim()).ToList();
            }
            return data;
        }

        public async Task<List<PatientPCCareMainInfo>> GetCareMainsByInpatientIDAsNoTrackAsync(string inpatientID, DateTime startDateTime, DateTime endDateTime)
        {
            var list = await _medicalDbContext.PatientPCCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*"
                && m.AssessDate >= startDateTime.Date && m.AssessDate <= endDateTime.Date).
                Select(m => new PatientPCCareMainInfo
                {
                    PatientPCRecordID = m.PatientPCRecordID,
                    PatientPCCareMainID = m.PatientPCCareMainID,
                    AssessDate = m.AssessDate,
                    AssessTime = m.AssessTime,
                    BringToShift = m.BringToShift,
                    RecordsCode = m.RecordsCode
                })
                .ToListAsync();

            return list.Where(m => m.AssessDate.Add(m.AssessTime) >= startDateTime && m.AssessDate.Add(m.AssessTime) <= endDateTime).ToList();
        }

        public async Task<List<SpecificHandoverView>> GetHandoverView(string inpatientID, DateTime startDateTime, DateTime endDateTime)
        {
            var list = await (from m in _medicalDbContext.PatientPCCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*"
                              && m.AssessDate >= startDateTime.Date && m.AssessDate <= endDateTime.Date)
                              join n in _medicalDbContext.PatientPCRecordInfos.Where(n => n.InpatientID == inpatientID && n.DeleteFlag != "*")
                              on m.PatientPCRecordID equals n.PatientPCRecordID
                              select new SpecificHandoverView
                              {
                                  RecordID = m.PatientPCRecordID,
                                  CareMainID = m.PatientPCCareMainID,
                                  AssessDate = m.AssessDate,
                                  AssessTime = m.AssessTime,
                                  BringToShift = m.BringToShift ?? false,
                                  RecordsCode = m.RecordsCode,
                                  BodyPartName = n.BodyShowName,
                              }).ToListAsync();

            return list.Where(m => m.AssessDate.Add(m.AssessTime) >= startDateTime && m.AssessDate.Add(m.AssessTime) <= endDateTime).OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).ToList();
        }
    }
}