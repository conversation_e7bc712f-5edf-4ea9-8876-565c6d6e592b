﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Medical.Data.GetCacheService;

namespace Medical.Data.Repository
{
    public class StationListRepository : IStationListRepository
    {
        private MedicalDbContext _dbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly IOptions<SystemConfig> _systemConfig;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public StationListRepository(
              MedicalDbContext db
            , IMemoryCache memoryCache
            , IOptions<SystemConfig> options
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService
            )
        {
            _dbContext = db;
            _memoryCache = memoryCache;
            _systemConfig = options;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        /// <summary>
        /// 根据病区代码获取病区信息（External用）
        /// </summary>
        /// <param name="code">病区代码</param>
        /// <returns></returns>
        public async Task<StationListInfo> GetAsync(string code)
        {
            var list = await GetCacheAsync() as List<StationListInfo>;
            return list.Where(m => m.StationCode == code).FirstOrDefault();
        }

        public async Task<StationListInfo> GetAsync(int id)
        {
            var list = await GetCacheAsync() as List<StationListInfo>;
            return list.Find(m => m.ID == id);
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<StationListInfo>>(key, GetDataBaseListData);
        }
        /// <summary>
        /// 获取数据库数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            var data = await _dbContext.StationListInfos.Where(m => m.HospitalID== hospitalID.ToString() && m.DeleteFlag != "*").ToListAsync();
            return data;
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.Station.GetKey(_sessionCommonServer);
        }
        //数据同步使用
        public List<StationListInfo> GetStationList()
        {
            return (List<StationListInfo>)GetCacheAsync().Result;
        }

        public async Task<List<StationListInfo>> GetAllAsync()
        {
            return (List<StationListInfo>)await GetCacheAsync();
        }
        /// <summary>
        /// 获取病区最大ID,数据同步使用
        /// </summary>
        /// <returns></returns>
        public int GetMaxID()
        {
            try
            {
                var list = (List<StationListInfo>)GetCacheAsync().Result;
                var user = list.OrderByDescending(m => m.ID).FirstOrDefault();
                if (user == null)
                {
                    return 1;
                }
                else
                {
                    return user.ID + 1;
                }
            }
            catch { }
            return 1;
        }

        public async Task<int> GetMaxIDAsync()
        {
            var maxID = await _dbContext.StationList.MaxAsync(m => (int?)m.ID);
            return maxID ?? 0;
        }

        public StationListInfo GetStationInfo(string StationCode)
        {
            var list = (List<StationListInfo>)GetCacheAsync().Result;
            return list.Where(m => m.StationCode == StationCode).FirstOrDefault();
        }

        public async Task<List<StationListInfo>> GetStationListByHospID()
        {
            return (List<StationListInfo>)await GetCacheAsync();
        }

        public async Task<string> GetStationName(int stationID)
        {
            var stations = (List<StationListInfo>)await GetCacheAsync();

            var station = stations.Find(m => m.ID == stationID);

            if (station == null)
            {
                return "";
            }

            return station.StationName;
        }

        public async Task<List<SimpleInfo>> GetSimpleList()
        {
            var stations = (List<StationListInfo>)await GetCacheAsync();
            return stations.Select(m => new SimpleInfo
            {
                ID = m.ID,
                Name = m.StationName,
                Code = m.StationCode
            }).ToList();
        }
        /// <summary>
        /// 获取icu监护室病区ID集合
        /// </summary>
        /// <returns></returns>
        public async Task<List<StationListInfo>> GetICUStationAsync()
        {
            var stationCache = await GetCacheAsync();

            if (stationCache != null && stationCache is List<StationListInfo> stations)
            {
                return stations.Where(m => m.ICUFlag == "*" || m.ICUFlag == "1" || m.ICUFlag == "Y").ToList();
            }

            return new List<StationListInfo>();
        }
        /// <summary>
        /// 根据病区码获取病区ID
        /// </summary>
        /// <param name="stationCode">病区码</param>
        /// <returns></returns>
        public async Task<int?> GetStationIDByCodeAsync(string stationCode)
        {
            var stationCache = await this.GetAllAsync<StationListInfo>();

            return stationCache.Find(m => m.StationCode == stationCode)?.ID;
        }
        public async Task<List<StationListInfo>> GetAllStationCodeAsync()
        {
            var data = await this.GetAllAsync<StationListInfo>();

            return data.Select(m => new StationListInfo
            {
                ID = m.ID,
                StationCode = m.StationCode
            }).ToList();
        }
        /// <summary>
        /// 同步使用  medical禁止使用
        /// </summary>
        /// <returns></returns>
        public List<StationListInfo> GetAllStation()
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            return _dbContext.StationList.Where(m => m.DeleteFlag != "*" && m.HospitalID == hospitalID).ToList();
        }

        /// <summary>
        /// 同步使用  medical禁止使用(包含删除病区)
        /// </summary>
        /// <returns></returns>
        public List<StationListInfo> GetAllStationAsync()
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            return _dbContext.StationList.Where(m => m.HospitalID == hospitalID).ToList();
        }

        public async Task<List<StationListInfo>> GetStationListNoCache()
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            return await _dbContext.StationList.Where(m => m.HospitalID == hospitalID).ToListAsync();
        }

        public string GetHeadNurse(int stationID)
        {
            var list = (List<StationListInfo>)GetCacheAsync().Result;
            return list.Where(m => m.ID == stationID).Select(m => m.HeadNurse).FirstOrDefault();
        }
    }
}