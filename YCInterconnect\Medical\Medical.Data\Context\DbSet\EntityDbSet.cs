﻿using Medical.Models;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public partial class MedicalDbContext
    {
        /// <summary>
        /// 人员数据集
        /// </summary>
        public DbSet<UserInfo> Users { get; set; }
        /// <summary>
        /// 用户CA数据
        /// </summary>
        public DbSet<EmployeeCADataInfo> EmployeeCADataInfos { get; set; }
        /// <summary>
        /// 单位班别时段数据集
        /// </summary>
        public DbSet<StationShiftInfo> StationShifts { get; set; }
        /// <summary>
        /// 单位请单数据集
        /// </summary>
        public DbSet<StationListInfo> StationList { get; set; }
        /// <summary>
        /// 评估清单数据集
        /// </summary>
        public DbSet<AssessListInfo> AssessLists { get; set; }
        /// <summary>
        /// 护士班表
        /// </summary>
        public DbSet<NurseShiftInfo> NurseShifts { get; set; }
        /// <summary>
        /// 报表请单数据集
        /// </summary>
        public DbSet<RecordsListInfo> RecordsLists { get; set; }
        /// <summary>
        /// 住院病人数据集
        /// </summary>
        public DbSet<InpatientDataInfo> InpatientDatas { get; set; }
        /// <summary>
        /// 历史在院患者
        /// </summary>
        public DbSet<InpatientDataLogInfo> InpatientDataLogInfos { get; set; }
        /// <summary>
        /// 頻次数据集
        /// </summary>
        public DbSet<FrequencyInfo> Frequencies { get; set; }
        /// <summary>
        /// 床位数据集
        /// </summary>
        public DbSet<BedListInfo> BedListInfos { get; set; }
        /// <summary>
        /// 身体部位
        /// </summary>
        public DbSet<BodyPartListInfo> BodyPartListInfos { get; set; }
        /// <summary>
        /// 导管列表
        /// </summary>
        public DbSet<TubeListInfo> TubeListInfos { get; set; }

        /// <summary>
        /// 科别列表
        /// </summary>
        public DbSet<DepartmentListInfo> DepartmentListInfos { get; set; }

        /// <summary>
        /// 权限列表
        /// </summary>
        public DbSet<AuthorityListInfo> AuthorityListInfos { get; set; }
        /// <summary>
        /// 角色列表
        /// </summary>
        public DbSet<RoleInfo> RoleInfos { get; set; }
        /// <summary>
        /// 事件日志
        /// </summary>
        public DbSet<EventLogInfo> EventLogs { get; set; }

        /// <summary>
        /// 登入日志
        /// </summary>
        public DbSet<LoginLogInfo> LoginLogInfos { get; set; }
        /// <summary>
        /// 菜单列表
        /// </summary>
        public DbSet<MenuListInfo> MenuListInfos { get; set; }

        /// <summary>
        /// 手术字典
        /// </summary>
        public DbSet<OperationListInfo> OperationListInfos { get; set; }

        /// <summary>
        /// 岗位分组字典
        /// </summary>
        public DbSet<CareGroupListInfo> CareGroupListInfos { get; set; }

        /// <summary>
        /// 仪器数据与AssessList对应
        /// </summary>
        public DbSet<EquipmentItemToAssessInfo> EquipmentItemToAssessInfos { get; set; }
        /// <summary>
        /// 系统前端按钮字典表
        /// </summary>
        public DbSet<ButtonListInfo> ButtonListInfos { get; set; }
        /// <summary>
        /// 系统前端路由字典表
        /// </summary>
        public DbSet<RouterListInfo> RouterListInfos { get; set; }
        /// <summary>
        /// 前端路由画面使用按钮表
        /// </summary>
        public DbSet<RouterUseButtonInfo> RouterUseButtonInfos { get; set; }
        /// <summary>
        /// 前端路由画面使用按钮权限表
        /// </summary>
        public DbSet<AuthorityRouterUseButtonInfo> AuthorityRouterUseButtonInfos { get; set; }
        /// <summary>
        /// 床位数据集
        /// </summary>
        public DbSet<BedLendRecordInfo> BedLendRecordInfos { get; set; }
    }
}