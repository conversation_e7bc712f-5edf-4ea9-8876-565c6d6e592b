﻿using Interconnect.ViewModels;
using System.Collections.Generic;

namespace Interconnect.Services.Interface
{
   public interface IJobLogService
    {
        /// <summary>
        /// 确认作业是否可以开始执行
        /// </summary>
        /// <param name="jobId"></param>
        /// <param name="jobName"></param>
        /// <returns></returns>
        bool GetJobStatus(string jobId, string jobName, string chartNum);       

        /// <summary>
        /// 移除作业
        /// </summary>
        /// <param name="jobId"></param>
        /// <returns></returns>
         void RemoveJob(string jobId, string jobName, string chartNum);

        /// <summary>
        /// 查询作业
        /// </summary>
        /// <param name="jobId">all 返回所有</param>
        /// <param name="jobName"></param>
        /// <param name="subJobId"></param>
        /// <returns></returns>
        List<SyncJob> GetJobByID(string jobId, string jobName, string subJobId);
    }
}
