﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientFlapCareDetailRepository : IPatientFlapCareDetailRepository
    {
        private readonly MedicalDbContext _db = null;

        public PatientFlapCareDetailRepository(MedicalDbContext medicalDbContext)
        {
            _db = medicalDbContext;
        }

        /// <summary>
        /// 根据CareMainID获取明细表数据
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        public async Task<List<PatientFlapCareDetailInfo>> GetDetailsByMainID(string mainID)
        {
            return await _db.PatientFlapCareDetailInfos.Where(m =>
            m.PatientFlapCareMainID == mainID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据维护记录ID获取明细集合
        /// </summary>
        /// <param name="mainIDs">维护记录ID集合</param>
        /// <returns></returns>
        public async Task<List<Detail>> GetCareDetailsByCareMainIDs(IEnumerable<string> mainIDs)
        {
            return await _db.PatientFlapCareDetailInfos.Where(m =>
            mainIDs.Contains(m.PatientFlapCareMainID) && m.DeleteFlag != "*").Select(m => new Detail
            {
                ID = m.PatientFlapCareMainID,
                AssessListID = m.AssessListID,
                AssessValue = m.AssessValue,
                AssessListGroupID = m.AssessListGroupID,
            }).ToListAsync();
        }
    }
}
