﻿using System.ComponentModel;
 

namespace Interconnect.Models
{
    /// <summary>
    /// 配置参数
    /// </summary>
    public class SystemConfig
    {
        /// <summary>
        /// 修改人
        /// </summary>
        [Description("修改人")]
        public string ModifyPersonID { get; set; }
        /// <summary>
        /// 年龄对应评估项目
        /// </summary>
        [Description("年龄对应评估项目")]
        public int AgeAssessListID { get; set; }
        /// <summary>
        /// 医院代码
        /// </summary>
        [Description("医院代码")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 语言
        /// </summary>
        [Description("语言")]
        public int Language { get; set; }
        /// <summary>
        /// 提前天数
        /// </summary>
        [Description("提前天数")]
        public double PreDate { get; set; }

        /// <summary>
        /// 药物过敏
        /// </summary>
        [Description("药物过敏AssessListID")]
        public int DrugAllergyID { get; set; }

        /// <summary>
        /// 食物物过敏
        /// </summary>
        [Description("食物物过敏AssessListID")]
        public int FoodAllergyID { get; set; }

        /// <summary>
        /// 数据同步时，一次从中介数据库取出的记录条数
        /// </summary>
        [Description("一次从中介数据库取出的记录条数")]
        public int TakeRows { get; set; }

        /// <summary>
        /// 同步日志的保存天数
        /// </summary>
        [Description("同步日志的保存天数")]
        public int LogSaveDays { get; set; }

        /// <summary>
        /// 是否启动定时作业，1启动定时作业，0或其它 不启动定时作业
        /// </summary>
        [Description("是否启动定时作业 1启动，0或其它不启动")]
        public int StartupJob { get; set; }


        /// <summary>
        /// 导入哪些病区的住院信息,*，所有的病区。如果有多个病区用“，”号分割
        /// </summary>
        [Description("导入哪些病区的住院信息")]
        public string Stations { get; set; }

        /// <summary>
        /// 一条数据的最多同步次数(住院病人信息排除)
        /// </summary>
        [Description("一条数据的最多同步次数,0 表示不限制次数")]
        public string TongbuCount { get; set; }

        /// <summary>
        ///同步几天内未同步的数据
        /// </summary>
        [Description("同步几天内未同步的数据")]
        public int SyncDays { get; set; }       

        /// <summary>
        ///同步多少小时内的数据，住院病人同步使用
        /// </summary>
        [Description("同步多少小时内的数据")]
        public int SyncHour { get; set; }

        /// <summary>
        ///住院病人提取的时间范围
        /// </summary>
        [Description("住院病人提取的时间范围")]
        public int InPatientDay { get; set; }
        

        /// <summary>
        /// 同步医嘱的时候，是否判断人员信息，0 不判断，1 判断
        /// </summary>
        [Description("同步医嘱的时候，是否判断人员信息，0 不判断，1 判断")]
        public int CheckEmployeeFlag { get; set; }
        /// <summary>
        /// 同步医嘱的时候，是是否记录全部没有同步成功的日志信息，0 不判断，1 判断
        /// </summary>
        [Description("//是否记录全部没有同步成功的日志信息，1 是，0否")]
        public int AllLogSet { get; set; }
        /// <summary>
        /// API
        /// </summary>
        [Description("PatientProfileAPI")]
        public string PatientProfileAPI { get; set; }
        /// <summary>
        /// 停止问题API
        /// </summary>
        public string StopPatientProblemAPI { get; set; }
        /// <summary>
        /// 更新缓存
        /// </summary>
        public string CacheUpdateAPI { get; set; }
        /// <summary>
        /// 根据医嘱ID停止时间点后排程
        /// </summary>
        public string StopSchduleByOrderAPI { get; set; }
        /// <summary>
        /// 术后集束护理
        /// </summary>
        public string PostOperationAPI { get; set; }
        /// <summary>
        /// 术前集束护理
        /// </summary>
        public string PreOperationAPI { get; set; }
        /// <summary>
        /// 获取病人风险数据API
        /// </summary>
        public string GetPatientRiskDataListAPI { get; set; }
        /// <summary>
        /// 验证排程中病人信息
        /// </summary>
        public string CheckSchedulePatientInfoAPI { get; set; }

        /// <summary>
        /// 作业的执行时间长度，超过这个时间，重新开启一个作业,单位分钟
        /// </summary>
        public int JobRunTime { get; set; }

        /// <summary>
        /// //发送者昵称"xml"
        /// </summary>
        public string SentFromUser { get; set; }

        /// <summary>
        ///  //发送者邮箱 "<EMAIL>"
        /// </summary>
        public string SentFromAddress { get; set; }

        /// <summary>
        /// SMTP连接用户85759437，有时候要求和SentFromAddress一样
        /// </summary>
        public string SmtpUserName { get; set; }
        /// <summary>
        /// //邮箱SMTP授权码"oaqqgatslinvbjad"
        /// </summary>
        public string SmtpAuthorizationCode { get; set; }

        /// <summary>
        /// //发送方邮箱服务器"smtp.qq.com"
        /// </summary>
        public string MailHost { get; set; }

        /// <summary>
        /// //发送方邮件服务器端口587
        /// </summary>
        public int MailHostPost { get; set; }

        /// <summary>
        /// 人员信息同步时，默认的较色
        /// </summary>
        public int? EmployeeDefaultRole { get; set; }

        /// <summary>
        /// 打印接口数据，1打印接口数据,其他不打印
        /// </summary>
        public int PrintInterfaceData { get; set; }
        /// <summary>
        /// 新入患者ProfileMark
        /// </summary>
        public string InpatientProfileMarkAPI { get; set; }
        /// <summary>
        /// 转科ProfileMark
        /// </summary>
        public string TransferProfileMarkAPI { get; set; }
        /// <summary>
        /// 出院ProfileMark
        /// </summary>
        public string DischargeProfileMarkAPI { get; set; }

        /// <summary>
        /// //电子病历服务允许启动线程数量，系统内部默认10
        /// </summary>
        public string EmrServerAllowNum { get; set; }
        /// <summary>
        /// 环境状态 1:正式环境 2:测试环境
        /// </summary>
        public string Status { get; set; }
        /// <summary>
        /// 调用medicalAPI
        /// </summary>
        public string SyncMedicalApi { get; set; }
        /// <summary>
        /// 调用DataInterfaceAPI
        /// </summary>
        public string DataInterfaceAPI { get; set; }
        /// <summary>
        /// 系统使用缓存类型
        /// </summary>
        public string UseCacheType { get; set; }
    }
}