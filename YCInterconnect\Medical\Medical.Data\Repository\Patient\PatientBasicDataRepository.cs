﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientBasicDataRepository : IPatientBasicDataRepository
    {
        private readonly MedicalDbContext _dbContext = null;

        public PatientBasicDataRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }

        public async Task<List<PatientBasicDataInfo>> GetAsync()
        {
            return await _dbContext.PatientBasicDatas.ToListAsync();
        }
        public async Task<List<PatientBasicDataInfo>> GetAsyncGetPatientInfoByIDAsync(string hospitalID, string PatientID)
        {
            var list = await _dbContext.PatientBasicDatas.Where(t => t.HospitalID == hospitalID && t.PatientID == PatientID).Select(m => new PatientBasicDataInfo
            {
                PatientID = m.PatientID,
                HospitalID = m.HospitalID,
                ChartNo = m.ChartNo,
                InpatientNo = m.InpatientNo,
                IdentityID = m.IdentityID,
                PatientName = m.PatientName,
                Gender = m.Gender,
                DateOfBirth = m.DateOfBirth,
                TimeOfBirth = m.TimeOfBirth,
                BloodType = m.BloodType,
                NativePlace = m.NativePlace,
                RH = m.RH,
                LocalChartNO = m.LocalChartNO,
            }).ToListAsync();
            if (hospitalID == "5")
            {
                list.Select(m => new PatientBasicDataInfo
                {
                    PatientID = m.PatientID,
                    HospitalID = m.HospitalID,
                    ChartNo = m.ChartNo,
                    InpatientNo = m.InpatientNo,
                    IdentityID = EncryptionAndDecryption.DecryptStr(m?.IdentityID),
                    PatientName = m.PatientName,
                    Gender = m.Gender,
                    DateOfBirth = m.DateOfBirth,
                    TimeOfBirth = m.TimeOfBirth,
                    BloodType = m.BloodType,
                    NativePlace = m.NativePlace,
                    RH = m.RH,
                    LocalChartNO = m.LocalChartNO,
                }).ToList();
            }
            return list;
        }

        /// <summary>
        /// 透过住院序号取得病人基本数据
        /// </summary>
        /// <param name="patientID">病人序号</param>
        /// <returns></returns>
        public async Task<PatientBasicDataInfo> GetByPatientIDAsync(string patientID)
        {
            var data = await _dbContext.PatientBasicDatas.Where(m => m.PatientID == patientID).Select(m => new PatientBasicDataInfo
            {
                PatientID = m.PatientID,
                HospitalID = m.HospitalID,
                ChartNo = m.ChartNo,
                InpatientNo = m.InpatientNo,
                IdentityID = m.IdentityID,
                PatientName = m.PatientName,
                Gender = m.Gender,
                DateOfBirth = m.DateOfBirth,
                TimeOfBirth = m.TimeOfBirth,
                BloodType = m.BloodType,
                NativePlace = m.NativePlace,
                RH = m.RH,
                LocalChartNO = m.LocalChartNO,
            }).FirstOrDefaultAsync();
            if (data != null && data?.HospitalID == "5")
            {
                data.IdentityID = EncryptionAndDecryption.DecryptStr(data?.IdentityID);
            }
            return data;
        }

        public async Task<PatientBasicDataInfo> GetByPatientIDAsNoTrackAsync(string patientID)
        {
            var data = await _dbContext.PatientBasicDatas.AsNoTracking().Where(m => m.PatientID == patientID).FirstOrDefaultAsync();
            if (data != null && data?.HospitalID == "5")
            {
                data.IdentityID = EncryptionAndDecryption.DecryptStr(data?.IdentityID);
            }
            return data;
        }

        /// <summary>
        /// 获取病人基本信息
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<PatientBasicDataInfo> GetInpPatientByInpatientID(string inpatientID)
        {
            var query = (from InpatientData in _dbContext.InpatientDatas
                         join PatientBasic in _dbContext.PatientBasicDatas
                         on new { InpatientData.PatientID, InpatientData.ID }
                         equals new { PatientBasic.PatientID, ID = inpatientID }
                         where InpatientData.DeleteFlag != "*"
                         && InHospitalStatus.INHOSPITALLIST.Contains(InpatientData.InHospitalStatus ?? -1)
                         select PatientBasic);
            return await query.FirstOrDefaultAsync();
        }
        public async Task<PatientBasicDataInfo> GetInpPatientBychartNo(string chartNo)
        {
            return await _dbContext.PatientBasicDatas.Where(m => m.ChartNo == chartNo).FirstOrDefaultAsync();
        }
        public async Task<DateTime?> GetDateOfBirthByInpatientIDAsync(string inpatientID)
        {
            var query = (from inpaitentData in _dbContext.InpatientDatas
                         join patientBasicData in _dbContext.PatientBasicDatas
                          on inpaitentData.PatientID equals patientBasicData.PatientID
                         where inpaitentData.ID == inpatientID && inpaitentData.DeleteFlag != "*"
                         select patientBasicData);
            return await query.Select(m => m.DateOfBirth).FirstOrDefaultAsync();
        }

        public async Task<PatientBasicDataInfo> GetAsync(string chartNO)
        {
            var data = await _dbContext.PatientBasicDatas.Where(m => m.ChartNo == chartNO && m.DeleteFlag != "*").Select(m => new PatientBasicDataInfo
            {
                PatientID = m.PatientID,
                HospitalID = m.HospitalID,
                ChartNo = m.ChartNo,
                InpatientNo = m.InpatientNo,
                IdentityID = m.IdentityID,
                PatientName = m.PatientName,
                Gender = m.Gender,
                DateOfBirth = m.DateOfBirth,
                TimeOfBirth = m.TimeOfBirth,
                BloodType = m.BloodType,
                NativePlace = m.NativePlace,
                RH = m.RH,
                LocalChartNO = m.LocalChartNO,
            }).FirstOrDefaultAsync();
            if (data?.HospitalID == "5")
            {
                data.IdentityID = EncryptionAndDecryption.DecryptStr(data?.IdentityID);
            }
            return data;
        }

        public async Task<PatientBasicDataInfo> GetAsync(string hospitalID, string chartNO)
        {
            var data = await _dbContext.PatientBasicDatas.Where(m => m.HospitalID == hospitalID && m.ChartNo == chartNO).Select(m => new PatientBasicDataInfo
            {
                PatientID = m.PatientID,
                HospitalID = m.HospitalID,
                ChartNo = m.ChartNo,
                InpatientNo = m.InpatientNo,
                IdentityID = m.IdentityID,
                PatientName = m.PatientName,
                Gender = m.Gender,
                DateOfBirth = m.DateOfBirth,
                TimeOfBirth = m.TimeOfBirth,
                BloodType = m.BloodType,
                NativePlace = m.NativePlace,
                RH = m.RH,
                LocalChartNO = m.LocalChartNO,
            }).FirstOrDefaultAsync();
            if (data?.HospitalID == "5")
            {
                data.IdentityID = EncryptionAndDecryption.DecryptStr(data?.IdentityID);
            }
            return data;
        }

        public PatientBasicDataInfo GetOnePatientBasic(string hospitalID, string chartNO)
        {
            var patientBasicData = _dbContext.PatientBasicDatas.Where(m => m.HospitalID == hospitalID && m.ChartNo == chartNO).FirstOrDefault();
            if (patientBasicData != null && patientBasicData?.HospitalID == "5")
            {
                patientBasicData.IdentityID = EncryptionAndDecryption.DecryptStr(patientBasicData?.IdentityID);
            }
            return patientBasicData;
        }

        //获得病区病人基本信息 xml 20190601
        public async Task<List<PatientBasicDataInfo>> GetStaionPatientBaseAsync(string hospitalID, int stationid)
        {
            var query = (from InpatientData in _dbContext.InpatientDatas
                         join PatientBasic in _dbContext.PatientBasicDatas
                         on new { InpatientData.PatientID, InpatientData.HospitalID }
                         equals new { PatientBasic.PatientID, PatientBasic.HospitalID }
                         where InpatientData.HospitalID == hospitalID
                         && InpatientData.StationID == stationid
                         && InpatientData.DeleteFlag != "*"
                         && PatientBasic.DeleteFlag != "*"
                         && InHospitalStatus.INHOSPITALLIST.Contains(InpatientData.InHospitalStatus ?? -1)
                         select PatientBasic);
            return await query.ToListAsync();
        }

        public async Task<List<PatientBasicDataInfo>> GetAsync(string[] patientIDs)
        {
            var patientBasicDataList = await _dbContext.PatientBasicDatas.Where(m => patientIDs.Contains(m.PatientID) && m.DeleteFlag != "*")
                                        .Select(m => new PatientBasicDataInfo
                                        {
                                            PatientID = m.PatientID,
                                            HospitalID = m.HospitalID,
                                            ChartNo = m.ChartNo,
                                            InpatientNo = m.InpatientNo,
                                            IdentityID = m.HospitalID == "5" ? EncryptionAndDecryption.DecryptStr(m.IdentityID) : m.IdentityID,
                                            PatientName = m.PatientName,
                                            Gender = m.Gender,
                                            DateOfBirth = m.DateOfBirth,
                                            TimeOfBirth = m.TimeOfBirth,
                                            BloodType = m.BloodType,
                                            NativePlace = m.NativePlace,
                                            RH = m.RH,
                                            LocalChartNO = m.LocalChartNO,
                                        }).ToListAsync();
            return patientBasicDataList;
        }
        public async Task<List<PatientBasicDataInfo>> GetByChartNOsAsync(string hospitalID, string[] chartNOs)
        {
            var patientBasicDataList = await _dbContext.PatientBasicDatas.Where(m => m.HospitalID == hospitalID && chartNOs.Contains(m.ChartNo)).Select(m => new PatientBasicDataInfo
            {
                PatientID = m.PatientID,
                HospitalID = m.HospitalID,
                ChartNo = m.ChartNo,
                InpatientNo = m.InpatientNo,
                IdentityID = m.IdentityID,
                PatientName = m.PatientName,
                Gender = m.Gender,
                DateOfBirth = m.DateOfBirth,
                TimeOfBirth = m.TimeOfBirth,
                BloodType = m.BloodType,
                NativePlace = m.NativePlace,
                RH = m.RH,
                LocalChartNO = m.LocalChartNO,
            }).ToListAsync();

            if (hospitalID == "5")
            {
                patientBasicDataList = patientBasicDataList.Select(m => new PatientBasicDataInfo
                {
                    PatientID = m.PatientID,
                    HospitalID = m.HospitalID,
                    ChartNo = m.ChartNo,
                    InpatientNo = m.InpatientNo,
                    IdentityID = EncryptionAndDecryption.DecryptStr(m?.IdentityID),
                    PatientName = m.PatientName,
                    Gender = m.Gender,
                    DateOfBirth = m.DateOfBirth,
                    TimeOfBirth = m.TimeOfBirth,
                    BloodType = m.BloodType,
                    NativePlace = m.NativePlace,
                    RH = m.RH,
                    LocalChartNO = m.LocalChartNO,
                }).ToList();
            }

            return patientBasicDataList;
        }

        public async Task<bool> AddAsync(PatientBasicDataInfo t)
        {
            if (t == null)
            {
                return false;
            }
            _dbContext.PatientBasicDatas.Add(t);
            return await _dbContext.SaveChangesAsync() > 0;
        }
        public async Task<bool> UpdateAsync(PatientBasicDataInfo t)
        {
            _dbContext.PatientBasicDatas.Update(t);
            return await _dbContext.SaveChangesAsync() > 0;
        }

        public async Task<PatientBasicDataInfo> GetByPatientIDAsync(string hospitalID, string PatientID)
        {
            var data = await _dbContext.PatientBasicDatas.Where(m => m.PatientID == PatientID
            && m.HospitalID.Trim() == hospitalID).Select(m => new PatientBasicDataInfo
            {
                PatientID = m.PatientID,
                HospitalID = m.HospitalID,
                ChartNo = m.ChartNo,
                InpatientNo = m.InpatientNo,
                IdentityID = m.IdentityID,
                PatientName = m.PatientName,
                Gender = m.Gender,
                DateOfBirth = m.DateOfBirth,
                TimeOfBirth = m.TimeOfBirth,
                BloodType = m.BloodType,
                NativePlace = m.NativePlace,
                RH = m.RH,
                LocalChartNO = m.LocalChartNO,
            }).FirstOrDefaultAsync();
            if (hospitalID == "5")
            {
                data.IdentityID = EncryptionAndDecryption.DecryptStr(data?.IdentityID);
            }
            return data;
        }
        public async Task<PatientBasicDataInfo> GetByChartNo(string hospitalID, string chartNo)
        {
            var data = await _dbContext.PatientBasicDatas.Where(m => m.ChartNo == chartNo
            && m.HospitalID == hospitalID).Select(m => new PatientBasicDataInfo
            {
                PatientID = m.PatientID,
                HospitalID = m.HospitalID,
                ChartNo = m.ChartNo,
                InpatientNo = m.InpatientNo,
                IdentityID = m.IdentityID,
                PatientName = m.PatientName,
                Gender = m.Gender,
                DateOfBirth = m.DateOfBirth,
                TimeOfBirth = m.TimeOfBirth,
                BloodType = m.BloodType,
                NativePlace = m.NativePlace,
                RH = m.RH,
                LocalChartNO = m.LocalChartNO,
            }).FirstOrDefaultAsync();
            if (hospitalID == "5")
            {
                data.IdentityID = EncryptionAndDecryption.DecryptStr(data?.IdentityID);
            }
            return data;
        }
        public async Task<List<PatientBasicDataInfo>> GetByInpNo(string hospitalID, string inpNO)
        {
            var list = await _dbContext.PatientBasicDatas.Where(m => (m.InpatientNo == inpNO)
            && m.HospitalID == hospitalID).Select(m => new PatientBasicDataInfo
            {
                PatientID = m.PatientID,
                HospitalID = m.HospitalID,
                ChartNo = m.ChartNo,
                InpatientNo = m.InpatientNo,
                IdentityID = m.IdentityID,
                PatientName = m.PatientName,
                Gender = m.Gender,
                DateOfBirth = m.DateOfBirth,
                TimeOfBirth = m.TimeOfBirth,
                BloodType = m.BloodType,
                NativePlace = m.NativePlace,
                RH = m.RH,
                LocalChartNO = m.LocalChartNO,
            }).ToListAsync();
            if (hospitalID == "5")
            {
                list = list.Select(m => new PatientBasicDataInfo
                {
                    PatientID = m.PatientID,
                    HospitalID = m.HospitalID,
                    ChartNo = m.ChartNo,
                    InpatientNo = m.InpatientNo,
                    IdentityID = EncryptionAndDecryption.DecryptStr(m?.IdentityID),
                    PatientName = m.PatientName,
                    Gender = m.Gender,
                    DateOfBirth = m.DateOfBirth,
                    TimeOfBirth = m.TimeOfBirth,
                    BloodType = m.BloodType,
                    NativePlace = m.NativePlace,
                    RH = m.RH,
                    LocalChartNO = m.LocalChartNO,
                }).ToList();
            }
            return list;
        }
        public async Task<PatientBasicDataInfo> GetLastData()
        {
            var data = await _dbContext.PatientBasicDatas.OrderByDescending(m => m.ModifyDate).Select(m => new PatientBasicDataInfo
            {
                PatientID = m.PatientID,
                HospitalID = m.HospitalID,
                ChartNo = m.ChartNo,
                InpatientNo = m.InpatientNo,
                IdentityID = m.IdentityID,
                PatientName = m.PatientName,
                Gender = m.Gender,
                DateOfBirth = m.DateOfBirth,
                TimeOfBirth = m.TimeOfBirth,
                BloodType = m.BloodType,
                NativePlace = m.NativePlace,
                RH = m.RH,
                LocalChartNO = m.LocalChartNO,
            }).FirstOrDefaultAsync();
            if (data?.HospitalID == "5")
            {
                data.IdentityID = EncryptionAndDecryption.DecryptStr(data?.IdentityID);
            }
            return data;
        }

        public async Task<PatientBasicDataInfo> GetOnePatientBasicData(string PatientID)
        {
            var data = await _dbContext.PatientBasicDatas.Where(m => m.DeleteFlag != "*" && m.PatientID == PatientID).Select(m => new PatientBasicDataInfo
            {
                PatientID = m.PatientID,
                HospitalID = m.HospitalID,
                ChartNo = m.ChartNo,
                InpatientNo = m.InpatientNo,
                IdentityID = m.IdentityID,
                PatientName = m.PatientName,
                Gender = m.Gender,
                DateOfBirth = m.DateOfBirth,
                TimeOfBirth = m.TimeOfBirth,
                BloodType = m.BloodType,
                NativePlace = m.NativePlace,
                RH = m.RH,
                LocalChartNO = m.LocalChartNO,
            }).SingleAsync();
            if (data?.HospitalID == "5")
            {
                data.IdentityID = EncryptionAndDecryption.DecryptStr(data?.IdentityID);
            }
            return data;
        }

        //根据patientID查出需要获取的病人信息--20-01-07梁宝华
        public async Task<List<PatientBasicDataInfo>> GetPatientBasicDatasBypatientIDs(string hospitalID, List<string> patientIDs)
        {
            var patientIDSet = new HashSet<string>(patientIDs);
            var result = await _dbContext.PatientBasicDatas.Where(m =>
              //m.OrderCode != null &&
              patientIDSet.Contains(m.PatientID) && m.DeleteFlag != "*" && m.HospitalID == hospitalID).Select(m => new PatientBasicDataInfo
              {
                  PatientID = m.PatientID,
                  HospitalID = m.HospitalID,
                  ChartNo = m.ChartNo,
                  InpatientNo = m.InpatientNo,
                  IdentityID = m.IdentityID,
                  PatientName = m.PatientName,
                  Gender = m.Gender,
                  DateOfBirth = m.DateOfBirth,
                  TimeOfBirth = m.TimeOfBirth,
                  BloodType = m.BloodType,
                  NativePlace = m.NativePlace,
                  RH = m.RH,
                  LocalChartNO = m.LocalChartNO,
              }).AsNoTracking().Distinct().ToListAsync();
            if (hospitalID == "5")
            {
                result = result.Select(m => new PatientBasicDataInfo
                {
                    PatientID = m.PatientID,
                    HospitalID = m.HospitalID,
                    ChartNo = m.ChartNo,
                    InpatientNo = m.InpatientNo,
                    IdentityID = EncryptionAndDecryption.DecryptStr(m?.IdentityID),
                    PatientName = m.PatientName,
                    Gender = m.Gender,
                    DateOfBirth = m.DateOfBirth,
                    TimeOfBirth = m.TimeOfBirth,
                    BloodType = m.BloodType,
                    NativePlace = m.NativePlace,
                    RH = m.RH,
                    LocalChartNO = m.LocalChartNO,
                }).ToList();
            }
            return result;
        }

        public async Task<List<PatientBasicDataView>> GetBasicDataAsync(string hospitalID, string[] patientIDs)
        {
            return await _dbContext.PatientBasicDatas.Where(m => patientIDs.Contains(m.PatientID) && m.HospitalID == hospitalID && m.DeleteFlag != "*")
                .Select(m => new PatientBasicDataView
                {
                    PatientID = m.PatientID,
                    PatientName = m.PatientName,
                    Gender = m.Gender
                }).ToListAsync();
        }

        public async Task<PatientBasicDataInfo> GetPatientByInpatientID(string inpatientID)
        {
            var query = (from inpaitentData in _dbContext.InpatientDatas
                         join patientBasicData in _dbContext.PatientBasicDatas
                          on inpaitentData.PatientID equals patientBasicData.PatientID
                         where inpaitentData.ID == inpatientID && inpaitentData.DeleteFlag != "*"
                         select patientBasicData);
            return await query.FirstOrDefaultAsync();
        }
        public async Task<string> GetPatientNameAsync(string chartNO)
        {
            return await _dbContext.PatientBasicDatas.Where(m => m.ChartNo == chartNO && m.DeleteFlag != "*").Select(m => m.PatientName).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 历史患者敏感数据查询
        /// </summary>
        /// <param name="charNo"></param>
        /// <param name="offDateTime"></param>
        /// <returns></returns>
        public async Task<List<PatientBasicDataInfo>> GetPatientHistoryList(string charNo, DateTime offDateTime)
        {
            if (charNo == null)
            {
                return await _dbContext.PatientBasicDatas.Where(m => m.ModifyDate < offDateTime).ToListAsync();
            }
            else
            {
                return await _dbContext.PatientBasicDatas.Where(m => m.ChartNo == charNo).ToListAsync();
            }
        }

        public async Task<List<PatientBasicDataView>> GetAllBasicInfoAsNoTrackAsync()
        {
            return await _dbContext.PatientBasicDatas.AsNoTracking().Select(
                m => new PatientBasicDataView
                {
                    PatientID = m.PatientID,
                    PatientName = m.PatientName,
                    Gender = m.Gender,
                    LocalChartNO = m.LocalChartNO,
                    HospitalID = m.HospitalID.Trim(),
                    DateOfBirth = m.DateOfBirth,
                    ChartNo = m.ChartNo,
                    IdentityID = m.IdentityID,
                    BirthDate = m.DateOfBirth
                }).ToListAsync();
        }

        /// <summary>
        /// 获取病人身份证号和姓名
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<PatientBasicDataView> GetPatientBasicInfoByInpatientIDAsync(string inpatientID)
        {
            var query = from m in _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*").AsNoTracking()
                        join n in _dbContext.PatientBasicDatas.Where(m => m.DeleteFlag != "*").AsNoTracking()
                         on m.PatientID equals n.PatientID
                        where m.ID == inpatientID
                        select new PatientBasicDataView
                        {
                            IdentityID = n.IdentityID,
                            PatientName = n.PatientName,
                        };

            return await query.FirstOrDefaultAsync();
        }
    }
}