﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Interconnect.Services
{
    public class PatientBasicService : IPatientBasicService
    {
        //Mdeical
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IPatientBasicDataRepository _patientBasicDataRepository;
        private readonly IInpatientDataRepository _inPatientRepository;
        private readonly IOptions<SystemConfig> _config;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ILogInfoServices _ILogInfoServices;
        private readonly ISettingDescriptionRepository _medicalSettingDescriptionRepository;
        private readonly ISettingDescRepository _ICSettingDescriptionRepository;
        private readonly ICommonHelper _commonHelper;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;

        /// <summary>
        /// 医疗院代码
        /// </summary>
        private readonly string HOSPITALID = "";
        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";

        public PatientBasicService(
              IUnitOfWork<MedicalDbContext> UnitOfWork
            , IPatientBasicDataRepository patientBasicDataRepository
            , IOptions<SystemConfig> config
             , ILogInfoServices LogInfoServices
             , ISettingDescRepository settingDescriptionRepository
            , ISettingDescriptionRepository medicalSettingDescriptionRepository
            , ICommonHelper commonHelper
            , IInpatientDataRepository inPatientRepository
            , IAppConfigSettingRepository appConfigSettingRepository
            )
        {
            _unitOfWork = UnitOfWork;
            _patientBasicDataRepository = patientBasicDataRepository;
            _config = config;
            _ILogInfoServices = LogInfoServices;
            _ICSettingDescriptionRepository = settingDescriptionRepository;
            _medicalSettingDescriptionRepository = medicalSettingDescriptionRepository;
            _commonHelper = commonHelper;
            _inPatientRepository = inPatientRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
            HOSPITALID = _config.Value.HospitalID;
        }

        public async Task<bool> SynchronizationMain(string chartNO)
        {
            var resultFalg = false;
            if (chartNO == "" || chartNO == null)
            {
                _logger.Info("同步所有在院病人基本信息");
                var medicalInPatientList = await _inPatientRepository.GetInpatientData();

                foreach (var item in medicalInPatientList)
                {
                    resultFalg = await SyncPatientBaseMain(item.ChartNo);
                    if (!resultFalg)
                    {
                        _logger.Error("病人：chartNO=" + item.ChartNo + "基本信息同步错误");
                    }
                }
            }
            else
            {
                resultFalg = await SyncPatientBaseMain(chartNO);
            }
            return resultFalg;
        }

        /// <summary>
        ///  病人信息数据同步
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncPatientBaseMain(string chartNO)
        {
            _logger.Info("开始获取病人信息api");
            string apiStr = "";
            var apiStrList = _ICSettingDescriptionRepository.GetAsync(1, "7");
            if (apiStrList.Count > 0)
            {
                apiStr = apiStrList[0].Description;
            }
            else
            {
                _logger.Error("获取病人信息API失败");
                return false;
            }
            _logger.Info("获取病人信息数据");
            var data = new Dictionary<string, string>
            {
                { "ChartNo", chartNO }
            };
            //呼叫API获得数据
            var resultData = _commonHelper.GetInterconnectData(apiStr, data);

            //从配置当中获取数据 梁宝华 2020-04-29
            var printInterfaceData = 0;
            var resultPrintDate = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "PrintInterfaceData");
            if (StringCheck.IsNumeric(resultPrintDate))
            {
                printInterfaceData = int.Parse(resultPrintDate);
            }

            if (printInterfaceData == 1)
            {
                _logger.Info(resultData + "获得数据如下");
            }
            var Interconnect_Data = new List<PatientBasicInfo>();

            var result = new ResponseResult();
            result = JsonConvert.DeserializeObject<ResponseResult>(resultData);
            try
            {
                Interconnect_Data = JsonConvert.DeserializeObject<List<PatientBasicInfo>>(result.Data.ToString());
            }
            catch (Exception ex)
            {
                _logger.Error("Json格式化失败:" + ex.ToString());
                return false;
            }
            _logger.Info("获得" + Interconnect_Data.Count() + "条数据");
            //中介库                  
            if (Interconnect_Data.Count > 0) //如果没有同步完成，则继续同步
            {
                var patientBase = await SyncPatientBaseDetail(Interconnect_Data);
                if (!(patientBase==null?false:true))
                {
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 更新病人基本信息
        /// </summary>
        /// <param name="gender"></param>
        /// <param name="item"></param>
        /// <param name="patient"></param>
        /// <returns></returns>
        private bool UPPatientBase(string gender, PatientBasicInfo item, PatientBasicDataInfo patient)
        {
            bool upDataflag = false;
            if (patient.PatientName != (item.PatientName ?? ""))
            {
                _logger.Info("更新PatientName" + item.ChartNo);
                patient.PatientName = (item.PatientName ?? "").Trim();
                upDataflag = true;
            }
            if (patient.IdentityID != (item.IdentityID ?? ""))
            {
                _logger.Info("更新IdentityID" + item.ChartNo);
                patient.IdentityID = (item.IdentityID ?? "").Trim();
                upDataflag = true;
            }
            if (patient.BloodType != (item.BloodType ?? ""))
            {
                _logger.Info("更新BloodType" + item.ChartNo);
                patient.BloodType = (item.BloodType ?? "").Trim();
                upDataflag = true;
            }
            if (patient.Gender != gender)
            {
                _logger.Info("更新Gender" + item.ChartNo);
                patient.Gender = gender;
                upDataflag = true;
            }
            if (patient.NativePlace != (item.NativePlace ?? ""))
            {
                _logger.Info("更新NativePlace" + item.ChartNo);
                patient.NativePlace = (item.NativePlace ?? "").Trim();
                upDataflag = true;
            }
            if (patient.DateOfBirth.HasValue && patient.DateOfBirth.Value != item.DateOfBirth)
            {
                patient.DateOfBirth = item.DateOfBirth;
                upDataflag = true;
            }
            if (patient.TimeOfBirth.HasValue && patient.TimeOfBirth.Value != item.TimeOfBirth)
            {
                patient.TimeOfBirth = item.TimeOfBirth;
                upDataflag = true;
            }
            if (upDataflag)
            {
                patient.DeleteFlag = "";
                patient.ModifyDate = DateTime.Now;
                patient.ModifyPersonID = "TongBu";
                patient.DateOfBirth = item.DateOfBirth;
                patient.TimeOfBirth = item.TimeOfBirth;
                return true;
            }
            else
            {
                return false;
            }
        }

        public async Task<List<PatientBasicDataInfo>> SyncPatientBaseDetail(List<PatientBasicInfo> originalData)
        {
            string tablename = " PatientBasicData";
            var PatientBasicList = new List<PatientBasicDataInfo>();
            var gender = "";
            var settingDescription = await _medicalSettingDescriptionRepository.GetBySettingTypeCode("Gender");
            _logger.Info("获取性别数据" + ListToJson.ToJson(settingDescription));
            LogInfo TempLog = null;
            TempLog = _ILogInfoServices.InnsertLogAsync(tablename, " 开始进行数据同步，数据条数：" + originalData.Count);

            foreach (var item in originalData)
            {
                bool upDataFlag = false;
                if (string.IsNullOrEmpty(item.ChartNo))
                {
                    continue;
                }
                //获得性别
                var settingDescriptionTemp = settingDescription.Where(m => m.Description == item.Gender).ToList();
                if (settingDescriptionTemp.Count <= 0)
                {
                    continue;
                }
                gender = settingDescriptionTemp[0].TypeValue;
                var patient = _patientBasicDataRepository.GetOnePatientBasic(HOSPITALID, item.ChartNo);
                if (patient != null)
                {
                    upDataFlag = UPPatientBase(gender, item, patient);
                    PatientBasicList.Add(patient);
                    _logger.Info(tablename + item.ChartNo+ "||病人基本信息修改完成!" + DateTime.Now);
                }
                else
                {
                    var data = new PatientBasicDataInfo
                    {
                        HospitalID = HOSPITALID,
                        PatientID = Guid.NewGuid().ToString("N"),
                        ChartNo = item.ChartNo ?? "",
                        PatientName = item.PatientName ?? "",
                        IdentityID = item.IdentityID ?? "",
                        BloodType = item.BloodType ?? "",
                        Gender = gender,
                        NativePlace = item.NativePlace ?? "",
                        DeleteFlag = "",
                        ModifyDate = DateTime.Now,
                        ModifyPersonID = "SYS",
                        DateOfBirth = item.DateOfBirth,
                        TimeOfBirth = item.TimeOfBirth
                    };
                    upDataFlag = true;
                    _unitOfWork.GetRepository<PatientBasicDataInfo>().Insert(data);
                    PatientBasicList.Add(data);
                    _logger.Info(tablename + item.ChartNo + "||病人基本信息新增完成!" + DateTime.Now);
                }
                //记录插入的数据，避免数据重复

                //如果数据有变动，进行更新
                if (upDataFlag)
                {
                   
                    try
                    {
                        _unitOfWork.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        _logger.Error(tablename + "||同步失败||" + ex.ToString());
                        return  null;
                    }
                    _logger.Info("更新病人基本信息成功,ChartNo:" + item.ChartNo);
                }
            }
          

            return PatientBasicList;
        }
    }
}