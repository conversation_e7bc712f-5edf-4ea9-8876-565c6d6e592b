﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("PatientOrderDetail")]
    public class PatientOrderDetailInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///医嘱KEY
        ///</summary>
        public string OrderID { get; set; }
        /// <summary>
        ///医嘱代码

        ///</summary>
        public string OrderCode { get; set; }
        /// <summary>
        ///医嘱类别
        ///</summary>
        public string OrderPattern { get; set; }
        /// <summary>
        ///医嘱内容

        ///</summary>
        public string OrderContent { get; set; }
        /// <summary>
        ///剂量
        ///</summary>
        [Column(TypeName = "decimal(10,2)")]
        public decimal OrderDose { get; set; }
        /// <summary>
        ///频次
        ///</summary>
        public string Frequency { get; set; }
        /// <summary>
        ///单位
        ///</summary>
        public string Unit { get; set; }
        /// <summary>
        ///总剂量
        ///</summary>
        [Column(TypeName = "decimal(10,2)")]
        public decimal TotalVolume { get; set; }
        /// <summary>
        ///服法/途径/姿势

        ///</summary>
        public string OrderRule { get; set; }
        /// <summary>
        ///部位

        ///</summary>
        public string Location { get; set; }
        /// <summary>
        ///检验类别
        ///</summary>
        public string MethodCategory { get; set; }
        /// <summary>
        ///检体
        ///</summary>
        public string SampleName { get; set; }
        /// <summary>
        ///执行次数

        ///</summary>
        public Byte? NumberOfExecution { get; set; }
        /// <summary>
        ///开始日期
        ///</summary>
        public DateTime StartDate { get; set; }
        /// <summary>
        ///开始时间
        ///</summary>
        public TimeSpan StartTime { get; set; }
        /// <summary>
        ///结束日期
        ///</summary>
        public DateTime EndDate { get; set; }
        /// <summary>
        ///结束时间

        ///</summary>
        public TimeSpan? EndTime { get; set; }
        /// <summary>
        ///医嘱已执行标记

        ///</summary>
        public string PerformedFlag { get; set; }
        /// <summary>
        ///医嘱已执行人员工号

        ///</summary>
        public string PerformedPersonID { get; set; }
        /// <summary>
        ///医嘱已执行日期时间
        ///</summary>
        public DateTime PerformedDate { get; set; }
    }
}