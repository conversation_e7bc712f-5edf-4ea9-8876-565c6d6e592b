﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class NursingInterventionMainRepository : INursingInterventionMainRepository
    {
        private MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public NursingInterventionMainRepository(
               MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService

            )
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<NursingInterventionMainInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("language", out var language);
            return await _medicalDbContext.NursingInterventionMain.Where(m =>  m.Language == (Int32)language && m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.NursingInterventionMain.GetKey(_sessionCommonServer);
        }

        /// <summary>
        /// 透过护理措施序号获得措施
        /// </summary>
        /// <param name="nursingInterventionMainID">护理措施序号</param>
        /// <returns></returns>
        public async Task<List<NursingInterventionMainInfo>> GetByID(int[] nursingInterventionMainID)
        {
            var datas = await GetAsync();

            return datas.Where(m => nursingInterventionMainID.Contains(m.ID)).ToList();
        }

        /// <summary>
        /// 透过护理措施序号获得措施
        /// </summary>
        /// <param name="nursingInterventionMainID">护理措施序号</param>
        /// <param name="language">语言</param>
        /// <returns></returns>
        public async Task<NursingInterventionMainInfo> GetByID(int nursingInterventionMainID)
        {
            var datas = await GetAsync();

            return datas.Where(m => m.ID == nursingInterventionMainID).FirstOrDefault();
        }

        /// <summary>
        /// 透过护理措施序号获得措施
        /// </summary>
        /// <param name="nursingInterventionMainID">护理措施序号</param>
        /// <param name="language">语言</param>
        /// <returns></returns>
        public async Task<NursingInterventionMainInfo> GetByIDNoCache(int nursingInterventionMainID, int language)
        {
            var datas = await _medicalDbContext.NursingInterventionMain.Where(m => m.Language == language && m.DeleteFlag != "*").ToListAsync();

            return datas.Where(m => m.ID == nursingInterventionMainID).FirstOrDefault();
        }
        /// <summary>
        /// 获取护理问题，电子病历使用，所以需要获得已经删除的措施码
        /// </summary>
        /// <param name="language"></param>
        /// <returns></returns>
        public async Task<NursingInterventionMainView> GetNursingInterventionMainByID(int mainID, int language)
        {
            var datas = await GetAsync();

            var mainInfo = datas.Where(m => m.ID == mainID).FirstOrDefault();

            if (mainInfo != null)
            {
                return new NursingInterventionMainView
                {
                    ID = mainInfo.ID,
                    InterventionCode = mainInfo.InterventionCode,
                    ActionType = mainInfo.ActionType,
                    ActionTypeName = mainInfo.ActionTypeName,
                    Intervention = mainInfo.Intervention,
                    InterventionLongName = mainInfo.InterventionLongName,
                    Reference = mainInfo.Reference,
                    RecordsContent = mainInfo.RecordsContent,
                    Sort = mainInfo.Sort,
                    InforbuttonType = mainInfo.InforbuttonType,
                    InforbuttonContent = mainInfo.InforbuttonContent,
                    LinkForm = mainInfo.LinkForm,
                    InstrumentTimeRange = mainInfo.InstrumentTimeRange,
                    PDAFlag = mainInfo.PDAFlag,
                    AlertTime = mainInfo.AlertTime,
                    InterventionType = mainInfo.InterventionType
                };
            }
            //从删除的措施里面获取
            var nursingInterventionMainDelete = await GetNursingInterventionMainDeleteByID(mainID, language);

            if (nursingInterventionMainDelete != null)
            {
                return nursingInterventionMainDelete;
            }
            return null;
        }

        /// <summary>
        /// 获取删除的护理措施
        /// </summary>
        /// <param name="mainID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        private async Task<NursingInterventionMainView> GetNursingInterventionMainDeleteByID(int mainID, int language)
        {
            var result = from n in _medicalDbContext.NursingInterventionMain
                         where mainID == n.ID && n.Language == language
                         && n.DeleteFlag == "*"
                         select new NursingInterventionMainView
                         {
                             ID = n.ID,
                             InterventionCode = n.InterventionCode,
                             ActionType = n.ActionType,
                             ActionTypeName = n.ActionTypeName,
                             Intervention = n.Intervention,
                             InterventionLongName = n.InterventionLongName,
                             Reference = n.Reference,
                             RecordsContent = n.RecordsContent,
                             Sort = n.Sort,
                             InforbuttonType = n.InforbuttonType,
                             InforbuttonContent = n.InforbuttonContent,
                             LinkForm = n.LinkForm,
                             InstrumentTimeRange = n.InstrumentTimeRange,
                             PDAFlag = n.PDAFlag,
                             AlertTime = n.AlertTime,
                             InterventionType = n.InterventionType

                         };
            return await result.FirstOrDefaultAsync();
        }

        public async Task<List<SimpleIntervention>> GetSimpleInterventions()
        {
            var datas = await GetAsync();

            return datas.Select(n => new SimpleIntervention
            {
                ID = n.ID,
                InterventionCode = n.InterventionCode,
                ActionType = n.ActionType,
                ActionTypeName = n.ActionTypeName,
                Intervention = n.Intervention,
                InterventionType = string.IsNullOrEmpty(n.InterventionType) ? "D" : n.InterventionType,
                LinkForm = n.LinkForm,
                Sort = n.Sort,
                InstrumentTimeRange = n.InstrumentTimeRange,
                BatchMonitorFlag = n.BatchMonitorFlag
            }).ToList();
        }

        public async Task<List<NursingInterventionMainInfo>> GetAsync()
        {
            var datas = (List<NursingInterventionMainInfo>)await GetCacheAsync();

            if (datas != null)
            {
                return datas;
            }

            return new List<NursingInterventionMainInfo>();
        }
        public async Task<List<NursingInterventionMainInfo>> GetInterventionMainOfBoard()
        {
            var datas = await GetAsync();

            return datas.Where(m => m.ActionType == "2" && m.PerformType != null).ToList();
        }

        public async Task<List<NursingInterventionMainInfo>> GetInterventionMainByActionType(string actionType)
        {
            var datas = await GetAsync();

            return datas.Where(m => m.ActionType.Trim() == actionType).ToList();
        }
        /// <summary>
        /// 透过护理措施序号获得措施
        /// </summary>
        /// <param name="nursingInterventionMainID">护理措施序号</param>
        /// <returns></returns>
        public async Task<List<KeyValueString>> GetByIDArr(int[] nursingInterventionMainID)
        {
            var datas = await GetAsync();

            return datas.Where(m => nursingInterventionMainID.Contains(m.ID)).Select(m =>
                    new KeyValueString
                    {
                        Key = m.ID.ToString(),
                        Value = m.InterventionLongName,
                    }).ToList();
        }
        public async Task<List<int>> GetInterventionMainIDByLinkForm(string linkForm)
        {
            var datas = await GetAsync();
            return datas.Where(m => m.LinkForm.Trim() == linkForm).Select(m => m.ID).ToList();
        }
        /// <summary>
        /// 根据ID区间获取数据
        /// </summary>
        /// <param name="maxID"></param>
        /// <param name="minID"></param>
        /// <returns></returns>
        public async Task<List<NursingInterventionMainInfo>> GetInterventionMainByIDRange(int maxID, int minID)
        {
            var datas = await GetAsync();
            return datas.Where(m => m.ID <= maxID && m.ID >= minID).ToList();
        }
        /// <summary>
        /// 根据批量监测标记获取措施
        /// </summary>
        /// <param name="batchMonitorFlag">标记</param>
        /// <returns></returns>
        public async Task<SimpleIntervention[]> GetInterventionsByBatchMonitorFlag(bool batchMonitorFlag)
        {
            var interventions = await GetAsync();

            return interventions.Where(m => m.BatchMonitorFlag == batchMonitorFlag).Select(n => new SimpleIntervention
            {
                ID = n.ID,
                InterventionCode = n.InterventionCode,
                ActionType = n.ActionType,
                ActionTypeName = n.ActionTypeName,
                Intervention = n.Intervention,
                InterventionType = string.IsNullOrEmpty(n.InterventionType) ? "D" : n.InterventionType,
                LinkForm = n.LinkForm,
                Sort = n.Sort,
                InstrumentTimeRange = n.InstrumentTimeRange,
            }).ToArray();
        }
    }
}
