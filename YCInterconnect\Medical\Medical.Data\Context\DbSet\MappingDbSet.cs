﻿using Medical.Models;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public partial class MedicalDbContext
    {
        /// <summary>
        /// 问题因素
        /// </summary>
        public DbSet<ProblemToRelatedFactorInfo> ProblemToRelatedFactors { get; set; }

        /// <summary>
        /// 问题措施
        /// </summary>
        public DbSet<ProblemToInterventionInfo> ProblemToInterventions { get; set; }

        /// <summary>
        /// 问题目标
        /// </summary>
        public DbSet<ProblemToGoalInfo> ProblemToGoals { get; set; }

        /// <summary>
        /// 评估对应问题
        /// </summary>
        public DbSet<AssessToProblemInfo> AssessToProblems { get; set; }

        /// <summary>
        /// 评估对应相关因素
        /// </summary>
        public DbSet<AssessToRelatedFactorInfo> AssessToRelatedFactors { get; set; }

        /// <summary>
        /// 导管对应身体部位
        /// </summary>
        public DbSet<TubeToBodyPartInfo> TubeToBodyParts { get; set; }

        /// <summary>
        /// 问题对实际目标
        /// </summary>
        public DbSet<ProblemToOutcomeInfo> ProblemToOutcomes { get; set; }


        /// <summary>
        /// 医嘱对应护理措施
        /// </summary>
        public DbSet<PhysicianToInterventionInfo> PhysicianToInterventions { get; set; }
        /// <summary>
        /// 角色对应权限
        /// </summary>
        public DbSet<AuthorityRoleListInfo> AuthorityRoleListInfos { get; set; }
        /// <summary>
        /// 人员对应角色
        /// </summary>
        public DbSet<UserRoleInfo> UserRoleInfos { get; set; }
        /// <summary>
        /// 检验项目对应项目
        /// </summary>
        public DbSet<TestItemToAssessListInfo> TestItemToAssessListInfos { get; set; }
        /// <summary>
        /// 护理单元科室对应
        /// </summary>
        public DbSet<StationToDeptInfo> StationToDeptInfos { get; set; }
        /// <summary>
        /// HIS医嘱频次对照
        /// </summary>
        public DbSet<HISFrequencyToNISInfo> HISFrequencyToNISInfos { get; set; }
        /// <summary>
        /// 集束护理对应问题
        /// </summary>
        public DbSet<NursingClusterOrderInfo> NursingClusterOrderInfos { get; set; }
        /// <summary>
        /// 评估项目多对一
        /// </summary>
        public DbSet<AssessIncludeInfo> AssessIncludeInfos { get; set; }
        /// <summary>
        /// 医嘱对应评估
        /// </summary>
        public DbSet<OrderToAssessListInfo> OrderToAssessListInfos { get; set; }

        /// <summary>
        /// 医嘱对应评估
        /// </summary>
        public DbSet<DepartmentToRecordListInfo> DeptmentToRecordListInfos { get; set; }

        /// <summary>
        /// 班表对照
        /// </summary>
        public DbSet<HRShiftToNISInfo> HRShiftToNISInfos { get; set; }
        /// <summary>
        /// 虚拟病区
        /// </summary>
        public DbSet<VirtualStationListInfo> VirtualStationListInfos { get; set; }
        /// <summary>
        /// 虚拟床位
        /// </summary>
        public DbSet<VirtualBedListInfo> VirtualBedListInfos { get; set; }

        // 报表对部位
        public DbSet<RecordsToBodyPartInfo> RecordsToBodyPartInfos { get; set; }
        /// <summary>
        /// 门诊编号对应
        /// </summary>
        public DbSet<MRNMappingInfo> MrnMappingInfos { get; set; }
    }
}