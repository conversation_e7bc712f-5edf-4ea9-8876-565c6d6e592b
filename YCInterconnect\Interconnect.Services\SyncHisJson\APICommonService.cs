﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Interconnect.Services.Interface;
using NLog;
using Medical.Common;

namespace Interconnect.Services
{
    public class APICommonService : IAPICommonService
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        /// <summary>
        /// 异步GET方式获取平台数据
        /// </summary>
        /// <param name="api"></param>
        /// <param name="dictionarys"></param>
        /// <returns></returns>
        public  async Task<string> GetApiDataHttpGet(string url, string contentType, Dictionary<string, string> headers)
        {
            if (string.IsNullOrEmpty(url))
            {
                return "没有获取API";
            }
            _logger.Info(url);
            string hisData = "";
            try
            {
                hisData = await HttpHelper.HttpGetAsync(url, contentType, headers);
            }
            catch (Exception ex)
            {
                hisData = null;
                _logger.Error("URL||" + url + ex.ToString());
                return "呼叫API错误，请查看日志";
            }
            _logger.Info("hisData||"+hisData);
            if (hisData == "{}" || hisData == "[]" || hisData == "[{}]")
            {
                return "";
            }
            //检核数据格式是否正常
            hisData = CheckJsonData(hisData);
            return hisData;
        }
        public static string CheckJsonData(string data)
        {
            var subStrFlag = data.Contains('[');
            if (!subStrFlag)
            {
                data = "[" + data + "]";

            }
            return data;
        }


    }
}