﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Medical.Models;
using ViewModel;

namespace Interconnect.Services.Interface
{
    public interface IPatientOrderMainService
    {      
        /// <summary>
        /// 同步医嘱主记录
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncPatientOrderByDateTime();

        /// <summary>
        /// 同步医嘱
        /// </summary>
        /// <param name="ApiID"></param>
        /// <returns></returns>
        Task<bool> SyncHistoryPatientOrder();

        /// <summary>
        /// 同比指定时间段内的医嘱
        /// </summary>
        /// <param name="startDateTime"></param>
        /// <param name="endDateTime"></param>
        /// <param name="apiAddress"></param>
        /// <param name="hospitalID"></param>
        /// <param name="syncStopFlag"></param>
        /// <returns></returns>
        Task SyncHisOrderByManual(DateTime startDateTime, DateTime endDateTime, string apiAddress, string hospitalID
             , bool syncStopFlag, string caseNumber);
        /// <summary>
        /// 同步his医嘱数据
        /// </summary>
        /// <param name="hISOrderViews"></param>
        /// <param name="orderToAssessList"></param>
        /// <param name="physicianToInterventionList"></param>
        /// <param name="frequencyList"></param>
        /// <param name="physicianOrderInfos"></param>
        /// <param name="OrderKeyWordList"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task SyncHisData(List<HISOrderView> hISOrderViews
            , List<OrderToAssessListInfo> orderToAssessList
            , List<PhysicianToInterventionInfo> physicianToInterventionList
            , List<FrequencyInfo> frequencyList
            , List<PhysicianOrderInfo> physicianOrderInfos
            , List<string> OrderKeyWordList
            , string hospitalID);
        /// <summary>
        /// 病区同步his医嘱数据
        /// </summary>
        /// <param name="hISOrderViews"></param>
        /// <param name="orderToAssessList"></param>
        /// <param name="physicianToInterventionList"></param>
        /// <param name="frequencyList"></param>
        /// <param name="physicianOrderInfos"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task<bool> SyncHisDataByStationCode(List<HISOrderView> hISOrderViews, List<OrderToAssessListInfo> orderToAssessList, List<PhysicianToInterventionInfo> physicianToInterventionList
            , List<FrequencyInfo> frequencyList, List<PhysicianOrderInfo> physicianOrderInfos, string hospitalID);
    }
}