﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class AuthorityRoleListRepository : IAuthorityRoleListRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly GetCacheService _getCacheService;

        public AuthorityRoleListRepository(MedicalDbContext db, IMemoryCache memoryCache, GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _getCacheService = getCacheService;
        }
        /// <summary>
        /// 根据角色ID获取 角色权限
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<List<AuthorityRoleListInfo>> GetAsync(int roleID)
        {
            //var datas = await GetCacheAsync();
            //if (datas != null)
            //{
            //    return (datas as List<AuthorityRoleListInfo>).Where(t => t.AuthorityRoleID == roleID &&t.DeleteFlag != "*").ToList();

            //}
            //return null;
            return await _medicalDbContext.AuthorityRoleListInfos.Where(m => m.AuthorityRoleID == roleID
           && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据角色ID获取权限
        /// </summary>
        /// <param name="roleID"></param>
        /// <returns></returns>
        public async Task<List<AuthorityRoleListInfo>> GetNoCacheAsync(int roleID)
        {
            return await _medicalDbContext.AuthorityRoleListInfos.Where(m => m.AuthorityRoleID == roleID
            && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据角色ID获取 角色权限
        /// </summary>
        /// <param name="roleIDs"></param>
        /// <returns></returns>
        public async Task<List<AuthorityRoleListInfo>> GetAsync(int[] roleIDs)
        {
            var datas = await GetCacheAsync();
            if (datas != null)
            {
                return (datas as List<AuthorityRoleListInfo>).Where(t => roleIDs.Contains(t.AuthorityRoleID)).ToList();
            }
            return null;
        }

        public async Task<int> GetMaxIDAsync()
        {
            var datas = await GetCacheAsync();
            if (datas != null)
            {
                var list = datas as List<AuthorityRoleListInfo>;
                list = list.OrderByDescending(m => m.ID).ToList();
                if (list.Count == 0)
                {
                    return 1;
                }
                return list[0].ID + 1;
            }
            return 1;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<AuthorityRoleListInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            return await _medicalDbContext.AuthorityRoleListInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.AuthorityRoleList.ToString();
        }

        public async Task<bool> add(AuthorityRoleListInfo roleAuth)
        {
            _medicalDbContext.AuthorityRoleListInfos.Add(roleAuth);
            return await _medicalDbContext.SaveChangesAsync() > 0;
        }
        /// <summary>
        /// 获取所有权限数据,包含删除
        /// </summary>
        /// <returns></returns>
        public async Task<List<AuthorityRoleListInfo>> GetAllListAsync()
        {
            return await _medicalDbContext.AuthorityRoleListInfos.ToListAsync();
        }
    }
}
