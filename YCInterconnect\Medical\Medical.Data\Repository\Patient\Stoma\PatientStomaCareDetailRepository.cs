﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientStomaCareDetailRepository : IPatientStomaCareDetailRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientStomaCareDetailRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }
        /// <summary>
        /// 根据造口评估主表获取明细列表
        /// </summary>
        /// <param name="stomaCareMainID"></param>
        /// <param name=""></param>
        /// <returns></returns>
        public async Task<List<PatientStomaCareDetailInfo>> GetByMainIDAsync(string stomaCareMainID)
        {
            return await _medicalDbContext.PatientStomaCareDetailInfos.Where(t => t.PatientStomaCareMainID == stomaCareMainID && t.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据患者住院号获取明细列表
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientStomaCareDetailInfo>> GetByInpatientIDAsync(string inpatientID)
        {
            return await _medicalDbContext.PatientStomaCareDetailInfos.Where(t => t.InpatientID == inpatientID && t.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 查询主表所对应的同组下的选项
        /// </summary>
        /// <param name="careMainIDs">主表IDs</param>
        /// <param name="groupID">组ID</param>
        /// <returns></returns>
        public async Task<Dictionary<string, int>> GetDetailsByCareMainAndGroupID(string[] careMainIDs, int groupID)
        {
            return await _medicalDbContext.PatientStomaCareDetailInfos.Where(m => careMainIDs.Contains(m.PatientStomaCareMainID) && m.AssessListGroupID == groupID && m.DeleteFlag != "*")
                 .ToDictionaryAsync(m => m.PatientStomaCareMainID, m => m.AssessListID);
        }
        public async Task<List<ProfileDetail>> GetByParamAsync(string inpatientID, int groupID)
        {
            return await (from m in _medicalDbContext.PatientStomaCareMainInfos
                          join n in _medicalDbContext.PatientStomaCareDetailInfos on m.PatientStomaCareMainID equals n.PatientStomaCareMainID
                          where m.InpatientID == inpatientID && m.DeleteFlag != "*" && n.AssessListGroupID == groupID && n.DeleteFlag != "*"
                          select new ProfileDetail
                          {
                              ProfileDate = m.AssessDate,
                              ProfileTime = m.AssessTime,
                              AssessListID = n.AssessListID,
                              StationID = m.StationID
                          }).ToListAsync();
        }
        /// <summary>
        /// 根据维护记录ID集合获取明细
        /// </summary>
        /// <param name="careMainIDs">维护记录ID集合</param>
        /// <returns></returns>
        public async Task<List<Detail>> GetDetailsByCareMainIDs(params string[] careMainIDs)
        {
            return await _medicalDbContext.PatientStomaCareDetailInfos
                .Where(m => careMainIDs.Contains(m.PatientStomaCareMainID))
                .Select(m => new Detail
                {
                    ID = m.PatientStomaCareMainID,
                    AssessListID = m.AssessListID,
                    AssessListGroupID = m.AssessListGroupID,
                    AssessValue = m.AssessValue,
                }).ToListAsync();
        }
    }
}