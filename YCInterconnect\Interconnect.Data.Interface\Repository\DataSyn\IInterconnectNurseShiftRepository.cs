﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Interconnect.Models;

namespace Interconnect.Data.Interface
{
   public interface IInterconnectNurseShiftRepository
    {
        /// <summary>
        /// 获取基本信息
        /// </summary>
        /// <returns></returns>
        List<NurseShiftInfo> GetAsync();


        /// <summary>
        /// 获取需要抽取的数据
        /// </summary>
        /// <param name="tongbuCount"></param>
        /// <returns></returns>
        List<NurseShiftInfo> GetDataPump(int tongbuCount);
    }
}
