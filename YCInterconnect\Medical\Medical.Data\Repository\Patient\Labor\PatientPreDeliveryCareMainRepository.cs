﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.Models.Patient;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    /// <summary>
    /// 维护记录副表,待产录仓储层
    /// </summary>
    public class PatientPreDeliveryCareMainRepository : IPatientPreDeliveryCareMainRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientPreDeliveryCareMainRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }
        /// <summary>
        /// 根据维护记录ID集合获取待产数据
        /// </summary>
        /// <param name="careMainInfos">维护记录主表集合</param>
        /// <returns></returns>
        public async Task<List<dynamic>> GetViewsByCareMainIDs(List<PatientDeliveryCareMainInfo> careMainInfos)
        {
            var careMainIDs = careMainInfos.Select(m => m.PatientDeliveryCareMainID);
            var preDeliveryCareMains = await _medicalDbContext.PatientPreDeliveryCareMainInfos.Where(m => careMainIDs.Contains(m.PatientDeliveryCareMainID)).ToListAsync();
            var preDeliveryViews = preDeliveryCareMains.Join(careMainInfos,
                m => m.PatientDeliveryCareMainID, n => n.PatientDeliveryCareMainID,
                (m, n) => (dynamic)new PatientPreDeliveryCareMainView
                {
                    PatientDeliveryCareMainID = m.PatientDeliveryCareMainID,
                    InpatientID = n.InpatientID,
                    StationID = n.StationID,
                    DepartmentListID = n.DepartmentListID,
                    AssessDate = n.AssessDate,
                    AssessTime = n.AssessTime,
                    ContractionIntensity = m.ContractionIntensity,
                    UterineContractions = m.UterineContractions,
                    ContractionInterval = m.ContractionInterval,
                    ContractionDuration = m.ContractionDuration,
                    FetalPosition = m.FetalPosition,
                    FetalHeartRates = m.FetalHeartRates,
                    FetalPresentation = m.FetalPresentation,
                    CervixDilatatio = m.CervixDilatatio,
                    Effacement = m.Effacement,
                    MembraneStatus = m.MembraneStatus,
                    Oxytocin = m.Oxytocin,
                    RecordContent = m.RecordContent,
                    PainAssess = m.PainAssess,
                    UserID = m.AddEmployeeID,
                    BringToShift = n.BringToShift,
                    InformPhysician = n.InformPhysician,
                    BringToNursingRecord = n.BringToNursingRecord
                }).ToList();
            return preDeliveryViews;
        }
        /// <summary>
        /// 根据维护记录ID获取副表数据
        /// </summary>
        /// <param name="careMainID">维护记录ID</param>
        /// <returns></returns>
        public async Task<dynamic> GetByCareMainID(string careMainID)
        {
            return await _medicalDbContext.PatientPreDeliveryCareMainInfos.Where(m => careMainID == m.PatientDeliveryCareMainID)
                .Select(m => (dynamic)m).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据主键集合获取产前数据
        /// </summary>
        /// <param name="careMainIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientPreDeliveryCareMainInfo>> GetByCareMainIDs(List<string> careMainIDs)
        {
            return await _medicalDbContext.PatientPreDeliveryCareMainInfos.Where(m => careMainIDs.Contains(m.PatientDeliveryCareMainID)).ToListAsync();
        }
    }
}
