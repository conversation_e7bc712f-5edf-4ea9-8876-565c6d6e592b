﻿using Medical.Models;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public partial class MedicalDbContext
    {
        public DbSet<EMRListInfo> EMRListInfos { get; set; }
        public DbSet<NurseEMRFileListInfo> NurseEMRFileListInfos { get; set; }
        public DbSet<NurseEMRFileListBakInfo> nurseEMRFileListBakInfos { get; set; }
        public DbSet<NurseEMRFileLogInfo> NurseEMRFileLogInfos { get; set; }
        public DbSet<EMRRecordFieldInfo> EMRRecordFieldInfos { get; set; }
        public DbSet<EMRFieldInfo> EMRFieldInfos { get; set; }
        public DbSet<EMRSourceInfo> EMRSourceInfos { get; set; }
        public DbSet<EMRSourceListInfo> EMRSourceListInfos { get; set; }
        public DbSet<EMRDocumentInfo> EMRDocumentInfos { get; set; }
        public DbSet<PatientNursingRecordDetailLogInfo> PatientNursingRecordDetailLogInfos { get; set; }
        public DbSet<PatientNursingRecordDetailInfo> PatientNursingRecordDetailInfos { get; set; }
        public DbSet<PatientDataStorageRecordInfo> PatientDataStorageRecordInfos { get; set; }
    }
}
