﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
namespace Medical.Data.Repository
{
    public class NurseEMRFileListRepository : INurseEMRFileListRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;

        public NurseEMRFileListRepository(
            MedicalDbContext medicalDb)
        {
            _medicalDbContext = medicalDb;
        }

        public async Task<List<NurseEMRFileListInfo>> GetAllNurseEMRFileList(string inpateintID)
        {
            return await _medicalDbContext.NurseEMRFileListInfos.Where(m => m.Inpatientid == inpateintID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<NurseEMRFileView>> GetAllNurseEMRFileViews(string inpateintID)
        {
            var result = (from m in _medicalDbContext.NurseEMRFileListInfos
                          where m.Inpatientid == inpateintID && m.DeleteFlag != "*"
                          select new NurseEMRFileView
                          {
                              InpatientID = m.Inpatientid,
                              StationID = int.Parse(m.Stationid),
                              FileClass = m.FileClass,
                              RecordListID = m.RecordListID,
                              SerialNumber = m.SerialNumber
                          });
            return await result.ToListAsync();
        }


        public List<NurseEMRFileListInfo> GetOnePatientNurseEMRFileList(string inpateintID)
        {
            return _medicalDbContext.NurseEMRFileListInfos.Where(m => m.Inpatientid == inpateintID && m.DeleteFlag != "*").ToList();
        }

        public async Task<List<NurseEMRFileListInfo>> GetAllNurseEMRFileList(string[] inpateintIDs)
        {
            var nurseEMRFileList = new List<NurseEMRFileListInfo>();

            for (int i = 0; i < inpateintIDs.Length; i++)
            {
                //根据病人ID查询
                var tempList = await _medicalDbContext.NurseEMRFileListInfos.Where(m => m.Inpatientid == inpateintIDs[i] && m.DeleteFlag != "*").ToListAsync();
                nurseEMRFileList = nurseEMRFileList.Union(tempList).ToList();
            }
            return nurseEMRFileList;
        }

        public async Task<List<NurseEMRFileListInfo>> GetPatientRecordByFileClass(string inpateintID, int fileClass, int stationID)
        {
            return await _medicalDbContext.NurseEMRFileListInfos.Where(m => m.Inpatientid == inpateintID
            && m.Stationid == stationID.ToString()
            && m.FileClass == fileClass && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<NurseEMRFileListInfo> GetInpatientRecordByFileClass(string inpateintID, int fileClass, int stationID, int? recordListID, string serialNumber)
        {
            return await _medicalDbContext.NurseEMRFileListInfos.Where(m => m.Inpatientid == inpateintID
                      && m.Stationid == stationID.ToString()
                      && m.FileClass == fileClass && m.RecordListID == recordListID && m.SerialNumber == serialNumber
                      ).FirstOrDefaultAsync();
        }

        public async Task<List<NurseEMRFileListInfo>> GetAllPatientRecordByFileClass(string inpateintID, int fileClass, int stationID)
        {
            return await _medicalDbContext.NurseEMRFileListInfos.Where(m => m.Inpatientid == inpateintID
            && m.Stationid == stationID.ToString()
            && m.FileClass == fileClass).ToListAsync();
        }


        public async Task<List<NurseEMRFileListInfo>> GetNurseEMRFileListByCaseNumber(string caseNumber, int fileClass)
        {
            if (fileClass == 0)
            {
                return await _medicalDbContext.NurseEMRFileListInfos.Where(m => m.CaseNumber == caseNumber && m.DeleteFlag != "*").ToListAsync();
            }
            else
            {
                //根据传来的fileClass查询
                return await _medicalDbContext.NurseEMRFileListInfos.Where(m => m.CaseNumber == caseNumber && m.FileClass == fileClass && m.DeleteFlag != "*").ToListAsync();
            }
        }

        public async Task<List<string>> GetNurseEMRFileListByDateTime(DateTime startDateTime, DateTime endDateTime)
        {

            return await _medicalDbContext.NurseEMRFileListInfos.Where(m => m.AddDate >= startDateTime && m.AddDate <= endDateTime && m.DeleteFlag != "*").Select(m => m.FileName).ToListAsync();
        }

        public async Task<List<NurseEMRFileListInfo>> GetNurseEMRFileListsByDateTime(DateTime startDateTime, DateTime endDateTime)
        {

            return await _medicalDbContext.NurseEMRFileListInfos.Where(m => m.AddDate >= startDateTime && m.AddDate <= endDateTime && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<NurseEMRFileListInfo>> GetUnPumpEMRFiles(int fileClass)
        {
            if (fileClass == 0)
            {
                return await _medicalDbContext.NurseEMRFileListInfos.Where(m => m.DataPumpFlag != "*" && m.DeleteFlag != "*").ToListAsync();
            }

            return await _medicalDbContext.NurseEMRFileListInfos.Where(m => m.FileClass == fileClass && m.DataPumpFlag != "*" && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 获取已归档、未上传的电子病历
        /// </summary>
        /// <param name="count">一次回传文件数，不传默认5000条</param>
        /// <param name="id">可空，若传递，则只获取这一个病历</param>
        /// <returns></returns>
        public async Task<List<NurseEMRFileListInfo>> GetAllUnPumpEMRFiles(int count, int? id = null)
        {
            var data = await _medicalDbContext.NurseEMRFileListInfos.Join(_medicalDbContext.InpatientDatas,
                a => a.Inpatientid, b => b.ID, (a, b) => new { a, b }).Where(m =>
                InHospitalStatus.DISCHARGEDHOSPITALLIST.Contains(m.b.InHospitalStatus ?? -1) && m.a.DataPumpFlag != "*" &&
                m.a.DeleteFlag != "*" && m.b.DeleteFlag != "*" && m.b.EMRArchivingFlag == "*").Where(id.HasValue, m => m.a.ID == id)
                .OrderByDescending(m => m.b.DischargeDate).Select(m => m.a).Take(count == 0 ? 5000 : count).ToListAsync();
            return data;
        }



        //获取已经归档，没有签名的病人列表
        public async Task<List<string>> GetAllUnCAEMRFiles()
        {
            return await (from a in _medicalDbContext.NurseEMRFileListInfos
                          join b in _medicalDbContext.InpatientDatas
                          on a.Inpatientid equals b.ID
                          where InHospitalStatus.DISCHARGEDHOSPITALLIST.Contains(b.InHospitalStatus ?? -1) && a.DeleteFlag != "*"
                          select a.Inpatientid).Distinct().ToListAsync();
        }


        public async Task<NurseEMRFileListInfo> GetOneEMRFile(string fileID)
        {
            return await _medicalDbContext.NurseEMRFileListInfos.Where(m => m.FileId == fileID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<int> GetEMRFileIDByFileID(string fileID)
        {
            return await _medicalDbContext.NurseEMRFileListInfos.Where(m => m.FileId == fileID && m.DeleteFlag != "*").Select(m => m.ID).FirstOrDefaultAsync();

        }

        public async Task<string> GetFileIDByID(int ID)
        {
            return await _medicalDbContext.NurseEMRFileListInfos.Where(m => m.ID == ID && m.DeleteFlag != "*").Select(m => m.FileId).FirstOrDefaultAsync();
        }

        public async Task<List<NurseEMRFileListInfo>> GetRiskEMRFileListByRecordListID(string Inpatientid, int RecordListID)
        {

            return await _medicalDbContext.NurseEMRFileListInfos.Where(m => m.Inpatientid == Inpatientid && m.RecordListID == RecordListID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<string>> GetEMRFileLogFileId(DateTime startDateTime, DateTime endDatetime)
        {
            return await _medicalDbContext.NurseEMRFileLogInfos.Where(m => m.AddDate >= startDateTime && m.AddDate <= endDatetime).Select(m => m.FileId).ToListAsync();
        }

        /// <summary>
        /// 根据患者CaseNumber获取数据 
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        public async Task<List<int>> GetByCaseNumber(string caseNumber)
        {
            //根据传来的fileClass查询
            return await _medicalDbContext.NurseEMRFileListInfos.Where(m => m.CaseNumber == caseNumber && m.DeleteFlag != "*")
                .Select(m => m.FileClass)
                .ToListAsync();
        }
        /// <summary>
        /// 根据序列号获取数据
        /// </summary>
        /// <param name="serialNumber"></param>
        /// <param name="fileclass"></param>
        /// <returns></returns>
        public async Task<List<NurseEMRFileListInfo>> GetBySN(string serialNumber, int fileclass)
        {
            return await _medicalDbContext.NurseEMRFileListInfos.Where(m => m.SerialNumber == serialNumber && m.FileClass == fileclass).ToListAsync();
        }

        public async Task<NurseEMRFileListInfo> GetByID(int nurseEMRFileListID)
        {
            return await _medicalDbContext.NurseEMRFileListInfos.Where(m => m.ID == nurseEMRFileListID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<List<NurseEMRFileListInfo>> GetIDByInpatientIDs(List<string> inpatientIDs)
        {
            return await _medicalDbContext.NurseEMRFileListInfos.Where(m => inpatientIDs.Contains(m.Inpatientid) && m.DeleteFlag != "*")
                .Select(m => new NurseEMRFileListInfo
                {
                    Inpatientid = m.Inpatientid,
                    FileClass = m.FileClass,
                    Stationid = m.Stationid
                }).ToListAsync();
        }
        public async Task<NurseEMRFileListInfo> GetFileIDByIDView(int ID)
        {
            return await _medicalDbContext.NurseEMRFileListInfos.Where(m => m.ID == ID && m.DeleteFlag != "*").Select(m => new NurseEMRFileListInfo
            {
                FileId = m.FileId,
                FileName = m.FileName,
                Inpatientid = m.Inpatientid
            }
           ).FirstOrDefaultAsync();
        }

        public async Task<NurseEMRFileListInfo> GetFileIDByFileIDView(string fileId)
        {
            return await _medicalDbContext.NurseEMRFileListInfos.Where(m => m.FileId == fileId && m.DeleteFlag != "*").Select(m => new NurseEMRFileListInfo
            {
                FileId = m.FileId,
                FileName = m.FileName,
                Inpatientid = m.Inpatientid
            }
           ).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据SerialNumber获取患者的数据
        /// </summary>
        /// <param name="inpatientId">在院患者ID</param>
        /// <param name="serialNumber">序列号</param>
        /// <returns></returns>
        public async Task<bool> GetDataBySerialNumber(string inpatientId,string serialNumber)
        {
            return await _medicalDbContext.NurseEMRFileListInfos.AnyAsync(m => m.Inpatientid == inpatientId && serialNumber == m.SerialNumber && m.DeleteFlag != "*");
        }
    }
}
