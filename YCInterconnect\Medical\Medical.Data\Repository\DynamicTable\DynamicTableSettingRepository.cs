﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.ViewModels.View;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class DynamicTableSettingRepository : IDynamicTableSettingRepository
    {
        private MedicalDbContext _medicalDbContext;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IMemoryCache _memoryCache;
        public DynamicTableSettingRepository(
            MedicalDbContext medicalDbContext
            , SessionCommonServer sessionCommonServer
            , IMemoryCache memoryCache
            )
        {
            _medicalDbContext = medicalDbContext;
            _sessionCommonServer = sessionCommonServer;
            _memoryCache = memoryCache;
        }
        /// <summary>
        /// 根据表格ID和用户ID获取表格列配置
        /// </summary>
        /// <param name="dynamicTableListID"></param>
        /// <param name="useID"></param>
        /// <returns></returns>
        public async Task<List<DynamicTableSettingView>> GetViewListByID(int dynamicTableListID, string useID)
        {
            var session = await _sessionCommonServer.GetSession();
            var list = (from setting in _medicalDbContext.DynamicTableSettingInfos
                        join column in _medicalDbContext.DynamicTableColumnInfos
                        on setting.ColumnID equals column.ColumnID
                        where column.DynamicTableListID == dynamicTableListID && setting.HospitalID == session.HospitalID && column.DefaultShowFlag == 1 && column.DeleteFlag != "*" && setting.DynamicTableListID == dynamicTableListID
                        && setting.DeleteFlag != "*"
                        select new DynamicTableSettingView
                        {
                            DynamicTableListID = setting.DynamicTableListID,
                            ColumnID = setting.ColumnID,
                            Level = setting.Level,
                            ColumnShowName = column.ColumnShowName,
                            DefaultShowFlag = column.DefaultShowFlag == 1
                        }).ToList();
            if (string.IsNullOrEmpty(useID))
            {
                return list;
            }
            var userList = (from userSetting in _medicalDbContext.DynamicTableUserSettingInfos
                            join column in _medicalDbContext.DynamicTableColumnInfos
                            on userSetting.ColumnID equals column.ColumnID
                            where column.DynamicTableListID == dynamicTableListID && column.DefaultShowFlag == 0 && column.DeleteFlag != "*" && userSetting.DynamicTableListID == dynamicTableListID
                            && userSetting.HospitalID == session.HospitalID && userSetting.UserID == useID && userSetting.DeleteFlag != "*"
                            select new DynamicTableSettingView
                            {
                                DynamicTableListID = userSetting.DynamicTableListID,
                                ColumnID = userSetting.ColumnID,
                                Level = userSetting.Level,
                                ColumnShowName = column.ColumnShowName,
                                DefaultShowFlag = column.DefaultShowFlag == 1,
                                UserID = userSetting.UserID
                            }).ToList();
            list.AddRange(userList);
            return list;
        }
        /// <summary>
        /// 根据表格ID获取所有表格列配置
        /// </summary>
        /// <param name="dynamicTableListID"></param>
        /// <returns></returns>
        public async Task<List<DynamicTableSettingView>> GetCommonViewByID(int dynamicTableListID)
        {
            var session = await _sessionCommonServer.GetSession();
            return (from setting in _medicalDbContext.DynamicTableSettingInfos
                    join column in _medicalDbContext.DynamicTableColumnInfos
                    on setting.ColumnID equals column.ColumnID
                    where column.DynamicTableListID == dynamicTableListID && setting.HospitalID == session.HospitalID && column.DeleteFlag != "*" && setting.DynamicTableListID == dynamicTableListID
                    && setting.DeleteFlag != "*"
                    select new DynamicTableSettingView
                    {
                        DynamicTableListID = setting.DynamicTableListID,
                        ColumnID = setting.ColumnID,
                        Level = setting.Level,
                        ColumnShowName = column.ColumnShowName,
                        DefaultShowFlag = column.DefaultShowFlag == 1
                    }).ToList();
        }
    }
}
