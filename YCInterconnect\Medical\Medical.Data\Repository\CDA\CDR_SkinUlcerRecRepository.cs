﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.Models.CDADocument;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class CDR_SkinUlcerRecRepository : ICDR_SkinUlcerRecRepository
    {
        private readonly CDADBContext _cDADBContext = null;

        public CDR_SkinUlcerRecRepository(CDADBContext cDADBContext)
        {
            _cDADBContext = cDADBContext;
        }

        public Task AddRecord(CDA_SkinUlcerRecInfo recInfo)
        {
            _cDADBContext.Add(recInfo);
            return Task.CompletedTask;
        }

        public Task AddRecordItem(CDA_SkinUlcerRec_ItemInfo recInfo)
        {
            _cDADBContext.Add(recInfo);
            return Task.CompletedTask;
        }

        /// <summary>
        /// 取得最后一笔数据异动时间
        /// </summary>
        /// <returns>DateTime</returns>
        public async Task<DateTime> GetLastTimeSpanAsync()
        {
            return await _cDADBContext.CDA_SkinUlcerRecInfos.Select(m => m.TimeStamp).OrderBy(m => m).LastOrDefaultAsync();
        }

        public async Task<CDA_SkinUlcerRecInfo> GetRecordByDCIDAsync(string dCID)
        {
            return await _cDADBContext.CDA_SkinUlcerRecInfos.Where(m => m.DCID == dCID).FirstOrDefaultAsync();
        }

        public async Task<CDA_SkinUlcerRec_ItemInfo> GetRecordItemByDCIDAsync(string dCID)
        {
            return await _cDADBContext.CDA_SkinUlcerRec_ItemInfos.Where(m => m.DCID == dCID).FirstOrDefaultAsync();
        }

        public async Task<int> Save()
        {
            return await _cDADBContext.SaveChangesAsync();
        }
    }
}
