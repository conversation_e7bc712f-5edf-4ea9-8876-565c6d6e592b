﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    /// <summary>
    /// 病区分组字典
    /// </summary>
    [Serializable]
    [Table("StationGroupList")]
    public class StationGroupListInfo
    {
        /// <summary>
        /// 病区编号
        /// </summary>
        [Key]
        public int StationID { get; set; }

        /// <summary>
        /// 病区代码
        /// </summary>
        public string StationCode { get; set; }

        /// <summary>
        /// 病区名称
        /// </summary>
        public string StationName { get; set; }

        /// <summary>
        /// 病区分组
        /// </summary>
        public int StationGroup { get; set; }
    }
}
