﻿using Interconnect.Models;
using System;
using System.Collections.Generic;
using System.Text;

namespace Interconnect.Data.Interface
{
    public interface IStationGroupListRepository
    {
        /// <summary>
        /// 根据分组编号，获取病区分组字典
        /// </summary>
        /// <param name="GroupID"></param>
        /// <returns></returns>
        List<StationGroupListInfo> GetStationGroupListByGroupID(int GroupID);

        /// <summary>
        /// 获取所有病区数据
        /// </summary>
        /// <returns></returns>
        List<StationGroupListInfo> GetStationGroupListAll();
    }
}
