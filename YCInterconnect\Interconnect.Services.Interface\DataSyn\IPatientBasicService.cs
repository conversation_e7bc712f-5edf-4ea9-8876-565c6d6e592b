﻿using Interconnect.Models;
using Medical.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Interconnect.Services.Interface
{
    public interface IPatientBasicService
    {
        /// <summary>
        /// 病人信息同步
        /// </summary>
        /// <returns></returns>
        Task<bool> SynchronizationMain(string ChartNo);

        /// <summary>
        /// 同步病人基本信息
        /// </summary>
        /// <param name="originalData"></param>
        /// <returns></returns>
        Task<List<PatientBasicDataInfo>> SyncPatientBaseDetail(List<PatientBasicInfo> originalData);
    }
}