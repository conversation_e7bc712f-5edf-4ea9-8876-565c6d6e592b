﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Interconnect.ViewModels;
using Medical.Data.Context;
using Medical.Models;
using Medical.ViewModels.Interface;
using Medical.ViewModels.Query;
using Medical.ViewModels.View;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Interconnect.Services.Interface
{
    public interface ICommonHelper
    {
        /// <summary>
        /// 增加PatientProfile
        /// </summary>
        /// <param name="list"></param>
        void AddProfile(List<PatientProfile> list);
        /// <summary>
        /// 缓存更新
        /// </summary>
        /// <param name="query"></param>
        void UpdateCache(CacheQuery query);
        //发送邮件
        bool SendMail(MailBaseViewInfo MailBase, MailReceptionViewInfo MailAccept);

        /// <summary>
        /// 获取科室
        /// </summary>
        /// <param name="DepartmentID"></param>
        /// <returns></returns>
        Task<DepartmentListInfo> GetDepartmentByID(int DepartmentID);
        Task<DepartmentListInfo> GetDepartmentByCode(string DepartmentCode);
        /// <summary>
        /// 获取科室
        /// </summary>
        /// <param name="stationId"></param>
        /// <returns></returns>
        Task<StationListInfo> GetStationByID(int StationId);
        Task<StationListInfo> GetStationByCode(string StationCode);

        /// <summary>
        /// 获取床位
        /// </summary>
        /// <param name="BedCode"></param>
        /// <param name="StationId"></param>
        /// <returns></returns>
        Task<BedListInfo> GetBedByCode(string BedCode, int StationId);

        /// <summary>
        /// 出院，或则护理级别发生变化，停止问题
        /// </summary>
        /// <param name="inPatientID"></param>
        void StopProblem(string inPatientID, string nursingLevel, bool stopProblem, MedicalEnumUtility.StopProblemType type);

        /// <summary>
        ///病人信息发生变化，验证排程中的病人信息
        /// </summary>
        /// <param name="inPatientID"></param>
        void CheckSchedulePatientInfo(string inPatientID, string caseNumber, bool isTransfer);

        /// <summary>
        /// 医嘱停住，停止时间点后的排程
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="orderDetialID"></param>
        /// <param name="dateTime"></param>
        /// <returns></returns>
        Task StopSchduleByOrder(string inpatientID, string orderDetialID, DateTime dateTime);

        /// <summary>
        /// 呼叫API，获得数据
        /// </summary>
        /// <param name="apiStr"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        string GetInterconnectData(string apiStr, Dictionary<string, string> data);

        string GetApiStr(int settingType, string typeCode);

        DateTime? GetDateTime(string dateTime);

        /// <summary>
        /// 写日志
        /// </summary>
        /// <param name="apiURL"></param>
        /// <param name="arg"></param>
        /// <param name="inpatientID"></param>
        /// <param name="caseNumber"></param>
        /// <param name="_unitOfWork"></param>
        /// <param name="isCommit"></param>
        /// <returns></returns>
        SynchronizeLogInfo SaveLog(string apiURL, string arg, string inpatientID,
          string caseNumber, IUnitOfWork<MedicalDbContext> _unitOfWork, bool isCommit = true, bool isPost = true);

        /// <summary>
        /// 获取API执行结果
        /// </summary>
        /// <param name="result"></param>
        /// <param name="syncLog"></param>
        bool GetAPIExecResult(string result, SynchronizeLogInfo syncLog, IUnitOfWork<MedicalDbContext> _unitOfWork);
        /// <summary>
        /// 呼叫API及保存日志
        /// /// </summary>
        /// <param name="url"></param>
        /// <param name="inpatientID"></param>
        /// <param name="submit"></param>
        bool CallPostAPI(string url, string inpatientID, object submit);
    }
}
