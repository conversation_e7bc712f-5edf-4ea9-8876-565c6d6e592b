using Interconnect.Models;
using Interconnect.Services.Interface;
using Interconnect.ViewModels;
using Medical.Common;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Interconnect.Services
{
    /// <summary>
    /// 输血数据同步到NursingRecordDetail服务实现
    /// </summary>
    public class BloodTransfusionSyncService : IBloodTransfusionSyncService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IOptions<SystemConfig> _config;
        private readonly string _bloodTransfusionApiUrl;
        private readonly string _wileyBloodTransfusionApiUrl;
        public BloodTransfusionSyncService(IOptions<SystemConfig> config)
        {
            _config = config;
            // TODO: 从配置文件中获取API地址，这里先使用占位符
            _bloodTransfusionApiUrl = _config.Value.DataInterfaceAPI + "/api/BloodTransfusion/GetData";
            _wileyBloodTransfusionApiUrl = _config.Value.DataInterfaceAPI + "/api/BloodTransfusion/GetWileyData";
        }
        /// <summary>
        /// 同步输血数据到NursingRecordDetail
        /// </summary>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <returns>同步结果</returns>
        public async Task<bool> SyncTransfusionToRecordDetail(DateTime startDateTime, DateTime endDateTime)
        {
            // 参数检核
            if (startDateTime >= endDateTime)
            {
                _logger.Error("开始时间不能大于或等于结束时间");
                return false;
            }
            _logger.Info($"开始同步输血数据到NursingRecordDetail，时间范围：{startDateTime:yyyy-MM-dd HH:mm:ss} - {endDateTime:yyyy-MM-dd HH:mm:ss}");
            // 从API获取数据
            var bloodTransfusionData = await GetBloodTransfusionDataFromApi(startDateTime, endDateTime);
            if (bloodTransfusionData == null || !bloodTransfusionData.Any())
            {
                _logger.Info("未获取到输血数据");
                return true;
            }
            _logger.Info($"获取到输血数据 {bloodTransfusionData.Count} 条");
            // 处理数据同步到NursingRecordDetail
            var result = await ProcessBloodTransfusionToNursingRecordDetail(bloodTransfusionData);
            _logger.Info($"输血数据同步到NursingRecordDetail完成，结果：{(result ? "成功" : "失败")}");
            return result;
        }
        /// <summary>
        /// 同步Wiley补录单病人输血数据到NursingRecordDetail
        /// </summary>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <param name="caseNumber">病案号</param>
        /// <returns>同步结果</returns>
        public async Task<bool> SyncSinglePatientTransfusionToRecordDetail(DateTime startDateTime, DateTime endDateTime, string caseNumber)
        {
            // 参数检核
            if (startDateTime >= endDateTime)
            {
                _logger.Error("开始时间不能大于或等于结束时间");
                return false;
            }
            if (string.IsNullOrWhiteSpace(caseNumber))
            {
                _logger.Error("病案号不能为空");
                return false;
            }
            _logger.Info($"开始同步单个病人输血数据到NursingRecordDetail，时间范围：{startDateTime:yyyy-MM-dd HH:mm:ss} - {endDateTime:yyyy-MM-dd HH:mm:ss}，病案号：{caseNumber}");
            // 从API获取数据
            var bloodTransfusionData = await GetWileyBloodTransfusionDataFromApi(startDateTime, endDateTime, caseNumber);
            if (bloodTransfusionData == null || !bloodTransfusionData.Any())
            {
                _logger.Info("未获取到单个病人输血数据");
                return true;
            }
            _logger.Info($"获取到单个病人输血数据 {bloodTransfusionData.Count} 条");
            // 处理数据同步到NursingRecordDetail
            var result = await ProcessBloodTransfusionToNursingRecordDetail(bloodTransfusionData);
            _logger.Info($"单个病人输血数据同步到NursingRecordDetail完成，结果：{(result ? "成功" : "失败")}");
            return result;
        }
        /// <summary>
        /// 从API获取输血数据
        /// </summary>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <returns>输血数据列表</returns>
        private async Task<List<BloodTransfusionSyncView>> GetBloodTransfusionDataFromApi(DateTime startDateTime, DateTime endDateTime)
        {
            var parameters = new Dictionary<string, string>
            {
                { "startDateTime", startDateTime.ToString("yyyy-MM-dd HH:mm:ss") },
                { "endDateTime", endDateTime.ToString("yyyy-MM-dd HH:mm:ss") }
            };
            var apiResult = await HttpHelper.HttpGetAsync(_bloodTransfusionApiUrl + "?" + string.Join("&", parameters.Select(p => $"{p.Key}={p.Value}")));
            if (string.IsNullOrEmpty(apiResult))
            {
                _logger.Error("API返回数据为空");
                return new List<BloodTransfusionSyncView>();
            }
            var apiResponse = JsonConvert.DeserializeObject<ResponseResult>(apiResult);
            if (apiResponse?.Code == 1 && apiResponse.Data != null)
            {
                var dataJson = JsonConvert.SerializeObject(apiResponse.Data);
                var bloodTransfusionData = JsonConvert.DeserializeObject<List<BloodTransfusionSyncView>>(dataJson);
                return bloodTransfusionData ?? new List<BloodTransfusionSyncView>();
            }
            _logger.Error($"API返回错误：{apiResponse?.Message}");
            return new List<BloodTransfusionSyncView>();
        }
        /// <summary>
        /// 从API获取Wiley补录单病人输血数据
        /// </summary>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <param name="caseNumber">病案号</param>
        /// <returns>输血数据列表</returns>
        private async Task<List<BloodTransfusionSyncView>> GetWileyBloodTransfusionDataFromApi(DateTime startDateTime, DateTime endDateTime, string caseNumber)
        {
            var parameters = new Dictionary<string, string>
            {
                { "startDateTime", startDateTime.ToString("yyyy-MM-dd HH:mm:ss") },
                { "endDateTime", endDateTime.ToString("yyyy-MM-dd HH:mm:ss") },
                { "caseNumber", caseNumber }
            };
            var apiResult = await HttpHelper.HttpGetAsync(_wileyBloodTransfusionApiUrl + "?" + string.Join("&", parameters.Select(p => $"{p.Key}={p.Value}")));
            if (string.IsNullOrEmpty(apiResult))
            {
                _logger.Error("Wiley补录单API返回数据为空");
                return new List<BloodTransfusionSyncView>();
            }
            var apiResponse = JsonConvert.DeserializeObject<ResponseResult>(apiResult);
            if (apiResponse?.Code == 1 && apiResponse.Data != null)
            {
                var dataJson = JsonConvert.SerializeObject(apiResponse.Data);
                var bloodTransfusionData = JsonConvert.DeserializeObject<List<BloodTransfusionSyncView>>(dataJson);
                return bloodTransfusionData ?? new List<BloodTransfusionSyncView>();
            }
            _logger.Error($"Wiley补录单API返回错误：{apiResponse?.Message}");
            return new List<BloodTransfusionSyncView>();
        }
        /// <summary>
        /// 处理输血数据同步到NursingRecordDetail（新增和修改）
        /// </summary>
        /// <param name="bloodTransfusionData">输血数据列表</param>
        /// <returns>处理结果</returns>
        private async Task<bool> ProcessBloodTransfusionToNursingRecordDetail(List<BloodTransfusionSyncView> bloodTransfusionData)
        {
            var successCount = 0;
            var failCount = 0;
            foreach (var data in bloodTransfusionData)
            {
                // 检查数据在NursingRecordDetail中是否存在
                var exists = await CheckBloodTransfusionExistsInNursingRecordDetail(data.TransfusionID, data.BloodDonorBagsCode);
                bool result;
                if (exists)
                {
                    // 更新数据到NursingRecordDetail
                    result = await UpdateBloodTransfusionToNursingRecordDetail(data);
                    if (result)
                    {
                        successCount++;
                        _logger.Info($"更新输血数据成功：输血单号={data.TransfusionID}，血袋码={data.BloodDonorBagsCode}");
                    }
                    else
                    {
                        failCount++;
                        _logger.Error($"更新输血数据失败：输血单号={data.TransfusionID}，血袋码={data.BloodDonorBagsCode}");
                    }
                }
                else
                {
                    // 新增数据到NursingRecordDetail
                    result = await AddBloodTransfusionToNursingRecordDetail(data);
                    if (result)
                    {
                        successCount++;
                        _logger.Info($"新增输血数据成功：输血单号={data.TransfusionID}，血袋码={data.BloodDonorBagsCode}");
                    }
                    else
                    {
                        failCount++;
                        _logger.Error($"新增输血数据失败：输血单号={data.TransfusionID}，血袋码={data.BloodDonorBagsCode}");
                    }
                }
            }
            _logger.Info($"处理输血数据完成，成功：{successCount}，失败：{failCount}");
            return failCount == 0;
        }
        /// <summary>
        /// 添加输血数据到NursingRecordDetail
        /// </summary>
        /// <param name="bloodTransfusionData">输血数据</param>
        /// <returns>添加结果</returns>
        private async Task<bool> AddBloodTransfusionToNursingRecordDetail(BloodTransfusionSyncView bloodTransfusionData)
        {
            // TODO: 实现具体的数据库添加逻辑到NursingRecordDetail
            // 这里预留具体处理逻辑
            _logger.Info($"添加输血数据到NursingRecordDetail：输血单号={bloodTransfusionData.TransfusionID}，血袋码={bloodTransfusionData.BloodDonorBagsCode}");
            // 模拟处理
            await Task.Delay(10);
            return true;
        }
        /// <summary>
        /// 更新输血数据到NursingRecordDetail
        /// </summary>
        /// <param name="bloodTransfusionData">输血数据</param>
        /// <returns>更新结果</returns>
        private async Task<bool> UpdateBloodTransfusionToNursingRecordDetail(BloodTransfusionSyncView bloodTransfusionData)
        {
            // TODO: 实现具体的数据库更新逻辑到NursingRecordDetail
            // 这里预留具体处理逻辑
            _logger.Info($"更新输血数据到NursingRecordDetail：输血单号={bloodTransfusionData.TransfusionID}，血袋码={bloodTransfusionData.BloodDonorBagsCode}");
            // 模拟处理
            await Task.Delay(10);
            return true;
        }
        /// <summary>
        /// 检查输血数据在NursingRecordDetail中是否存在
        /// </summary>
        /// <param name="transfusionID">输血单号</param>
        /// <param name="bloodDonorBagsCode">血袋码</param>
        /// <returns>是否存在</returns>
        private async Task<bool> CheckBloodTransfusionExistsInNursingRecordDetail(string transfusionID, string bloodDonorBagsCode)
        {
            // TODO: 实现具体的数据库查询逻辑在NursingRecordDetail中
            // 这里预留具体处理逻辑
            _logger.Info($"检查输血数据在NursingRecordDetail中是否存在：输血单号={transfusionID}，血袋码={bloodDonorBagsCode}");
            // 模拟处理
            await Task.Delay(5);
            return false; // 暂时返回false，表示不存在，实际应该查询数据库
        }
    }
}
