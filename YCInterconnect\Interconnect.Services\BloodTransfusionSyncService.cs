using Interconnect.Models;
using Interconnect.Services.Interface;
using Interconnect.ViewModels;
using Medical.Common;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Interconnect.Services
{
    /// <summary>
    /// 输血数据同步服务实现
    /// </summary>
    public class BloodTransfusionSyncService : IBloodTransfusionSyncService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IOptions<SystemConfig> _config;
        private readonly string _bloodTransfusionApiUrl;
        private readonly string _wileyBloodTransfusionApiUrl;
        public BloodTransfusionSyncService(IOptions<SystemConfig> config)
        {
            _config = config;
            // TODO: 从配置文件中获取API地址，这里先使用占位符
            _bloodTransfusionApiUrl = _config.Value.DataInterfaceAPI + "/api/BloodTransfusion/GetData";
            _wileyBloodTransfusionApiUrl = _config.Value.DataInterfaceAPI + "/api/BloodTransfusion/GetWileyData";
        }
        /// <summary>
        /// 获取输血数据并同步
        /// </summary>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <returns>同步结果</returns>
        public async Task<BloodTransfusionSyncResponse> GetAndSyncBloodTransfusionData(DateTime startDateTime, DateTime endDateTime)
        {
            var response = new BloodTransfusionSyncResponse();
            try
            {
                _logger.Info($"开始获取输血数据，时间范围：{startDateTime:yyyy-MM-dd HH:mm:ss} - {endDateTime:yyyy-MM-dd HH:mm:ss}");
                // 从API获取数据
                var bloodTransfusionData = await GetBloodTransfusionDataFromApi(startDateTime, endDateTime);
                if (bloodTransfusionData == null || !bloodTransfusionData.Any())
                {
                    response.Code = 1;
                    response.Message = "未获取到输血数据";
                    _logger.Info("未获取到输血数据");
                    return response;
                }
                _logger.Info($"获取到输血数据 {bloodTransfusionData.Count} 条");
                // 处理数据同步
                response = await ProcessBloodTransfusionData(bloodTransfusionData);
                _logger.Info($"输血数据同步完成，成功：{response.SuccessCount}，失败：{response.FailCount}，新增：{response.AddCount}，更新：{response.UpdateCount}");
            }
            catch (Exception ex)
            {
                response.Code = 0;
                response.Message = "获取输血数据失败：" + ex.Message;
                _logger.Error($"获取输血数据失败：{ex}");
            }
            return response;
        }
        /// <summary>
        /// 获取Wiley补录单病人输血数据并同步
        /// </summary>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <param name="caseNumber">病案号</param>
        /// <returns>同步结果</returns>
        public async Task<BloodTransfusionSyncResponse> GetAndSyncWileyBloodTransfusionData(DateTime startDateTime, DateTime endDateTime, string caseNumber)
        {
            var response = new BloodTransfusionSyncResponse();
            try
            {
                _logger.Info($"开始获取Wiley补录单病人输血数据，时间范围：{startDateTime:yyyy-MM-dd HH:mm:ss} - {endDateTime:yyyy-MM-dd HH:mm:ss}，病案号：{caseNumber}");
                // 从API获取数据
                var bloodTransfusionData = await GetWileyBloodTransfusionDataFromApi(startDateTime, endDateTime, caseNumber);
                if (bloodTransfusionData == null || !bloodTransfusionData.Any())
                {
                    response.Code = 1;
                    response.Message = "未获取到Wiley补录单病人输血数据";
                    _logger.Info("未获取到Wiley补录单病人输血数据");
                    return response;
                }
                _logger.Info($"获取到Wiley补录单病人输血数据 {bloodTransfusionData.Count} 条");
                // 处理数据同步
                response = await ProcessBloodTransfusionData(bloodTransfusionData);
                _logger.Info($"Wiley补录单病人输血数据同步完成，成功：{response.SuccessCount}，失败：{response.FailCount}，新增：{response.AddCount}，更新：{response.UpdateCount}");
            }
            catch (Exception ex)
            {
                response.Code = 0;
                response.Message = "获取Wiley补录单病人输血数据失败：" + ex.Message;
                _logger.Error($"获取Wiley补录单病人输血数据失败：{ex}");
            }
            return response;
        }
        /// <summary>
        /// 从API获取输血数据
        /// </summary>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <returns>输血数据列表</returns>
        public async Task<List<BloodTransfusionSyncView>> GetBloodTransfusionDataFromApi(DateTime startDateTime, DateTime endDateTime)
        {
            try
            {
                var parameters = new Dictionary<string, string>
                {
                    { "startDateTime", startDateTime.ToString("yyyy-MM-dd HH:mm:ss") },
                    { "endDateTime", endDateTime.ToString("yyyy-MM-dd HH:mm:ss") }
                };
                var apiResult = await HttpHelper.HttpGetAsync(_bloodTransfusionApiUrl + "?" + string.Join("&", parameters.Select(p => $"{p.Key}={p.Value}")));
                if (string.IsNullOrEmpty(apiResult))
                {
                    _logger.Error("API返回数据为空");
                    return new List<BloodTransfusionSyncView>();
                }
                var apiResponse = JsonConvert.DeserializeObject<BloodTransfusionApiResponse>(apiResult);
                if (apiResponse?.Code == 1 && apiResponse.Data != null)
                {
                    return apiResponse.Data;
                }
                _logger.Error($"API返回错误：{apiResponse?.Message}");
                return new List<BloodTransfusionSyncView>();
            }
            catch (Exception ex)
            {
                _logger.Error($"调用输血数据API失败：{ex}");
                return new List<BloodTransfusionSyncView>();
            }
        }
        /// <summary>
        /// 从API获取Wiley补录单病人输血数据
        /// </summary>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <param name="caseNumber">病案号</param>
        /// <returns>输血数据列表</returns>
        public async Task<List<BloodTransfusionSyncView>> GetWileyBloodTransfusionDataFromApi(DateTime startDateTime, DateTime endDateTime, string caseNumber)
        {
            try
            {
                var parameters = new Dictionary<string, string>
                {
                    { "startDateTime", startDateTime.ToString("yyyy-MM-dd HH:mm:ss") },
                    { "endDateTime", endDateTime.ToString("yyyy-MM-dd HH:mm:ss") },
                    { "caseNumber", caseNumber }
                };
                var apiResult = await HttpHelper.HttpGetAsync(_wileyBloodTransfusionApiUrl + "?" + string.Join("&", parameters.Select(p => $"{p.Key}={p.Value}")));
                if (string.IsNullOrEmpty(apiResult))
                {
                    _logger.Error("Wiley补录单API返回数据为空");
                    return new List<BloodTransfusionSyncView>();
                }
                var apiResponse = JsonConvert.DeserializeObject<BloodTransfusionApiResponse>(apiResult);
                if (apiResponse?.Code == 1 && apiResponse.Data != null)
                {
                    return apiResponse.Data;
                }
                _logger.Error($"Wiley补录单API返回错误：{apiResponse?.Message}");
                return new List<BloodTransfusionSyncView>();
            }
            catch (Exception ex)
            {
                _logger.Error($"调用Wiley补录单输血数据API失败：{ex}");
                return new List<BloodTransfusionSyncView>();
            }
        }
        /// <summary>
        /// 处理输血数据同步（新增和修改）
        /// </summary>
        /// <param name="bloodTransfusionData">输血数据列表</param>
        /// <returns>处理结果</returns>
        public async Task<BloodTransfusionSyncResponse> ProcessBloodTransfusionData(List<BloodTransfusionSyncView> bloodTransfusionData)
        {
            var response = new BloodTransfusionSyncResponse
            {
                Code = 1,
                Message = "处理完成"
            };
            foreach (var data in bloodTransfusionData)
            {
                try
                {
                    // 检查数据是否存在
                    var exists = await CheckBloodTransfusionDataExists(data.TransfusionID, data.BloodDonorBagsCode);
                    bool result;
                    if (exists)
                    {
                        // 更新数据
                        result = await UpdateBloodTransfusionData(data);
                        if (result)
                        {
                            response.UpdateCount++;
                            response.SuccessCount++;
                        }
                        else
                        {
                            response.FailCount++;
                        }
                    }
                    else
                    {
                        // 新增数据
                        result = await AddBloodTransfusionData(data);
                        if (result)
                        {
                            response.AddCount++;
                            response.SuccessCount++;
                        }
                        else
                        {
                            response.FailCount++;
                        }
                    }
                }
                catch (Exception ex)
                {
                    response.FailCount++;
                    _logger.Error($"处理输血数据失败，输血单号：{data.TransfusionID}，血袋码：{data.BloodDonorBagsCode}，错误：{ex}");
                }
            }
            return response;
        }
        /// <summary>
        /// 添加输血数据
        /// </summary>
        /// <param name="bloodTransfusionData">输血数据</param>
        /// <returns>添加结果</returns>
        public async Task<bool> AddBloodTransfusionData(BloodTransfusionSyncView bloodTransfusionData)
        {
            try
            {
                // TODO: 实现具体的数据库添加逻辑
                // 这里预留具体处理逻辑
                _logger.Info($"添加输血数据：输血单号={bloodTransfusionData.TransfusionID}，血袋码={bloodTransfusionData.BloodDonorBagsCode}");
                // 模拟处理
                await Task.Delay(10);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"添加输血数据失败：{ex}");
                return false;
            }
        }
        /// <summary>
        /// 更新输血数据
        /// </summary>
        /// <param name="bloodTransfusionData">输血数据</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateBloodTransfusionData(BloodTransfusionSyncView bloodTransfusionData)
        {
            try
            {
                // TODO: 实现具体的数据库更新逻辑
                // 这里预留具体处理逻辑
                _logger.Info($"更新输血数据：输血单号={bloodTransfusionData.TransfusionID}，血袋码={bloodTransfusionData.BloodDonorBagsCode}");
                // 模拟处理
                await Task.Delay(10);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"更新输血数据失败：{ex}");
                return false;
            }
        }
        /// <summary>
        /// 检查输血数据是否存在
        /// </summary>
        /// <param name="transfusionID">输血单号</param>
        /// <param name="bloodDonorBagsCode">血袋码</param>
        /// <returns>是否存在</returns>
        public async Task<bool> CheckBloodTransfusionDataExists(string transfusionID, string bloodDonorBagsCode)
        {
            try
            {
                // TODO: 实现具体的数据库查询逻辑
                // 这里预留具体处理逻辑
                _logger.Info($"检查输血数据是否存在：输血单号={transfusionID}，血袋码={bloodDonorBagsCode}");
                // 模拟处理
                await Task.Delay(5);
                return false; // 暂时返回false，表示不存在，实际应该查询数据库
            }
            catch (Exception ex)
            {
                _logger.Error($"检查输血数据是否存在失败：{ex}");
                return false;
            }
        }
    }
}
