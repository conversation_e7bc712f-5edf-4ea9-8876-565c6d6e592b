﻿//using System;
//using System.Collections.Generic;
//using Interconnect.Services.Interface;
//using NLog;
//using Interconnect.Models;
//using Microsoft.Extensions.Options;
//using System.Threading.Tasks;
//using Medical.Services.Interface;
//using System.Linq;
//using Interconnect.Data.Context;
//using Medical.Data.Context;
//using Microsoft.EntityFrameworkCore;
//using Medical.Models;

//namespace Interconnect.Services
//{
//    //public class PushDataSwitchCenterService : IPushDataSwitchCenterService
//    //{
//    //   // private readonly IIntakeOutputRecordService _IntakeOutputRecordService;
//    //   // private readonly IRiskRecordService _IRiskRecordService;
//    //   // //private readonly ISpecializedRecordService _ISpecializedRecordService;
//    //   // private readonly ISelectNormalNewService _ISelectNormalNewService;
//    //   // private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
//    //   // private readonly IOptions<SystemConfig> _config;
//    //   // private readonly IJobLogService _jobLogService;
//    //   // private readonly IReadJsonService _IReadJsonService;
//    //   //// private readonly INRInputOutputWaterService _nRInputOutputWaterService;
//    //   // private readonly IDataTableEditListService _dataTableEditListService;
//    //   // private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;

//    //    public PushDataSwitchCenterService(
//    //      //    IIntakeOutputRecordService intakeOutputRecordService
//    //      //  , IRiskRecordService riskRecordService
//    //      // // , ISpecializedRecordService specializedRecordService
//    //      //  , IOptions<SystemConfig> config
//    //      //  , ISelectNormalNewService selectNormalNewService
//    //      //  , IJobLogService jobLogService
//    //      //  , IReadJsonService readJsonService
//    //      ////  , INRInputOutputWaterService nRInputOutputWaterSeIPushDataSwitchCenterServicervice
//    //      //  , IDataTableEditListService dataTableEditListService
//    //      //  , IUnitOfWork<MedicalDbContext> unitOfWork
//    //        )
//    //    {
//    //     //   _IntakeOutputRecordService = intakeOutputRecordService;
//    //     //   _IRiskRecordService = riskRecordService;
//    //     ////   _ISpecializedRecordService = specializedRecordService;
//    //     //   _config = config;
//    //     //   _ISelectNormalNewService = selectNormalNewService;
//    //     //   _jobLogService = jobLogService;
//    //     //   _IReadJsonService = readJsonService;
//    //     // //  _nRInputOutputWaterService = nRInputOutputWaterService;
//    //     //   _dataTableEditListService = dataTableEditListService;
//    //     //   _unitOfWork = unitOfWork;
//    //    }

//        //List<string> ResultSrt = new List<string>();

//        ////CCC数据送交换中心，并推送中介库
//        //public async Task<List<string>> GetPushDataSwitchCenter()
//        //{
//        //    //生成一个随机的日志序号
//        //    var guid = Guid.NewGuid().ToString("N");
//        //    var JobId = 3;//作业编号
//        //    // 确认作业是否可以被执行
//        //    var JobStatus = _jobLogService.GetJobStatus(JobId, "CCC数据送交换中心");
//        //    //不能执行，返回
//        //    if (!JobStatus)
//        //    {
//        //        _logger.Info("CCC数据送交换中心作业正在执行！，停止本次作业启动！");
//        //        ResultLog(guid, false, "CCC数据送交换中心作业正在执行，停止本次作业启动！");
//        //        return ResultSrt;
//        //    }
//        //    var Language = _config.Value.Language;
//        //    var HospitalID = _config.Value.HospitalID;
//        //    var resultSign = false;
//        //    ResultSrt.Add("**CCC数据送交换中心:本次同步开始**");
//        //    // 输入输出液送电子病历记录            
//        //  //  resultSign = await _IntakeOutputRecordService.SendIntakeOutputRecordByDate(DateTime.Now, Language, HospitalID, 300);
//        //  //  ResultLog(guid, resultSign, "输入输出液送电子病历记录");

//        //    //压疮数据交换
//        //    resultSign = await _ISpecializedRecordService.SwitchPressureSoresData(DateTime.Now, Language, 54, 53);
//        //    ResultLog(guid, resultSign, "压疮数据交换");
//        //    //压疮删除数据交换
//        //    resultSign = await _ISpecializedRecordService.SwitchPressureSoresDeleteInfo(DateTime.Now, Language, 55);
//        //    ResultLog(guid, resultSign, "压疮删除数据交换");
//        //    //2.1护理记录,3.1体温单-病人体温记录：Nurse_Heat_Record
//        //    resultSign = await _ISelectNormalNewService.SaveNormalNewData(DateTime.Now, int.Parse(HospitalID));
//        //    ResultLog(guid, resultSign, "2.1护理记录");

//        //    //护理记录中的出水量,病人出入水量明细(2.5\2.6)
//        //    resultSign = await _nRInputOutputWaterService.ToDataCenter();
//        //    ResultLog(guid, resultSign, "护理记录中的出水量,病人出入水量明细(2.5,2.6)");
//        //    ResultSrt.Add("**CCC数据送交换中心:本次同步结束*");
//        //    //更新作业状态
//        //    JobStatus = _jobLogService.UpJobStatus(JobId, "同步完成");
//        //    if (!JobStatus)
//        //    {
//        //        _logger.Info("更新作业同步状态失败！");
//        //        ResultLog(guid, false, "更新作业同步状态失败！");
//        //    }
//        //    ResultLog(guid, JobStatus, "更新作业同步状态成功！");

//        //    //回写数据到中介库
//        //    var jobStatusStr = _jobLogService.GetJobStatusStr(JobId);//获得作业状态
//        //    //不能执行，返回
//        //    if (jobStatusStr != "正在执行")
//        //    {
//        //        _logger.Info("开始回写数据到中介库！");
//        //        resultSign = await _IReadJsonService.ReadDataSwitchCenterMain(); //向中介库写数据
//        //        ResultLog(guid, resultSign, "回写数据到中介库");
//        //        _logger.Info("回写数据到中介库完成！");
//        //    }
//        //    return ResultSrt;
//        //}


//        // 同步日志
//        //private void ResultLog(string guid, bool Sign, string Text)
//        //{
//        //    string resultStr = "";
//        //    if (Sign)
//        //    {
//        //        resultStr = "本次作业编号[" + guid + "]" + Text + "同步成功";
//        //    }
//        //    else
//        //    {
//        //        resultStr = "本次作业编号[" + guid + "]" + Text + "同步失败";
//        //    }
//        //    ResultSrt.Add(resultStr);
//        //    _logger.Info(resultStr);
//        //}

//        ////获取评量表的变化记录,同步评量表
//        //public async Task<List<string>> PatientScoreJobMain()
//        //{
//        //    var dataTableEditList = await _dataTableEditListService.GetDataTableEditList("PatientScoreMain");
//        //    var resultList = new List<string>();
//        //    if (dataTableEditList.Count > 0)
//        //    {
//        //        resultList = await PatientScoreJobDetail();
//        //    }
//        //    else
//        //    {
//        //        return null;
//        //    }

//        //    foreach (var item in dataTableEditList)
//        //    {
//        //        item.DataPumpFlag = "*";
//        //        item.DataPumpData = DateTime.Now;
//        //    }

//        //    try
//        //    {
//        //        _unitOfWork.GetRepository<DataTableEditListInfo>().Update(dataTableEditList);
//        //        await _unitOfWork.SaveChangesAsync();
//        //    }
//        //    catch (Exception ex)
//        //    {

//        //        _logger.Error("更新DataTableEditList失败" + ex.ToString());
//        //    }
//        //    resultList.Add("更新DataTableEditList完成");
//        //    return resultList;
//        //}

//        //private async Task<List<string>> PatientScoreJobDetail()
//        //{
//        //    //生成一个随机的日志序号
//        //    var guid = Guid.NewGuid().ToString("N");
//        //    var JobId = 4;//作业编号
//        //    // 确认作业是否可以被执行
//        //    var JobStatus = _jobLogService.GetJobStatus(JobId, "评量表数据送交换中心");
//        //    //不能执行，返回
//        //    if (!JobStatus)
//        //    {
//        //        _logger.Info("评量表数据送交换中心！，停止本次作业启动！");
//        //        ResultLog(guid, false, "评量表数据送交换中心，停止本次作业启动！");
//        //        return ResultSrt;
//        //    }
//        //    var Language = _config.Value.Language;
//        //    var HospitalID = _config.Value.HospitalID;
//        //    var resultSign = false;
//        //    ResultSrt.Add("*评量表数据送交换中心:本次同步开始**");
//        //    //启动任务风险评量数据送电子交换
//        //    resultSign = await _IRiskRecordService.SendRiskEMRRecordByDate(DateTime.Now, Language, HospitalID);
//        //    ResultLog(guid, resultSign, "风险评量数据送电子交换");          
//        //    //更新作业状态
//        //    JobStatus = _jobLogService.UpJobStatus(JobId, "同步完成");
//        //    if (!JobStatus)
//        //    {
//        //        _logger.Info("更新作业同步状态失败！");
//        //        ResultLog(guid, false, "更新作业同步状态失败！");
//        //    }
//        //    ResultLog(guid, JobStatus, "更新作业同步状态成功！");

//        //    //回写数据到中介库
//        //    var jobStatusStr = _jobLogService.GetJobStatusStr(JobId);//获得作业状态
//        //    //不能执行，返回
//        //    if (jobStatusStr != "正在执行")
//        //    {
//        //        _logger.Info("开始回写数据到中介库！");
//        //        resultSign = await _IReadJsonService.ReadDataSwitchCenterMain(); //向中介库写数据
//        //        ResultLog(guid, resultSign, "回写数据到中介库");
//        //        _logger.Info("回写数据到中介库完成！");
//        //    }
//        //    ResultSrt.Add("*****评量表数据送交换中心:本次同步结束**********");
//        //    return ResultSrt;
//        //}
////    }
////}
