﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Medical.Common;

namespace Medical.Data.Repository
{
    public class PatientAttachedInterventionRepository : IPatientAttachedInterventionRepository
    {
        private MedicalDbContext _dbContext = null;

        public PatientAttachedInterventionRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="mainID"></param>
        /// <param name="interventionID"></param>
        /// <returns></returns>
        public async Task<List<PatientAttachedInterventionInfo>> GetByPatientScheduleMainID(string mainID, int? interventionID)
        {
            return await _dbContext.PatientAttachedInterventions
                .Where(interventionID.HasValue, m => m.InterventionID == interventionID)
                .Where(m => m.PatientScheduleMainID == mainID && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 获取InterventionID
        /// </summary>
        /// <param name="ScheduleMainID"></param>
        /// <returns></returns>
        public async Task<List<PatientAttachedInterventionInfo>> GetScheduleMainID(string ScheduleMainID)
        {
            return await _dbContext.PatientAttachedInterventions.Where(m => m.PatientScheduleMainID == ScheduleMainID).ToListAsync();
        }

        public async Task<List<PatientAttachedInterventionInfo>> GetByInPatientID(string InpatienID)
        {
            return await _dbContext.PatientAttachedInterventions.Where(m => m.InpatientID == InpatienID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientAttachedInterventionInfo>> GetByPatientProblemID(string patientProblemID)
        {
            return await _dbContext.PatientAttachedInterventions.Where(m => m.PatientProblemID == patientProblemID && m.EndDate == null).ToListAsync();
        }

        public async Task<List<PatientAttachedInterventionInfo>> GetAllByPatientProblemID(string inpatientID, string patientProblemID)
        {
            return await _dbContext.PatientAttachedInterventions.Where(m => m.InpatientID == inpatientID
                                                       && m.PatientProblemID == patientProblemID).ToListAsync();
        }

        /// <summary>
        /// 透过病人护理问题序号取得附加措施数据
        /// </summary>
        /// <param name="patientProblemID">病人问题序号[]</param>
        /// <returns></returns>
        public async Task<List<PatientAttachedInterventionInfo>> GetByPatientProblemID(string[] patientProblemID)
        {
            return await _dbContext.PatientAttachedInterventions.Where(m => patientProblemID.Contains(m.PatientProblemID)).ToListAsync();
        }

        public async Task<PatientAttachedInterventionInfo> GetByAttacheID(string attacheID)
        {
            return await _dbContext.PatientAttachedInterventions.Where(m => m.PatientAttachedInterventionID == attacheID).SingleOrDefaultAsync();
        }

        public async Task<List<PatientAttachedInterventionInfo>> GetAsync(string inpatientID, DateTime startDate, DateTime endDate)
        {
            return await _dbContext.PatientAttachedInterventions.Where(m => m.InpatientID == inpatientID
            && m.StartDate >= startDate && m.StartDate <= endDate).ToListAsync();
        }

        public async Task<List<PatientAttachedInterventionInfo>> GetByInPatientID(string inPatientID, string frequency)
        {
            return await _dbContext.PatientAttachedInterventions.Where(m => m.InpatientID == inPatientID && m.Frequency == frequency && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<string>> GetInterventionIDs(string[] patientScheduleMainIDs, int interventionID)
        {
            var hasSet = new HashSet<string>(patientScheduleMainIDs);
            return await _dbContext.PatientAttachedInterventions.AsNoTracking().Where(m => hasSet.Contains(m.PatientScheduleMainID) &&
            m.InterventionID == interventionID).Select(m => m.PatientAttachedInterventionID).ToListAsync();
        }

        /// <summary>
        /// 获取未结束的计划ID
        /// </summary>
        /// <param name="inpatientID">患者ID</param>
        /// <returns></returns>
        public async Task<List<string>> GetUnEndIDs(string inpatientID)
        {
            var endTime = DateTime.Now.AddHours(12);

            var tempDate = endTime.Date;

            var datas = await _dbContext.PatientAttachedInterventions.Where(m => m.InpatientID == inpatientID
                                && m.EndDate == null || (m.EndDate.HasValue && m.EndDate.Value >= tempDate)
                                && m.DeleteFlag != "*").Select(m => new
                                {
                                    m.PatientAttachedInterventionID,
                                    m.EndDate,
                                    m.EndTime
                                }).ToListAsync();

            return datas.Where(m => m.EndDate == null || (m.EndDate.HasValue && m.EndTime.HasValue && m.EndDate.Value.Add(m.EndTime.Value) > endTime))
                .Select(m => m.PatientAttachedInterventionID).ToList();

            //var list = await _dbContext.PatientAttachedInterventions.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*")
            //    .AsNoTracking().ToListAsync();

            //list = list.Where(m => m.EndDate == null || (m.EndDate.HasValue && m.EndTime.HasValue && m.EndDate.Value.Add(m.EndTime.Value) > endTime))
            //    .ToList();

            //return list.Select(m => m.PatientAttachedInterventionID).ToList();
        }

        public async Task<List<PatientAttachedInterventionInfo>> GetTigger(string inpatientID, string patientProblemID, string PatientScheduleMainID)
        {
            return await _dbContext.PatientAttachedInterventions
                            .Where(m => m.InpatientID == inpatientID
                                && (m.PatientProblemID == patientProblemID
                                || m.PatientScheduleMainID == PatientScheduleMainID)
                                && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<string>> GetByPatientInterventionID(string inPatientID, string patientInterventionID)
        {
            return await _dbContext.PatientAttachedInterventions.Where(m => m.InpatientID == inPatientID && m.PatientInterventionID == patientInterventionID && m.DeleteFlag != "*")
                .Select(m => m.PatientAttachedInterventionID).ToListAsync();
        }

        public async Task<string> GetEMRRecordIDByAttacheID(string attacheID)
        {
            return await _dbContext.PatientAttachedInterventions.Where(m => m.PatientAttachedInterventionID == attacheID).Select(m => m.EMRRecordID).FirstOrDefaultAsync();
        }
        public async Task<string> GetInterventionIDByScheduleMainID(string ScheduleMainID, int interventionID)
        {
            var query = await _dbContext.PatientAttachedInterventions.Where(m => m.PatientScheduleMainID == ScheduleMainID && m.InterventionID == interventionID).FirstOrDefaultAsync();
            return query == null ? "" : query.PatientAttachedInterventionID;
        }
        /// <summary>
        /// 根据ScheduleMainID查找数据
        /// </summary>
        /// <param name="scheduleMainIDs">排程ID</param>
        /// <returns>
        /// </returns>
        public async Task<List<PatientAttachedInterventionInfo>> GetViewByScheduleMainIDs(IEnumerable<string> scheduleMainIDs)
        {
            var query = await _dbContext.PatientAttachedInterventions
                .Where(m => scheduleMainIDs.Contains(m.PatientScheduleMainID) && m.DeleteFlag != "*")
                .Select(m => new PatientAttachedInterventionInfo
                {
                    PatientScheduleMainID = m.PatientScheduleMainID,
                    Frequency = m.Frequency,
                    FrequencyID = m.FrequencyID,
                    FixTimeFlag = m.FixTimeFlag,
                    StartDate = m.StartDate,
                    StartTime = m.StartTime,
                    NumberOfTimes = m.NumberOfTimes,
                    PatientAttachedInterventionID = m.PatientAttachedInterventionID,
                    InterventionID = m.InterventionID
                })
                .ToListAsync();
            return query;
        }
        /// <summary>
        /// 获取存在触发记录的排程ID
        /// </summary>
        /// <param name="scheduleMainIDs">排程ID集合</param>
        /// <returns></returns>
        public async Task<string[]> GetExistAttachedRecordScheduleMainIDs(List<string> scheduleMainIDs, List<int> interventionIDs)
        {
            return await _dbContext.PatientAttachedInterventions
                .Where(m => scheduleMainIDs.Contains(m.PatientScheduleMainID) && interventionIDs.Contains(m.InterventionID) && m.DeleteFlag != "*")
                .Select(m => m.PatientScheduleMainID).Distinct().ToArrayAsync();
        }
    }
}