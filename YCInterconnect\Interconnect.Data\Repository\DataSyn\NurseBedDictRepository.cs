﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;


namespace Interconnect.Data 
{
   public class NurseBedDictRepository
    {
        private DataOutConnection _DataOutConnection = null;

        public NurseBedDictRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }

        public async Task<List<NurseBedDictInfo>> GetAllAsync()
        {
            try
            {
                return await _DataOutConnection.NurseBedDictInfos.ToListAsync();
            }
            catch (Exception ex)
            {

                throw ex;
            }
        }
    }
}
