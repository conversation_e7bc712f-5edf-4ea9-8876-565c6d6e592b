﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientNursingRecordDetailLogRepository : IPatientNursingRecordDetailLogRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientNursingRecordDetailLogRepository(
            MedicalDbContext medicalDb)
        {
            _medicalDbContext = medicalDb;
        }
        /// <summary>
        /// 获取没有同步的日志记录
        /// </summary>
        /// <returns></returns>
        public async Task<List<PatientNursingRecordDetailLogInfo>> GetNursingRecordDetailLog()
        {
            return await _medicalDbContext.PatientNursingRecordDetailLogInfos.Where(m => m.DataPumpFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据来源查询
        /// </summary>
        /// <param name="conditionType"></param>
        /// <param name="conditionValue"></param>
        /// <returns></returns>
        public async Task<PatientNursingRecordDetailLogInfo> GetDetailLogByConditionValue(string conditionType, string conditionValue)
        {
            return await _medicalDbContext.PatientNursingRecordDetailLogInfos.Where(m => m.ConditionType == conditionType && m.ConditionValue == conditionValue).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取特定患者的特定明细数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="sourceID"></param>
        /// <returns></returns>
        public async Task<PatientNursingRecordDetailLogInfo> GetNursingRecordDetailLog(string inpatientID, string sourceID)
        {
            return await _medicalDbContext.PatientNursingRecordDetailLogInfos.Where(m => m.InpatientID == inpatientID && m.SourceID1 == sourceID && m.DataPumpFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据sourceID获取数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="sourceID"></param>
        /// <returns></returns>
        public async Task<List<PatientNursingRecordDetailLogInfo>> GetDetailLogListBySourceID(string inpatientID, string sourceID)
        {
            return await _medicalDbContext.PatientNursingRecordDetailLogInfos.Where(m => m.InpatientID == inpatientID && m.SourceID1 == sourceID).ToListAsync();
        }
    }
}
