﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class QualityControlDetailRepository : IQualityControlDetailRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public QualityControlDetailRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        ///根据主表ID获取明细表数据
        /// </summary>
        /// <param name="qualityControlMainID"></param>
        /// <returns></returns>
        public async Task<List<QCCheckMainInfo>> GetByMainIDAsync(string qualityControlMainID)
        {
            return await _medicalDbContext.QualityControlDetails.Where(t => t.QCCheckRecordID == qualityControlMainID && t.DeleteFlag != "*").ToListAsync();
        }
    }
}
