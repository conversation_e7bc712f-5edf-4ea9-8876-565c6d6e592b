﻿/**
 * 2022-05-15   新增母婴关系同步，可通过母亲信息补全婴儿信息  ——孟昭永
 * 
 */
using Interconnect.Services.Interface;
using Interconnect.ViewModels;
using NLog;
using System;
using System.Threading.Tasks;

namespace Interconnect.Services
{
    public class FocusService : IFocusService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IWardDeptService _IWardDeptService;
        private readonly IBedService _IBedService;
        private readonly IDepartmentService _departmentService;
        private readonly IEmployeeService _employeeService;
        private readonly IInpatientService _InpatientService;
        private readonly IOrderService _IOrderService;
        private readonly IPatientBasicService _IPatientBasicService;
        private readonly IPatientDiagnosisService _IPatientDiagnosisService;
        private readonly IStationService _IStationService;
        private readonly ITestScheduleService _ITestScheduleService;
        private readonly IICInpatientLogService _ICInpatientLogService;
        private readonly IJobLogService _jobLogService;
        private readonly IEmployeeStationSwitchService _employeeStationSwitchService;
        private readonly IPatientOrderMainService _patientOrderMainService;
        private readonly IMedicineRecordService _medicineRecordService;
        private readonly IOperateListService _operateListService;

        public FocusService(IWardDeptService WardDeptService
            , IBedService BedServic
            , IDepartmentService departmentService
            , IEmployeeService employeeService
            , IInpatientService InpatientService
            , IOrderService OrderService
            , IPatientBasicService IPatientBasicService
            , IPatientDiagnosisService PatientDiagnosisService
            , IStationService StationService
            , ITestScheduleService TestScheduleService
            , IICInpatientLogService iCInpatientLogService
            , IJobLogService jobLogService
            , IEmployeeStationSwitchService employeeStationSwitchService
            , IPatientOrderMainService patientOrderMainService
            , IMedicineRecordService medicineRecordService
            , IOperateListService operateListService
            )
        {
            _IWardDeptService = WardDeptService;
            _IBedService = BedServic;
            _departmentService = departmentService;
            _employeeService = employeeService;
            _InpatientService = InpatientService;
            _IOrderService = OrderService;
            _IPatientBasicService = IPatientBasicService;
            _IPatientDiagnosisService = PatientDiagnosisService;
            _IStationService = StationService;
            _ITestScheduleService = TestScheduleService;
            _ICInpatientLogService = iCInpatientLogService;
            _jobLogService = jobLogService;
            _employeeStationSwitchService = employeeStationSwitchService;
            _patientOrderMainService = patientOrderMainService;
            _medicineRecordService = medicineRecordService;
            _operateListService = operateListService;
        }

        public async Task<bool> SyncPatientBasic(string chartNum)
        {
            //生成一个随即的日志序号
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.PatientBasicJob).ToString();
            var jobName = "";
            var jobStatus = false;
            var logMsg = "作业编号:" + guid + "启动同步任务:" + jobName;
            jobName = "病人基本信息";
            jobStatus = _jobLogService.GetJobStatus(jobId, jobName, chartNum);
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                ResultLog(guid, await _IPatientBasicService.SynchronizationMain(chartNum), "病人基本信息");// 同步病人基本信息
                _jobLogService.RemoveJob(jobId, jobName, chartNum);
            }
            return true;
        }

        /// <summary>
        ///  同步医院数据（除了在院病人信息）
        /// </summary>
        /// <returns></returns>
        public bool SyncInpatientBaseDict()
        {
            //生成一个随即的日志序号
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.StationJob).ToString();
            var jobName = "病区信息";
            var chartNum = "";
            var jobStatus = false;
            var logMsg = "作业编号:" + guid + "启动同步任务:" + jobName;
            chartNum = "";
            jobStatus = _jobLogService.GetJobStatus(jobId, jobName, chartNum);
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                ResultLog(guid, _IStationService.SynchronizationMain(), "病区信息");// 同步病区信息
                _jobLogService.RemoveJob(jobId, jobName, chartNum);
            }

            jobId = ((int)JobType.BedJob).ToString();
            jobName = "床位信息";
            chartNum = "";
            jobStatus = _jobLogService.GetJobStatus(jobId, jobName, chartNum);
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                ResultLog(guid, _IBedService.SynchronizationMain(), "床位信息"); //同步床位信息
                _jobLogService.RemoveJob(jobId, jobName, chartNum);
            }

            jobId = ((int)JobType.DepartmentJob).ToString();
            jobName = "科室信息";
            chartNum = "";
            jobStatus = _jobLogService.GetJobStatus(jobId, jobName, chartNum);
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                ResultLog(guid, _departmentService.DataPump(), "科室信息");// 同步科室信息
                _jobLogService.RemoveJob(jobId, jobName, chartNum);
            }

            jobId = ((int)JobType.StationToDepartment).ToString();
            jobName = "护理单元与科室对应字典";
            chartNum = "";
            jobStatus = _jobLogService.GetJobStatus(jobId, jobName, chartNum);
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                ResultLog(guid, _IWardDeptService.SynchronizationMain(), "护理单元与科室对应字典"); //同步护理单元与科室对应字典
                _jobLogService.RemoveJob(jobId, jobName, chartNum);
            }

            //jobId = 114;
            //jobName = "药物过敏";
            //chartNum = "";
            //jobStatus = _jobLogService.GetJobStatus(jobId, jobName, chartNum);
            //if (jobStatus)
            //{
            //    _logger.Info(logMsg + jobName);
            //    ResultLog(guid, await _IDrugAllergyService.SynchronizationMain(), "药物过敏信息");// 同步药物过敏信息
            //    _jobLogService.RemoveJob(jobId, jobName, chartNum);
            //}
            //jobId = 115;
            //jobName = "食物过敏";
            //chartNum = "";
            //jobStatus = _jobLogService.GetJobStatus(jobId, jobName, chartNum);
            //if (jobStatus)
            //{
            //    _logger.Info(logMsg + jobName);
            //    ResultLog(guid, await _IFoodAllergyService.SynchronizationMain(), "食物过敏信息");// 同步食物过敏信息
            //    _jobLogService.RemoveJob(jobId, jobName, chartNum);
            //}

            _logger.Info("作业编号:" + guid + "**所有数据同步作业:本次同步结束**");
            return true;
        }

        /// <summary>
        ///  同步新入院病人数据
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncNewInPatient()
        {
            var syncResult = false;
            //生成一个随机的日志序号
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.NewInpatientJob).ToString();
            var jobName = "新入院病人信息";
            var logMsg = "作业编号:" + guid + "启动同步任务:" + jobName;
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                try
                {
                    syncResult = await _InpatientService.SyncNewInPatient();
                }
                catch (Exception ex)
                {
                    _logger.Error("新入院病人信息同步失败" + ex.ToString());
                }

                ResultLog(guid, syncResult, "住院病人信息");// 同步住院病人信息
                _jobLogService.RemoveJob(jobId, jobName, "");
                _logger.Info("作业ID：" + jobId.ToString() + "作业编号:" + guid + "**同步住院病人信息作业:本次同步结束*");
            }
            return syncResult;
        }

        /// <summary>
        ///  根据病区分组同步在院病人数据
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncInPatientByStationGroup(int StationGroup)
        {
            //生成一个随机的日志序号
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.GroupStationJob).ToString();
            var jobName = "分组同步住院病人信息";
            var logMsg = "作业编号:" + guid + "启动同步任务:" + jobName;
            var subJobId = StationGroup.ToString();
            var jobStatus = false;
            try
            {
                jobStatus = _jobLogService.GetJobStatus(jobId, jobName, subJobId);
            }
            catch (Exception ex)
            {
                _logger.Error("获取" + jobName + "作业状态失败" + ex.ToString());
                return false;
            }

            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                var resultFlag = false;
                try
                {
                    resultFlag = await _InpatientService.SyncInPatientByStationGroup(StationGroup);
                }
                catch (Exception ex)
                {
                    _logger.Error("住院病人信息同步失败,StationGroup:" + StationGroup + ex.ToString());
                    resultFlag = false;
                }

                ResultLog(guid, resultFlag, "住院病人信息");// 同步住院病人信息
                _jobLogService.RemoveJob(jobId, jobName, subJobId);
                _logger.Info("作业ID：" + jobId.ToString() + "作业编号:" + guid + "**同步住院病人信息作业:本次同步结束*");
            }
            return true;
        }

        /// <summary>
        ///  根据病区同步在院病人数据
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncInPatientByStationCode(string stationCode)
        {
            //生成一个随机的日志序号
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.OneStationInpatientJob).ToString();
            var jobName = "根据病区同步在院病人数据";
            var logMsg = "作业编号:" + guid + "启动同步任务:" + jobName;
            _logger.Info("启动作业" + logMsg);
            var subJobId = stationCode;
            var jobStatus = false;
            try
            {
                jobStatus = _jobLogService.GetJobStatus(jobId, jobName, subJobId);
            }
            catch (Exception ex)
            {
                _logger.Error(jobName + "判断作业状态失败," + ex.ToString());
                return false;
            }

            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                var resultFlag = false;
                try
                {
                    resultFlag = await _InpatientService.SyncInPatientByStationCode(stationCode);
                }
                catch (Exception ex)
                {
                    _logger.Error("根据病区同步在院病人数据 同步失败" + ex.ToString());
                    resultFlag = false;
                }
                ResultLog(guid, resultFlag, "住院病人信息");// 同步住院病人信息
                _jobLogService.RemoveJob(jobId, jobName, subJobId);
                _logger.Info("作业ID：" + jobId.ToString() + "作业编号:" + guid + "**同步住院病人信息作业:本次同步结束*");
            }
            return true;
        }

        /// <summary>
        ///  同步历史医嘱数据
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncHistoryPatientOrder()
        {
            //生成一个随即的日志序号
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.HistoryPatientOrderJob).ToString();
            var jobName = "历史医嘱记录";
            var jobStatus = false;
            var logMsg = "作业编号:" + guid + "启动同步任务:";
            _logger.Info("作业ID:" + jobId.ToString() + "作业编号:" + guid + "**同步医嘱数据:本次同步开始**");
            jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "0");
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                var resultFlag = false;
                try
                {
                    resultFlag = await _patientOrderMainService.SyncHistoryPatientOrder();
                }
                catch (Exception ex)
                {
                    _logger.Error("同步医院医嘱数据失败" + ex.ToString());
                    resultFlag = false;
                }
                ResultLog(guid, resultFlag, jobName);// 同步医嘱明细记录
                _jobLogService.RemoveJob(jobId, jobName, "0");
                _logger.Info("作业ID：" + jobId.ToString() + "作业编号:" + guid + "**同步医嘱信息:本次同步结束*");
            }
            return true;
        }

        /// <summary>
        ///  同步医嘱数据(手动)
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncHisOrderByManual(DateTime startDateTime, DateTime endDateTime, string apiAddress, string hospitalID, bool syncStopFlag, string caseNumber)
        {
            try
            {
                await _patientOrderMainService.SyncHisOrderByManual(startDateTime, endDateTime, apiAddress, hospitalID, syncStopFlag, caseNumber);
            }
            catch (Exception ex)
            {
                _logger.Error("同步医院医嘱数据失败" + ex.ToString());
            }
            return true;
        }

        /// <summary>
        ///  同步医院医嘱数据
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncPatientOrderByDateTime()
        {
            //生成一个随即的日志序号
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.PatientOrderJob).ToString();
            var jobName = "医嘱记录";
            var jobStatus = false;
            var logMsg = "作业编号:" + guid + "启动同步任务:";
            _logger.Info("作业ID:" + jobId.ToString() + "作业编号:" + guid + "**同步医嘱数据:本次同步开始**");
            jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "0");
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                var resultFlag = false;
                try
                {
                    resultFlag = await _patientOrderMainService.SyncPatientOrderByDateTime();
                }
                catch (Exception ex)
                {
                    _logger.Error("同步医院医嘱数据失败" + ex.ToString());
                    resultFlag = false;
                }
                ResultLog(guid, resultFlag, "医嘱记录");// 同步医嘱明细记录
                _jobLogService.RemoveJob(jobId, jobName, "0");
                _logger.Info("作业ID：" + jobId.ToString() + "作业编号:" + guid + "**同步医嘱信息:本次同步结束*");
            }
            return true;
        }

        /// <summary>
        ///  根据时间段同步检验数据 
        /// </summary>
        /// <param name="chartNum">如果为"",则同步所有病人的数据</param>
        /// <returns></returns>
        public async Task<bool> SyncTestReportByDateTime()
        {
            //生成一个随即的日志序号
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.TestJob).ToString();
            var jobName = "检验数据";
            var jobStatus = false;
            var logMsg = "作业编号:" + guid + "启动同步任务:";
            _logger.Info("作业ID:" + jobId.ToString() + "作业编号:" + guid + "**同步检验数据:本次同步开始**");
            jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "0");
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                var resultFlag = false;
                try
                {
                    resultFlag = await _ITestScheduleService.SyncTestReportByDateTime();
                }
                catch (Exception ex)
                {
                    _logger.Error("同步医院医嘱数据失败" + ex.ToString());
                    resultFlag = false;
                }
                ResultLog(guid, resultFlag, "检验数据");// 同步检验数据
                _jobLogService.RemoveJob(jobId, jobName, "0");
            }
            _logger.Info("作业ID：" + jobId.ToString() + "作业编号:" + guid + "**同步检验数据:本次同步结束*");
            return true;
        }

        /// <summary>
        ///  根据CaseNumber同步检验数据
        /// </summary>
        /// <param name="chartNum">如果为"",则同步所有病人的数据</param>
        /// <returns></returns>
        public async Task<bool> SyncTestReportByCaseNumber(string caseNumber)
        {
            //生成一个随即的日志序号
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.TestJob).ToString();
            var jobName = "检验数据";
            var jobStatus = false;
            var logMsg = "作业编号:" + guid + "启动同步任务:";
            _logger.Info("作业ID:" + jobId.ToString() + "作业编号:" + guid + "**同步检验数据:本次同步开始**");
            jobStatus = _jobLogService.GetJobStatus(jobId, jobName, caseNumber);
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                var resultFlag = false;
                try
                {
                    resultFlag = await _ITestScheduleService.SyncTestReportByCaseNumber(caseNumber);
                }
                catch (Exception ex)
                {
                    _logger.Error("同步检验数据失败" + ex.ToString());
                    resultFlag = false;
                }
                ResultLog(guid, resultFlag, "检验数据");// 同步检验数据
                _jobLogService.RemoveJob(jobId, jobName, caseNumber);
            }
            _logger.Info("作业ID：" + jobId.ToString() + "作业编号:" + guid + "**同步检验数据:本次同步结束*");
            return true; ;
        }

        /// <summary>
        /// <summary>
        /// 写同步日志
        /// </summary>
        /// <param name="Sign"></param>
        /// <param name="Text"></param>
        private void ResultLog(string guid, bool Sign, string Text)
        {
            string resultStr = "";
            if (Sign)
            {
                resultStr = "本次作业编号[" + guid + "]" + Text + "同步成功";
            }
            else
            {
                resultStr = "本次作业编号[" + guid + "]" + Text + "同步失败";
            }
            _logger.Info(resultStr);
        }

        public async Task<bool> SyncEmployee()
        {
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.PatientBasicJob).ToString();
            var jobName = "员工基本信息";
            var chartNum = "";
            var jobStatus = false;
            var logMsg = "作业编号:" + guid + "启动同步任务:";
            jobStatus = _jobLogService.GetJobStatus(jobId, jobName, chartNum);
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                var resultFlag = false;
                try
                {
                    resultFlag =await _employeeService.Synchronization();
                }
                catch (Exception ex)
                {
                    _logger.Error("同步员工基本信息失败" + ex.ToString());
                    resultFlag = false;
                }
                ResultLog(guid, resultFlag, "员工基本信息");// 同步院内人员基本信息
                _jobLogService.RemoveJob(jobId, jobName, chartNum);
            }
            return true;
        }

        public async Task<bool> SyncEmployeeStationSwitch()
        {
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.EmployeeToStation).ToString();
            var jobName = "病区权限信息";
            var chartNum = "";
            var jobStatus = false;
            var logMsg = "作业编号:" + guid + "启动同步任务:";
            jobStatus = _jobLogService.GetJobStatus(jobId, jobName, chartNum);
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                var resultFlag = false;
                try
                {
                    resultFlag = await _employeeStationSwitchService.SynchronizationMain();
                }
                catch (Exception ex)
                {
                    _logger.Error("同步病区权限信息失败" + ex.ToString());
                    resultFlag = false;
                }
                ResultLog(guid, resultFlag, "人员病区权限信息");// 人员病区信息
                _jobLogService.RemoveJob(jobId, jobName, chartNum);
            }
            return true;
        }

        public bool SyncWardDept()
        {
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.StationToDepartment).ToString();
            var jobName = "护理单元与科室对应字典";
            var chartNum = "";
            var jobStatus = false;
            var logMsg = "作业编号:" + guid + "启动同步任务:";
            jobStatus = _jobLogService.GetJobStatus(jobId, jobName, chartNum);
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                var resultFlag = false;
                try
                {
                    resultFlag = _IWardDeptService.SynchronizationMain();
                }
                catch (Exception ex)
                {
                    _logger.Error("同步护理单元与科室对应字典失败" + ex.ToString());
                    resultFlag = false;
                }
                ResultLog(guid, resultFlag, "护理单元与科室对应字典"); //同步护理单元与科室对应字典
                _jobLogService.RemoveJob(jobId, jobName, chartNum);
            }
            return true;
        }

        public async Task<bool> SyncOrderDict()
        {
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.OrderDictJob).ToString();
            var jobName = "医嘱字典";
            var chartNum = "";
            var jobStatus = false;
            var logMsg = "作业编号:" + guid + "启动同步任务:";
            jobStatus = _jobLogService.GetJobStatus(jobId, jobName, chartNum);
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                ResultLog(guid, await _IOrderService.SynchronizationMain(), "医嘱字典信息");// 同步医嘱信息
                _jobLogService.RemoveJob(jobId, jobName, chartNum);
            }
            return true;
        }

        public async Task<bool> SyncInpatientLogData(string caseNumber)
        {
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.InpatientLog).ToString();
            var jobName = "住院病人历史信息";
            var logMsg = "作业编号:" + guid + "启动同步任务:";
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, caseNumber);
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                ResultLog(guid, await _ICInpatientLogService.SynchronizationMain(caseNumber), "住院病人历史信息");// 同步住院病人历史信息
                _jobLogService.RemoveJob(jobId, jobName, caseNumber);
            }
            return true;
        }

        public bool SyncBed()
        {
            //生成一个随即的日志序号
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.BedJob).ToString();
            var jobName = "床位信息";
            var chartNum = "";
            var jobStatus = false;
            var logMsg = "作业编号:" + guid + "启动同步任务:" + jobName;
            chartNum = "";
            jobStatus = _jobLogService.GetJobStatus(jobId, jobName, chartNum);
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                ResultLog(guid, _IBedService.SynchronizationMain(), "床位信息"); //同步床位信息
                _jobLogService.RemoveJob(jobId, jobName, chartNum);
            }
            return true;
        }

        public bool SyncStation()
        {
            //生成一个随即的日志序号
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.StationJob).ToString();
            var jobName = "病区信息";
            var chartNum = "";
            var jobStatus = false;
            var logMsg = "作业编号:" + guid + "启动同步任务:" + jobName;
            chartNum = "";
            jobStatus = _jobLogService.GetJobStatus(jobId, jobName, chartNum);
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                ResultLog(guid, _IStationService.SynchronizationMain(), "病区信息");// 同步病区信息
                _jobLogService.RemoveJob(jobId, jobName, chartNum);
            }
            return true;
        }

        public bool SyncDepartmen()
        {
            //生成一个随即的日志序号
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.DepartmentJob).ToString();
            var jobName = "科室信息";
            var chartNum = "";
            var jobStatus = false;
            var logMsg = "作业编号:" + guid + "启动同步任务:" + jobName;
            chartNum = "";
            jobStatus = _jobLogService.GetJobStatus(jobId, jobName, chartNum);
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                ResultLog(guid, _departmentService.DataPump(), "科室信息");// 同步科室信息
                _jobLogService.RemoveJob(jobId, jobName, chartNum);
            }
            return true;
        }

        /// <summary>
        /// 同步病区病人主诉
        /// </summary>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<bool> SyncPatientChiefComplaint(int? stationID)
        {
            //生成一个随机的日志序号
            var guid = Guid.NewGuid().ToString("N");

            var jobId = ((int)JobType.StationPatientChiefComplaint).ToString();
            var jobName = "病区病人主诉";
            var logMsg = "作业编号:" + guid + "启动同步任务:" + jobName;
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                var resultFlag = false;
                try
                {
                    resultFlag = await _InpatientService.SyncPatientChiefComplaintAsync(stationID);
                }
                catch (Exception ex)
                {
                    _logger.Error("同步手术信息失败" + ex.ToString());
                    resultFlag = false;
                }
                ResultLog(guid, resultFlag, "病区病人主诉");
                _jobLogService.RemoveJob(jobId, jobName, "");
                _logger.Info("作业ID：" + jobId.ToString() + "作业编号:" + guid + "**同步病区病人主诉作业:本次同步结束*");
            }
            return true;
        }

        /// <summary>
        /// 出院病人同步,同步一段时间内的出院病人数据
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncDischargedPatientsByDateTime()
        {
            //生成一个随机的日志序号
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.InPatientDischargeJob).ToString();
            var jobName = "出院病人信息";
            var logMsg = "作业编号:" + guid + "启动同步任务:" + jobName;
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            if (jobStatus)
            {
                var resultFlag = false;
                try
                {
                    resultFlag = await _InpatientService.SyncDischargedPatientsEventByDateTime();
                }
                catch (Exception ex)
                {
                    _logger.Error("出院病人信息同步失败" + ex.ToString());
                    resultFlag = false;
                }
                _logger.Info(logMsg + jobName);
                ResultLog(guid, resultFlag, "出院病人信息");// 同步住院病人信息
                _jobLogService.RemoveJob(jobId, jobName, "");
                _logger.Info("作业ID：" + jobId.ToString() + "作业编号:" + guid + "***同步出院病人信息作业:本次同步结束***");
            }
            return true;
        }

        /// <summary>
        /// 同步一段时间内的给药记录数据
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncMedicineRecordByDateTimeAsync()
        {
            //生成一个随即的日志序号
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.MedicineIntakeOutPutJob).ToString();
            var jobName = "给药写出入量信息";
            var jobStatus = false;
            var logMsg = "作业编号:" + guid + "启动同步任务:";
            _logger.Info("作业ID:" + jobId.ToString() + "作业编号:" + guid + "***同步给药写出入量数据:本次同步开始***");
            jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "0");
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                ResultLog(guid, await _medicineRecordService.SyncMedicineRecordByDateTimeAsync(), "给药写出入量信息");// 同步给药写出入量信息
                _jobLogService.RemoveJob(jobId, jobName, "0");
            }
            _logger.Info("作业ID：" + jobId.ToString() + "作业编号:" + guid + "***同步给药写出入量数据:本次同步结束***");
            return true;
        }
        /// <summary>
        /// 病人出院病人数据补救
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncDisChargeInPatientByDatetimeSupplement()
        {
            //生成一个随机的日志序号
            var syncResult = false;
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.DisChargeInPatientJobBuJiu).ToString();
            var jobName = "出院病人信息同步";
            var logMsg = "作业编号:" + guid + "启动同步任务:" + jobName;
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                var resultFlag = false;
                try
                {
                    resultFlag = await _InpatientService.SyncDisChargeInPatientByDatetimeSupplement();
                }
                catch (Exception ex)
                {
                    _logger.Error("出院病人病区信息同步失败" + ex.ToString());
                    resultFlag = false;
                }

                ResultLog(guid, resultFlag, "病人信息");// 同步住院病人信息
                _jobLogService.RemoveJob(jobId, jobName, "");
                _logger.Info("作业ID：" + jobId.ToString() + "作业编号:" + guid + "**同步出院信息错误病人信息作业:本次同步结束*");
            }
            return syncResult;
        }

        /// <summary>
        /// 同步手术记录,同步一段时间内的手术数据
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncOperateDataByDateTime()
        {
            //生成一个随机的日志序号
            var syncResult = true;
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.NewInpatientJob).ToString();
            var jobName = "病人手术信息成功";
            var logMsg = "作业编号:" + guid + "启动同步任务:" + jobName;
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                var resultFlag = false;
                try
                {
                    resultFlag = await _operateListService.SyncOperateDataByDateTime();
                }
                catch (Exception ex)
                {
                    _logger.Error("同步手术信息失败" + ex.ToString());
                    resultFlag = false;
                    syncResult = false;
                }
                ResultLog(guid, resultFlag, "病人信息");//同步患者手术信息
                _jobLogService.RemoveJob(jobId, jobName, "");
                _logger.Info("作业ID：" + jobId.ToString() + "作业编号:" + guid + "**同步病人手术信息错误病人信息作业:本次同步结束*");
            }
            return syncResult;
        }

        /// <summary>
        /// 根据时间段同步出院患者（刷新用）
        /// </summary>
        /// <param name="startDateTime"></param>
        /// <param name="endDateTime"></param>
        /// <returns></returns>
        public async Task<bool> SyncDischargedByDateTime(DateTime? startDateTime, DateTime? endDateTime)
        {
            //生成一个随机的日志序号
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.PatientDischargeJob).ToString();
            var jobName = "出院病人信息";
            var logMsg = "作业编号:" + guid + "启动同步任务:" + jobName;
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            if (jobStatus)
            {
                var resultFlag = false;
                try
                {
                    resultFlag = await _InpatientService.SyncDischargedByDateTime(startDateTime, endDateTime);
                }
                catch (Exception ex)
                {
                    _logger.Error("出院病人信息同步失败" + ex.ToString());
                    resultFlag = false;
                }
                _logger.Info(logMsg + jobName);
                ResultLog(guid, resultFlag, "出院病人信息");// 同步住院病人信息
                _jobLogService.RemoveJob(jobId, jobName, "");
                _logger.Info("作业ID：" + jobId.ToString() + "作业编号:" + guid + "***同步出院病人信息作业:本次同步结束***");
            }
            return true;
        }

        /// <summary>
        /// 同步母婴关系
        /// </summary>
        /// <param name="stationCode">有母婴关系的病区码</param>
        /// <returns></returns>
        public async Task<bool> SyncMotherAndChildList(string stationCode)
        {
            //生成一个随机的日志序号
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.MotherAndChild).ToString();
            var jobName = "同步母婴关系";
            var logMsg = "作业编号:" + guid + "启动同步任务:" + jobName;
            _logger.Info("启动作业" + logMsg);
            var subJobId = stationCode;
            var jobStatus = false;
            try
            {
                jobStatus = _jobLogService.GetJobStatus(jobId, jobName, subJobId);
            }
            catch (Exception ex)
            {
                _logger.Error(jobName + "判断作业状态失败," + ex.ToString());
                return false;
            }

            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                var resultFlag = false;
                try
                {
                    resultFlag = await _InpatientService.SyncMotherAndChildList(stationCode);
                }
                catch (Exception ex)
                {
                    _logger.Error("同步母婴关系失败" + ex.ToString());
                    resultFlag = false;
                }
                ResultLog(guid, resultFlag, "同步母婴关系");// 同步住院病人信息
                _jobLogService.RemoveJob(jobId, jobName, subJobId);
                _logger.Info("作业ID：" + jobId.ToString() + "作业编号:" + guid + "***同步母婴关系:本次同步结束***");
            }
            return true;
        }
    }
}