﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class AdminNoticeReadRepository : IAdminNoticeReadRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public AdminNoticeReadRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }
        /// <summary>
        /// AdminNoticeReadInfo
        /// </summary>
        /// <param name="AddEmployeeID"></param>
        /// <returns></returns>
        public async Task<List<AdminNoticeReadInfo>> GetAsync(string AddEmployeeID)
        {
            return await _medicalDbContext.AdminNoticeReadInfos.Where(m =>
            m.EmployeeID != null && m.EmployeeID.Trim() == AddEmployeeID.Trim() &&
            m.DeleteFlag != "*").ToListAsync();
        }
    }
}
