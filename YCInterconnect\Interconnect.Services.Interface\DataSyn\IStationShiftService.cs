﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Models;

namespace Interconnect.Services.Interface
{
    
    public interface IStationShiftService
    {
        /// <summary>
        /// 获取没有同步的病区信息
        /// </summary>
        /// <returns></returns>
        Task<List<StationShiftInfo>> GetAllAsync();
        /// <summary>
        /// 同步病区信息 
        /// </summary>
        /// <returns></returns>
        Task<bool> SynchronizationMain();
    }
}
