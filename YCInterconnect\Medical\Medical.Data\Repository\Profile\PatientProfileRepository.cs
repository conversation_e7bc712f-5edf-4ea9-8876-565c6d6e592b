﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModel;
using Medical.ViewModels;
using Medical.ViewModels.Interface;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientProfileRepository : IPatientProfileRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientProfileRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 取得Profile资料
        /// </summary>
        /// <returns></returns>
        public async Task<List<PatientProfileInfo>> GetAsync(string inpatientID, bool autoAddMark)
        {
            if (autoAddMark)
                return await _medicalDbContext.PatientProfile.Where(m => m.InpatientID == inpatientID).ToListAsync();
            else
                return await _medicalDbContext.PatientProfile.Where(m => m.InpatientID == inpatientID && m.AutoAddFlag != "V").ToListAsync();
        }

        public async Task<List<PatientProfileInfo>> GetByAssessListID(string inpatientID, int assessListID)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.InpatientID == inpatientID
            && m.AssessListID == assessListID).ToListAsync();
        }

        /// <summary>
        /// 获取病人Profile数据
        /// </summary>
        /// <param name="hospitalID">医疗院所序号</param>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="assessListIDs">评估序号</param>
        /// <returns></returns>
        public async Task<List<PatientProfileInfo>> GetPatientProfileDataByAssessListID(string hospitalID, string inpatientID, int[] assessListIDs)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.HospitalID == hospitalID
               && m.InpatientID == inpatientID && assessListIDs.Contains(m.AssessListID)
              ).ToListAsync();
        }

        public async Task<List<PatientProfileInfo>> GetByMoreThanEditTime(string hospitalID, string inpatientID, DateTime date, TimeSpan time)
        {
            var profiles = await _medicalDbContext.PatientProfile.Where(m =>
             m.HospitalID == hospitalID &&
             m.InpatientID == inpatientID &&
             ((m.ProfileDate == date && m.ProfileTime > time) ||
             m.ProfileDate > date)
             ).ToListAsync();
            return profiles;
        }

        public async Task<List<PatientProfileInfo>> GetByChartNo(string hospitalID, string chartNo)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.ChartNo == chartNo)
                .OrderByDescending(m => m.ProfileDate).ThenByDescending(m => m.ProfileTime).ToListAsync();
        }

        public async Task<List<PatientProfileDataView>> GetPatientProfileByChartNo(string chartNo)
        {
            var datetime = DateTime.Now;
            return await _medicalDbContext.PatientProfile.Where(m => m.ChartNo == chartNo
            && (m.DueDay == null || m.DueDay > datetime.Date))
                .Select(m => new PatientProfileDataView
                {
                    AssessListID = m.AssessListID,
                    AssessValue = m.AssessValue,
                    ProfileDateTime = m.ProfileDate.Add(m.ProfileTime),
                    SerialNumber = m.SerialNumber
                }).Distinct().ToListAsync();
        }

        public async Task<List<PatientProfileInfo>> GetByChartNo(string hospitalID, string chartNo, bool autoAddMark)
        {
            if (autoAddMark)
                return await _medicalDbContext.PatientProfile.Where(m => m.HospitalID == hospitalID && m.ChartNo == chartNo).ToListAsync();
            else
                return await _medicalDbContext.PatientProfile.Where(m => m.HospitalID == hospitalID && m.ChartNo == chartNo && m.AutoAddFlag != "V").ToListAsync();
        }

        public async Task<List<PatientProfileInfo>> GetByChartNoToPatientAssess(string hospitalID, string chartNo, List<int> assessListIDs)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.HospitalID == hospitalID && m.ChartNo == chartNo && m.ModelName == "Assess" && assessListIDs.Contains(m.AssessListID))
                .Select(m => new PatientProfileInfo
                {
                    ID = m.ID,
                    AssessListID = m.AssessListID,
                    ChartNo = m.ChartNo
                }).ToListAsync();
        }

        public async Task<List<PatientProfileInfo>> GetFeedingProfileAgeStatus(string hospitalID, string inpatientID)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.HospitalID == hospitalID && m.InpatientID == inpatientID && m.AssessListID == 2559).ToListAsync();
        }

        public async Task<List<PatientProfileIDView>> GetDuyDayData(string hospitalID, DateTime dateTime)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.HospitalID == hospitalID
            && m.DueDay <= dateTime)
                .Select(m => new PatientProfileIDView { PatientProfileID = m.ID, ChartNo = m.ChartNo, InpatientID = m.InpatientID })
                .ToListAsync();
        }

        public async Task<List<PatientProfileInfo>> GetAssessListIDByChartNo(string chartNo, int assessListID)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.ChartNo == chartNo
               && m.AssessListID == assessListID).ToListAsync();
        }

        public async Task<List<PatientProfileIDView>> GetPatientProfileIDSByChartNo(string chartNo, int assessListID)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.ChartNo == chartNo
               && m.AssessListID == assessListID)
                .Select(m => new PatientProfileIDView { PatientProfileID = m.ID, ChartNo = m.ChartNo }).ToListAsync();
        }

        public async Task<KeyValue> GetByAssessListID(string hospitalID, string chartNo, int assessListID)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.HospitalID == hospitalID
                        && m.ChartNo == chartNo && m.AssessListID == assessListID)
                        .Select(m => new KeyValue { ID = m.AssessListID, Value = m.AssessValue }).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 取得评估序号数据
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="assessListIDs">护理评估序号数组</param>
        /// <returns></returns>
        public async Task<Dictionary<int, string>> GetAssessDictByAssessListIDs(string inpatientID, int[] assessListIDs)
        {
            var result = await _medicalDbContext.PatientProfile.Where(m => m.InpatientID == inpatientID && assessListIDs.Contains(m.AssessListID))
                     .Select(m => new PatientProfile
                     {
                         AssessListID = m.AssessListID,
                         AssessValue = m.AssessValue
                     }
                     ).ToListAsync();

            var dict = result.GroupBy(m => new { m.AssessListID },
             (m, n) => new
             {
                 m.AssessListID,
                 n.FirstOrDefault().AssessValue
             })
            .ToDictionary(m => m.AssessListID, m => m.AssessValue);
            return dict;
        }

        public async Task<List<int>> GetAssessListIDs(string inpatientID)
        {
            var list = await _medicalDbContext.PatientProfile.AsNoTracking().Where(m => m.InpatientID == inpatientID)
                .Select(m => m.AssessListID).Distinct().ToListAsync();
            return list;
        }

        public async Task<List<int>> GetAssessList(string hospitalID, string chartNo)
        {
            return await _medicalDbContext.PatientProfile.AsNoTracking().Where(m => m.HospitalID == hospitalID && m.ChartNo == chartNo)
                                  .Select(m => m.ID).ToListAsync();
        }

        public async Task<List<PatientProfileInfo>> GetAllergyProfile(string hospitalID, string chartNo)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.HospitalID == hospitalID && m.ChartNo == chartNo
                      && (m.AssessListID == 185 || m.AssessListID == 186 || m.AssessListID == 3878)).ToListAsync();
        }

        //确认AssessListId 是否存在
        public async Task<List<int>> GetPatientAssessListIDs(string inpatientID, int[] assessListIDs)
        {
            return await _medicalDbContext.PatientProfile.AsNoTracking().Where(m => m.InpatientID == inpatientID
                 && assessListIDs.Contains(m.AssessListID)).Select(m => m.AssessListID).ToListAsync();
        }

        //根据inpatientId,assessListIDs，获取数据
        public async Task<List<PatientProfileInfo>> GetPatientProfileByassessListIDs(string inpatientID, int[] assessListIDs)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.InpatientID == inpatientID
                 && assessListIDs.Contains(m.AssessListID)).Select(m => new PatientProfileInfo
                 {
                     InpatientID = m.InpatientID,
                     AssessListID = m.AssessListID,
                     AssessValue = m.AssessValue,
                     ProfileDate = m.ProfileDate,
                     ProfileTime = m.ProfileTime
                 }).ToListAsync();
        }

        public async Task<bool> GetNecessaryAssessListAsync(string inpatientID, int assessListID)
        {
            return await _medicalDbContext.PatientProfile.AnyAsync(m =>
            m.InpatientID == inpatientID
            && m.AssessListID == assessListID);
        }

        public async Task<Dictionary<int, DateTime>> GetProfileForScore(string inpatientID)
        {
            var result = await _medicalDbContext.PatientProfile.AsNoTracking().Where(m => m.InpatientID == inpatientID)
                .Select(m => new ProfileAssessListView
                {
                    AssessListID = m.AssessListID,
                    ModifyDate = m.ModifyDate
                }).ToListAsync();

            var dict = result.GroupBy(m => new { m.AssessListID },
              (m, n) => new
              {
                  m.AssessListID,
                  n.OrderByDescending(o => o.ModifyDate).FirstOrDefault().ModifyDate
              })
             .ToDictionary(m => m.AssessListID, m => m.ModifyDate);
            return dict;
        }

        public async Task<List<KeyValue>> GetByChartNo(string chartNo, List<int> assessListIDs)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.ChartNo == chartNo
               && assessListIDs.Contains(m.AssessListID))
               .Select(m => new KeyValue
               {
                   ID = m.AssessListID,
                   Value = m.AssessValue
               }).ToListAsync();
        }

        /// <summary>
        /// 取得病人profile数据
        /// </summary>
        /// <param name="chartNo"></param>
        /// <param name="assessListIDs"></param>
        /// <returns></returns>
        public async Task<List<KeyValue>> GetByInpatientID(string inpatientID, List<int> assessListIDs)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.InpatientID == inpatientID
               && assessListIDs.Contains(m.AssessListID))
               .Select(m => new KeyValue
               {
                   ID = m.AssessListID,
                   Value = m.AssessValue
               }).ToListAsync();
        }

        /// <summary>
        /// 根据assessListID数组获取数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="assessListIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientProfileInfo>> GetPatientAssessList(string inpatientID, int[] assessListIDs)
        {
            return await _medicalDbContext.PatientProfile.AsNoTracking().Where(m => m.InpatientID == inpatientID
                 && assessListIDs.Contains(m.AssessListID)).ToListAsync();
        }

        /// <summary>
        /// 根据病人ID取得profile数据
        /// </summary>
        /// <param name="patientID"></param>
        /// <param name="autoAddMark"></param>
        /// <returns></returns>
        public async Task<List<PatientProfileInfo>> GetProfileBypatientIDAsync(string patientID, bool autoAddMark)
        {
            if (autoAddMark)
                return await _medicalDbContext.PatientProfile.Where(m => m.PatientID == patientID).ToListAsync();
            else
                return await _medicalDbContext.PatientProfile.Where(m => m.PatientID == patientID && m.AutoAddFlag != "V").ToListAsync();
        }

        public async Task<List<PatientProfileInfo>> GetPatientAssessList(List<string> inpatientIDs, int[] assessListIDs)
        {
            return await _medicalDbContext.PatientProfile.AsNoTracking().Where(m => inpatientIDs.Contains(m.InpatientID)
               && assessListIDs.Contains(m.AssessListID)).ToListAsync();
        }

        public async Task<List<ProfileNursingLevelView>> GetNursingLevelAsync(string hospitalID, List<string> inpatientIDs)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.HospitalID == hospitalID && inpatientIDs.Contains(m.InpatientID))
                .Select(m => new ProfileNursingLevelView
                {
                    InpatientID = m.InpatientID,
                    AssessListID = m.AssessListID
                }).ToListAsync();
        }

        public async Task<List<ProfileDetail>> GetProfilesDetail(string hospitalID, string inpatientID)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.HospitalID == hospitalID && m.InpatientID == inpatientID)
                .Select(m => new ProfileDetail
                {
                    ProfileDate = m.ProfileDate,
                    ProfileTime = m.ProfileTime,
                    AssessListID = m.AssessListID,
                    AssessValue = m.AssessValue,
                    Unit = m.Unit,
                    NormalRange = m.NormalRange,
                    ModelName = m.ModelName
                }).ToListAsync();
        }

        public async Task<List<PatientProfileInfo>> GetPatientProfileBySerialNumber(string hospitalID, string inpatientID, string serialNumber)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.HospitalID == hospitalID
                        && m.InpatientID == inpatientID
                        && m.SerialNumber == serialNumber).ToListAsync();
        }

        public async Task<List<KeyValue>> GetProfileKeyValue(string hospitalID, string chartNo)
        {
            return await _medicalDbContext.PatientProfile.Where(m =>
                          m.HospitalID == hospitalID
                       && m.ChartNo == chartNo)
                       .Select(m => new KeyValue { ID = m.AssessListID, Value = m.AssessValue }).ToListAsync();
        }

        public async Task<List<KeyValue>> GetProfileKeyValue(string hospitalID, string inpatientID, int[] assessListIDs)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.HospitalID == hospitalID
                        && m.InpatientID == inpatientID
                        && assessListIDs.Contains(m.AssessListID))
                        .Select(m => new KeyValue { ID = m.AssessListID, Value = m.AssessValue }).ToListAsync();
        }

        public async Task<Dictionary<string, string>> GetDictAsync(int assessListID, List<string> inpatientIDs)
        {
            var result = await _medicalDbContext.PatientProfile.Where(m => inpatientIDs.Contains(m.InpatientID) && m.AssessListID == assessListID)
                 .Select(m => new ProfileNursingLevelView
                 {
                     InpatientID = m.InpatientID,
                     ProfileDate = m.ProfileDate,
                     ProfileTime = m.ProfileTime,
                     AssessValue = m.AssessValue
                 })
                .ToListAsync();

            var dict = result.GroupBy(m => new { m.InpatientID },
              (m, n) => new
              {
                  m.InpatientID,
                  n.OrderByDescending(o => o.ProfileDate).ThenByDescending(o => o.ProfileTime).First().AssessValue
              })
             .ToDictionary(m => m.InpatientID, m => m.AssessValue);
            return dict;
        }

        /// <summary>
        /// 获取病人一段时间内最近一次指定细项profile值
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="assessListID">评估细项</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns></returns>
        public async Task<string> GetPatientLastAssessListIDProfileByShift(string inpatientID, int assessListID, DateTime startDate, DateTime endDate)
        {
            var datas = await _medicalDbContext.PatientProfile.Where(m => m.InpatientID == inpatientID && m.AssessListID == assessListID
            && m.ProfileDate >= startDate.Date && m.ProfileDate <= endDate.Date).ToListAsync();

            return datas.Where(m => m.ProfileDate.Add(m.ProfileTime) >= startDate && m.ProfileDate.Add(m.ProfileTime) <= endDate)
                .OrderByDescending(m => m.ProfileDate).ThenByDescending(m => m.ProfileTime)
                .Select(m => m.AssessValue).FirstOrDefault();
        }

        /// <summary>
        /// 根据AssessListID获取病人Profile值
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="assessListID">评估项ID</param>
        /// <returns></returns>
        public async Task<string> GetInpatientProfileValueByAssessListID(string inpatientID, int assessListID)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.InpatientID == inpatientID && m.AssessListID == assessListID)
                .Select(m => m.AssessValue).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取Profile中的有效数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientProfileDataView>> GetPatientProfileByInpatientID(string inpatientID)
        {
            var datetime = DateTime.Now;
            return await _medicalDbContext.PatientProfile.Where(m => m.InpatientID == inpatientID
            && (m.DueDay == null || m.DueDay > datetime.Date))
                .Select(m => new PatientProfileDataView
                {
                    ID = m.ID,
                    AssessListID = m.AssessListID,
                    AssessValue = m.AssessValue,
                    ProfileDateTime = m.ProfileDate.Add(m.ProfileTime),
                    SerialNumber = m.SerialNumber,
                    ModifyDate = m.ModifyDate,
                    Unit = m.Unit,
                    NormalRange = m.NormalRange,
                    ModelName = m.ModelName
                }).Distinct().ToListAsync();
        }
        //获取历史住院Profile不包含本次住院Profilehistory
        public async Task<List<PatientProfileDataView>> GetHistoryPatientProfileByPatientID(string patientID, string unInpatientID)
        {
            var datetime = DateTime.Now;
            return await _medicalDbContext.PatientProfile.Where(m => m.PatientID == patientID && m.InpatientID != unInpatientID)
                .Select(m => new PatientProfileDataView
                {
                    AssessListID = m.AssessListID,
                    AssessValue = m.AssessValue,
                    ProfileDateTime = m.ProfileDate.Add(m.ProfileTime),
                    SerialNumber = m.SerialNumber,
                    ModifyDate = m.ModifyDate
                }).Distinct().ToListAsync();
        }
        public async Task<List<PatientProfileInfo>> GetPatientProfilesByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.InpatientID == inpatientID).ToListAsync();
        }
        /// <summary>
        /// 根据序列号获取PatientProfile
        /// </summary>
        /// <param name="serialNumber"></param>
        /// <returns></returns>
        public async Task<List<PatientProfileInfo>> GetPatientProfilesByserialNumber(string serialNumber)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.SerialNumber == serialNumber).ToListAsync();
        }

        public async Task<List<PatientProfileInfo>> GetProfileOwnDueDayByChartNoAsync(string chartNo, string hospitalID, int language)
        {
            return await (from a in _medicalDbContext.PatientProfile
                          join b in _medicalDbContext.AssessLists
                          on a.AssessListID equals b.ID
                          where a.HospitalID == hospitalID && a.ChartNo == chartNo
                          && b.Language == language && b.DischargeUnclear.HasValue && !b.DischargeUnclear.Value
                          select new PatientProfileInfo()
                          {
                              ID = a.ID,
                              ChartNo = a.ChartNo
                          }).ToListAsync();
        }
        /// <summary>
        /// 根据序列号获取相关Profile
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="modeName"></param>
        /// <returns></returns>
        public async Task<List<PatientProfileInfo>> GetProfilesBymodeName(string inpatientID,string modeName)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.InpatientID == inpatientID && m.ModelName.Contains(modeName)).ToListAsync();
        }
        /// <summary>
        /// 获取病人特定的Profile数据
        /// </summary>
        /// <param name="inpatientIDs">患者主键集合</param>
        /// <param name="profiles">特定profiles集合</param>
        /// <returns></returns>
        public async Task<List<PatientProfileInfo>> GetProfilesByInpatinets(List<string> inpatientIDs, int[] profiles)
        {
            return await _medicalDbContext.PatientProfile.Where(m => inpatientIDs.Contains(m.InpatientID) && profiles.Contains(m.AssessListID))
                .Select(m => new PatientProfileInfo
                {
                    InpatientID = m.InpatientID,
                    AssessListID = m.AssessListID,
                    AssessValue = m.AssessValue,
                }).ToListAsync();
        }
        #region medical服务未引用

        public async Task<List<PatientProfileInfo>> GetByChartNoOrder(string hospitalID, string chartNo)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.ChartNo == chartNo)
                .ToListAsync();
        }

        /// <summary>
        /// 取得Profile资料
        /// </summary>
        /// <param name="inPatientId">住院序号</param>
        /// <param name="ModelName">系统名</param>
        /// <param name="serialNumber">序号</param>
        /// <returns></returns>
        public async Task<List<PatientProfileInfo>> GetAsync(string inpatientID, string modelName, string serialNumber)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.InpatientID == inpatientID && m.ModelName == modelName && m.SerialNumber == serialNumber).ToListAsync();
        }

        public async Task<List<PatientProfileInfo>> GetAsync(string hospitalID, List<string> inpatientIDs)
        {
            var PatientProfileList = new List<PatientProfileInfo>();
            foreach (var item in inpatientIDs)
            {
                var tempList = await _medicalDbContext.PatientProfile.Where(m => m.HospitalID == hospitalID
               && m.InpatientID == item).ToListAsync();
                PatientProfileList = PatientProfileList.Union(tempList).ToList();
            }
            return PatientProfileList;
        }

        /// <summary>
        /// 获取病人Profile数据
        /// </summary>
        /// <param name="hospitalID">医疗院所序号</param>
        /// <param name="patientID">病人序号</param>
        /// <param name="assessListID">评估序号</param>
        /// <returns></returns>
        public async Task<List<PatientProfileInfo>> GetPatientProfileDataByAssessListID(string hospitalID, string inpatientID, int assessListID)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.HospitalID == hospitalID
            && m.InpatientID == inpatientID
            && m.AssessListID == assessListID).ToListAsync();
        }

        public async Task<PatientProfileInfo> GetBySerialNumberAsyc(string hospitalID, string serialNumber)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.HospitalID == hospitalID && m.SerialNumber == serialNumber).SingleOrDefaultAsync();
        }

        public async Task<List<PatientProfileInfo>> GetPatientProfilesByInpatientID(string hospitalID, string inpatientID)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.HospitalID == hospitalID && m.InpatientID == inpatientID).ToListAsync();
        }

        public async Task<List<PatientProfileInfo>> GetPatientProfilesByPatientIDAsync(string hospitalID, string patientID)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.HospitalID == hospitalID && m.PatientID == patientID).ToListAsync();
        }

        //确认AssessListId 是否存在
        public async Task<List<int>> GetPatientAssessListIDsByCaseNumber(string caseNumber, int assessListID)
        {
            return await _medicalDbContext.PatientProfile.AsNoTracking().Where(m => m.CaseNumber == caseNumber
                 && m.AssessListID == assessListID).Select(m => m.AssessListID).ToListAsync();
        }

        public async Task<List<KeyValueString>> GetNecessaryAssessListAsync(string hospitalID, int stationID, int language)
        {
            var data = await (from m in _medicalDbContext.PatientProfile
                              join n in _medicalDbContext.RecordsLists
                              on m.AssessListID equals n.NecessaryAssessListID
                              join i in _medicalDbContext.InpatientDatas
                              on m.InpatientID equals i.ID
                              where m.HospitalID == hospitalID
                              && n.DeleteFlag != "*"
                              && n.Language == language
                              && n.NecessaryAssessListID != null
                              && n.RecordType == "risk"
                              && i.StationID == stationID
                              && n.HospitalID == hospitalID
                              && i.HospitalID == hospitalID
                              select new KeyValueString
                              {
                                  Key = m.InpatientID,
                                  Value = n.ID.ToString()
                              }).Distinct().ToListAsync();
            return data;
        }

        public async Task<Dictionary<int, DateTime>> GetUnDueProfileForScore(string inpatientID)
        {
            var result = await _medicalDbContext.PatientProfile.AsNoTracking().Where(m => m.InpatientID == inpatientID && (m.DueDay == null || m.DueDay > DateTime.Now.Date))
                .Select(m => new ProfileAssessListView
                {
                    AssessListID = m.AssessListID,
                    ModifyDate = m.ModifyDate
                }).ToListAsync();

            var dict = result.GroupBy(m => new { m.AssessListID },
              (m, n) => new
              {
                  m.AssessListID,
                  n.OrderByDescending(o => o.ModifyDate).FirstOrDefault().ModifyDate
              })
             .ToDictionary(m => m.AssessListID, m => m.ModifyDate);
            return dict;
        }

        public async Task<KeyValue> GetByCaseNumber(string caseNumber, int assessListID)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.AssessListID == assessListID
                && m.CaseNumber == caseNumber)
                .OrderByDescending(m => m.ProfileDate).ThenByDescending(m => m.ProfileTime)
               .Select(m => new KeyValue
               {
                   ID = m.AssessListID,
                   Value = m.AssessValue
               }).FirstOrDefaultAsync();
        }

        public async Task<ProfileDetail> GetProfilesDetailByAssessListID(string hospitalID, string inpatientID, int assesslistID)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.HospitalID == hospitalID && m.InpatientID == inpatientID && m.AssessListID == assesslistID)
                .OrderByDescending(m => m.ProfileDate).ThenBy(m => m.ProfileTime)
                .Select(m => new ProfileDetail
                {
                    ProfileDate = m.ProfileDate,
                    ProfileTime = m.ProfileTime,
                    AssessListID = m.AssessListID,
                    AssessValue = m.AssessValue,
                    Unit = m.Unit,
                    NormalRange = m.NormalRange,
                    ModelName = m.ModelName
                }).FirstOrDefaultAsync();
        }

        public async Task<List<PatientProfileIDView>> GetPatientProfileIDsByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientProfile.Where(m => m.InpatientID == inpatientID)
                .Select(m => new PatientProfileIDView { PatientProfileID = m.ID, ChartNo = m.ChartNo }).ToListAsync();
        }

        #endregion medical服务未引用
    }
}