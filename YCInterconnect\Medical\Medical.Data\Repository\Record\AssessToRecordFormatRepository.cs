﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class AssessToRecordFormatRepository : IAssessToRecordFormatRepository
    {
        private readonly MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly GetCacheService _getCacheService;

        public AssessToRecordFormatRepository(MedicalDbContext medicalDbContext, IMemoryCache memoryCache, GetCacheService getCacheService)
        {
            _medicalDbContext = medicalDbContext;
            _memoryCache = memoryCache;
            _getCacheService = getCacheService;
        }

        public async Task<List<AssessToRecordFormatInfo>> GetAsync()
        {
            return await GetCacheAsync() as List<AssessToRecordFormatInfo>;
        }

        public async Task<bool> VerificationAssessToRecordFormat(int AssessListID, int RecordFormatID)
        {
            List<AssessToRecordFormatInfo> list = await _medicalDbContext.AssessToRecordFormats.Where(m => m.AssessListID == AssessListID && m.RecordFormatID == RecordFormatID).ToListAsync();
            if (list.Count > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        public async Task<List<AssessToRecordFormatInfo>> GetByRecordFormatAsync(int RecordFormatID)
        {
            return await _medicalDbContext.AssessToRecordFormats.Where(m => m.RecordFormatID == RecordFormatID).ToListAsync();
        }

        public async Task<List<AssessToRecordFormatInfo>> GetByRecordFormatAsync(List<int> recordFormatIDs)
        {
            var data = await GetAsync();

            return data.Where(m => recordFormatIDs.Contains(m.RecordFormatID)).ToList();
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<AssessToRecordFormatInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            return await _medicalDbContext.AssessToRecordFormats.Where(m => m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.AssessToRecordFormat.ToString();
        }
    }
}