﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Interconnect.ViewModels
{
    /// <summary>
    /// 新生儿数据
    /// </summary>
    public class NewBornView
    {
        /// <summary>
        /// 新生儿MRN,chartNO
        /// </summary>
        public string ChartNO { get; set; }
        /// <summary>
        /// 新生儿CaseNumber
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 母亲MRN，ChartNO
        /// </summary>
        public string MotherChartNO { get; set; }
        /// <summary>
        /// 母亲CaseNumber
        /// </summary>
        public string MotherCaseNumber{ get; set; }
        /// <summary>
        /// 新生儿名字
        /// </summary>
        public string NewbornName { get; set; }
        /// <summary>
        /// 新生儿性别1男，2女
        /// </summary>
        public string Gender{ get; set; }
        /// <summary>
        /// 新生儿出生日期
        /// </summary>
        public string Birthday { get; set; }
        /// <summary>
        /// 新生儿身高
        /// </summary>
        public decimal? Height { get; set; }
        /// <summary>
        /// 新生儿体重
        /// </summary>
        public decimal? Weight { get; set; }
        /// <summary>
        /// 母亲妊娠周
        /// </summary>
        public int GestationalWeek { get; set; }
        /// <summary>
        /// 母亲天数
        /// </summary>
        public int GestationalDay { get; set; }
        /// <summary>
        /// 是否删除
        /// </summary>
        public string DeleteFlag { get; set; }
        /// <summary>
        /// 新生儿出生日期
        /// </summary>
        public DateTime? NewbornBirthday { get; set; }
    }
}
