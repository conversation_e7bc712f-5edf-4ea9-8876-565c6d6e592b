﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    /// <summary>
    /// 存储过程调用参数的表字典
    /// </summary>
    [Serializable]
    [Table("ExecTableDict")]
    public class ExecTableDictInfo
    {
        [Key]
        [Column("TableName")]
        public string TableName { get; set; }
        /// <summary>
        /// 表说明
        /// </summary>
        [Column("TableMemos")]
        public string TableMemos { get; set; }

        //同步顺序
        [Column("Sort")]
        public int Sort { get; set; }
    }
}
