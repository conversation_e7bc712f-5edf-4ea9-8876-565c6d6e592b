﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("NursingRecord_NormalNew")]
    public class NRNormalNewInfo : ModifyReturnInfo
    {
        /// <summary>
        ///	同NursingReocrd_Main的SN	
        ///</summary>
        [Key]
        [Column("SN")]
        public int SN { get; set; }
        /// <summary>
        ///	就诊序号	
        ///</summary>
        public string CureNo { get; set; }
        /// <summary>
        ///	病区编号	
        ///</summary>
        public string WardCode { get; set; }
        /// <summary>
        ///	病区名称	
        ///</summary>
        public string WardName { get; set; }
        /// <summary>
        ///	住院号 	
        ///</summary>
        public string AdmitNo { get; set; }
        /// <summary>
        ///	病人姓名	
        ///</summary>
        public string Name { get; set; }
        /// <summary>
        ///	床位号	
        ///</summary>
        public string BedNo { get; set; }
        /// <summary>
        ///	护理时间	
        ///</summary>
        public DateTime? ReportDate { get; set; }
        /// <summary>
        ///	护理级别：I,II,III,IV	
        ///</summary>
        public string NursingLevel { get; set; }
        /// <summary>
        ///	心电监护	
        ///</summary>
        public string ECG { get; set; }
        /// <summary>
        ///	意识	
        ///</summary>
        public string Sense { get; set; }
        /// <summary>
        ///	1口腔,2腋温,3肛温,4腔温	
        ///</summary>
        public int? TemperatureType { get; set; }
        /// <summary>
        ///	体温	
        ///</summary>
        public float? T { get; set; }
        /// <summary>
        ///	脉搏	
        ///</summary>
        public float? P { get; set; }
        /// <summary>
        ///	呼吸	
        ///</summary>
        public float? Breath { get; set; }
        /// <summary>
        ///	心率，如：齐	
        ///</summary>
        public string Rhythem { get; set; }
        /// <summary>
        ///	血氧饱和度	
        ///</summary>
        public float? Spo2 { get; set; }
        /// <summary>
        ///	舒张压	
        ///</summary>
        public float? BPLower { get; set; }
        /// <summary>
        ///	收缩压	
        ///</summary>
        public float? BPUpper { get; set; }
        /// <summary>
        ///	中心静脉压	
        ///</summary>
        public float? CVP { get; set; }
        /// <summary>
        ///	疼痛评分	
        ///</summary>
        public int? PainScore { get; set; }
        /// <summary>
        ///	护理级别	
        ///</summary>
        public string Nursing { get; set; }
        /// <summary>
        ///	伤口观察	
        ///</summary>
        public string Observing { get; set; }
        /// <summary>
        ///	导管护理	
        ///</summary>
        public string PipesContent { get; set; }
        /// <summary>
        ///	护理措施	
        ///</summary>
        public string Content { get; set; }
        /// <summary>
        ///	输入时间	
        ///</summary>
        public DateTime? InputTime { get; set; }
        /// <summary>
        ///	输入护士工号	
        ///</summary>
        public string  InputerCode { get; set; }
        /// <summary>
        ///	输入护士姓名	
        ///</summary>
        public string InputerName { get; set; }
        /// <summary>
        ///	0 有效，-1 无效	
        ///</summary>
        public int? Status { get; set; }
        /// <summary>
        ///	膳食医嘱，如：普食	
        ///</summary>
        public string FoodOrder { get; set; }
        /// <summary>
        ///	进食量	
        ///</summary>
        public float? FoodOrderEnter { get; set; }
        /// <summary>
        ///	物理降温	
        ///</summary>
        public float? TemperatureReduction { get; set; }
        /// <summary>
        ///	气道护理	
        ///</summary>
        public string AirwayNursing { get; set; }
        /// <summary>
        ///	异常检查及检验	
        ///</summary>
        public string AbnormalTest { get; set; }
        /// <summary>
        ///	手术及其他特殊治疗	
        ///</summary>
        public string Surgery { get; set; }
        /// <summary>
        ///	交班护士姓名	
        ///</summary>
        public string JBNurseName { get; set; }
        /// <summary>
        ///	专科观察	
        ///</summary>
        public string SpecialtyObservation { get; set; }
        /// <summary>
        ///	其他基本信息	
        ///</summary>
        public string OtherBaseInfo { get; set; }
        /// <summary>
        ///	术后首次下床活动时间，如：201
        ///</summary>
        public string ActiveHourAfterSurgery { get; set; }

        /// <summary>
        /// 病人排程编号
        /// </summary>
        public string PatientScheduleMainID { get; set; }
    }
}