﻿using System;
 
namespace Interconnect.Models
{
    public class ModifyInfo
    {
        /// <summary>
        /// 修改人员
        /// </summary>     
        public string ModifyPersonID { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>      
        public DateTime? ModifyDate { get; set; }
        /// <summary>
        /// 删除标志 *表示删除
        /// </summary>       
        public string DeleteFlag { get; set; }
        /// <summary>
        /// 抽档日期
        /// </summary>
        public DateTime? DataPumpDate { get; set; }
        /// <summary>
        /// 抽档标志   标志 *表示已经抽档
        /// </summary>
        public string DataPumpFlag { get; set; }

        /// <summary>
        /// 同步次数
        /// </summary>
        public int? Counts { get; set; }
    }
}
