﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientCINVCareDetailRepository : IPatientCINVCareDetailRepository
    {
        private MedicalDbContext _medicalDbContext;

        public PatientCINVCareDetailRepository(
              MedicalDbContext medicalDbContext)
        {
            _medicalDbContext = medicalDbContext;
        }

        public async Task<List<PatientCINVCareDetailInfo>> GetByCareMainID(string mainID)
        {
            return await _medicalDbContext.PatientCINVCareDetailInfos.Where(m => m.PatientCINVCareMainID == mainID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<AssessContentValue>> GetAssessValueByCareMainID(string mainID)
        {
            return await _medicalDbContext.PatientCINVCareDetailInfos
                .Where(m => m.PatientCINVCareMainID == mainID && m.DeleteFlag != "*")
                .Select(m => new AssessContentValue
                {
                    AssessListID = m.AssessListID,
                    AssessValue = m.AssessValue
                })
                .ToListAsync();
        }

        public async Task<List<PatientCINVCareDetailInfo>> GetAssessValueByCareMainIDs(List<string> mainIDs, List<int> assessListIDs)
        {
            return await _medicalDbContext.PatientCINVCareDetailInfos
                .Where(m => mainIDs.Contains(m.PatientCINVCareMainID) && assessListIDs.Contains(m.AssessListID) && m.DeleteFlag != "*")
                .Select(m => new PatientCINVCareDetailInfo
                {
                    InpatientID = m.InpatientID,
                    PatientCINVCareMainID = m.PatientCINVCareMainID,
                    AssessListID = m.AssessListID,
                    AssessValue = m.AssessValue
                })
                .ToListAsync();
        }
    }
}