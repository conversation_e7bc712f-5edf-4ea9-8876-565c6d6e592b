﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientOrdersMergeRepository : IPatientOrdersMergeRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientOrdersMergeRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<List<string>> GetMergeAsync(string printId)
        {
            var schedules = await _medicalDbContext.PatientOrdersMergeInfos.Where(t => t.OrdersPrintID == printId && t.DeleteFlag != "*").ToListAsync();
            return schedules.Select(t => t.PatientMedicineScheduleID).ToList();
        }

        public async Task<string> MergeOrdersAsync(string[] ids, string userId)
        {
            string printId = "M" + BaseInfo.Get16Id();
            foreach (string id in ids)
            {
                var model = new PatientOrdersMergeInfo
                {
                    PatientMedicineScheduleID = id
                };
                model.OrdersPrintID = printId;
                model.Add(userId);
                _medicalDbContext.PatientOrdersMergeInfos.Add(model);
            }
            if (await _medicalDbContext.SaveChangesAsync() > 0)
            {
                return printId;
            }
            return null;
        }
    }
}