﻿/**
 * 新增GetScoreValueByRecordListIDs方法， 用于获取相同recordListID最后一次记录  2022-04-26 苏军志
 */
using Arch.EntityFrameworkCore.UnitOfWork;
using Interconnect.ViewModels;
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.Data;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientScoreMainRepository : IPatientScoreMainRepository
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IStationShiftRepository _stationShiftRepository;
        private MedicalDbContext _medicalDbContext = null;
        private IAssessScoreRangeRepository _assessScoreRangeRepository;
        private IRecordsFormatRepository _recordsFormatRepository;
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();

        public PatientScoreMainRepository(
              MedicalDbContext db
            , IUnitOfWork unitOfWork
            , IStationShiftRepository stationShiftRepository
            , IAssessScoreRangeRepository assessScoreRangeRepository
            , IRecordsFormatRepository recordsFormatRepository
            )
        {
            _medicalDbContext = db;
            _unitOfWork = unitOfWork;
            _stationShiftRepository = stationShiftRepository;
            _assessScoreRangeRepository = assessScoreRangeRepository;
            _recordsFormatRepository = recordsFormatRepository;
        }

        public async Task<List<PatientScoreMainInfo>> GetPatientScoreList(string inpatientID)
        {
            return await _medicalDbContext.PatientScoreMainInfos.Where(m =>
            m.InpatientID == inpatientID &&
            m.DeleteFlag != "*").ToListAsync();
        }



        public async Task<List<PatientScoreMainInfo>> GetPatientScoreByRecordID(string inpatientID, int recordListID)
        {
            return await _medicalDbContext.PatientScoreMainInfos.Where(m =>
            m.InpatientID == inpatientID &&
            m.RecordListID == recordListID &&
            m.DeleteFlag != "*").OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).ToListAsync();
        }

        public async Task<List<PatientScoreMainInfo>> GetPatientScoreByInpatientIDAndSourceIDView(string inpatientID, string sourceID)
        {
            var query = await _medicalDbContext.PatientScoreMainInfos
                .Where(m => m.InpatientID == inpatientID && m.SourceID == sourceID && m.DeleteFlag != "*")
                .Select(m => new PatientScoreMainInfo
                {
                    InpatientID = inpatientID,
                    ScorePoint = m.ScorePoint,
                    AddDate = m.AddDate
                }
                ).ToListAsync();

            return query;
        }

        public async Task<List<PatientScoreMainInfo>> GetPatientScoreByInpatientIDAndSourceID(string inpatientID, string sourceID)
        {
            var query = await _medicalDbContext.PatientScoreMainInfos
                .Where(m => m.InpatientID == inpatientID && m.SourceID == sourceID && m.DeleteFlag != "*")
                .ToListAsync();
            return query;
        }

        public async Task<List<PatientScoreMainInfo>> GetPatientScoreBySourceID(string sourceID, int? recordListID = null)
        {
            var query = await _medicalDbContext.PatientScoreMainInfos.Where(m => m.SourceID == sourceID && m.DeleteFlag != "*").ToListAsync();
            if (recordListID != null)
            {
                query = query.Where(t => t.RecordListID == recordListID.Value).ToList();
            }
            query = query.OrderBy(t => t.AddDate).ToList();
            return query;
        }

        public async Task<PatientScoreMainInfo> GetPatientScoreMainByID(string patientScoreMainID)
        {
            return await _medicalDbContext.PatientScoreMainInfos.Where(m =>
            m.PatientScoreMainID == patientScoreMainID &&
            m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取病人最近一次到期的风险评估
        /// </summary>
        /// <param name="inpatientID">在院病人本次在院的唯一ID</param>
        /// <param name="recordListID">风险表类型的唯一标识</param>
        /// <returns></returns>
        public async Task<string> getLastExpireScoreMainIDByInpatientIDAndRecordListID(string inpatientID, int recordListID)
        {
            var obj = await _medicalDbContext.PatientScoreMainInfos.AsNoTracking().Where(m =>
               m.InpatientID == inpatientID && m.RecordListID == recordListID && m.DeleteFlag != "*")
                .OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime)
                .Select(m => new { m.PatientScoreMainID, m.DueDay }).LastOrDefaultAsync();

            if (obj == null)
            {
                return null;
            }
            if (obj.DueDay <= DateTime.Now.Date)
            {
                return obj.PatientScoreMainID;
            }
            return null;
        }


        public async Task<List<PatientScoreMainInfo>> GetSendEMRData(DateTime startDate, DateTime endDate)
        {
            return await _medicalDbContext.PatientScoreMainInfos.Where(m =>
            m.ModifyDate >= startDate
            && m.ModifyDate <= endDate
            && m.DataPumpFlag != "*"
            && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<PatientScoreMainInfo> GetPatientScore(string inpatientID, string sourceID, int recordListID)
        {
            return await _medicalDbContext.PatientScoreMainInfos.Where(m => m.InpatientID == inpatientID
                    && m.RecordListID == recordListID && m.SourceID == sourceID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取每个ID最后一条数据
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="inpatientIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientScoreMainInfo>> GetAsync(int stationID, List<string> inpatientIDs)
        {
            var patientScoreMainList = new List<PatientScoreMainInfo>();
            foreach (var item in inpatientIDs)
            {
                var tempList = await _medicalDbContext.PatientScoreMainInfos.Where(m => m.StationID == stationID
              && m.InpatientID == item).ToListAsync();
                patientScoreMainList = patientScoreMainList.Union(tempList).ToList();
            }
            var list = patientScoreMainList.OrderByDescending(x => x.AddDate).GroupBy(m => new { m.InpatientID, m.RecordListID }).Select(m => m.First()).ToList();
            return list;
        }

        public async Task<List<RiskAssessResult>> GetLastPatientRisk(string inpatientID, string hospitalID, int language)
        {
            var patientScoreMainInfos = await _medicalDbContext.PatientScoreMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").Select
                (m => new PatientScoreMainInfo
                {
                    RecordListID = m.RecordListID,
                    ScorePoint = m.ScorePoint,
                    InpatientID = m.InpatientID,
                    AddDate = m.AddDate,
                    ScoreRange = m.ScoreRange
                }).OrderByDescending(m => m.AddDate).ToListAsync();
            patientScoreMainInfos = patientScoreMainInfos.GroupBy(m => new { m.InpatientID, m.RecordListID }).Select(m => m.First()).ToList();
            var assessScoreRangeInfos = await _assessScoreRangeRepository.GetAsync();
            var data = patientScoreMainInfos.Join(assessScoreRangeInfos
                            , m => new { m.RecordListID, m.ScoreRange }, n => new { n.RecordListID, ScoreRange = n.AssessScoreRangeID }, (u, d) => new { u, d })
                            .Where(m => m.u.InpatientID == inpatientID && m.d.Language == language)
                            .Select(m => new RiskAssessResult
                            {
                                AddDate = m.u.AddDate,
                                AssessScoreRangeID = m.u.ScoreRange,
                                ScoreRangeContent = m.d.RangeContent,
                                RecordID = m.d.RecordListID,
                                Point = m.u.ScorePoint,
                                InpatientID = m.u.InpatientID,
                            }
                            ).ToList();
            return data;
        }

        public async Task<List<PatientScoreMainInfo>> GetAssessmentRisk(string inpatientid, string sourceID)
        {
            return await _medicalDbContext.PatientScoreMainInfos.Where(m => m.InpatientID == inpatientid &
                   (m.SourceType == "AdmissionAssess" || m.SourceType == "PhysicalAssessment")
                 && m.SourceID == sourceID
                 && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<ScoreDocumentView>> GetPatientScoreBySourceIDs(string[] sourceIDs)
        {
            return await _medicalDbContext.PatientScoreMainInfos.Where(m =>
                 sourceIDs.Contains(m.PatientScoreMainID) && m.DeleteFlag != "*")
                .Select(m => new ScoreDocumentView
                {
                    PatientScoreMainID = m.PatientScoreMainID,
                    ScorePoint = m.ScorePoint
                })
                .ToListAsync();
        }

        public async Task<List<RiskAssessResult>> GetScoreBySourceID(string inpatientID, string SourceID, int language, string hospitalID)
        {
            var datas = await (from a in _medicalDbContext.PatientScoreMainInfos
                               join b in _medicalDbContext.RecordsLists on new { ID = a.RecordListID, Language = language, HospitalID = hospitalID } equals new { b.ID, b.Language, b.HospitalID }
                               join c in _medicalDbContext.AssessScoreRangeInfos on new { ID = a.ScoreRange, Language = language, HospitalID = hospitalID } equals new { ID = c.AssessScoreRangeID, c.Language, c.HospitalID }
                               where a.InpatientID == inpatientID && a.SourceID == SourceID && a.DeleteFlag != "*" && b.DeleteFlag != "*" && c.DeleteFlag != "*"
                               select new RiskAssessResult
                               {
                                   RecordName = b.RecordName,
                                   Point = a.ScorePoint,
                                   ScoreRangeContent = c.RangeContent
                               }).ToListAsync();
            return datas;
        }

        public async Task<int?> GetPatientLastScoreRiskID(string inpatientID, int recordListID)
        {
            return await _medicalDbContext.PatientScoreMainInfos.Where(m => m.InpatientID == inpatientID
                   && m.RecordListID == recordListID && m.DeleteFlag != "*")
                   .OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime)
                   .Select(m => m.AssessListID).FirstOrDefaultAsync();
        }

        public async Task<List<KeyValue>> GetNotSourceIDLastPatientRisk(string inpatientID, string sourceID)
        {
            var list = await _medicalDbContext.PatientScoreMainInfos
                            .Where(m => m.InpatientID == inpatientID && m.SourceID != sourceID)
                            .OrderByDescending(m => m.AddDate).ToListAsync();

            var data = list.GroupBy(m => new { m.InpatientID, m.RecordListID }).Select(m => m.First())
                            .Select(m => new KeyValue
                            {
                                ID = m.RecordListID,
                                Value = m.PatientScoreMainID,
                            }).ToList();
            return data;
        }

        public async Task<KeyValue> GetNotSourceIDLastPatientRisk(string inpatientID, int recordListID, string sourceID)
        {
            var data = await _medicalDbContext.PatientScoreMainInfos
                            .Where(m => m.InpatientID == inpatientID && m.RecordListID == recordListID && m.SourceID != sourceID)
                            .OrderByDescending(m => m.AddDate).ToListAsync();

            return data.GroupBy(m => new { m.InpatientID, m.RecordListID }).Select(m => m.First())
                            .Select(m => new KeyValue
                            {
                                ID = m.RecordListID,
                                Value = m.PatientScoreMainID,
                            }).FirstOrDefault();
        }
        public async Task<List<PatientScoreMainInfo>> GetLastPatientRisk(string inpatientID)
        {
            var list = await _medicalDbContext.PatientScoreMainInfos
                            .Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*")
                            .OrderByDescending(m => m.AddDate).ToListAsync();

            return list.GroupBy(m => new { m.InpatientID, m.RecordListID }).Select(m => m.First()).ToList();
        }

        public async Task<List<PatientRiskAssess>> GetLastRisk(string inpatientID, int language)
        {
            var datas = await _medicalDbContext.PatientScoreMainInfos.
                Where(m => m.DeleteFlag != "*" && m.InpatientID == inpatientID).OrderByDescending(m => m.AddDate).ToListAsync();
            return datas.GroupBy(m => (m.InpatientID, m.RecordListID)).Select(m => m.First())
                                            .Select(m => new PatientRiskAssess
                                            {
                                                InpatientID = m.InpatientID,
                                                PatientScoreMainID = m.PatientScoreMainID,
                                                RecordListID = m.RecordListID,
                                                Point = m.ScorePoint,
                                                AssessScoreRangeID = m.ScoreRange,
                                                AddDate = m.AddDate,
                                                DueDay = m.DueDay,
                                                Confirm = true,
                                                ShiftDate = m.ShiftDate,
                                                AssessDateTime = m.AssessDate.Add(m.AssessTime),
                                                SourceID = m.SourceID,
                                                SourceType = m.SourceType,
                                                ModifyDate = m.ModifyDate
                                            }).ToList();

        }

        public async Task<List<PatientRiskAssess>> GetPatientRiskListByInpatientIds(List<string> inpatientIDs, string hospitalID, int language)
        {
            var result = await (from m in _medicalDbContext.PatientScoreMainInfos
                                join n in _medicalDbContext.RecordsLists
                                on new { ID = m.RecordListID, RecordType = "Risk", Language = language, HospitalID = hospitalID }
                                equals new { n.ID, n.RecordType, n.Language, n.HospitalID }
                                where inpatientIDs.Contains(m.InpatientID) && (m.DueDay == null || m.DueDay >= DateTime.Now.Date)
                                select new PatientRiskAssess
                                {
                                    InpatientID = m.InpatientID,
                                    PatientScoreMainID = m.PatientScoreMainID,
                                    RecordListID = m.RecordListID,
                                    Point = m.ScorePoint,
                                    AssessScoreRangeID = m.ScoreRange,
                                    AddDate = m.AddDate,
                                    DueDay = m.DueDay,
                                    Confirm = true,
                                    ShiftDate = m.ShiftDate,
                                    AssessDateTime = m.AssessDate.Add(m.AssessTime),
                                    SourceID = m.SourceID,
                                    SourceType = m.SourceType,
                                    RecordName = n.RecordName
                                }).ToListAsync();
            return result;
        }

        public async Task<List<PatientRiskAssess>> GetPatientRiskByInpatientIdAndSourceID(string inpatientID, string sourceID, int language, string hospitalID)
        {

            var result = await (from m in _medicalDbContext.PatientScoreMainInfos
                                join n in _medicalDbContext.RecordsLists
                                on new { ID = m.RecordListID, RecordType = "Risk", Language = language, HospitalID = hospitalID }
                                equals new { n.ID, n.RecordType, n.Language, n.HospitalID }
                                where m.InpatientID == inpatientID && m.SourceID == sourceID
                                select new PatientRiskAssess
                                {
                                    InpatientID = m.InpatientID,
                                    PatientScoreMainID = m.PatientScoreMainID,
                                    RecordListID = m.RecordListID,
                                    Point = m.ScorePoint,
                                    AssessScoreRangeID = m.ScoreRange,
                                    AddDate = m.AddDate,
                                    DueDay = m.DueDay,
                                    Confirm = true,
                                    ShiftDate = m.ShiftDate,
                                    AssessDateTime = m.AssessDate.Add(m.AssessTime),
                                    SourceID = m.SourceID,
                                    SourceType = m.SourceType,
                                    RecordName = n.RecordName
                                }).ToListAsync();
            return result;
        }

        public async Task<List<PatientRiskAssess>> GetPatientsAllLastRiskScoreMain(List<string> inpatientIDs, string hospitalID, int language)
        {
            var result = await (from m in _medicalDbContext.PatientScoreMainInfos
                                join n in _medicalDbContext.RecordsLists on
                                   new { m.RecordListID, Language = language, HospitalID = hospitalID } equals new { RecordListID = n.ID, n.Language, n.HospitalID }
                                where inpatientIDs.Contains(m.InpatientID) && n.RecordType == "Risk" && m.DeleteFlag != "*"
                                select new PatientRiskAssess
                                {
                                    InpatientID = m.InpatientID,
                                    PatientScoreMainID = m.PatientScoreMainID,
                                    RecordListID = m.RecordListID,
                                    AddDate = m.AddDate,
                                    SourceID = m.SourceID,
                                    DueDay = m.DueDay,
                                    DueShift = m.DueShift,
                                    Point = m.ScorePoint,
                                    SourceType = m.SourceType,
                                    AssessScoreRangeID = m.ScoreRange,
                                    AssessDateTime = m.AssessDate.Add(m.AssessTime),
                                    ModifyDate = m.ModifyDate,
                                    ShowPointFlag = n.ShowPointFlag ?? true,
                                    ShowPriority = n.ShowPriority,
                                    RecordCategoryName = n.RecordCategoryCode,
                                    ShiftDate = m.ShiftDate
                                }).ToListAsync();
            result = result.OrderByDescending(m => m.AssessDateTime).ThenByDescending(m => m.DueDay).ToList();

            result = result.GroupBy(m => new { m.InpatientID, m.RecordListID }).Select(m => m.First())
                .Select(m => new PatientRiskAssess
                {
                    InpatientID = m.InpatientID,
                    PatientScoreMainID = m.PatientScoreMainID,
                    RecordListID = m.RecordListID,
                    AddDate = m.AddDate,
                    SourceID = m.SourceID,
                    DueDay = m.DueDay,
                    DueShift = m.DueShift,
                    Point = m.Point,
                    SourceType = m.SourceType,
                    AssessScoreRangeID = m.AssessScoreRangeID,
                    AssessDateTime = m.AssessDateTime,
                    Confirm = true,
                    ModifyDate = m.ModifyDate,
                    ShowPointFlag = m.ShowPointFlag,
                    ShowPriority = m.ShowPriority,
                    RecordCategoryName = m.RecordCategoryName,
                    ShiftDate = m.ShiftDate
                }).ToList();
            return result;
        }

        public async Task<List<PatientDueScoreView>> GetLastRiskListByStationID(int stationID, DateTime shiftDate, string hospitalID, int language)
        {
            DateTime? dischargeDate = DateTime.Now;
            dischargeDate = null;
            var resultCommon = await (from a in _medicalDbContext.InpatientDatas
                                      join b in _medicalDbContext.PatientScoreMainInfos
                                      on new { a.StationID, a.ID, a.DischargeDate } equals new { StationID = stationID, ID = b.InpatientID, DischargeDate = dischargeDate }
                                      join c in _medicalDbContext.RecordsLists on
                                      new { b.RecordListID, HospitalID = hospitalID, Language = language } equals new { RecordListID = c.ID, c.HospitalID, c.Language }
                                      join d in _medicalDbContext.DeptmentToRecordListInfos on
                                      new { DepartmentListID = 999999, b.RecordListID, HospitalID = hospitalID } equals new { d.DepartmentListID, d.RecordListID, d.HospitalID }
                                      where a.StationID == stationID && a.DeleteFlag != "*"
                                      && b.DeleteFlag != "*"
                                      && c.DeleteFlag != "*" && c.HospitalID == hospitalID && c.Language == language && c.RecordType == "Risk"
                                      && d.DeleteFlag != "*"
                                      select new PatientDueScoreView
                                      {
                                          InpatientID = a.ID,
                                          RecordListID = b.RecordListID,
                                          RecordListName = c.RecordName,
                                          DueDay = b.DueDay,
                                          AddDate = b.AddDate
                                      }).ToListAsync();
            resultCommon = resultCommon.OrderByDescending(m => m.AddDate)
                          .GroupBy(m => new { m.InpatientID, m.RecordListID })
                          .Select(m => m.First())
                          .Where(m => m.DueDay <= shiftDate).ToList();

            var resultParticular = await (from a in _medicalDbContext.InpatientDatas
                                          join b in _medicalDbContext.PatientScoreMainInfos
                                          on new { a.StationID, a.ID, a.DischargeDate } equals new { StationID = stationID, ID = b.InpatientID, DischargeDate = dischargeDate }
                                          join c in _medicalDbContext.RecordsLists on
                                          new { b.RecordListID, HospitalID = hospitalID, Language = language } equals new { RecordListID = c.ID, c.HospitalID, c.Language }
                                          join d in _medicalDbContext.DeptmentToRecordListInfos on
                                          new { a.DepartmentListID, b.RecordListID, HospitalID = hospitalID } equals new { d.DepartmentListID, d.RecordListID, d.HospitalID }
                                          where a.StationID == stationID && a.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(a.InHospitalStatus ?? -1)
                                          && b.DeleteFlag != "*"
                                          && c.DeleteFlag != "*" && c.HospitalID == hospitalID && c.Language == language && c.RecordType == "Risk"
                                          && d.DeleteFlag != "*"
                                          select new PatientDueScoreView
                                          {
                                              InpatientID = a.ID,
                                              RecordListID = b.RecordListID,
                                              RecordListName = c.RecordName,
                                              DueDay = b.DueDay,
                                              AddDate = b.AddDate
                                          }).ToListAsync();

            resultParticular = resultParticular.OrderByDescending(m => m.AddDate)
                                              .GroupBy(m => new { m.InpatientID, m.RecordListID })
                                              .Select(m => m.First())
                                              .Where(m => m.DueDay <= shiftDate).ToList();

            var result = resultCommon.Union(resultParticular).ToList();
            return result;
        }

        public async Task<PatientScoreMainInfo> GetPatientLastScoreRisk(string inpatientID, int recordListID)
        {
            return await _medicalDbContext.PatientScoreMainInfos.Where(m => m.InpatientID == inpatientID
                   && m.RecordListID == recordListID && m.DeleteFlag != "*")
                   .OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取某个风险的最后一次评估数据(不跟踪)
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="recordListID"></param>
        /// <returns></returns>
        public async Task<PatientRiskAssess> GetPatientLastScoreRiskByRecordListID(string inpatientID, int recordListID)
        {
            return await _medicalDbContext.PatientScoreMainInfos.AsNoTracking().
                 Where(m => m.DeleteFlag != "*" && m.InpatientID == inpatientID && m.RecordListID == recordListID).OrderByDescending(m => m.AddDate)
                .Select(m => new PatientRiskAssess
                {
                    InpatientID = m.InpatientID,
                    PatientScoreMainID = m.PatientScoreMainID,
                    RecordListID = m.RecordListID,
                    AddDate = m.AddDate,
                    DueDay = m.DueDay,
                    AssessDateTime = m.AssessDate.Add(m.AssessTime),
                    ModifyDate = m.ModifyDate
                }).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 更新未停止的风险数据
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="endDate">停止日期时间</param>
        /// <param name="employeeID">护士工号</param>
        /// <returns></returns>
        public async Task<bool> UpdateUnEndScore(string inpatientID, DateTime endDate, string employeeID)
        {
            int cout = 0;
            var stationShiftInfos = await _stationShiftRepository.GetAsync();
            var query = from a in _medicalDbContext.PatientScoreMainInfos
                        where a.InpatientID == inpatientID && a.DueDay >= endDate.Date
                        select a;
            foreach (var item in query)
            {
                cout += 1;
                item.Modify(employeeID);
                item.DueDay = endDate;
                var currentStationShiftInfos = stationShiftInfos.Where(m => m.StationID == item.StationID).ToList();
                var nowShiftInfo = GetCurrentShift(currentStationShiftInfos, endDate.TimeOfDay);
                item.DueShift = nowShiftInfo.Shift;
                _logger.Info($"更新患者{inpatientID}未停止的风险：{item.RecordListID}，更新后的DueDay：{item.DueDay},DueShift:{item.DueShift}");
            }

            //中山护理评估偶而会出现加上志观察
            if (cout == 0)
            {
                _logger.Warn("★病人住院序号:" + inpatientID + "查无需要停止的风险,到期日:" + endDate.ToShortDateString() + ",操作人员:" + employeeID);
            }
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
        public async Task<List<PatientScoreMainInfo>> GetRecordByAssessNum(string inpatientID, string sourceType, string sourceID)
        {
            return await _medicalDbContext.PatientScoreMainInfos.Where(m => m.InpatientID == inpatientID
                    && m.SourceType == sourceType && m.SourceID == sourceID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<RiskScreenRecordListView>> GetRiskScreens(bool typeSwitch, int stationID, DateTime startDate
            , DateTime endDate, int recordListID, int[] assessScoreRangeIDs)
        {
            var data = new List<RiskScreenRecordListView>();

            if (typeSwitch)
            {
                data = await (from a in _medicalDbContext.PatientScoreMainInfos
                              join b in _medicalDbContext.InpatientDatas on a.InpatientID equals b.ID
                              join c in _medicalDbContext.PatientBasicDatas on b.PatientID equals c.PatientID
                              where a.DeleteFlag != "*" && b.DeleteFlag != "*"
                                    && a.RecordListID == recordListID
                                    && a.AssessDate >= startDate && a.AssessDate <= endDate
                                    && (stationID == 999999 || b.StationID == stationID)
                                    && assessScoreRangeIDs.Contains(a.ScoreRange)
                              select new RiskScreenRecordListView
                              {
                                  InaptientID = a.InpatientID,
                                  ChartNo = a.ChartNo,
                                  PatientScoreMainID = a.PatientScoreMainID,
                                  AssessDateTime = a.AssessDate + a.AssessTime,
                                  RecordListID = a.RecordListID,
                                  ScorePoint = a.ScorePoint,
                                  ScoreRange = a.ScoreRange,
                                  Shift = a.Shift,
                                  EmployeeID = a.AddEmployeeID,
                                  Diagnosis = b.Diagnosis,
                                  BedNumber = b.BedNumber,
                                  StationID = b.StationID,
                                  DepartmentListID = b.DepartmentListID,
                                  LocalCaseNumber = b.LocalCaseNumber,
                                  Age = b.Age,
                                  AdmissionDate = b.AdmissionDate,
                                  NursingLevel = b.NursingLevel,
                                  PatientName = c.PatientName,
                                  Gender = c.Gender,
                              }).ToListAsync();
            }
            else
            {
                data = await (from a in _medicalDbContext.PatientScoreMainInfos
                              join b in _medicalDbContext.InpatientDatas on a.InpatientID equals b.ID
                              join c in _medicalDbContext.PatientBasicDatas on b.PatientID equals c.PatientID
                              where a.DeleteFlag != "*" && b.DeleteFlag != "*"
                                    && a.RecordListID == recordListID
                                    && b.AdmissionDate >= startDate && b.AdmissionDate <= endDate
                                    && (stationID == 999999 || b.StationID == stationID)
                                    && assessScoreRangeIDs.Contains(a.ScoreRange)
                              select new RiskScreenRecordListView
                              {
                                  InaptientID = a.InpatientID,
                                  ChartNo = a.ChartNo,
                                  PatientScoreMainID = a.PatientScoreMainID,
                                  AssessDateTime = a.AssessDate + a.AssessTime,
                                  RecordListID = a.RecordListID,
                                  ScorePoint = a.ScorePoint,
                                  ScoreRange = a.ScoreRange,
                                  Shift = a.Shift,
                                  EmployeeID = a.AddEmployeeID,
                                  Diagnosis = b.Diagnosis,
                                  BedNumber = b.BedNumber,
                                  StationID = b.StationID,
                                  DepartmentListID = b.DepartmentListID,
                                  LocalCaseNumber = b.LocalCaseNumber,
                                  Age = b.Age,
                                  AdmissionDate = b.AdmissionDate,
                                  NursingLevel = b.NursingLevel,
                                  PatientName = c.PatientName,
                                  Gender = c.Gender,
                              }).ToListAsync();
            }
            data = data.OrderByDescending(m => m.AssessDateTime)
                .GroupBy(m => new { m.InaptientID, m.RecordListID })
                .Select(m => m.First()).ToList();

            return data;
        }

        public async Task<List<ScoreDocumentView>> GetScoreDocumentViews(string inpatientID, int recordListID, string hospitalID)
        {
            var query = await (from a in _medicalDbContext.PatientScoreMainInfos
                               join b in _medicalDbContext.PatientScoreDetailInfos on a.PatientScoreMainID equals b.PatientScoreMainID
                               join c in _medicalDbContext.userInfos on new { UserID = a.ModifyPersonID, HospitalID = hospitalID } equals new { c.UserID, c.HospitalID } into userJoin
                               from c in userJoin.DefaultIfEmpty() // Left join
                               where a.InpatientID == inpatientID && a.RecordListID == recordListID && a.DeleteFlag != "*"
                               select new ScoreDocumentView
                               {
                                   PatientScoreMainID = a.PatientScoreMainID,
                                   AssessDate = a.AssessDate,
                                   AssessTime = a.AssessTime,
                                   ScoreRange = a.ScoreRange,
                                   ScorePoint = a.ScorePoint,
                                   RecordForamtID = b.RecordFormatID,
                                   UserName = c != null ? c.Name : "",
                                   UserID = a.ModifyPersonID,
                                   ScoreContent = a.ScoreContent
                               }).OrderBy(a => a.AssessDate).ThenBy(m => m.AssessTime).ToListAsync();
            return query;
        }
        public async Task<List<KeyValueInt>> GetScoreRangeByRecordListIDs(string inpatientID, List<int> recordListIDs)
        {
            var query = await _medicalDbContext.PatientScoreMainInfos.Where(m => m.InpatientID == inpatientID
                && recordListIDs.Contains(m.RecordListID) && m.DeleteFlag != "*").ToListAsync();

            return query.OrderByDescending(m => m.AddDate)
                .GroupBy(m => new { m.InpatientID, m.RecordListID })
                .Select(m => new KeyValueInt
                {
                    ID = m.First().RecordListID,
                    Value = m.First().ScoreRange
                }).ToList();
        }

        public async Task<AssessTimeView> GetAssessTimeByID(string assessMainID)
        {
            var assessMain = await _medicalDbContext.PatientScoreMainInfos.Where(m => m.PatientScoreMainID == assessMainID && m.DeleteFlag != "*")
                     .Select(m => new AssessTimeView { StartDate = m.AssessDate, StartTime = m.AssessTime })
                     .FirstOrDefaultAsync();
            return assessMain;
        }

        public async Task<string> GetNotDuePatientScoreMainIDBySourceID(string inpatientID, string sourceID)
        {
            return await _medicalDbContext.PatientScoreMainInfos.Where(m => m.InpatientID == inpatientID &&
                     m.SourceID == sourceID && m.DeleteFlag != "*" && (m.DueDay == null || m.DueDay >= DateTime.Now.Date)).OrderByDescending(m => m.DueDay).Select(m => m.PatientScoreMainID).FirstOrDefaultAsync();
        }
        public async Task<List<PatientScoreMainInfo>> GetRecordsBySourceID(string sourceID, string sourceType)
        {
            var data = await _medicalDbContext.PatientScoreMainInfos.Where(m => m.SourceID == sourceID && m.DeleteFlag != "*").ToListAsync();
            if (!string.IsNullOrEmpty(sourceType))
            {
                data = data.Where(m => !string.IsNullOrEmpty(m.SourceType) && m.SourceType.Trim() == sourceType.Trim()).ToList();
            }
            return data;
        }
        public async Task<List<PatientScoreMainInfo>> GetRecordsByInpatientIDAndSourceID(string inpatientId, string sourceID, string sourceType)
        {
            var data = await _medicalDbContext.PatientScoreMainInfos.Where(m => m.InpatientID == inpatientId && m.SourceID == sourceID && m.DeleteFlag != "*").ToListAsync();
            if (!string.IsNullOrEmpty(sourceType))
            {
                data = data.Where(m => !string.IsNullOrEmpty(m.SourceType) && m.SourceType.Trim() == sourceType.Trim()).ToList();
            }
            return data;
        }

        public async Task<List<PatientScoreMainInfo>> GetPatientScoreMainByIDs(List<string> patientScoreMainIDs)
        {
            return await _medicalDbContext.PatientScoreMainInfos.Where(m => patientScoreMainIDs.Contains(m.PatientScoreMainID) && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<Dictionary<string, string>>> GetScoreValueByRecordListIDs(string inpatientID, int[] recordListIDs)
        {
            var query = await _medicalDbContext.PatientScoreMainInfos.Where(m => m.InpatientID == inpatientID
                    && recordListIDs.Contains(m.RecordListID) && m.DeleteFlag != "*").ToListAsync();
            return query.OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime)
                .GroupBy(m => new { m.InpatientID, m.RecordListID })
                .Select(m => m.First())
                .Select(m => new Dictionary<string, string>()
                {
                    { "key", m.RecordListID.ToString() },
                    { "value", !string.IsNullOrEmpty(m.ScoreContent) ? m.ScoreContent.Trim() : m.ScorePoint.ToString() + "分" }
                }).ToList();
        }

        public async Task<List<PatientScoreMainInfo>> GetInpatientScoreByRecordListIDs(string inpatientID, int[] recordListIDs)
        {
            return await _medicalDbContext.PatientScoreMainInfos.Where(m => m.InpatientID == inpatientID
                     && recordListIDs.Contains(m.RecordListID) && m.DeleteFlag != "*")
                 .Select(m => new PatientScoreMainInfo
                 {
                     PatientScoreMainID = m.PatientScoreMainID,
                     RecordListID = m.RecordListID,
                     DueDay = m.DueDay,
                     CategoryGroupID = m.CategoryGroupID,
                     AddDate = m.AddDate,
                     ScorePoint = m.ScorePoint,
                     ScoreRange = m.ScoreRange,
                     AssessDate = m.AssessDate,
                     AssessTime = m.AssessTime,
                 }).ToListAsync();

        }

        public async Task<List<string>> GetScorePatientListByPoint(int[] stationIDs, int recordsListID, int point)
        {
            return await _medicalDbContext.PatientScoreMainInfos.Where(m => stationIDs.Contains(m.StationID) && m.RecordListID == recordsListID && m.ScorePoint >= point && m.DeleteFlag != "*")
                                                                 .Select(m => m.InpatientID).Distinct().ToListAsync();
        }

        public async Task<PatientScoreDataView> GetPatientScoreViewByID(string patientScoreMainID)
        {
            return await _medicalDbContext.PatientScoreMainInfos.Where(m =>
                    m.PatientScoreMainID == patientScoreMainID &&
                    m.DeleteFlag != "*").Select(m => new PatientScoreDataView
                    {
                        PatientScoreMainID = m.PatientScoreMainID,
                        InpatientID = m.InpatientID,
                        CaseNumber = m.CaseNumber,
                        ChartNo = m.ChartNo,
                        BedNumber = m.BedNumber,
                        AssessDate = m.AssessDate,
                        AssessTime = m.AssessTime,
                        DepartmentListID = m.DepartmentListID,
                        StationID = m.StationID,
                        AddEmployeeID = m.AddEmployeeID,
                        AddDate = m.AddDate,
                        RecordListID = m.RecordListID,
                        ScoreRangeID = m.ScoreRange,
                        ScorePoint = m.ScorePoint,
                        ModifyPersonID = m.ModifyPersonID,
                        ModifyDate = m.ModifyDate,
                        DeleteFlag = m.DeleteFlag,
                    }).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取指定风险班内的最后一次评估数据
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="recordListID">评量表ID</param>
        /// <param name="shift">班别</param>
        /// <param name="shiftDate">班别日期</param>
        /// <returns></returns>
        public async Task<PatientRiskAssess> GetPatientLastRiskByRecordListIDAndShift(string inpatientID, int recordListID, string shift, DateTime shiftDate)
        {
            return await _medicalDbContext.PatientScoreMainInfos.Where(m => m.InpatientID == inpatientID && m.RecordListID == recordListID && m.DeleteFlag != "*"
            && m.Shift == shift && m.ShiftDate == m.ShiftDate).OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime)
            .Select(m => new PatientRiskAssess
            {
                ScoreRangeContent = m.ScoreContent,
                AssessScoreRangeID = m.ScoreRange,
                Point = m.ScorePoint
            })
            .FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取指定风险班内的最后一次评估数据
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="recordListIDs">评量表ID集合</param>
        /// <param name="shift">班别</param>
        /// <param name="shiftDate">班别日期</param>
        /// <returns></returns>
        public async Task<PatientRiskAssess> GetPatientLastRiskByRecordListIDAndShift(string inpatientID, List<int> recordListIDs, string shift, DateTime shiftDate)
        {
            return await _medicalDbContext.PatientScoreMainInfos.Where(m => m.InpatientID == inpatientID && recordListIDs.Contains(m.RecordListID) && m.DeleteFlag != "*"
            && m.Shift == shift && m.ShiftDate == m.ShiftDate).OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime)
            .Select(m => new PatientRiskAssess
            {
                ScoreRangeContent = m.ScoreContent,
                AssessScoreRangeID = m.ScoreRange,
                Point = m.ScorePoint
            })
            .FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取病人指定风险评估数据
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="recordListID">评量表ID</param>
        /// <returns></returns>
        public async Task<List<ScoreDocumentView>> GetPatientRiskViewByRecordID(string inpatientID, int recordListID)
        {
            return await _medicalDbContext.PatientScoreMainInfos.Where(m => m.InpatientID == inpatientID && m.RecordListID == recordListID
            && m.DeleteFlag != "*").Select(m => new ScoreDocumentView
            {
                AssessDate = m.AssessDate,
                AssessTime = m.AssessTime,
                ScorePoint = m.ScorePoint,
                ScoreRange = m.ScoreRange,
                ScoreContent = m.ScoreContent,
                UserID = m.AddEmployeeID,
                PatientScoreMainID = m.PatientScoreMainID
            }).ToListAsync();
        }
        /// <summary>
        /// 获取同组风险
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="categoryGroupID"></param>
        /// <returns></returns>
        public async Task<List<int>> GetScoresByCategoryGroupID(string inpatientID, string categoryGroupID)
        {
            return await _medicalDbContext.PatientScoreMainInfos.Where(m => m.InpatientID == inpatientID && m.CategoryGroupID == categoryGroupID).Select(m => m.RecordListID).Distinct().ToListAsync();
        }
        /// <summary>
        /// 获取同组的风险
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="categoryGroupID">组号</param>
        /// <returns></returns>
        public async Task<List<PatientScoreMainInfo>> GetInfosByCategoryID(string inpatientID, string categoryGroupID)
        {
            return await _medicalDbContext.PatientScoreMainInfos.Where(m => m.InpatientID == inpatientID && m.CategoryGroupID == categoryGroupID).ToListAsync();
        }

        /// <summary>
        /// 获取当前班别
        /// </summary>
        /// <param name="stationShifts">当前病区班别集合</param>
        /// <param name="timeSpan">当前时间</param>
        /// <returns></returns>
        private StationShiftInfo GetCurrentShift(List<StationShiftInfo> stationShifts, TimeSpan timeSpan)
        {
            StationShiftInfo stationShiftInfo = null;
            //判断目前所属班别
            foreach (StationShiftInfo item in stationShifts)
            {
                if (item.ShiftStartTime <= timeSpan && item.ShiftEndTime >= timeSpan)
                {
                    stationShiftInfo = item;
                    break;
                }
                if (item.ShiftEndTime < item.ShiftStartTime)
                {
                    TimeSpan maxSpan = new TimeSpan(23, 59, 59);
                    TimeSpan zeroSpan = new TimeSpan(0, 0, 0);
                    if ((timeSpan >= item.ShiftStartTime && timeSpan <= maxSpan)
                        || (timeSpan >= zeroSpan && timeSpan <= item.ShiftEndTime))
                    {
                        stationShiftInfo = item;
                    }
                }
            }
            return stationShiftInfo;
        }
        public async Task<HisRiskMainScoreView> GetMainDataByMainID(string patientScoreMainID, int recordListID, int languang, string hoapitalID
            , int scorePoint, string caseNumber)
        {
            return await (from a in _medicalDbContext.PatientScoreMainInfos.Where(m => m.CaseNumber == caseNumber && m.PatientScoreMainID == patientScoreMainID && m.DeleteFlag != "*")
                          join b in _medicalDbContext.AssessScoreRangeInfos.Where(m => m.Language == languang && m.HospitalID == hoapitalID
                          && m.RecordListID == recordListID && m.DeleteFlag != "*" && scorePoint >= m.ScoreLowerLimit && scorePoint <= m.ScoreUpperLimit)
                          on a.RecordListID equals b.RecordListID
                          select new HisRiskMainScoreView
                          {
                              PatientScoreMainID = a.PatientScoreMainID,
                              CaseNumber = a.CaseNumber,
                              ChartNo = a.ChartNo,
                              RecordListID = a.RecordListID,
                              AssessDate = a.AssessDate,
                              AssessTime = a.AssessTime,
                              ScorePoint = a.ScorePoint,
                              ScoreRange = a.ScoreRange,
                              ScoreContent = b.RangeContent,
                              ModifyPersonID = a.ModifyPersonID,
                              ModifyDate = a.ModifyDate
                          }).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据主键号获取风险表ID
        /// </summary>
        /// <param name="careMainIDs">来源序号</param>
        /// <returns></returns>
        public async Task<List<PatientScoreMainInfo>> GetRecordListIDsBySourceIDs(List<string> careMainIDs)
        {
            var recordListIDs = await _medicalDbContext.PatientScoreMainInfos.Where(m => careMainIDs.Contains(m.PatientScoreMainID))
                 .Select(m => new PatientScoreMainInfo
                 {
                     PatientScoreMainID = m.PatientScoreMainID,
                     RecordListID = m.RecordListID
                 }).ToListAsync();
            return recordListIDs;
        }

        public async Task<DateTime?> GetNextAssessTime(string inpatientID, int recordListID, DateTime dateTime, string patientScoreMainID)
        {
            var list = await _medicalDbContext.PatientScoreMainInfos.Where(m => m.InpatientID == inpatientID && m.RecordListID == recordListID
            && m.AssessDate >= dateTime.Date && m.DeleteFlag != "*" && m.PatientScoreMainID != patientScoreMainID)
                .AsNoTracking()
                .Select(m => new { m.AssessDate, m.AssessTime }).ToListAsync();
            list = list.Where(m => m.AssessDate.Add(m.AssessTime) > dateTime).OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).ToList();
            if (list.Count > 0)
            {
                return list[0].AssessDate.Add(list[0].AssessTime);
            }
            return null;
        }
        /// <summary>
        /// 根据病区和时间段获取相关风险数据
        /// </summary>
        /// <param name="handoverQueryView"></param>
        /// <param name="recordListIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientScoreMainInfo>> GetInpatientScoreByStationIDAndTime(HandoverQueryView handoverQueryView, int[] recordListIDs)
        {
            var query = await _medicalDbContext.PatientScoreMainInfos.Where(m => m.InpatientID == handoverQueryView.InpatientID && m.StationID == handoverQueryView.StationID
            && m.AssessDate >= handoverQueryView.StartDate && m.AssessDate <= handoverQueryView.EndDate
                     && recordListIDs.Contains(m.RecordListID) && m.DeleteFlag != "*")
                 .Select(m => new PatientScoreMainInfo
                 {
                     PatientScoreMainID = m.PatientScoreMainID,
                     RecordListID = m.RecordListID,
                     DueDay = m.DueDay,
                     CategoryGroupID = m.CategoryGroupID,
                     AddDate = m.AddDate,
                     ScorePoint = m.ScorePoint
                 }).ToListAsync();
            return query.Where(m => m.AssessTime >= handoverQueryView.StartTime && m.AssessTime <= handoverQueryView.EndTime).ToList();
        }
        public async Task<List<ScoreDataView>> GetSocrePointMoreThanSet(string hospitalID, int language, int recordListID, int scorePoint)
        {

            var list = await (from inp in _medicalDbContext.InpatientDatas
                              join basic in _medicalDbContext.PatientBasicDatas on inp.PatientID equals basic.PatientID
                              join station in _medicalDbContext.StationList on inp.StationID equals station.ID
                              join score in _medicalDbContext.PatientScoreMainInfos on inp.ID equals score.InpatientID
                              where inp.HospitalID == hospitalID && inp.DeleteFlag != "*" && basic.DeleteFlag != "*"
                              && InHospitalStatus.INHOSPITALLIST.Contains(inp.InHospitalStatus ?? -1)
                              && station.DeleteFlag != "*" && station.HospitalID == hospitalID && score.RecordListID == recordListID && score.DeleteFlag != "*"
                              && inp.HospitalID == hospitalID && basic.HospitalID == hospitalID
                              select new ScoreDataView
                              {
                                  StationName = station.StationName,
                                  LocalCaseNumber = inp.LocalCaseNumber,
                                  PatientName = basic.PatientName,
                                  BedNumber = inp.BedNumber,
                                  Diagnosis = inp.Diagnosis,
                                  ScorePoint = score.ScorePoint,
                                  AssessDate = score.AssessDate,
                                  AssessTime = score.AssessTime,
                                  StationID = inp.StationID,
                                  RecordsFormatContent = score.PatientScoreMainID
                              }).ToListAsync();

            list = list.OrderByDescending(n => n.AssessDate).ThenByDescending(n => n.AssessTime)
                .GroupBy(m => m.LocalCaseNumber).Select(m => m.First()).ToList();

            list = list.Where(m => m.ScorePoint >= scorePoint).ToList();

            var hashSet = new HashSet<string>(list.Select(m => m.RecordsFormatContent).Distinct());
            var details = await _medicalDbContext.PatientScoreDetailInfos.Where(m => hashSet.Contains(m.PatientScoreMainID)).ToListAsync();
            var formatList = await _recordsFormatRepository.GetAsync();
            return (from main in list
                    join detail in details on main.RecordsFormatContent equals detail.PatientScoreMainID
                    join format in formatList on detail.RecordFormatID equals format.ID
                    select new ScoreDataView
                    {
                        StationName = main.StationName,
                        LocalCaseNumber = main.LocalCaseNumber,
                        PatientName = main.PatientName,
                        BedNumber = main.BedNumber,
                        Diagnosis = main.Diagnosis,
                        ScorePoint = main.ScorePoint,
                        AssessDate = main.AssessDate,
                        AssessTime = main.AssessTime,
                        StationID = main.StationID,
                        ParentID = format.ParentID,
                        RecordsFormatContent = format.RecordsFormatContent
                    }).ToList();
        }
        public async Task<List<PatientScoreMainInfo>> GetUnPumpData(int recordListID, DateTime startDate)
        {
            return await _medicalDbContext.PatientScoreMainInfos.Where(m => m.AssessDate >= startDate && m.RecordListID == recordListID && m.DeleteFlag != "*" && m.DataPumpFlag != "*").ToListAsync();
        }
        public async Task<List<string>> GetScorePatientList(List<string> inpatientIDs, int recordsListID, int point)
        {
            return await _medicalDbContext.PatientScoreMainInfos.AsNoTracking().Where(m => inpatientIDs.Contains(m.InpatientID) && m.RecordListID == recordsListID && m.ScorePoint >= point && m.DeleteFlag != "*")
                                                                 .Select(m => m.InpatientID).Distinct().ToListAsync();
        }
        /// <summary>
        /// 获取时间段内的评估数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public async Task<List<PatientScoreMainInfo>> GetPatientScoresAsyncByTime(string inpatientID, DateTime startTime, DateTime endTime)
        {
            var query = await _medicalDbContext.PatientScoreMainInfos.Where(m => m.InpatientID == inpatientID && m.AssessDate >= startTime.Date && m.AssessDate <= endTime.Date && m.DeleteFlag != "*").ToListAsync();
            return query.Where(m => m.AssessDate.Add(m.AssessTime) >= startTime && m.AssessDate.Add(m.AssessTime) <= endTime).ToList();
        }

        public async Task<int> GetRecordListID(string patientScoreMainID)
        {
            return await _medicalDbContext.PatientScoreMainInfos.AsNoTracking().Where(m => m.PatientScoreMainID == patientScoreMainID && m.DeleteFlag != "*")
                .Select(m => m.RecordListID).FirstOrDefaultAsync();
        }
    }
}

