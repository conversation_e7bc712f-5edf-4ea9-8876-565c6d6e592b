﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models.RecordSupplement;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class SupplementRecordLogRepository : ISupplementRecordLogRepository
    {
        private MedicalDbContext _medicalDbContext;

        public SupplementRecordLogRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }
        /// <summary>
        /// 根据来源ID获取数据
        /// </summary>
        /// <param name="souceID"></param>
        /// <returns></returns>
        public async Task<SupplementRecordLogInfo> GetLogBySouceID(string souceID)
        {
            return await _medicalDbContext.SupplementRecordLogInfos.Where(m => m.SouceID == souceID).FirstOrDefaultAsync();
        }
    }
}
