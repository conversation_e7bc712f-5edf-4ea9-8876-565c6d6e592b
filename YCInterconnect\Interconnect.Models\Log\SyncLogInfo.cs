﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 

namespace Interconnect.Models
{
    /// <summary>
    /// 因日志数量会比较大，所以程序架构不提供查询方法，只封装新增方法,
    /// 如需要新增查询方法，请认真评估
    /// </summary>
    [Serializable]
    [Table("SyncLog")]
    public class SyncLogInfo
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Key]
        public int? LogID { get; set; }
        /// <summary>
        /// 日志级别 Error 1,Warn 2,Info 3,使用数值存储，提高写效能
        /// </summary>
        public byte LogLevel  { get; set; }
        /// <summary>
        /// 证明是一组日志，如同一个事务产生的日志，使用GuID区分，方便查询
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string LogGroupID { get; set; }
        /// <summary>
        /// 同步的日志类别编码
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string LogTypeCode { get; set; }
        /// <summary>
        /// 同步的日志类别名称
        /// </summary>
        public string LogTypeName { get; set; }
        /// <summary>
        /// 日志内容描述
        /// </summary>
        public string Contents { get; set; }
        /// <summary>
        /// 新增人员
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string AddPersonID { get; set; }

        /// <summary>
        /// 新增时间
        /// </summary>
        public DateTime AddDateTime { get; set; }

    }
}
