﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Interconnect.Data.Interface;
using Interconnect.Services.Interface;
using Interconnect.ViewModels;
using MailKit.Net.Smtp;
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.Interface;
using Medical.ViewModels.Query;
using Medical.ViewModels.View;
using Microsoft.Extensions.Options;
using MimeKit;
using Newtonsoft.Json;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Interconnect.Services
{
    public class CommonHelper : ICommonHelper
    {
        private readonly IOptions<Models.SystemConfig> _config;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IStationListRepository _stationListRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly ISettingDescRepository _interconnectSDRepository;
        private readonly IBedListRepository _bedListRepository;
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly ISyncLogService _syncLogService;
        private readonly IPatientProfileRepository _patientProfileRepository;
        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";

        public CommonHelper(IOptions<Models.SystemConfig> config
            , IStationListRepository stationListRepository
            , IDepartmentListRepository departmentListRepository
            , ISettingDescRepository InterconnectSettingDescriptionRepository
            , IBedListRepository bedListRepository
            , IUnitOfWork<MedicalDbContext> unitOfWork
            , IAppConfigSettingRepository appConfigSettingRepository
            , ISyncLogService syncLogService
            , IPatientProfileRepository patientProfileRepository
          )
        {
            this._config = config;
            _stationListRepository = stationListRepository;
            _departmentListRepository = departmentListRepository;
            _interconnectSDRepository = InterconnectSettingDescriptionRepository;
            _bedListRepository = bedListRepository;
            _unitOfWork = unitOfWork;
            _appConfigSettingRepository = appConfigSettingRepository;
            _syncLogService = syncLogService;
            _patientProfileRepository = patientProfileRepository;
        }


        /// <summary>
        /// 获取变更内容
        /// </summary>
        /// <param name="builder">变更内容</param>
        /// <param name="key">字段</param>
        /// <param name="oldValue">原有值</param>
        /// <param name="newValue">变更值</param>
        public void GetChange(StringBuilder builder, string key, string oldValue, string newValue)
        {
            if (builder.ToString().Length > 0)
            {
                builder.Append("; ");
            }
            builder.Append(key);
            builder.Append(":");
            builder.Append(oldValue);
            builder.Append("-->");
            builder.Append(newValue);
        }

        public void AddProfile(List<PatientProfile> patientProfileList)
        {
            var hospitalID = _config.Value.HospitalID;
            if (patientProfileList == null || patientProfileList.Count <= 0)
            {
                return;
            }
            WebRequestSugar wrs = new WebRequestSugar();
            List<string> inps = patientProfileList.Select(m => m.InpatientID).Distinct().ToList();
            _logger.Info("获取atientProfileAPI" + hospitalID + "|" + "APPCONFIGSETTING_SETTINGTYPE_CONFIGS" + "|PatientProfileAPI");
            //var patientProfileAPI = _appConfigSettingRepository.GetConfigSettingValue( APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "PatientProfileAPI").Result;
            //本机测试修改api地址
            var patientProfileAPI =_config.Value.Status=="2"?(_config.Value.SyncMedicalApi+"/api/PatientProfile/Add"): _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "PatientProfileAPI").Result;
            if (string.IsNullOrEmpty(patientProfileAPI))
            {
                _logger.Error("获取PatientProfileAPI为空");
                return;
            }
            
            //按病人分组提交，在profile中会有按病人过滤
            _logger.Info("循环呼叫API:" + patientProfileAPI + "||" + inps.Count.ToString() + "次");
            foreach (var item in inps)
            {
                var tmpPatientProfiles = patientProfileList.Where(m => m.InpatientID == item).ToList();
                if (tmpPatientProfiles.Count == 0)
                {
                    continue;
                }
                var arg = JsonConvert.SerializeObject(tmpPatientProfiles);
                var syncLog = SaveLog(patientProfileAPI, arg, tmpPatientProfiles[0].InpatientID, tmpPatientProfiles[0].CaseNumber, _unitOfWork, true, true);
                var result = "";
                try
                {
                    result = wrs.SendObjectAsJsonInBody(patientProfileAPI, tmpPatientProfiles);
                }
                catch (Exception ex)
                {
                    _syncLogService.InsertSyncLog(1, "1", "SyncInpatient", "AddProfile"
                   , "InpatientID[" + item + "] 呼叫Profile失败,SynchronizeLogID[" + syncLog.ID + "]", "TongBu", true);
                    _logger.Error("呼叫：AddProfile失败，结束循环：" + ex.ToString());
                    return;
                }
                GetAPIExecResult(result, syncLog, _unitOfWork);
            }
        }
        /// <summary>
        /// 缓存更新
        /// </summary>
        /// <param name="query"></param>
        public void UpdateCache(CacheQuery query)
        {
            var hospitalID = _config.Value.HospitalID;
            var cacheUpdateAPI = _appConfigSettingRepository.GetConfigSettingValue( APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "CacheUpdateAPI").Result;
            cacheUpdateAPI = cacheUpdateAPI + "?key=" + query.Type + "_" + hospitalID;
            WebRequestSugar wrs = new WebRequestSugar();
            _logger.Info("呼叫API：" + cacheUpdateAPI);
            wrs.SendObjectAsJsonInBody(cacheUpdateAPI, $"{query.Type}_{hospitalID}");
        }

        /// <summary>
        /// 发送邮件
        /// </summary>
        /// <param name="MailBase"></param>
        /// <param name="MailAccept"></param>
        /// <returns></returns>
        /// 

        public bool SendMail(MailBaseViewInfo mailBase, MailReceptionViewInfo mailAccept)
        {
            var message = new MimeMessage();
            //发送方       
            try
            {
                message.From.Add(new MailboxAddress(mailBase.SentFromUser, mailBase.SentFromAddress));
                //message.From.Add(new MailboxAddress(MailBase.SentFromUser, "<EMAIL>"));
            }
            catch (Exception ex)
            {
                _logger.Error("发送方信息错误" + ex.ToString());
                return false;
            }

            //接受方
            foreach (var item in mailAccept.ReceptionUser)
            {
                message.To.Add(new MailboxAddress(item.Value, item.Key));//可以有多个接收方
            }
            //标题
            message.Subject = mailAccept.MailTitle;
            //赋值邮件内容
            message.Body = mailAccept.TextParts;
            //开始发送
            using (var client = new SmtpClient())
            {

                // For demo-purposes, accept all SSL certificates (in case the server supports STARTTLS)
                client.ServerCertificateValidationCallback = (s, c, h, e) => true;
                try
                {
                    _logger.Info("连接邮箱服务器Smtp：邮箱服务地址:[" + mailBase.MailHost + "]，端口:[" + mailBase.MailHostPost.ToString() + "]");
                    client.Connect(mailBase.MailHost, mailBase.MailHostPost, false);
                    _logger.Info("连接邮箱服务器验证通过!");

                }
                catch (Exception ex)
                {
                    _logger.Error("连接邮箱服务器Smtp验证失败!" + ex.ToString());
                    return false;
                }

                // Note: since we don't have an OAuth2 token, disable
                // the XOAUTH2 authentication mechanism.
                client.AuthenticationMechanisms.Remove("XOAUTH2");
                // Note: only needed if the SMTP server requires authentication
                try
                {
                    _logger.Info("开始验证Smtp邮箱信息，邮箱：[" + mailBase.SentFromAddress + "],密码；[" + mailBase.SmtpAuthorizationCode + "]");
                    client.Authenticate(mailBase.SmtpUserName, mailBase.SmtpAuthorizationCode);
                    _logger.Info("验证Smtp邮箱信息通过!");
                }
                catch (Exception ex)
                {
                    _logger.Error("验证Smtp邮箱信息错误" + ex.ToString());
                    return false;
                }
                try
                {
                    _logger.Info("发送邮件");
                    client.Send(message);
                    client.Disconnect(true);
                    _logger.Info("发送邮件成功");
                }
                catch (Exception ex)
                {
                    _logger.Warn("邮件发送错误" + ex.ToString());
                    return false;
                }

                return true;
            }
        }
        public async Task<StationListInfo> GetStationByID(int stationId)
        {
            //转换病区、科室编码   
            return await _stationListRepository.GetAsync(stationId);

        }
        public async Task<StationListInfo> GetStationByCode(string StationCode)
        {
            //转换病区、科室编码
            return _stationListRepository.GetStationInfo(StationCode);
        }

        public async Task<DepartmentListInfo> GetDepartmentByID(int DepartmentID)
        {
            //转换病区、科室编码
            return await _departmentListRepository.GetAsync(DepartmentID);
        }
        public async Task<DepartmentListInfo> GetDepartmentByCode(string DepartmentCode)
        {
            //转换病区、科室编码
            var DepartmentList = _departmentListRepository.GetDepartmentInfo(DepartmentCode);
            return DepartmentList;
        }
        public async Task<BedListInfo> GetBedByCode(string bedCode, int stationID)
        {
            //转换病区、科室编码
            List<BedListInfo> bedList = await _bedListRepository.GetAllAsync<BedListInfo>();
            return bedList.Where(m => m.StationID == stationID && m.BedNumber == bedCode && m.DeleteFlag != "").FirstOrDefault();
        }


        public void StopProblem(string inPatientID, string nursingLevel, bool stopProblem, MedicalEnumUtility.StopProblemType type)
        {
            var hospitalID = _config.Value.HospitalID;
            var modifyPersonId = _appConfigSettingRepository.GetConfigSettingValue( APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "ModifyPersonID").Result;
            WebRequestSugar wrs = new WebRequestSugar();

            var submit = new StopProlemSubmit
            {
                InpatientID = inPatientID,
                EmployeeID = modifyPersonId,
                NursingLevel = nursingLevel,
                StopProlem = stopProblem,
                Type = type
            };

            try
            {
                var arg = JsonConvert.SerializeObject(submit);
                // 从配置档中获取数据 梁宝华 2020 - 04 - 29
                var stopPatientProblemAPI =_config.Value.Status=="2"?(_config.Value.SyncMedicalApi+"/api/External/StopPatientProblem") : _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "StopPatientProblemAPI").Result;
                _logger.Info(" 停止病人问题 呼叫API：" + stopPatientProblemAPI);

                var syncLog = SaveLog(stopPatientProblemAPI,
                  arg, inPatientID, "", _unitOfWork, true);

                _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "StopProblem"
                   , "inPatientID[" + inPatientID + "] 呼叫Profile停止护理问题，" + stopPatientProblemAPI, "TongBu", true);
                string result = wrs.SendObjectAsJsonInBody(stopPatientProblemAPI, submit);

                GetAPIExecResult(result, syncLog, _unitOfWork);
            }
            catch (Exception ex)
            {
                _syncLogService.InsertSyncLog(1, "1", "SyncInpatient", "StopProblem"
                   , "inPatientID[" + inPatientID + "] 呼叫Profile停止护理问题,失败", "TongBu", true);
                _logger.Error(ex, "停止病人问题失败");
            }
        }

        /// <summary>
        ///病人信息发生变化，验证排程中的病人信息
        /// </summary>
        /// <param name="inPatientID"></param>
        public void CheckSchedulePatientInfo(string inPatientID, string caseNumber, bool isTransfer)
        {
            WebRequestSugar wrs = new WebRequestSugar();
            Dictionary<string, string> data = new Dictionary<string, string>
            {
                { "inpatientID", inPatientID },
                { "dateTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm") },
                { "isTransfer", isTransfer.ToString() }
            };
            var hospitalID = _config.Value.HospitalID;
            var url =_config.Value.Status=="2"?(_config.Value.SyncMedicalApi + "/api/PatientSchedule/CheckSchedulePatientInfo") :_appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "CheckSchedulePatientInfoAPI").Result;
            var arg = JsonConvert.SerializeObject(data);
            try
            {
                _logger.Info("病人信息发生变化，验证排程中的病人信息 呼叫API：" + url);
                var syncLog = SaveLog(url, arg, inPatientID, caseNumber, _unitOfWork, true, false);
                string result = wrs.HttpGet(url, data);
                _logger.Info("更新API执行结果：" + url);
                GetAPIExecResult(result, syncLog, _unitOfWork);
            }
            catch (Exception ex)
            {
                _logger.Error("验证排程中病人信息失败|" + ex.ToString());
            }
        }

        /// <summary>
        /// 医嘱停住，停止时间点后的排程
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="orderDetialID"></param>
        /// <param name="dateTime"></param>
        /// <returns></returns>
        public async Task StopSchduleByOrder(string inpatientID, string orderDetialID, DateTime dateTime)
        {
            var guid = Guid.NewGuid().ToString("N");
            var hospitalID = _config.Value.HospitalID;
            WebRequestSugar wrs = new WebRequestSugar();
            var syncLog = new SynchronizeLogInfo();
            Dictionary<string, string> data = new Dictionary<string, string>
            {
                { "inpatientID", inpatientID },
                { "patientOrderMainID", orderDetialID },
                { "dateTime", dateTime.ToString("yyyy-MM-dd HH:mm:ss") }
            };
            var result = "";
            try
            {
                _logger.Info("orderDetialID||" + orderDetialID);
                var stopSchduleByOrderAPI = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "StopSchduleByOrderAPI");
                //stopSchduleByOrderAPI = "http://localhost:56194/api/PatientSchedule/StopByOrder";
                stopSchduleByOrderAPI = stopSchduleByOrderAPI + "?inpatientID=" + inpatientID + "&patientOrderMainID=" + orderDetialID + "&dateTime=" + dateTime.ToString("yyyy-MM-dd HH:mm:ss");
                _logger.Info("呼叫API：" + stopSchduleByOrderAPI);
                _syncLogService.InsertSyncLog(3, guid, "StopSchduleByOrder", "", "停止医嘱呼叫API" + result + "，InpatientID：" + inpatientID, "Tongbu", true);
                syncLog = SaveLog(stopSchduleByOrderAPI, stopSchduleByOrderAPI, inpatientID, "", _unitOfWork, true, true);
                result = wrs.HttpPost(stopSchduleByOrderAPI, data, null);
            }
            catch (Exception ex)
            {
                _logger.Error("呼叫API失败" + ex.Message);
                _syncLogService.InsertSyncLog(1, guid, "StopSchduleByOrder", "", "停止医嘱呼叫API,停止排程失败，" + result + "，InpatientID：" + inpatientID, "Tongbu", true);
                return;
            }

            try
            {
                GetAPIExecResult(result, syncLog, _unitOfWork);
            }
            catch (Exception ex)
            {
                _syncLogService.InsertSyncLog(1, guid, "StopSchduleByOrder", "", "停止医嘱呼叫API,停止排程失败，" + result + "，InpatientID：" + inpatientID, "Tongbu", true);

                _logger.Error("呼叫：AddProfile失败!" + ex.ToString());
                return;
            }
        }

        public string GetInterconnectData(string apiStr, Dictionary<string, string> data)
        {
            WebRequestSugar wrs = new WebRequestSugar();
            var result = new ResponseResult();
            //呼叫前写入log 
            _logger.Info("API||" + apiStr + "获取数据");
            //呼叫登入API
            string resultApi = "";
            try
            {
                resultApi = wrs.HttpGet(apiStr, data);
               if (resultApi.IndexOf("errorcode") > 0)
                {
                    _logger.Info("错误||获取失败||" + resultApi);
                    result.Code = 0;
                    result.Message = "获取失败";
                    return resultApi;
                }
            }
            catch (Exception ex)
            {
                _logger.Info("错误||验证人员||" + resultApi + "||" + ex.ToString());
                result.Code = 0;
                result.Message = ex.ToString();
                return resultApi;
            }
            return resultApi;
        }

        public string GetApiStr(int settingType, string typeCode)
        {
            string apiStr = "";
            var apiStrList = _interconnectSDRepository.GetAsync(1, typeCode);
            if (apiStrList.Count > 0)
            {
                apiStr = apiStrList[0].Description;
            }
            else
            {
                _logger.Error("获取在院病人API 失败");
                return "";
            }
            return apiStr;
        }

        public DateTime? GetDateTime(string datetime)
        {
            if (!string.IsNullOrEmpty(datetime))
            {
                return Convert.ToDateTime(datetime);
            }
            else
            {
                return null;
            }
        }
        //保存日志
        public SynchronizeLogInfo SaveLog(string apiURL, string arg, string inpatientID,
        string caseNumber, IUnitOfWork<MedicalDbContext> _unitOfWork, bool isCommit = true, bool isPost = true)
        {
            var syncLog = CreateLogInfo(apiURL, arg, inpatientID, caseNumber, isPost);
            _unitOfWork.GetRepository<SynchronizeLogInfo>().Insert(syncLog);
            if (isCommit)
            {
                int num = _unitOfWork.SaveChanges();
                if (num == 0)
                {
                    _logger.Error("写入Sync日志错误，API:" + syncLog.ApiUrl + "，请求：" + syncLog.PostOrGet + "，参数：" + arg);
                }
            }
            return syncLog;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="apiURL"></param>
        /// <param name="arg">参数对象</param>
        /// <param name="inpatientID"></param>
        /// <param name="caseNumber"></param>
        /// <param name="isPost"></param>
        /// <returns></returns>
        private SynchronizeLogInfo CreateLogInfo(string apiURL, string arg, string inpatientID,
            string caseNumber, bool isPost)
        {
            var hospitalID = _config.Value.HospitalID;
            var modifyPersonId = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "ModifyPersonID").Result;
            var syncLog = new SynchronizeLogInfo
            {
                SynchronizeDate = DateTime.Now,
                ApiUrl = apiURL,
                PostOrGet = isPost ? "POST" : "Get",
                InpatientID = inpatientID,
                CaseNumber = caseNumber,
                Arguments = arg,
                SuccessFlag = "",
                ModifyDate = DateTime.Now,
                ModifyPersonID = modifyPersonId,
                DeleteFlag = ""
            };
            syncLog.ID = syncLog.GetId();
            return syncLog;
        }
        /// <summary>
        /// 获取API执行结果
        /// </summary>
        /// <param name="result"></param>
        /// <param name="syncLog"></param>
        /// <param name="_unitOfWork"></param>
        public bool GetAPIExecResult(string result, SynchronizeLogInfo syncLog, IUnitOfWork<MedicalDbContext> _unitOfWork)
        {
            if (result == "\"No Authorization\"")
            {
                return false;
            }
            var hospitalID = _config.Value.HospitalID;
            var modifyPersonId = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "ModifyPersonID").Result;

            try
            {
                ResponseResult response = JsonConvert.DeserializeObject<ResponseResult>(result);
                if (response.Code == 1 && response.Data != null && (bool)response.Data)
                {
                    syncLog.SuccessFlag = "*";
                    syncLog.ModifyDate = DateTime.Now;
                    syncLog.ModifyPersonID = modifyPersonId;
                    _unitOfWork.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                _logger.Error("更新SynchronizeLog失败" + ex.ToString());
                return false;
            }
            return true; ;
        }

        public bool CallPostAPI(string url, string inpatientID, object submit)
        {
            try
            {
                var arg = JsonConvert.SerializeObject(submit);
                WebRequestSugar wrs = new WebRequestSugar();
                var syncLog = SaveLog(url, arg, inpatientID, "", _unitOfWork, true);
                _syncLogService.InsertSyncLog(3, "1", "CallPostAPI", "CallPostAPI"
                   , "inPatientID[" + inpatientID + "] 呼叫API，" + url, "TongBu", true);
                string result = wrs.SendObjectAsJsonInBody(url, submit);
                return GetAPIExecResult(result, syncLog, _unitOfWork);
            }
            catch (Exception ex)
            {
                _syncLogService.InsertSyncLog(1, "1", "CallPostAPI", "CallPostAPI"
                   , "inPatientID[" + inpatientID + "] 呼叫呼叫API" + url + ",失败", "TongBu", true);
                _logger.Error("呼叫呼叫API" + url + ",失败" + ex.ToString());
                return false;
            }
        }
    }
}
