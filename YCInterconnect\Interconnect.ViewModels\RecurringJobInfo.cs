﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Interconnect.ViewModels
{
    public class RecurringJobInfo
    {
        /// <summary>
		/// The identifier of the RecurringJob
		/// </summary>
		public string RecurringJobId { get; set; }
        /// <summary>
        /// Request type
        /// </summary>
        public bool IsPost { get; set; }
        /// <summary>
        /// api
        /// </summary>
        public string ApiUrl { get; set; }
        /// <summary>
        /// Api Data
        /// </summary>
        public IDictionary<string, object> JobData { get; set; }
        /// <summary>
        /// Cron expressions
        /// </summary>
        public string Cron { get; set; }
    }
}
