﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientTubeCareDetailRepository : IPatientTubeCareDetailRepository
    {
        private MedicalDbContext _dbContext = null;

        public PatientTubeCareDetailRepository(
            MedicalDbContext db
            )
        {
            _dbContext = db;
        }

        /// <summary>
        /// 根据维护主表ID获取维护明细表
        /// </summary>
        /// <param name="careMainID"></param>
        /// <returns>Detail视图</returns>
        public async Task<List<Detail>> GetViewsByCareMainIDAsync(string careMainID)
        {
            return await _dbContext.PatientTubeCareDetailInfos
                .Where(t => t.PatientTubeCareMainID == careMainID && t.DeleteFlag != "*")
                .Select(n => new Detail()
                {
                    AssessListID = n.AssessListID,
                    AssessListGroupID = n.AssessListGroupID,
                    AssessValue = n.AssessValue
                })
                .ToListAsync();
        }

        /// <summary>
        /// 根据维护主表ID获取维护明细表
        /// </summary>
        /// <param name="careMainID"></param>
        /// <returns>明细表List</returns>
        public async Task<List<PatientTubeCareDetailInfo>> GetInfosByCareMainID(string careMainID)
        {
            return await _dbContext.PatientTubeCareDetailInfos
                .Where(t => t.PatientTubeCareMainID == careMainID && t.DeleteFlag != "*")
                .ToListAsync();
        }

        /// <summary>
        /// 获取插管记录
        /// </summary>
        /// <param name="careMainID"></param>
        /// <returns></returns>
        public async Task<List<PatientTubeCareDetailInfo>> GetStartDetailsByRecordIDAsync(string patientTubeRecordID)
        {
            return await (from a in _dbContext.PatientTubeCareMainInfos
                          join b in _dbContext.PatientTubeCareDetailInfos
                          on a.PatientTubeCareMainID equals b.PatientTubeCareMainID
                          where a.PatientTubeRecordID == patientTubeRecordID && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                           && a.RecordsCode.EndsWith("Start")
                          select b).ToListAsync();
        }

        /// <summary>
        /// 获取拔管记录
        /// </summary>
        /// <param name="careMainID"></param>
        /// <returns></returns>
        public async Task<List<PatientTubeCareDetailInfo>> GetRemoveDetailsByRecordIDAsync(string patientTubeRecordID)
        {
            return await (from a in _dbContext.PatientTubeCareMainInfos
                          join b in _dbContext.PatientTubeCareDetailInfos
                          on a.PatientTubeCareMainID equals b.PatientTubeCareMainID
                          where a.PatientTubeRecordID == patientTubeRecordID && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                           && a.RecordsCode.EndsWith("End")
                          select b).ToListAsync();
        }

        public async Task<List<KeyValue>> GetByGroupID(string patientTubeCareMainID, int groupID)
        {
            return await _dbContext.PatientTubeCareDetailInfos
                .Where(m => m.PatientTubeCareMainID == patientTubeCareMainID && m.AssessListGroupID == groupID
                && m.DeleteFlag != "*")
                .Select(m => new KeyValue
                {
                    ID = m.AssessListID,
                    Value = m.AssessValue
                }).ToListAsync();
        }
        /// <summary>
        /// 根据inpatientIDs和assessListIDs获取明细数据
        /// </summary>
        /// <param name="inpatientIDs"></param>
        /// <param name="assessListIDs"></param>
        /// <returns></returns>
        public async Task<List<KeyValue>> GetDetailByInpatientIDsAndAssessLists(List<string> careMainIDs, List<int> assessListIDs)
        {
            return await _dbContext.PatientTubeCareDetailInfos
                .Where(m => careMainIDs.Contains(m.PatientTubeCareMainID) && assessListIDs.Contains(m.AssessListID) && m.DeleteFlag != "*")
                .Select(m => new KeyValue
                {
                    Value = m.InpatientID,
                    ID = m.AssessListID
                }).ToListAsync();
        }
        /// <summary>
        /// 根据主表记录ID获取明细记录集合
        /// </summary>
        /// <param name="careMainIDs">维护记录IDs</param>
        /// <returns></returns>
        public async Task<List<SpecialListDetailView>> GetSpecialListDetailViewsByCareMainIDs(params string[] careMainIDs)
        {
            return await _dbContext.PatientTubeCareDetailInfos
                .Where(m => careMainIDs.Contains(m.PatientTubeCareMainID) && m.DeleteFlag != "*")
                .Select(m => new SpecialListDetailView
                {
                    MainID = m.PatientTubeCareMainID,
                    AssessListGroupID = m.AssessListGroupID,
                    AssessListID = m.AssessListID,
                    AssessValue = m.AssessValue
                })
                .ToListAsync();
        }
        /// <summary>
        /// 根据主表记录ID集合获取明细记录集合
        /// </summary>
        /// <param name="patientTubeRecordID"></param>
        /// <returns></returns>
        public async Task<List<PatientTubeCareDetailInfo>> GetRemoveByRecordIDAsync(string patientTubeRecordID)
        {
            return await (from a in _dbContext.PatientTubeCareDetailInfos
                          join b in _dbContext.PatientTubeCareMainInfos
                          on a.PatientTubeCareMainID equals b.PatientTubeCareMainID
                          where b.PatientTubeRecordID == patientTubeRecordID && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                          select a).ToListAsync();
        }
        /// <summary>
        /// 根据患者ID,导管ID以及维护特定时机(Record)获取记录明细
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="tubeID"></param>
        /// <param name="recordCode"></param>
        /// <returns></returns>
        public async Task<List<PatientTubeDetailView>> GetCareDetailsByInpatientAndIDAndCodeAsync(string inpatientID, int tubeID, string recordCode)
        {
            return await (from a in _dbContext.PatientTubeCareMainInfos.Where(m => m.InpatientID == inpatientID && m.TubeID == tubeID && m.RecordsCode == recordCode && m.DeleteFlag != "*")
                          join b in _dbContext.PatientTubeCareDetailInfos
                          on a.PatientTubeCareMainID equals b.PatientTubeCareMainID
                          where b.DeleteFlag != "*"
                          select new PatientTubeDetailView
                          {
                              AssessListID = b.AssessListID,
                              AssessValue = b.AssessValue,
                              PatientTubeCareMainID = b.PatientTubeCareMainID,
                              PatientTubeCareDetailID = b.PatientTubeCareDetailID,
                              InpatientID = a.InpatientID,
                              AssessDate = a.AssessDate,
                              AssessTime = a.AssessTime,
                              AddEmployeeID = a.AddEmployeeID
                          }).ToListAsync();

        }

        public async Task<int> GetDetailListByRecordAndAssessListIDs(string patientTubeRecordID, List<int> assessListIDs)
        {
            return await (from a in _dbContext.PatientTubeCareDetailInfos
                          join b in _dbContext.PatientTubeCareMainInfos
                          on a.PatientTubeCareMainID equals b.PatientTubeCareMainID
                          where b.PatientTubeRecordID == patientTubeRecordID && assessListIDs.Contains(a.AssessListID) && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                          select a.AssessListID).FirstOrDefaultAsync();
        }

        public async Task<List<Detail>> GetLastCareDetailAsync(string patientTubeRecordID)
        {
            var list = await (from a in _dbContext.PatientTubeCareDetailInfos
                              join b in _dbContext.PatientTubeCareMainInfos
                              on a.PatientTubeCareMainID equals b.PatientTubeCareMainID
                              where b.PatientTubeRecordID == patientTubeRecordID && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                              select new Detail
                              {
                                  AssessListID = a.AssessListID,
                                  AssessValue = a.AssessValue,
                                  AssessDateTime = b.AssessDate.Add(b.AssessTime),
                                  AssessListGroupID = a.AssessListGroupID
                              }).ToListAsync();
             list = list.OrderBy(m=>m.AssessDateTime)
                              .GroupBy(m=>m.AssessListID)
                              .Select(m=>m.LastOrDefault())
                              .ToList();
            return list;
        }
    }
}
