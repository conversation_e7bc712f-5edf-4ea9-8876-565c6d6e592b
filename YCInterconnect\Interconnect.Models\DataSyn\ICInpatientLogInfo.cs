﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("InpatientLog")]
    public class ICInpatientLogInfo : ModifyInfo
    {
        /// <summary>
        ///	序号	
        ///</summary>
        [Key]
        [Column("InpatientLogID")]
        public int InpatientLogID { get; set; }
        /// <summary>
        ///	住院号	
        ///</summary>
        public string CaseNumber { get; set; }
        /// <summary>
        ///	病案号	
        ///</summary>
        public string ChartNo { get; set; }
        /// <summary>
        ///	病区(护理单元)代码	
        ///</summary>
        public string StationCode { get; set; }
        /// <summary>
        ///	床位号码	
        ///</summary>
        public string BedCode { get; set; }
        /// <summary>
        ///	床位代码	
        ///</summary>
        public string BedNumber { get; set; }
        /// <summary>
        ///	新病区码	
        ///</summary>
        public string NewStationCode { get; set; }
        /// <summary>
        ///	新床位编号	
        ///</summary>
        public string NewBedCode { get; set; }
        /// <summary>
        ///	新床位号	
        ///</summary>
        public string NewBedNumber { get; set; }
        /// <summary>
        ///	日志类别(Admissiom\Discharge\
        ///</summary>
        public string LogCode { get; set; }
        /// <summary>
        ///	日志说明	
        ///</summary>
        public string LogContent { get; set; }
        /// <summary>
        ///手术编码
        ///</summary>
        public string OPCode { get; set; }
        /// <summary>
        ///手术名称
        ///</summary>
        public string OPName { get; set; }
        /// <summary>
        ///	日志时间	
        ///</summary>
        public DateTime LogDatetime { get; set; }  

    }
}