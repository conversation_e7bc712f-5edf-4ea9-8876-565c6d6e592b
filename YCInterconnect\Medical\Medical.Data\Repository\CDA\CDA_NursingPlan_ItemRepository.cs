﻿/*
 * 20220-04-26 2266 CDA护理计划数据推送新增GetByDCIDList -En
 */

using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models.CDADocument;
using Microsoft.EntityFrameworkCore;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class CDA_NursingPlan_ItemRepository : ICDA_NursingPlan_ItemRepository
    {
        private readonly CDADBContext _cDADBConnect = null;
        private readonly static Logger _logger = LogManager.GetCurrentClassLogger();

        public CDA_NursingPlan_ItemRepository(CDADBContext cDADBConnect)
        {
            _cDADBConnect = cDADBConnect;
        }

        public async Task<List<CDA_NursingPlans_ItemInfo>> GetByDCID(string dCID)
        {
            return await _cDADBConnect.CDA_NursingPlans_ItemInfos.Where(m => m.DCID == dCID).ToListAsync();
        }

        public async Task<List<CDA_NursingPlans_ItemInfo>> GetByDCIDList(List<string> dCIDs)
        {
            return await _cDADBConnect.CDA_NursingPlans_ItemInfos.Where(m => dCIDs.Contains(m.DCID)).ToListAsync();
        }

        public async Task<bool> Save(CDA_NursingPlans_ItemInfo data)
        {
            try
            {
                _cDADBConnect.Add(data);

                return await _cDADBConnect.SaveChangesAsync() > 0;
            }
            catch (Exception)
            {
                _logger.Error("CDA护理计划明细表写入数据失败,异常数据:" + Common.ListToJson.ToJson(data));

                return false;
            }
        }

        public async Task<bool> Update(CDA_NursingPlans_ItemInfo data)
        {
            var old = await _cDADBConnect.CDA_NursingPlans_ItemInfos.Where(m => m.DCID == data.DCID).FirstOrDefaultAsync();

            if (old == null)
            {
                return await Save(data);
            }

            try
            {
                _cDADBConnect.Entry(old).CurrentValues.SetValues(data);

                return await _cDADBConnect.SaveChangesAsync() > 0;
            }
            catch (Exception)
            {

                _logger.Error("CDA护理计划明细表更新数据失败,异常数据:" + Common.ListToJson.ToJson(data));

                return false;
            }
        }
        public async Task<bool> Delete(List<CDA_NursingPlans_ItemInfo> datas)
        {
            try
            {
                _cDADBConnect.CDA_NursingPlans_ItemInfos.RemoveRange(datas);
                return await _cDADBConnect.SaveChangesAsync() > 0;
            }
            catch (Exception)
            {
                _logger.Error("CDA护理计划明细表删除数据失败,异常数据:" + Common.ListToJson.ToJson(datas));

                return false;
            }
        }
    }
}
