﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("NursingRecord_PressureScoreInfo")]
    public class NRPressureScoreInfo : ModifyReturnInfo
    {
        /// <summary>
        ///	自增长编号	
        ///</summary>
        [Key]
        [Column("PressureScoreID")]
        public int? PressureScoreID { get; set; }
        /// <summary>
        ///	就诊序号	
        ///</summary>
        public string CureNo { get; set; }
        /// <summary>
        ///	病区代码	
        ///</summary>
        public string WardCode { get; set; }
        /// <summary>
        ///	床号	
        ///</summary>
        public string BedNo { get; set; }
        /// <summary>
        ///	评估日期	
        ///</summary>
        public DateTime? ReportDate { get; set; }
        /// <summary>
        ///	压疮编号	
        ///</summary>
        public string PressureScoreNo { get; set; }
        /// <summary>
        ///	压疮位置	
        ///</summary>
        public string Location { get; set; }
        /// <summary>
        ///	其他压疮位置 	
        ///</summary>
        public string LocationOther { get; set; }
        /// <summary>
        ///	结局：消褪,愈合,好转,无进展,
        ///</summary>
        public string Result { get; set; }
        /// <summary>
        ///	录入人工号	
        ///</summary>
        public string ResultOperator { get; set; }
        /// <summary>
        ///	录入人姓名	
        ///</summary>
        public DateTime? ResultOpTime { get; set; }
        /// <summary>
        ///	录入时间	
        ///</summary>
        public DateTime? CreateTime { get; set; }
        /// <summary>
        ///	删除人工号	
        ///</summary>
        public string DelOpCode { get; set; }
        /// <summary>
        ///	删除时间	
        ///</summary>
        public DateTime? DelOpTime { get; set; }
        /// <summary>
        ///	删除时间	
        ///</summary>
        public DateTime? DelTime { get; set; }
        /// <summary>
        ///	状态：0 待审核，1护士长已审核
        ///</summary>
        public int? Status { get; set; }
        /// <summary>
        ///	最后更新时间	
        ///</summary>
        public DateTime? LastUpdateTime { get; set; }

        /// <summary>
        /// Medical数据库PatientScoreMain的主键，
        /// 用来给【压疮处理护理记录(NursingRecord_PressureScoreNursingEvaluation)】表确定PressureScoreID
        /// </summary>
        public string PatientScoreMainID { get; set; }

    }
}