﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Interconnect.Data.Interface;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using NLog;
using System.Linq;
using Microsoft.Extensions.Options;
using Medical.Data.Context;
using Interconnect.Data.Context;
using Medical.Common;
using Newtonsoft.Json;
using Arch.EntityFrameworkCore.UnitOfWork;

namespace Interconnect.Services
{
    public class WardDeptService : IWardDeptService
    {
        //Mdeical
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IStationToDeptInfoRepository _IStationToDeptInfoRepository;
        private readonly IStationListRepository _IStationListRepository;
        private readonly IDepartmentListRepository _IDepartmentListRepository; //科室信息
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        //Interconnect
        private readonly IUnitOfWork<DataOutConnection> _unitOfWorkOut;
        private readonly IOptions<SystemConfig> _config;
        private readonly IWardDeptRepository _IWardDeptRepository;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ILogInfoServices _ILogInfoServices;
        private readonly ICommonHelper _commonHelper;
        private readonly ISettingDescRepository _ICSettingDescriptionRepository;

       
        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";
        /// <summary>
        /// 系统默认操作人
        /// </summary>
        private string MODIFYPERSONID = "TongBu";

        public WardDeptService(
               IUnitOfWork<MedicalDbContext> UnitOfWork
             , IUnitOfWork<DataOutConnection> UnitOfWorkOut
             , IStationToDeptInfoRepository StationToDeptInfoRepository
             , IWardDeptRepository WardDeptRepository
             , IStationListRepository StationListRepository
             , IDepartmentListRepository DepartmentListRepository
             , ILogInfoServices LogInfoServices
             , IOptions<SystemConfig> config
             , ICommonHelper commonHelper
             , ISettingDescRepository settingDescriptionRepository
             , IAppConfigSettingRepository  appConfigSettingRepository
             )
        {
            _unitOfWork = UnitOfWork;
            _unitOfWorkOut = UnitOfWorkOut;
            _IStationToDeptInfoRepository = StationToDeptInfoRepository;
            _IWardDeptRepository = WardDeptRepository;
            _IStationListRepository = StationListRepository;
            _IDepartmentListRepository = DepartmentListRepository;
            _ILogInfoServices = LogInfoServices;
            _config = config;
            _commonHelper = commonHelper;
            _ICSettingDescriptionRepository = settingDescriptionRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
           
        }
        


        /// <summary>
        /// 科室病区对照
        /// </summary>
        /// <returns></returns>
        public bool SynchronizationMain()
        {
            _logger.Info("开始获取科室病区对照api");
            string apiStr = "";
            var apiStrList = _ICSettingDescriptionRepository.GetAsync(1, "10");
            if (apiStrList.Count > 0)
            {
                apiStr = apiStrList[0].Description;
            }
            else
            {
                _logger.Error("获取科室病区对照API失败");
                return false;
            }
            _logger.Info("获取床位信息数据");
            var data = new Dictionary<string, string>();
            //呼叫API获得数据
            var resultData = _commonHelper.GetInterconnectData(apiStr, data);

            var printInterfaceData = 0;
            var resultPrintDate =  _appConfigSettingRepository.GetConfigSettingValue( APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "PrintInterfaceData").Result;
            if (StringCheck.IsNumeric(resultPrintDate))
            {
                printInterfaceData = int.Parse(resultPrintDate);
            }

            if (printInterfaceData == 1)
            {
                _logger.Info("获得数据如下:" + resultData);
            }
            var interconnect_Data = new List<WardDeptInfo>();

            var result = JsonConvert.DeserializeObject<ResponseResult>(resultData);
            if (result == null)
            {
                _logger.Error("病区科室对照获取反序列化数据为null:" );
                return false;
            }
            try
            {
                interconnect_Data = JsonConvert.DeserializeObject<List<WardDeptInfo>>(result.Data.ToString());
            }
            catch (Exception ex)
            {
                _logger.Error("Json格式化失败:" + ex.ToString());
                return false;
            }
            _logger.Info("获得" + interconnect_Data.Count() + "条数据");
            _logger.Info("HOSPITALID:" + _config.Value.HospitalID);
            var medicalStationList = _IStationListRepository.GetStationList();
            var medicalDepartList = _IDepartmentListRepository.GetDepartmentList();
            if (interconnect_Data.Count > 0)
            {
                if (!SynchronizationDetail(interconnect_Data, medicalStationList, medicalDepartList))
                {
                    return false;
                }
            }
            return true;
        }

        private bool SynchronizationDetail(List<WardDeptInfo> originalList
            , List<StationListInfo> medicalStationList, List<DepartmentListInfo> MedicalDepartList)
        {
            var medicalStationToDept = _IStationToDeptInfoRepository.GetAllStationToDeptList();
            var insertlist = new List<StationToDeptInfo>();
            StationToDeptInfo t = null;
            string tableName = "WardDept";
            int failcount = 0;
            _logger.Info(tableName + " 开始进行数据同步，数据条数：" + originalList.Count);
            var stationToDepartmentID = medicalStationToDept.Max(m => m.ID);
            #region "数据同步"
            foreach (var item in originalList)
            {
                //获取病区对照信息
                var tempStation = medicalStationList.Where(m => m.StationCode.Trim() == item.WardCode.Trim()).ToList();
                if (tempStation.Count != 1)
                {
                    _logger.Error(tableName + "StationCode:[" + item.WardCode + "]查询病区对照信息错误!");
                    failcount++;
                    continue;
                }
                //获取科室对照信息
                var TempDept = MedicalDepartList.Where(m => m.DepartmentCode.Trim() == item.DeptCode.Trim()).ToList();
                if (tempStation.Count != 1)
                {
                    _logger.Error(tableName + "StationCode:[" + item.DeptCode + "] 查询科室对照信息错误!");
                    failcount++;
                    continue;
                }
                //获取科室与病区对照信息
                var tempMedicalStationToDept = medicalStationToDept.Where(m => m.StationID == tempStation[0].ID && m.DepartmentListID == TempDept[0].ID).FirstOrDefault();             //获取插入的数据,避免数据重复插入
                var tempInsertlist = insertlist.Where(m => m.StationID == tempStation[0].ID && m.DepartmentListID == TempDept[0].ID).FirstOrDefault();               //如果不存在进行新增
                if (tempMedicalStationToDept == null && tempInsertlist == null)
                {
                    stationToDepartmentID = stationToDepartmentID + 1;
                    t = new StationToDeptInfo
                    {
                        ID = stationToDepartmentID,
                        StationID = tempStation[0].ID,
                        StationCode = item.WardCode,
                        DepartmentListID = TempDept[0].ID,
                        DepartmentCode = item.DeptCode,
                        HospitalID = _config.Value.HospitalID,
                        ModifyPersonID = MODIFYPERSONID,
                        ModifyDate = DateTime.Now,
                        DeleteFlag = ""
                    };
                    insertlist.Add(t);
                  

                }
                //如果根据病区和床号查询到这条记录，则判断其他的记录是否发生了改变
                if (tempMedicalStationToDept != null)
                {
                    var upFlag = false;
                    if (item.DeleteFlag == "" && tempMedicalStationToDept.DeleteFlag == "*")
                    {
                        tempMedicalStationToDept.DeleteFlag = "";
                        upFlag = true;
                    }
                    if (item.DeleteFlag == "*")
                    {
                        tempMedicalStationToDept.DeleteFlag = "*";
                        upFlag = true;
                    }
                    if (upFlag)
                    {
                        tempMedicalStationToDept.ModifyPersonID = MODIFYPERSONID;
                        tempMedicalStationToDept.ModifyDate = DateTime.Now;
                    }
                }
            }
            #endregion
            try
            {
                _unitOfWork.GetRepository<StationToDeptInfo>().Insert(insertlist);
                _unitOfWork.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error(tableName + "||保存失败||" + ex.ToString());
                return false;
            }
            DataDelete(originalList);//数据反比
            _logger.Info(tableName, " 同步结束 成功：" + (originalList.Count - failcount).ToString() + "条！ 失败：" + failcount.ToString() + "条！");
            return true;
        }

        private bool DataDelete(List<WardDeptInfo> interconnectList)
        {
            var medicalList = _IStationToDeptInfoRepository.GetStationToDeptList();
            //对比删除
            foreach (var item in medicalList)
            {
                var tempInterconnectInfo = interconnectList.Where(m => m.WardCode == item.StationCode 
                  && m.DeptCode == item.DepartmentCode
                  && item.DeleteFlag != "*").FirstOrDefault();

                if (tempInterconnectInfo == null)
                {
                    item.DeleteFlag = "*";
                }
            }
            try
            {
                _unitOfWork.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error("删除StationToDeptInfo信息失败||" + ex.ToString());
                return false;
            }
            _logger.Info("对比删除StationToDeptInfo信息完成");
            return true;
        }

    }
}
