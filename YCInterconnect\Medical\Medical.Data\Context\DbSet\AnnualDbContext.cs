﻿using Medical.Models;
using Medical.Models.Annual;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public partial class MedicalDbContext
    {
        //负责人表
        public DbSet<AnnualProgramLeaderInfo> AnnualProgramLeaderInfos { get; set; }
        //病区表
        public DbSet<StationListInfo> StationListInfos { get; set; }
        //员工表EmployeelData
        public DbSet<UserInfo> userInfos { get; set; }
        //科别表
        public DbSet<DepartmentListInfo> departmentListInfos { get; set; }
        //文件表
        public DbSet<NursingManagementFileinfo> NursingManagementFileinfos { get; set; }


    }
}
