﻿
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
namespace Medical.Data.Repository
{
    public class PatientProfileLogRepository : IPatientProfileLogRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientProfileLogRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 取得Profile资料
        /// </summary>
        /// <returns></returns>
        public async Task<List<PatientProfileLogInfo>> GetAsync(string inpatientID)
        {
            return await _medicalDbContext.PatientProfileLog.Where(m => m.InpatientID == inpatientID).ToListAsync();
        }

        public async Task<List<PatientProfileLogInfo>> GetAsync(string inpatientID, string serialNumber)
        {
            return await _medicalDbContext.PatientProfileLog.Where(m => m.InpatientID == inpatientID && m.SerialNumber == serialNumber).ToListAsync();
        }

        /// <summary>
        /// 取得ProfileLog资料
        /// </summary>
        /// <param name="inPatientId">住院序号</param>
        /// <param name="ModelName">系统名</param>
        /// <param name="serialNumber">序号</param>
        /// <returns></returns>
        public async Task<List<PatientProfileLogInfo>> GetAsync(string inpatientID, string modelName, string serialNumber)
        {
            return await _medicalDbContext.PatientProfileLog.Where(
            m => m.InpatientID == inpatientID && m.ModelName == modelName && m.SerialNumber == serialNumber
            ).ToListAsync();
        }
        /// <summary>
        /// 根据assesslistid获取profilelog记录,AsNoTracking
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="assessListID"></param>
        /// <returns></returns>
        public async Task<PatientProfileLogInfo> GetByAssessListID(string inpatientID, int assessListID)
        {
            return await _medicalDbContext.PatientProfileLog.AsNoTracking().Where(m => m.InpatientID == inpatientID
                && m.AssessListID == assessListID).OrderBy(m => m.ProfileDate).ThenBy(m => m.ProfileTime).
                Select(m => new PatientProfileLogInfo
                {
                    ProfileDate = m.ProfileDate,
                    ProfileTime = m.ProfileTime,
                    AssessValue = m.AssessValue,
                }).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据ChartNo拿数据
        /// </summary>
        /// <param name="chartNo"></param>
        /// <param name="assessListID"></param>
        /// <returns></returns>
        public async Task<List<HISPushNRSView>> GetByAssessListIDAndChartNo(string chartNo, int assessListID)
        {
            return await _medicalDbContext.PatientProfileLog.AsNoTracking().Where(m => m.ChartNo == chartNo
               && m.AssessListID == assessListID).Select(m => new HISPushNRSView
               {
                   Date = m.ProfileDate,
                   Time = m.ProfileTime,
                   AssessValue = m.AssessValue
               }).ToListAsync();
        }
        public async Task<List<PatientProfileLogInfo>> GetAsync(string inpatientID, string modelName, int assessListID)
        {
            return await _medicalDbContext.PatientProfileLog.Where(
            m => m.InpatientID == inpatientID && m.ModelName == modelName && m.AssessListID == assessListID
            ).ToListAsync();
        }

        public async Task<List<PatientProfileLogInfo>> GetProfileLogByInpatientIDAndAssessListID(string inpatientID, int[] assessListID)
        {
            return await _medicalDbContext.PatientProfileLog.Where(m => m.InpatientID == inpatientID && assessListID.Contains(m.AssessListID)).ToListAsync();

        }


        public async Task<List<PatientProfileLogInfo>> GetByScheduleStartAndEndTime(string inpatientID, DateTime startDate, DateTime endDate)
        {
            var datas = await _medicalDbContext.PatientProfileLog.Where(m => m.InpatientID == inpatientID
                             && m.ProfileDate >= startDate
                             && m.ProfileDate <= endDate).ToListAsync();
            return datas;
        }

        public async Task<List<PatientProfileLogInfo>> GetByScheduleStartAndEndTime(string inpatientID, DateTime startDate, DateTime endDate, int assessListID)
        {
            var datas = await _medicalDbContext.PatientProfileLog.Where(m => m.InpatientID == inpatientID
                             && m.ProfileDate >= startDate
                             && m.ProfileDate <= endDate
                             && m.AssessListID == assessListID)
                             .OrderByDescending(m => m.ProfileDate).OrderByDescending(m => m.ProfileTime).ToListAsync();
            return datas;
        }

        public async Task<List<PatientProfileLogInfo>> GetByScheduleStartAndEndTimeByChartNo(string chartNo, DateTime startDate, DateTime endDate, int assessListID)
        {
            var datas = await _medicalDbContext.PatientProfileLog.Where(m => m.ChartNo == chartNo
                             && m.ProfileDate >= startDate
                             && m.ProfileDate <= endDate
                             && m.AssessListID == assessListID)
                             .OrderByDescending(m => m.ProfileDate).OrderByDescending(m => m.ProfileTime).ToListAsync();
            return datas;
        }
        /// <summary>
        /// 起訖時間內取得profile日誌
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="assessListID"></param>
        /// <returns></returns>
        public async Task<List<PatientProfileLogInfo>> GetByScheduleStartAndEndTimeByInpatientID(string inpatientID, DateTime startDate, DateTime endDate, int assessListID)
        {
            var datas = await _medicalDbContext.PatientProfileLog.Where(m => m.InpatientID == inpatientID
                             && m.ProfileDate >= startDate
                             && m.ProfileDate <= endDate
                             && m.AssessListID == assessListID)
                             .OrderByDescending(m => m.ProfileDate).OrderByDescending(m => m.ProfileTime).ToListAsync();
            return datas;
        }

        public async Task<List<PatientProfileLogInfo>> GetNoTracking(string inpatientID, int[] assessListID)
        {
            return await _medicalDbContext.PatientProfileLog.Where(m => m.InpatientID == inpatientID
            && assessListID.Contains(m.AssessListID))
            .AsNoTracking()
            .ToListAsync();
        }

        /// <summary>
        /// 获取PatientProfileLogInfo的数据清单
        /// <param name="patientProfileLogID">patientProfileLog的唯一标识</param>
        /// </summary>
        /// <returns></returns>
        public async Task<PatientProfileLogInfo> GetBypatientProfileLogIDAsync(int patientProfileLogID)
        {
            return await _medicalDbContext.PatientProfileLog.Where(m => m.ID == patientProfileLogID).FirstOrDefaultAsync();
        }

        public async Task<List<int>> GetKey(string chartNo)
        {
            return await _medicalDbContext.PatientProfileLog.Where(m => m.ChartNo == chartNo).Select(m => m.AssessListID).ToListAsync();
        }

        public async Task<List<int>> GetKeyByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientProfileLog.Where(m => m.InpatientID == inpatientID).Select(m => m.AssessListID).ToListAsync();
        }

        public async Task<List<PatientProfileLogInfo>> GetShiftProfileLogAsync(string[] inpatientID, int[] assessListID)
        {
            return await _medicalDbContext.PatientProfileLog.Where(m => inpatientID.Contains(m.InpatientID)
            && assessListID.Contains(m.AssessListID)).ToListAsync();
        }

        /// <summary>
        /// 获取profileLog数据
        /// </summary>
        /// <param name="inpatientID">患者住院ID</param>
        /// <param name="assessListIDs">AssessListID集合</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns></returns>
        public async Task<List<ProfileDetail>> GetProfileData(string inpatientID, int[] assessListIDs, DateTime startTime, DateTime endTime)
        {
            var startDate = startTime.Date;
            var endDate = endTime.Date;

            var logs = await _medicalDbContext.PatientProfileLog.Where(m => m.InpatientID == inpatientID
                             && m.ProfileDate >= startDate
                             && m.ProfileDate <= endDate
                             && assessListIDs.Contains(m.AssessListID)).Select(m => new ProfileDetail
                             {
                                 ProfileDate = m.ProfileDate,
                                 ProfileTime = m.ProfileTime,
                                 AssessListID = m.AssessListID,
                                 AssessValue = m.AssessValue
                             }).ToListAsync();

            logs = logs.Where(m => m.ProfileDate.Add(m.ProfileTime) >= startTime && m.ProfileDate.Add(m.ProfileTime) <= endTime).ToList();
            return logs;
        }
        /// <summary>
        /// 获取最近的profilelog记录,AsNoTracking
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="assessListID">评估流水号</param>
        /// <returns></returns>
        public async Task<PatientProfileLogInfo> GetLatestProfileByAssessListID(string inpatientID, int assessListID)
        {
            return await _medicalDbContext.PatientProfileLog.AsNoTracking().Where(m => m.InpatientID == inpatientID
                && m.AssessListID == assessListID)
                .OrderByDescending(m => m.ProfileDate).ThenByDescending(m => m.ProfileTime).
                Select(m => new PatientProfileLogInfo
                {
                    ProfileDate = m.ProfileDate,
                    ProfileTime = m.ProfileTime,
                    AssessValue = m.AssessValue,
                }).FirstOrDefaultAsync();
        }
    }

}
