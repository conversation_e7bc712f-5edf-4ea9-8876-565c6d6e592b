﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientRescueMedicationRepository : IPatientRescueMedicationRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientRescueMedicationRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<List<PatientRescueMedicationInfo>> GetAllAsync(string mainID, DateTime rescueCareMainItem)
        {
            return await _medicalDbContext.PatientRescueMedicationInfos.Where(t => t.PatientRescueMainID == mainID && t.RecordTime == rescueCareMainItem).ToListAsync();
        }

        public async Task<List<PatientRescueMedicationInfo>> GetAsync(string mainID, DateTime? recordTime)
        {
            var query = _medicalDbContext.PatientRescueMedicationInfos.Where(t => t.PatientRescueMainID == mainID && t.DeleteFlag != "*");
            if (recordTime != null)
            {
                return await query.Where(t => t.RecordTime == recordTime.Value).ToListAsync();
            }
            return null;
        }

        public async Task<List<PatientRescueMedicationInfo>> GetAsync(string patientRescueCareMainID)
        {
            return await _medicalDbContext.PatientRescueMedicationInfos.Where(t => t.PatientRescueMainID == patientRescueCareMainID
            && t.DeleteFlag != "*").ToListAsync();
        }
    }
}