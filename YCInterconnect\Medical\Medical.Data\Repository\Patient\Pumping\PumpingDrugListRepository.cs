﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PumpingDrugListRepository : IPumpingDrugListRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public PumpingDrugListRepository(
            MedicalDbContext medicalDbContext,
            IMemoryCache memoryCache,
            SessionCommonServer sessionCommonServer,
            GetCacheService getCacheService
            )
        {
            _medicalDbContext = medicalDbContext;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }
        /// <summary>
        /// 获取医院所有泵入药物
        /// </summary>
        /// <param name="drugCodeIsNullFlag">泵入药物唯一码是否为空</param>
        /// <returns></returns>
        public async Task<List<PumpingDrugListInfo>> GetDataByDrugCodeNullFlag(bool? drugCodeIsNullFlag)
        {
            var list = await this.GetAllAsync<PumpingDrugListInfo>();
            if (drugCodeIsNullFlag.HasValue && drugCodeIsNullFlag.Value)
            {
                list = list.Where(m => !m.DrugCode.HasValue).ToList();
            }
            else
            {
                list = list.Where(m => m.DrugCode.HasValue).ToList();
            }
            return list;
        }

        /// <summary>
        /// 根据药物类型获取泵入药物
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public async Task<List<PumpingDrugListInfo>> GetDataByDrugType(string type)
        {
            var datas = await this.GetAllAsync<PumpingDrugListInfo>();
            return datas.Where(m => m.PumpingDrugType == type && !m.DrugCode.HasValue).ToList();
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<PumpingDrugListInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            return await _medicalDbContext.PumpingDrugListInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.PumpingDrugList.GetKey(_sessionCommonServer);
        }
    }
}
