﻿
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Interconnect.Data

{
    public class EmployeeStationSwitchRepository : IEmployeeStationSwitchRepository
    {
        private DataOutConnection _DataOutConnection = null;

        public EmployeeStationSwitchRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }

        public  List<EmployeeStationSwitchInfo> GetAllAsync()
        {
            try
            {
                return  _DataOutConnection.EmployeeStationSwitchInfos.ToList();
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}
