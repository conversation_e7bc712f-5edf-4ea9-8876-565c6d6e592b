﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("WardDept")]
    public class WardDeptInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///病房代码
        ///</summary>
        public string WardCode { get; set; }
        /// <summary>
        ///病房名称
        ///</summary>
        public string WardName { get; set; }
        /// <summary>
        ///部门编码
        ///</summary>
        public string DeptCode { get; set; }
        /// <summary>
        ///部门名称
        ///</summary>
        public string DeptName { get; set; }
     
    }
}