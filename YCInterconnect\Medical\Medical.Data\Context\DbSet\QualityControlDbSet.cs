﻿using Medical.Models;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public partial class MedicalDbContext
    {

        /// <summary>
        /// 考核项目配置档
        /// </summary>
        public DbSet<QCCheckAppraiseContentInfo> QCCheckAppraiseContentInfos { get; set; }
        /// <summary>
        /// 质控模板的主题表
        /// </summary>
        public DbSet<QCCheckSubjectInfo> QCSubjactiveInfos { get; set; }
        /// <summary>
        /// 质控字典档
        /// </summary>
        public DbSet<QCCheckDictionaryMainInfo> QCDictionarys { get; set; }
        /// <summary>
        /// 不符合项字典表
        /// </summary>
        public DbSet<QCCheckDictionaryDetailInfo> QCCheckDictionaryDetailInfos { get; set; }
        /// <summary>
        /// 质控主表
        /// </summary>
        public DbSet<QCCheckRecordInfo> QualityControlMainInfos { get; set; }
        /// <summary>
        /// 质控明细表
        /// </summary>
        public DbSet<QCCheckMainInfo> QualityControlDetails { get; set; }
        /// <summary>
        /// 不符合项结果存储表
        /// </summary>
        public DbSet<QCCheckDetailInfo> QCCheckDetailInfos { get; set; }
    }
}
