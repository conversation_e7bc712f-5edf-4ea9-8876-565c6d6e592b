﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Interconnect.ViewModels
{
    public class RiskData
    {
        /// <summary>
        /// 病人住院唯一号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 病人在院唯一号
        /// </summary>
        public string ChartNo { get; set; }
        /// <summary>
        /// 病人姓名
        /// </summary>
        public string PatientName { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public string Gender { get; set; }
        /// <summary>
        /// 病区码
        /// </summary>
        public string StationCode { get; set; }
        /// <summary>
        /// 床位号
        /// </summary>
        public string BedNumber { get; set; }
        /// <summary>
        /// 注记清单
        /// </summary>
        public string MarkList { get; set; }
    }
}