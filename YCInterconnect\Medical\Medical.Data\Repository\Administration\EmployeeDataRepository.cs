﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class EmployeeDataRepository : IEmployeeDataRepository
    {
        private readonly MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly GetCacheService _getCacheService;

        /// <summary>
        /// 离职状态
        /// </summary>
        private const string EMPLOYEEDATA_LEAVESTATUS_O = "0";

        public EmployeeDataRepository(MedicalDbContext db, IMemoryCache memoryCache, GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _getCacheService = getCacheService;
        }

        public async Task<EmployeeDataInfo> GetByEmployeeDataIDAsync(int employeeDataID)
        {
            return await _medicalDbContext.EmployeeDataInfos.Where(m => m.EmployeeDataID == employeeDataID).SingleOrDefaultAsync();
        }

        public async Task<EmployeeDataInfo> GetByEmployeeIDAsync(string EmployeeID)
        {
            return await _medicalDbContext.EmployeeDataInfos.Where(m => m.EmployeeID == EmployeeID).SingleOrDefaultAsync();
        }

        public async Task<List<EmployeeDataInfo>> GetEmployeeDataByPositionID(string positionID, List<int> stationList)
        {
            List<EmployeeDataInfo> employeeDataList = new List<EmployeeDataInfo>();
            foreach (var stationID in stationList)
            {
                var employeeData = await _medicalDbContext.EmployeeDataInfos.Where(m => m.DeleteFlag != "*" && m.PositionID == positionID && m.StationID == stationID.ToString()).ToListAsync();
                employeeDataList.AddRange(employeeData);
            }
            return employeeDataList;
        }

        public async Task<List<EmployeeDataInfo>> GetListAsync()
        {
            return await _medicalDbContext.EmployeeDataInfos.ToListAsync();
        }

        public async Task<List<EmployeeDataInfo>> GetWorkingEmployeeListAsync()
        {
            return await _medicalDbContext.EmployeeDataInfos.Where(m => m.DeleteFlag != "*" && m.LeaveStatus == EMPLOYEEDATA_LEAVESTATUS_O).ToListAsync();
        }

        public async Task<EmployeeDataInfo> GetEmployeeByID(string EmployeeID)
        {
            return await _medicalDbContext.EmployeeDataInfos.Where(m => m.EmployeeID == EmployeeID).FirstOrDefaultAsync();
        }

        public async Task<List<EmployeeDataInfo>> GetWorkingEmployeeListByDepartmentIDAsync(string departmentID)
        {
            var result = await this.GetAllAsync<EmployeeDataInfo>();
            return result.Where(m => m.DeleteFlag != "*"
            && m.LeaveStatus == EMPLOYEEDATA_LEAVESTATUS_O
            && m.DepartmentID == departmentID).ToList();
        }

        public async Task<List<EmployeeDataInfo>> GetWorkingEmployeeListByEmployeeNameAsync(string employeeName)
        {
            return await _medicalDbContext.EmployeeDataInfos.Where(m => m.DeleteFlag != "*"
            && m.EmployeeName == employeeName).ToListAsync();
        }

        public async Task<List<EmployeeDataInfo>> GetWorkingEmployeeListByParamsAsync(EmployeeDataInfo employeeDataInfo)
        {

            return await _medicalDbContext.EmployeeDataInfos.Where(m => m.DeleteFlag != "*"
            && m.LeaveStatus == EMPLOYEEDATA_LEAVESTATUS_O).ToListAsync();
        }

        public async Task<List<EmployeeDataInfo>> GetEmployeeDataByPositionIDAsync(string positionID, List<int> stationList)
        {
            List<EmployeeDataInfo> EmployeeDataList = new List<EmployeeDataInfo>();
            foreach (var stationID in stationList)
            {
                var list = await _medicalDbContext.EmployeeDataInfos.Where(m => m.PositionID == positionID && m.StationID == stationID.ToString() && m.DeleteFlag != "*").ToListAsync();
                if (list != null)
                {
                    EmployeeDataList.AddRange(list);
                }
            }
            return EmployeeDataList;
        }

        public async Task<List<EmployeeDataInfo>> GetNewListByEmployeeIDsAsync(int[] employeeIDs)
        {
            List<EmployeeDataInfo> employeeDataInfos = new List<EmployeeDataInfo>();
            foreach (var employeeID in employeeIDs)
            {
                var employeeDataInfo = await GetByEmployeeIDAsync(employeeID.ToString());
                if (employeeDataInfo != null)
                {
                    employeeDataInfos.Add(employeeDataInfo);
                }
            }
            return employeeDataInfos;
        }
        /// <summary>
        /// 根据HRHREmployeeID获取数据
        /// </summary>
        /// <param name="HREmployeeID"></param>
        /// <returns></returns>
        public async Task<EmployeeDataInfo> GetEmployeeDataByHREmployeeID(string HREmployeeID)
        {
            EmployeeDataInfo employeeDataInfoData = new EmployeeDataInfo();
            var employeeDataInfos = await _medicalDbContext.EmployeeDataInfos.Where(m => m.HREmployeeID == HREmployeeID && m.DeleteFlag != "*").ToListAsync();
            if (employeeDataInfos.Count != 0)
            {
                employeeDataInfoData = employeeDataInfos[0];
            }
            return employeeDataInfoData;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<EmployeeDataInfo>>(key, GetDataBaseListData);
        }
        /// <summary>
        /// 获取数据库数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            var data = await _medicalDbContext.EmployeeDataInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
            return data;
        }


        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.EmployeeData.ToString();
        }

        /// <summary>
        /// 获取多个科室的人员数据
        /// </summary>
        /// <param name="IDArr"></param>
        /// <returns></returns>
        public async Task<List<EmployeeDataInfo>> GetWorkingEmployeeListByDepartmentIDList(List<string> IDArr)
        {
            var c = await this.GetAllAsync<EmployeeDataInfo>();
            return await _medicalDbContext.EmployeeDataInfos.Where(m => m.DeleteFlag != "*"
            && m.LeaveStatus == EMPLOYEEDATA_LEAVESTATUS_O
            && IDArr.Contains(m.DepartmentID)).ToListAsync();
        }
        /// <summary>
        /// 根据主键ID数组获取数据
        /// </summary>
        /// <param name="IDArr"></param>
        /// <returns></returns>
        public async Task<List<EmployeeDataInfo>> GetDataByEmployeeIDArr(List<string> IDArr)
        {
            var c = await this.GetAllAsync<EmployeeDataInfo>();
            return await _medicalDbContext.EmployeeDataInfos.Where(m => m.DeleteFlag != "*" && IDArr.Contains(m.EmployeeID)).ToListAsync();
        }
        /// <summary>
        /// 查询员工历史敏感数据
        /// </summary>
        /// <param name="employeeID"></param>
        /// <param name="offDateTime"></param>
        /// <returns></returns>
        public async Task<List<EmployeeDataInfo>> GetEmployeeHistoryList(string employeeID, DateTime offDateTime)
        {
            if (employeeID == null)
            {
                return await _medicalDbContext.EmployeeDataInfos.Where(m => m.ModifyDate <= offDateTime && m.ModifyPersonID != "ElectBoardUser").ToListAsync();
            }
            else
            {
                return await _medicalDbContext.EmployeeDataInfos.Where(m => m.EmployeeID == employeeID && m.ModifyPersonID != "ElectBoardUser").ToListAsync();
            }
        }
        /// <summary>
        /// 通过userID获取数据集
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<EmployeeDataInfo> GetEmployeeID(string employeeID)
        {
            return await (from a in _medicalDbContext.EmployeeDataInfos
                          join b in _medicalDbContext.EmployeelDataInfos
                           on a.EmployeeID equals b.HREmployeeID
                          where a.DeleteFlag != "*" && b.DeleteFlag != "*" && b.UserID == employeeID
                          select a).FirstOrDefaultAsync();
        }
    }
}