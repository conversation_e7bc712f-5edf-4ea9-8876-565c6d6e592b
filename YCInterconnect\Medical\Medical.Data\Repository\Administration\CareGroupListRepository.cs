﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;


namespace Medical.Data
{

    public class CareGroupListRepository : ICareGroupListRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly GetCacheService _getCacheService;

        public CareGroupListRepository(
            MedicalDbContext medicalDbContext
            , IMemoryCache memoryCache
            , GetCacheService getCacheService
            )
        {
            _medicalDbContext = medicalDbContext;
            _memoryCache = memoryCache;
            _getCacheService = getCacheService;
        }

        public async Task<List<CareGroupListInfo>> GetCareGroupListByStationID(int stationID)
        {
            var jobGroupListInfos = await GetCacheAsync() as List<CareGroupListInfo>;
            return jobGroupListInfos.Where(m => m.StationID == stationID).ToList();
        }

        public async Task<CareGroupListInfo> GetCareGroupListByID(int careGroupID)
        {
            return await _medicalDbContext.CareGroupListInfos.Where(m => m.CareGroupID == careGroupID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<CareGroupListInfo>>(key, GetDataBaseListData);
        }
        /// <summary>
        /// 获取数据库数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            var data = await _medicalDbContext.CareGroupListInfos.Where(m =>  m.DeleteFlag != "*").ToListAsync();
            return data;
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.NurseShift.ToString();
        }
    }
}