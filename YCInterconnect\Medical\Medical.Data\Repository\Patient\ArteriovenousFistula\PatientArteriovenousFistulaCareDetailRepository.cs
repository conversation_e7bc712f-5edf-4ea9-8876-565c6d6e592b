﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientArteriovenousFistulaCareDetailRepository : IPatientArteriovenousFistulaCareDetailRepository
    {
        private readonly MedicalDbContext _db = null;

        public PatientArteriovenousFistulaCareDetailRepository(MedicalDbContext medicalDbContext)
        {
            _db = medicalDbContext;
        }

        /// <summary>
        /// 根据CareMainID获取明细表数据
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        public async Task<List<PatientArteriovenousFistulaCareDetailInfo>> GetDetailsByMainID(string mainID)
        {
            return await _db.PatientArteriovenousFistulaCareDetailInfos.Where(m => m.PatientArteriovenousFistulaCareMainID == mainID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取多个维护记录对应的明细数据
        /// </summary>
        /// <param name="careMainIDs">主记录ID集</param>
        /// <returns></returns>
        public async Task<List<PatientArteriovenousFistulaCareDetailInfo>> GetByCareMainIDsAsNoTrackAsync(List<string> careMainIDs)
        {
            return await _db.PatientArteriovenousFistulaCareDetailInfos.AsNoTracking().
                Where(m => careMainIDs.Contains(m.PatientArteriovenousFistulaCareMainID) && m.DeleteFlag != "*").ToListAsync();

        }
    }
}
