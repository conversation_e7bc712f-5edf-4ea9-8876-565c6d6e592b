﻿using Microsoft.CSharp.RuntimeBinder;

namespace Medical.Common
{
    /// <summary>
    /// 设置前端请求View公共属性
    /// </summary>
    public static class SetSubmitViewUtil
    {
        /// <summary>
        /// 设置公共属性
        /// </summary>
        /// <param name="view">拥有Session中共有属性的View</param>
        /// <param name="session"></param>
        public static T SetCommonProperty<T>(this T view, Session session)
        {
            SetProperty(view, "HospitalID", session);
            SetProperty(view, "Language", session);
            SetProperty(view, "UserID", session);
            SetProperty(view, "ClientType", session);
            SetProperty(view, "StationID", session);
            return view;
        }

        /// <summary>
        /// 设置属性
        /// </summary>
        /// <param name="view">传递的View</param>
        /// <param name="propName">属性名</param>
        /// <param name="session">session</param>
        private static void SetProperty(dynamic view, string propName, Session session)
        {
            try
            {
                _ = propName switch
                {
                    "HospitalID" => view.HospitalID ??= session.HospitalID,
                    "Language" => view.Language = view.Language == 0 ? session.Language : view.Language,
                    "UserID" => view.UserID ??= session.UserID,
                    "ClientType" => view.ClientType ??= session.ClientType,
                    "StationID" => view.StationID = view.StationID == 0 ? session.StationID : view.StationID,
                    _ => view,
                };
            }
            catch (RuntimeBinderException)
            {
                return;
            }
        }
    }
}
