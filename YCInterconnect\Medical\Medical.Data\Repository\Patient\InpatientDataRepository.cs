﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.Data;
using Medical.ViewModels.Interface;
using Medical.ViewModels.Verify;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class InpatientDataRepository : IInpatientDataRepository
    {
        private readonly MedicalDbContext _dbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly ISettingDescriptionRepository _settingDescriptionRepository;
        private readonly IStationShiftRepository _stationShiftRepository;
        private readonly IOptions<SystemConfig> _config;
        private readonly IBedListRepository _bedListRepository;
        /// <summary>
        /// 转出事件ID
        /// </summary>
        private const int EVENTSETTING_TRANSFEROUT_ID = 2875;
        /// <summary>`
        /// 出院事件ID
        /// </summary>
        protected const int EVENTSETTING_DISCHARGE_ID = 2873;

        public InpatientDataRepository(

              MedicalDbContext db
            , SessionCommonServer sessionCommonServer
            , ISettingDescriptionRepository settingDescriptionRepository
            , IStationShiftRepository stationShiftRepository
            , IOptions<SystemConfig> config
             , IBedListRepository bedListRepository
            )
        {
            _dbContext = db;
            _sessionCommonServer = sessionCommonServer;
            _settingDescriptionRepository = settingDescriptionRepository;
            _stationShiftRepository = stationShiftRepository;
            _config = config;
            _bedListRepository = bedListRepository;
        }

        #region NewRepository

        /// <summary>
        /// 获取全部患者信息（包含删除，包含出院）
        /// </summary>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetListAsync()
        {
            return await _dbContext.InpatientDatas.ToListAsync();
        }

        /// <summary>
        /// 通过inPatientID获取患者数据（包含出院）
        /// </summary>
        /// <param name="inPatientID">住院序号</param>
        /// <returns></returns>
        public async Task<InpatientDataInfo> GetAsync(string inPatientID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.ID == inPatientID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<InpatientDataInfo> GetAsNoTrackAsync(string inPatientID)
        {
            return await _dbContext.InpatientDatas.AsNoTracking().Where(m => m.ID == inPatientID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 通过bedID获取在院患者数据
        /// </summary>
        /// <param name="bedID">床位序号</param>
        /// <param name="hospitalID">医院序号</param>
        /// <returns>病人住院记录</returns>
        public async Task<InpatientDataInfo> GetByInpatientBedIDAsync(int bedID, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.HospitalID == hospitalID && m.BedID == bedID
            && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<InpatientDataInfo> GetByInpatientBedIDNoTrackingAsync(int bedID, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.HospitalID == hospitalID && m.BedID == bedID
            && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DeleteFlag != "*").AsNoTracking().FirstOrDefaultAsync();
        }

        /// <summary>
        /// 透过床位序号或取病人基本数据
        /// </summary>
        /// <param name="bedID">床位序号</param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientListByBedIDAsync(int bedID, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.HospitalID == hospitalID && m.BedID == bedID && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DeleteFlag != "*").ToListAsync();
        }

        #endregion NewRepository

        /// <summary>
        /// 透过住院序号取得病人归档标记
        /// </summary>
        /// <param name="inPatientID">住院序号</param>
        /// <returns></returns>
        public async Task<string> GetEMRArchivingFlagAsync(string inPatientID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.ID == inPatientID && m.DeleteFlag != "*").Select(m => m.EMRArchivingFlag).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取指定出院，还没有归档的病人
        /// </summary>
        /// <param name="inPatientID">住院序号</param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetUNEMRArchiving(int days)
        {
            var dateTimeNow = DateTime.Now.AddDays(-days);
            var inpatientDatas = await _dbContext.InpatientDatas.Where(m => m.DischargeDate.HasValue
            && m.DischargeDate.Value.Date <= dateTimeNow.Date
            && InHospitalStatus.DISCHARGEDHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)
            && m.DeleteFlag != "*" && m.EMRArchivingFlag != "*"
            ).ToListAsync();
            return inpatientDatas.Where(m => m.DischargeDate.Value.Add(m.DischargeTime.Value) <= dateTimeNow).ToList();
        }

        /// <summary>
        /// 通过住院号，获取病人基本信息，电子病历使用
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<PatientFilesHeaderView> GetPatientFilesHeaderByID(string inpatientID)
        {
            var result = (from inpatient in _dbContext.InpatientDatas
                          join patientBase in _dbContext.PatientBasicDatas
                          on new { inpatient.PatientID } equals new { patientBase.PatientID }
                          where inpatient.ID == inpatientID
                          select new PatientFilesHeaderView()
                          {
                              HospitalID = inpatient.HospitalID,
                              InpatientID = inpatient.ID,
                              CaseNumber = inpatient.CaseNumber,
                              ChartNo = inpatient.ChartNo,
                              PatientName = patientBase.PatientName,
                              Gender = patientBase.Gender,
                              DateOfBirth = patientBase.DateOfBirth,
                              AdmissionDate = inpatient.AdmissionDate,
                              Diagnose = inpatient.Diagnosis,
                              NursingLevel = inpatient.NursingLevel,
                              BedNumber = inpatient.BedNumber,
                              DischargeDate = inpatient.DischargeDate,
                              InpatientNo = inpatient.LocalCaseNumber,
                              Age = inpatient.Age.ToString(),
                              AgeDetail = inpatient.AgeDetail ?? "",
                              StationID = inpatient.StationID,
                              DeptID = inpatient.DepartmentListID
                          }
                        );
            return await result.FirstOrDefaultAsync();
        }

        /// <summary>
        /// 通过caseNumber获取患者数据（包含出院）
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        public async Task<InpatientDataInfo> GetAsyncByCaseNumber(string CaseNumber, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.CaseNumber == CaseNumber && m.HospitalID == hospitalID
            && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 通过caseNumber获取患者数据（包含出院）
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        public async Task<InpatientDataInfo> GetAsyncByCaseNumber(string caseNumber)
        {
            return await _dbContext.InpatientDatas.Where(m => m.CaseNumber == caseNumber && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<InpatientDataInfo> GetByCaseNumberAsNoTracking(string caseNumber)
        {
            return await _dbContext.InpatientDatas.Where(m => m.CaseNumber == caseNumber && m.DeleteFlag != "*").AsNoTracking().FirstOrDefaultAsync();
        }
        /// <summary>
        /// 透过住院号(CaseNumber)取得病人基本讯息
        /// </summary>
        /// <param name="CaseNumber">住院序号</param>
        /// <returns></returns>
        public async Task<InpatientDataInfo> GetInpatientViewByCaseNumber(string CaseNumber)
        {
            return await _dbContext.InpatientDatas.Where(m => m.CaseNumber == CaseNumber && m.DeleteFlag != "*")
                .Select(m => new InpatientDataInfo
                {
                    ID = m.ID,
                    StationID = m.StationID,
                    DepartmentListID = m.DepartmentListID,
                    BedID = m.BedID,
                    ChartNo = m.ChartNo,
                    PatientID = m.PatientID,
                    BedNumber = m.BedNumber,
                    CaseNumber = m.CaseNumber,
                    NursingLevel = m.NursingLevel
                }
                ).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 透过住院号(CaseNumber)取得病人基本讯息
        /// </summary>
        /// <param name="CaseNumber">住院序号</param>
        /// <returns></returns>
        public InpatientDataInfo GetInpatientDataByCaseNumber(string CaseNumber, bool includeDischarge = true)
        {
            if (includeDischarge)
            {
                return _dbContext.InpatientDatas.Where(m => m.CaseNumber == CaseNumber && m.DeleteFlag != "*").FirstOrDefault();
            }
            else
            {
                return _dbContext.InpatientDatas.Where(m => m.CaseNumber == CaseNumber && m.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).FirstOrDefault();
            }
        }

        public async Task<List<InpatientDataInfo>> GetListAsyncByCaseNumber(string CaseNumber)
        {
            return await _dbContext.InpatientDatas.Where(m => m.CaseNumber == CaseNumber).ToListAsync();
        }

        /// <summary>
        /// 通过localCaseNumber获取出院数据
        /// </summary>
        /// <param name="localCaseNumber"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientDischargeListByLocalCaseNumber(string localCaseNumber)
        {
            return await _dbContext.InpatientDatas.Where(m => m.LocalCaseNumber == localCaseNumber
            && InHospitalStatus.DISCHARGEDHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)
            && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 通过localCaseNumber获取出院数据
        /// </summary>
        /// <param name="localCaseNumber"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientDischargeListByLocalCaseNumber(string localCaseNumber, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.LocalCaseNumber == localCaseNumber
            && InHospitalStatus.DISCHARGEDHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)
            && m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 取得住院病人清单
        /// </summary>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientAsync()
        {
            return await _dbContext.InpatientDatas.Where(m => InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DeleteFlag != "*")
                           .Select(m => new InpatientDataInfo
                           {
                               ChartNo = m.ChartNo,
                               ID = m.ID,
                               PatientID = m.PatientID,
                               DepartmentListID = m.DepartmentListID,
                               StationID = m.StationID,
                               BedID = m.BedID,
                               BedNumber = m.BedNumber,
                               CaseNumber = m.CaseNumber,
                               Diagnosis = m.Diagnosis
                           }).ToListAsync();
        }

        public async Task<List<InpatientDataInfo>> GetByInpatientID(string[] inpatientIDs)
        {
            return await _dbContext.InpatientDatas.Where(m => inpatientIDs.Contains(m.ID)).ToListAsync();
        }

        public async Task<List<InpatientDataInfo>> GetInpatientDischargeList(int departmentListID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.DepartmentListID == departmentListID && m.DischargeDate.HasValue).ToListAsync();
        }

        public async Task<List<InpatientDataInfo>> GetAsyncByStaionID(int stationID)
        {
            var session = await _sessionCommonServer.GetSession();
            return await _dbContext.InpatientDatas.Where(m => m.HospitalID == session.HospitalID
                                && m.StationID == stationID && m.DeleteFlag != "*"
                                && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).ToListAsync();
        }

        public async Task<List<InpationDataView>> GetPatientDataViewByStaionID(int stationID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.StationID == stationID && m.DeleteFlag != "*")
            .Select(n => new InpationDataView
            {
                StationID = n.StationID,
                ID = n.ID,
                BedID = n.BedID,
                BedNumber = n.BedNumber,
                DischargeDate = n.DischargeDate,
                NursingProcedureCode = n.NursingProcedureCode
            }).ToListAsync();
        }

        /// <summary>
        /// 根据科室获取信息
        /// </summary>
        /// <param name="stationID"></param>
        /// <returns></returns>

        public async Task<InpatientDataInfo> GetByChartNo(string hospitalID, string chartNo)
        {
            if (hospitalID == null)
            {
                var session = await _sessionCommonServer.GetSession();
                hospitalID = session.HospitalID;
            }
            return await _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && m.ChartNo == chartNo
            && m.HospitalID == hospitalID && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).FirstOrDefaultAsync();
        }

        public async Task<InpatientDataInfo> GetInPatientData(string inpatientID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && m.ID == inpatientID && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).FirstOrDefaultAsync();
        }

        public async Task<List<InpatientDataInfo>> GetDischarge(DateTime dischargeDate)
        {
            return await _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && m.DischargeDate <= dischargeDate).ToListAsync();
        }
        /// <summary>
        /// 取得病人最后一次评估时间
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public async Task<InpatientLastAssessement> GetInpatientLastAssessement(string inpatientID, int language)
        {
            var query = await (from a in _dbContext.InpatientDatas
                               join b in _dbContext.AssessMains on new { InpatientID = a.ID, a.DeleteFlag } equals new { b.InpatientID, b.DeleteFlag }
                               join c in _dbContext.SettingDescriptions on new { SettingTypeCode = "ProblemRenew" + a.NursingLevel ?? "", Language = language } equals new { c.SettingTypeCode, c.Language }
                               join d in _dbContext.StationShifts on new { a.StationID, a.HospitalID } equals new { d.StationID, d.HospitalID }
                               join e in _dbContext.SettingDescriptions on new { SettingTypeCode = "NursingPlanRenewShift", TypeValue = d.Shift, Language = language } equals new { e.SettingTypeCode, e.TypeValue, e.Language }
                               where a.ID == inpatientID && a.DeleteFlag != "*"
                               select new InpatientLastAssessement
                               {
                                   InpatientID = a.ID,
                                   AssessDate = b.AddDate,
                                   AssessTime = b.AssessTime,
                                   RenewDay = c.TypeValue,
                                   ShiftStartTime = d.ShiftStartTime,
                                   ShiftEndTime = d.ShiftEndTime,
                                   NursingProcedureCode = a.NursingProcedureCode,
                               }).OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime).ToListAsync();

            return query.GroupBy(m => m.InpatientID).Select(m => m.First()).FirstOrDefault();
        }
        /// <summary>
        /// 取得病人最后一次评估时间
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="langauge"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<InpatientLastAssessement> GetInpatientLastAssessement(string inpatientID, int langauge, string hospitalID)
        {
            var query = await (from a in _dbContext.InpatientDatas
                               join b in _dbContext.AssessMains on new { InpatientID = a.ID, a.DeleteFlag } equals new { b.InpatientID, b.DeleteFlag }
                               join c in _dbContext.SettingDescriptions on new { SettingTypeCode = "ProblemRenew" + a.NursingLevel ?? "", Language = langauge } equals new { c.SettingTypeCode, c.Language }
                               join d in _dbContext.StationShifts on new { a.StationID, a.HospitalID } equals new { d.StationID, d.HospitalID }
                               join e in _dbContext.SettingDescriptions on new { SettingTypeCode = "NursingPlanRenewShift", TypeValue = d.Shift, Language = langauge } equals new { e.SettingTypeCode, e.TypeValue, e.Language }
                               where a.ID == inpatientID && a.DeleteFlag != "*"
                               && a.HospitalID == hospitalID && c.HospitalID == hospitalID
                               && d.HospitalID == hospitalID && e.HospitalID == hospitalID
                               select new InpatientLastAssessement
                               {
                                   InpatientID = a.ID,
                                   AssessDate = b.AssessDate,
                                   AssessTime = b.AssessTime,
                                   RenewDay = c.TypeValue,
                                   ShiftStartTime = d.ShiftStartTime,
                                   ShiftEndTime = d.ShiftEndTime,
                                   NursingProcedureCode = a.NursingProcedureCode,
                               }).OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime).ToListAsync();

            return query.GroupBy(m => m.InpatientID).Select(m => m.First()).FirstOrDefault();
        }
        /// <summary>
        /// 取得需要更新ProfileMark的数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<UpDateProfileMarkView>> GetNeedUpDateMark()
        {
            var query = await (from a in _dbContext.InpatientDatas
                               join b in _dbContext.PatientProfileMark on a.ChartNo equals b.ChartNo
                               into temp
                               from b in temp.DefaultIfEmpty()
                               where a.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(a.InHospitalStatus ?? -1) && b.DeleteFlag != "*"
                               && a.BedID != 0
                               select new UpDateProfileMarkView
                               {
                                   HospitalID = a.HospitalID,
                                   InpatientID = a.ID,
                                   PatientID = a.PatientID,
                                   CaseNumber = a.CaseNumber,
                                   ChartNo = a.ChartNo,
                                   StationID = a.StationID,
                                   StationCode = b.StationCode,
                                   DepartmentListID = a.DepartmentListID,
                                   DepartmentCode = b.DepartmentCode,
                                   BedNumber = a.BedNumber,
                                   ProfileBedNumber = b.BedNumber,
                                   MarkList = b.MarkList,
                                   NursingLevel = a.NursingLevel
                               }).ToListAsync();
            return query;
        }
        /// <summary>
        /// 取得需要更新ProfileMark的数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<UpDateProfileMarkView>> GetNeedUpDateMark(string hospitalID)
        {
            var query = await (from a in _dbContext.InpatientDatas
                               join b in _dbContext.PatientProfileMark on a.ChartNo equals b.ChartNo
                               into temp
                               from b in temp.DefaultIfEmpty()
                               where a.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(a.InHospitalStatus ?? -1) && b.DeleteFlag != "*"
                               && a.BedID != 0 && a.HospitalID == hospitalID
                               select new UpDateProfileMarkView
                               {
                                   HospitalID = a.HospitalID,
                                   InpatientID = a.ID,
                                   PatientID = a.PatientID,
                                   CaseNumber = a.CaseNumber,
                                   ChartNo = a.ChartNo,
                                   StationID = a.StationID,
                                   StationCode = b.StationCode,
                                   DepartmentListID = a.DepartmentListID,
                                   DepartmentCode = b.DepartmentCode,
                                   BedNumber = a.BedNumber,
                                   ProfileBedNumber = b.BedNumber,
                                   MarkList = b.MarkList,
                                   NursingLevel = a.NursingLevel
                               }).ToListAsync();
            return query;
        }

        /// <summary>
        /// 取得病人信息
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<PatientDetail> GetPatient(string caseNumber, string hospitalID)
        {
            var data = await _dbContext.InpatientDatas
                     .Where(m => m.CaseNumber == caseNumber && m.HospitalID == hospitalID
                     && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DeleteFlag != "*")
                     .Join(_dbContext.PatientBasicDatas,
                     m => m.PatientID,
                     n => n.PatientID,
                     (m, n) => new PatientDetail
                     {
                         InpatientID = m.ID,
                         PatientID = m.PatientID,
                         StationID = m.StationID,
                         Age = m.Age == null ? "" : m.Age.Value.ToString(),
                         AgeDetail = m.AgeDetail,
                         AttendingPhysician = m.AttendingPhysicianID,
                         PhysicianName = m.ChiefComplaint,
                         CareNure1 = "",
                         CareNure2 = "",
                         DateOfBirth = n.DateOfBirth,
                         PatientName = n.PatientName,
                         BedNumber = m.BedNumber,
                         Blood = n.BloodType,
                         Gender = n.Gender,
                         ChartNo = m.ChartNo,
                         CaseNumber = m.CaseNumber,
                         AdmissionDate = m.AdmissionDate,
                         AdmissionTime = m.AdmissionTime,
                         Allergic = "",
                         NursingLevel = m.NursingLevel,
                         CostType = "",
                         DepartmentListID = m.DepartmentListID,
                         BedID = m.BedID,
                         DischargeDate = m.DischargeDate,
                         NursingProcedureCode = m.NursingProcedureCode
                     }).FirstOrDefaultAsync();
            return data;
        }

        public async Task<PatientDetail> GetPatientAll(string caseNumber)
        {
            var data = await _dbContext.InpatientDatas
                     .Where(m => m.CaseNumber == caseNumber && m.DeleteFlag != "*")
                     .Join(_dbContext.PatientBasicDatas,
                     m => m.PatientID,
                     n => n.PatientID,
                     (m, n) => new PatientDetail
                     {
                         InpatientID = m.ID,
                         StationID = m.StationID,
                         Age = m.Age == null ? "" : m.Age.Value.ToString(),
                         AgeDetail = m.AgeDetail,
                         AttendingPhysician = m.AttendingPhysicianID,
                         PhysicianName = m.ChiefComplaint,
                         CareNure1 = "",
                         CareNure2 = "",
                         DateOfBirth = n.DateOfBirth,
                         PatientName = n.PatientName,
                         BedNumber = m.BedNumber,
                         Blood = n.BloodType,
                         Gender = n.Gender,
                         ChartNo = m.ChartNo,
                         CaseNumber = m.CaseNumber,
                         AdmissionDate = m.AdmissionDate,
                         AdmissionTime = m.AdmissionTime,
                         Allergic = "",
                         NursingLevel = m.NursingLevel,
                         CostType = "",
                         DepartmentListID = m.DepartmentListID
                     }).FirstOrDefaultAsync();
            return data;
        }

        public async Task<PatientDetail> GetPatientAll(string caseNumber, string hospitalID)
        {
            var data = await _dbContext.InpatientDatas
                     .Where(m => m.CaseNumber == caseNumber && m.DeleteFlag != "*" && m.HospitalID == hospitalID)
                     .Join(_dbContext.PatientBasicDatas,
                     m => m.PatientID,
                     n => n.PatientID,
                     (m, n) => new PatientDetail
                     {
                         InpatientID = m.ID,
                         StationID = m.StationID,
                         Age = m.Age == null ? "" : m.Age.Value.ToString(),
                         AgeDetail = m.AgeDetail,
                         AttendingPhysician = m.AttendingPhysicianID,
                         PhysicianName = m.ChiefComplaint,
                         CareNure1 = "",
                         CareNure2 = "",
                         DateOfBirth = n.DateOfBirth,
                         PatientName = n.PatientName,
                         BedNumber = m.BedNumber,
                         Blood = n.BloodType,
                         Gender = n.Gender,
                         ChartNo = m.ChartNo,
                         CaseNumber = m.CaseNumber,
                         AdmissionDate = m.AdmissionDate,
                         AdmissionTime = m.AdmissionTime,
                         Allergic = "",
                         NursingLevel = m.NursingLevel,
                         CostType = "",
                         DepartmentListID = m.DepartmentListID
                     }).FirstOrDefaultAsync();
            return data;
        }

        public async Task<InpatientView> GetPatientData(string inpatientID)
        {
            var query = await (from a in _dbContext.InpatientDatas
                               join b in _dbContext.PatientBasicDatas on a.PatientID equals b.PatientID
                               where a.ID == inpatientID && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                               select new InpatientView
                               {
                                   InpatientID = a.ID,
                                   PatientID = a.PatientID,
                                   PatientName = b.PatientName,
                                   CaseNumber = a.CaseNumber,
                                   ChartNo = a.ChartNo,
                                   DepartmentListID = a.DepartmentListID,
                                   StationID = a.StationID,
                                   Age = a.Age,
                                   AgeDetail = a.AgeDetail,
                                   Gender = b.Gender,
                                   LocalCaseNumber = a.LocalCaseNumber,
                                   NursingProcedureCode = a.NursingProcedureCode,
                                   ICDCode = a.ICDCode,
                                   NursingLevel = a.NursingLevel,
                                   Diagnosis = a.Diagnosis,
                                   Blood = b.BloodType,
                                   BedNumber = a.BedNumber,
                                   DateOfBirth = b.DateOfBirth
                               }).FirstOrDefaultAsync();
            return query;
        }

        /// <summary>
        ///  获得惠每需要的病人信息
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<object> GetPatientDataByCaseNumber(string caseNumber, string hospitalID)
        {
            var query = from a in _dbContext.InpatientDatas
                        join b in _dbContext.StationListInfos on a.StationID equals b.ID
                        join c in _dbContext.DepartmentListInfos on a.DepartmentListID equals c.ID
                        join d in _dbContext.PatientBasicDatas on a.PatientID equals d.PatientID
                        where a.CaseNumber == caseNumber && a.DeleteFlag != "*" && b.DeleteFlag != "*" && c.DeleteFlag != "*" && d.DeleteFlag != "*"
                        && a.HospitalID == hospitalID && b.HospitalID == hospitalID && c.HospitalID == hospitalID && d.HospitalID == hospitalID
                         && a.HospitalID == hospitalID && d.HospitalID == hospitalID
                        select new
                        {
                            a.ChartNo,
                            a.CaseNumber,
                            b.StationName,
                            b.StationCode,
                            c.DepartmentCode,
                            DepartmentName = c.Department,
                            d.PatientName,
                            a.BedNumber,
                            d.Gender,
                            a.Age,
                            a.AgeDetail,
                            d.DateOfBirth,
                            AdmissionDate = a.AdmissionDate.Add(a.AdmissionTime)
                        };
            return await query.FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据患者ID获得患者基本信息
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<RiskScreenRecordListView> GetPatientInfoByID(string inpatientID)
        {
            var query = await (from a in _dbContext.InpatientDatas
                               join b in _dbContext.PatientBasicDatas on a.PatientID equals b.PatientID
                               where a.ID == inpatientID && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                               select new RiskScreenRecordListView
                               {
                                   LocalCaseNumber = a.LocalCaseNumber,
                                   HospitalID = a.HospitalID,
                                   PatientName = b.PatientName,
                                   Age = a.Age,
                                   Gender = b.Gender,
                                   NursingLevel = a.NursingLevel,
                                   AdmissionDate = a.AdmissionDate,
                               }).FirstOrDefaultAsync();
            return query;
        }

        /// <summary>
        /// 获取病人性别和科室
        /// </summary>
        /// <param name="inPatientID"></param>
        /// <returns></returns>
        public async Task<InpatientView> GetDepartmentIDAndGenderByInpatientIDAsync(string inPatientID)
        {
            var query = await (from a in _dbContext.InpatientDatas.Where(m => m.ID == inPatientID && m.DeleteFlag != "*")
                               join b in _dbContext.PatientBasicDatas.Where(m => m.DeleteFlag != "*")
                               on a.PatientID equals b.PatientID
                               select new InpatientView
                               {
                                   InpatientID = a.ID,
                                   StationID = a.StationID,
                                   DepartmentListID = a.DepartmentListID,
                                   Gender = b.Gender,
                               }).FirstOrDefaultAsync();
            return query;
        }

        public async Task<List<InpatientView>> GetInPatientBasicInfoAsync(string[] inpatientIDs)
        {
            var query = await (from a in _dbContext.InpatientDatas.Where(m => inpatientIDs.Contains(m.ID) && m.DeleteFlag != "*" && m.StationID != 0 && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1))
                               join b in _dbContext.PatientBasicDatas.Where(m => m.DeleteFlag != "*") on a.PatientID equals b.PatientID
                               select new InpatientView
                               {
                                   InpatientID = a.ID,
                                   StationID = a.StationID,
                                   LocalCaseNumber = a.LocalCaseNumber,
                                   BedNumber = a.BedNumber,
                                   BedID = a.BedID,
                                   PatientName = b.PatientName,
                                   ChartNo = a.ChartNo,
                                   CaseNumber = a.CaseNumber,
                                   Age = a.Age,
                                   Gender = b.Gender,
                                   ICDCode = a.ICDCode,
                                   Diagnosis = a.Diagnosis,
                                   AdmissionDate = a.AdmissionDate,
                                   AdmissionTime = a.AdmissionTime
                               }).ToListAsync();
            return query;
        }

        /// <summary>
        /// 获取看板病人详情信息
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        public async Task<PatientDetailViewModel> GetPatientDetail(string caseNumber)
        {
            var data = await (from m in _dbContext.InpatientDatas.Where(m => m.CaseNumber == caseNumber && m.DeleteFlag != "*")
                              join n in _dbContext.PatientBasicDatas.Where(m => m.DeleteFlag != "*") on m.PatientID equals n.PatientID
                              select new PatientDetailViewModel
                              {
                                  InpatientID = m.ID,
                                  ChartNo = m.ChartNo,
                                  CaseNumber = m.CaseNumber,
                                  PatientName = n.PatientName,
                                  NursingLevel = m.NursingLevel,
                                  Gender = n.Gender,
                                  Age = m.Age == null ? "" : m.Age.Value.ToString(),
                                  AgeDetail = m.AgeDetail,
                                  Diagnosis = m.Diagnosis,
                                  BedNumber = m.BedNumber,
                                  StationID = m.StationID,
                                  Blood = n.BloodType,
                                  CostType = m.BillingPattern,
                                  AdmissionDate = m.AdmissionDate,
                                  AdmissionTime = m.AdmissionTime,
                                  InHospitalDay = "",
                                  OperationName = "",
                                  OperationDateTime = null,
                                  PostOperationDay = "",
                                  AttendingPhysician = m.AttendingPhysicianID,
                                  PhysicianName = m.ChiefComplaint,
                                  CareNure = "",
                                  DateOfBirth = n.DateOfBirth,
                                  Allergic = "",
                                  PastMedicalHistory = "",
                              }).FirstOrDefaultAsync();
            return data;
        }
        /// <summary>
        /// 获取看板病人详情信息
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="stationID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<PatientDetailViewModel> GetPatientDetail(string caseNumber, int stationID, string hospitalID)
        {
            var data = await (from m in _dbContext.InpatientDatas.Where(m => m.CaseNumber == caseNumber && m.StationID == stationID && m.HospitalID == hospitalID
                                                                && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DeleteFlag != "*")
                              join n in _dbContext.PatientBasicDatas.Where(m => m.DeleteFlag != "*") on m.PatientID equals n.PatientID
                              select new PatientDetailViewModel
                              {
                                  InpatientID = m.ID,
                                  ChartNo = m.ChartNo,
                                  CaseNumber = m.CaseNumber,
                                  PatientName = n.PatientName,
                                  NursingLevel = m.NursingLevel,
                                  Gender = n.Gender,
                                  Age = m.Age == null ? "" : m.Age.Value.ToString(),
                                  AgeDetail = m.AgeDetail,
                                  Diagnosis = m.Diagnosis,
                                  BedNumber = m.BedNumber,
                                  BedID = m.BedID,
                                  StationID = m.StationID,
                                  Blood = n.BloodType,
                                  CostType = m.BillingPattern,
                                  AdmissionDate = m.AdmissionDate,
                                  AdmissionTime = m.AdmissionTime,
                                  InHospitalDay = "",
                                  OperationName = "",
                                  OperationDateTime = null,
                                  PostOperationDay = "",
                                  AttendingPhysician = m.AttendingPhysicianID,
                                  PhysicianName = "",
                                  CareNure = "",
                                  DateOfBirth = n.DateOfBirth,
                                  Allergic = "",
                                  PastMedicalHistory = "",
                              }).FirstOrDefaultAsync();
            return data;
        }
        /// <summary>
        /// 取得病区在院数据信息
        /// </summary>
        /// <returns></returns>
        public async Task<List<InPatientDataCountView>> GetInpatientDataAsync()
        {
            return await _dbContext.InpatientDatas.Where(m => InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DeleteFlag != "*")
                .Select(n => new InPatientDataCountView
                {
                    StationID = n.StationID,
                    BedNumber = n.BedNumber,
                    InpatientID = n.ID,
                    BedID = n.BedID,
                    Age = n.Age
                }).ToListAsync();
        }
        /// <summary>
        /// 取得病区在院数据信息
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<InPatientDataCountView>> GetInpatientDataAsync(string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)
            && m.HospitalID == hospitalID && m.DeleteFlag != "*")
                .Select(n => new InPatientDataCountView
                {
                    StationID = n.StationID,
                    BedNumber = n.BedNumber,
                    InpatientID = n.ID,
                    BedID = n.BedID,
                    Age = n.Age
                }).ToListAsync();
        }

        /// <summary>
        /// 根据患者inpatientIDs取得病区在院数据信息
        /// </summary>
        /// <param name="inpatientIDs"></param>
        /// <returns></returns>
        public async Task<List<InPatientDataCountView>> GetInpatientViewByInpatientIDs(string[] inpatientIDs)
        {
            return await _dbContext.InpatientDatas.Where(m => inpatientIDs.Contains(m.ID) && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DeleteFlag != "*")
                .Select(n => new InPatientDataCountView
                {
                    StationID = n.StationID,
                    BedNumber = n.BedNumber,
                    InpatientID = n.ID,
                    BedID = n.BedID,
                    Age = n.Age
                }).ToListAsync();
        }
        /// <summary>
        /// 根据病区获取当前病区使用床位
        /// </summary>
        /// <param name="stationID"></param>
        /// <returns></returns>

        public async Task<List<int>> GetUsingBedByStationID(int stationID)
        {
            return await _dbContext.InpatientDatas.Where(m => InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DeleteFlag != "*" && m.StationID == stationID)
                .Select(m => m.BedID).ToListAsync();
        }
        /// <summary>
        /// 根据病区获取当前病区使用床位
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<int>> GetUsingBedByStationID(int stationID, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DeleteFlag != "*" && m.StationID == stationID)
                .Select(m => m.BedID).ToListAsync();
        }

        public async Task<AssessTimeView> GetAdmissionTimeByID(string inpatientID)
        {
            var assessMain = await _dbContext.InpatientDatas.Where(m => m.ID == inpatientID && m.DeleteFlag != "*")
                     .Select(m => new AssessTimeView { StartDate = m.AdmissionDate, StartTime = m.AdmissionTime })
                     .FirstOrDefaultAsync();
            return assessMain;
        }

        public async Task<InpatientUseSaveView> GetUseSaveView(string inpatientID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.ID == inpatientID && m.DeleteFlag != "*")
                .Select(m => new InpatientUseSaveView
                {
                    InpatientID = m.ID,
                    PatientID = m.PatientID,
                    BedNumber = m.BedNumber,
                    BedID = m.BedID,
                    CaseNumber = m.CaseNumber,
                    DepartmentID = m.DepartmentListID,
                    ChartNo = m.ChartNo,
                    StationID = m.StationID
                }).FirstOrDefaultAsync();
        }

        public async Task<InpatientDataInfo> GetPatientCaseNumber(string charNo, int numberOfAdmissions, string hospitalID)
        {
            if (hospitalID == "6")
            {
                return await _dbContext.InpatientDatas.Where(m => m.HospitalID == hospitalID && m.ChartNo == charNo && m.DeleteFlag != "*")
                .Select(m => new InpatientDataInfo
                {
                    ID = m.ID,
                    CaseNumber = m.CaseNumber
                }).FirstOrDefaultAsync();
            }
            else
            {
                return await _dbContext.InpatientDatas.Where(m => m.HospitalID == hospitalID && m.ChartNo == charNo && m.DeleteFlag != "*" && m.NumberOfAdmissions == numberOfAdmissions)
               .Select(m => new InpatientDataInfo
               {
                   ID = m.ID,
                   CaseNumber = m.CaseNumber
               }).FirstOrDefaultAsync();
            }
        }

        /// <summary>
        /// 获取病人入院次数
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<int> GetNumberOfAdmissionsByIDAsync(string inpatientID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.ID == inpatientID).Select(m => m.NumberOfAdmissions).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取病人入院次数和身份证号 =妇幼同步程序有调用
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<(int?, string)> GetVisitedAndIdentityIDAsync(string inpatientID)
        {
            var query = from m in _dbContext.InpatientDatas.Where(m => m.ID == inpatientID && m.DeleteFlag != "*")
                        join n in _dbContext.PatientBasicDatas.Where(m => m.DeleteFlag != "*")
                            on m.PatientID equals n.PatientID
                        select new
                        {
                            m.NumberOfAdmissions,
                            n.IdentityID,
                        };
            var result = await query.AsNoTracking().FirstOrDefaultAsync();
            return (result?.NumberOfAdmissions, result?.IdentityID);
        }
        /// <summary>
        /// 根据床号集合获取住院序号集合
        /// </summary>
        /// <param name="bedNumbers"></param>
        /// <returns></returns>
        public async Task<List<string>> GetInpatientIDsByBedNumbers(List<string> bedNumbers)
        {
            return await _dbContext.InpatientDatas.Where(m => bedNumbers.Contains(m.BedNumber) && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DeleteFlag != "*")
                    .Select(m => m.ID).ToListAsync();
        }
        /// <summary>
        /// 根据床号集合获取住院序号集合
        /// </summary>
        /// <param name="bedNumbers"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<string>> GetInpatientIDsByBedNumbers(List<string> bedNumbers, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => bedNumbers.Contains(m.BedNumber)
            && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)
            && m.HospitalID == hospitalID && m.DeleteFlag != "*").Select(m => m.ID).ToListAsync();
        }

        public async Task<InpatientDataInfo> GetPatientByChartNo(string hospitalID, string chartNo)
        {
            return await _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && m.ChartNo == chartNo).OrderByDescending(m => m.NumberOfAdmissions).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 判断病人是否有最新的在院记录
        /// </summary>
        /// <param name="chartNo"></param>
        /// <returns></returns>
        public async Task<bool> CheckInpatientByChartNo(string chartNo)
        {
            var count = await _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && m.ChartNo == chartNo && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).CountAsync();
            return count > 0;
        }

        /// <summary>
        /// 通过stationID获取患者数据（包含出院）
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetListByStationID(int stationID, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.StationID == stationID && m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 通过stationID获取患者数据（出院）
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetDischargePatientListByStationID(int stationID, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.StationID == stationID && m.HospitalID == hospitalID && m.DeleteFlag != "*" && InHospitalStatus.DISCHARGEDHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).ToListAsync();
        }

        /// <summary>
        /// 通过stationID获取患者基本信息（在院）
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<List<PatientData>> GetInpatientBasicData(string hospitalID, int stationID)
        {
            var query = from m in _dbContext.InpatientDatas
                        join n in _dbContext.PatientBasicDatas on new { m.HospitalID, m.PatientID } equals new { n.HospitalID, n.PatientID }
                        where m.StationID == stationID && m.HospitalID == hospitalID && m.DeleteFlag != "*"
                       && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)
                        select new PatientData
                        {
                            ChartNo = m.ChartNo,
                            InpatientID = m.ID,
                            PatientID = m.PatientID,
                            PatientName = n.PatientName,
                            Gender = n.Gender,
                            BedNumber = m.BedNumber,
                            NursingLevel = m.NursingLevel,
                            BedID = m.BedID,
                            Age = m.Age,
                            DateOfBirth = n.DateOfBirth,
                            AdmissionDate = m.AdmissionDate,
                            AdmissionTime = m.AdmissionTime,
                            StationID = m.StationID
                        };
            var inpatientDatas = await query.ToListAsync();
            var bedList = await _bedListRepository.GetByStationID(stationID);
            var patientData = (from m in inpatientDatas
                               join n in bedList on new { m.BedID } equals new { BedID = n.ID }
                               orderby n.Sort
                               select new PatientData
                               {
                                   ChartNo = m.ChartNo,
                                   InpatientID = m.InpatientID,
                                   PatientID = m.PatientID,
                                   PatientName = m.PatientName,
                                   Gender = m.Gender,
                                   BedNumber = m.BedNumber,
                                   NursingLevel = m.NursingLevel,
                                   BedID = m.BedID,
                                   Age = m.Age,
                                   DateOfBirth = m.DateOfBirth,
                                   CurrentAge = m.DateOfBirth.HasValue ? AgeCalculat.GetAge(m.DateOfBirth.Value, DateTime.Now.Date) : m.Age,
                                   AdmissionDate = m.AdmissionDate,
                                   AdmissionTime = m.AdmissionTime,
                                   StationID = m.StationID,
                                   Sort = n.Sort
                               }).ToList();
            return patientData;
        }
        public Task<List<InpatientDataInfo>> GetNoFilePatients(DateTime startTime, DateTime endTime, int stationID, string hospitalID)
        {
            return _dbContext.InpatientDatas.Where(m => m.HospitalID == hospitalID && m.DischargeDate != null && m.DischargeDate.Value > startTime && m.DischargeDate.Value < endTime && m.StationID == stationID && m.EMRFlag != "*").ToListAsync();
        }

        public async Task<InpatientDataInfo> GetLastData()
        {
            return await _dbContext.InpatientDatas.OrderByDescending(m => m.ModifyDate).FirstOrDefaultAsync();
        }

        public async Task<List<InpatientDataInfo>> GetInpatientsAsync(int stationID, DateTime startDate)
        {
            return await _dbContext.InpatientDatas.Where(m => m.StationID == stationID && m.DeleteFlag != "*" && ((m.DischargeDate != null && m.DischargeDate >= startDate) || m.DischargeDate == null)).ToListAsync();
        }

        public async Task<List<InpatientDataInfo>> GetInpatientsAsync(DateTime startDate)
        {
            return await _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && ((m.DischargeDate != null && m.DischargeDate >= startDate) || m.DischargeDate == null)).ToListAsync();
        }

        /// <summary>
        /// 获取住院病人数据
        /// </summary>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientDataViewStaionID(int stationID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.StationID == stationID && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DeleteFlag != "*")
            .Select(n => new InpatientDataInfo
            {
                StationID = n.StationID,
                ID = n.ID,
                BedID = n.BedID,
                BedNumber = n.BedNumber,
                DischargeDate = n.DischargeDate,
                NursingProcedureCode = n.NursingProcedureCode,
                CaseNumber = n.CaseNumber,
                ChartNo = n.ChartNo,
                DepartmentListID = n.DepartmentListID,
                AdmissionDate = n.AdmissionDate,
                AdmissionTime = n.AdmissionTime
            }).ToListAsync();
        }
        /// <summary>
        /// 获取住院病人数据
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientDataViewStaionID(int stationID, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.StationID == stationID && m.HospitalID == hospitalID && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DeleteFlag != "*")
            .Select(n => new InpatientDataInfo
            {
                StationID = n.StationID,
                PatientID = n.PatientID,
                ID = n.ID,
                BedID = n.BedID,
                BedNumber = n.BedNumber,
                DischargeDate = n.DischargeDate,
                NursingProcedureCode = n.NursingProcedureCode,
                CaseNumber = n.CaseNumber,
                ChartNo = n.ChartNo,
                DepartmentListID = n.DepartmentListID,
                AdmissionDate = n.AdmissionDate,
                AdmissionTime = n.AdmissionTime
            }).ToListAsync();
        }

        /// <summary>
        /// 通过stationID获取出院数据
        /// </summary>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientDischargeListByStationID(int stationID)
        {
            var InpatientData = new InpatientDataInfo();
            List<InpatientDataInfo> list = await _dbContext.InpatientDatas.Where(m => m.StationID == stationID && InHospitalStatus.DISCHARGEDHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DeleteFlag != "*").ToListAsync();
            return list;
        }
        /// <summary>
        /// 通过stationID获取出院数据
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientDischargeListByStationID(int stationID, string hospitalID)
        {
            var InpatientData = new InpatientDataInfo();
            List<InpatientDataInfo> list = await _dbContext.InpatientDatas.Where(m => m.StationID == stationID
            && InHospitalStatus.DISCHARGEDHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)
            && m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
            return list;
        }

        /// <summary>
        /// 通过stationID获取患者数据（在院）
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientListByStationID(int stationID, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.StationID == stationID && m.HospitalID == hospitalID && m.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).ToListAsync();
        }

        /// <summary>
        /// 根据科室id取得InpationDataView数据
        /// </summary>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<List<InpationDataView>> GetPartialDataStaionID(int stationID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.StationID == stationID && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DeleteFlag != "*")
            .Select(n => new InpationDataView
            {
                StationID = n.StationID,
                ID = n.ID,
                BedID = n.BedID,
                BedNumber = n.BedNumber,
                DischargeDate = n.DischargeDate,
                NursingProcedureCode = n.NursingProcedureCode
            }).ToListAsync();
        }
        /// <summary>
        /// 根据科室id取得InpationDataView数据
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<InpationDataView>> GetPartialDataStaionID(int stationID, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.StationID == stationID
            && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)
            && m.HospitalID == hospitalID && m.DeleteFlag != "*")
            .Select(n => new InpationDataView
            {
                StationID = n.StationID,
                ID = n.ID,
                BedID = n.BedID,
                BedNumber = n.BedNumber,
                DischargeDate = n.DischargeDate,
                NursingProcedureCode = n.NursingProcedureCode
            }).ToListAsync();
        }

        public async Task<List<InpatientLastAssessement>> GetInpatientLastAssessement(int stationID, int langauge)
        {
            DateTime? dischargeDate = DateTime.Now;
            dischargeDate = null;
            var query = await (from a in _dbContext.InpatientDatas
                               join b in _dbContext.AssessMains on new { InpatientID = a.ID, a.DeleteFlag, a.DischargeDate }
                               equals new { b.InpatientID, b.DeleteFlag, DischargeDate = dischargeDate }
                               join c in _dbContext.SettingDescriptions
                               on new { SettingTypeCode = "ProblemRenew" + (string.IsNullOrEmpty(a.NursingLevel) ? "" : a.NursingLevel), Language = langauge }
                               equals new { c.SettingTypeCode, c.Language }
                               join d in _dbContext.StationShifts on new { a.StationID, a.HospitalID }
                               equals new { d.StationID, d.HospitalID }
                               join e in _dbContext.SettingDescriptions on new { SettingTypeCode = "NursingPlanRenewShift", TypeValue = d.Shift, Language = langauge } equals new { e.SettingTypeCode, e.TypeValue, e.Language }
                               where a.StationID == stationID && a.DeleteFlag != "*"
                               select new InpatientLastAssessement
                               {
                                   InpatientID = a.ID,
                                   AssessDate = b.AssessDate,
                                   AssessTime = b.AssessTime,
                                   RenewDay = c.TypeValue,
                                   ShiftStartTime = d.ShiftStartTime,
                                   ShiftEndTime = d.ShiftEndTime,
                                   NursingProcedureCode = a.NursingProcedureCode,
                               }).OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime).ToListAsync();

            return query.GroupBy(m => m.InpatientID).Select(m => m.First()).ToList();
        }
        /// <summary>
        /// 取得病区病人最后一次护理评估
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="langauge"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<InpatientLastAssessement>> GetInpatientLastAssessement(int stationID, int langauge, string hospitalID)
        {
            var settingDescription = await _settingDescriptionRepository.GetAsync();
            var nursingPlanRenewShift = settingDescription.Find(m => m.SettingTypeCode == "NursingPlanRenewShift");
            var problemRenew = settingDescription.Where(m => m.SettingTypeCode.Contains("ProblemRenew")).ToList();
            var stationShift = await _stationShiftRepository.GetAsync();
            stationShift = stationShift.Where(m => m.Shift == nursingPlanRenewShift.TypeValue).ToList();
            var patientAssessMains = await (from a in _dbContext.InpatientDatas
                                            join b in _dbContext.AssessMains on new { InpatientID = a.ID }
                                            equals new { b.InpatientID }
                                            where a.StationID == stationID && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                                            && a.HospitalID == hospitalID
                                            && a.InHospitalStatus.HasValue
                                            && InHospitalStatus.INHOSPITALLIST.Contains(a.InHospitalStatus.Value)
                                            select new InpatientLastAssessement
                                            {
                                                InpatientID = b.InpatientID,
                                                AssessDate = b.AssessDate,
                                                AssessTime = b.AssessTime,
                                                NursingLevel = a.NursingLevel,
                                                StationID = a.StationID,
                                                NursingProcedureCode = a.NursingProcedureCode
                                            }).ToListAsync();

            patientAssessMains = patientAssessMains.OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime).ToList();
            patientAssessMains = patientAssessMains.GroupBy(m => m.InpatientID).Select(m => m.First()).ToList();

            var result = (from bb in patientAssessMains
                          join c in problemRenew
                          on new { SettingTypeCode = "ProblemRenew" + (string.IsNullOrEmpty(bb.NursingLevel) ? "" : bb.NursingLevel) }
                          equals new { c.SettingTypeCode }
                          join d in stationShift on new { bb.StationID }
                          equals new { d.StationID }
                          select new InpatientLastAssessement
                          {
                              InpatientID = bb.InpatientID,
                              AssessDate = bb.AssessDate,
                              AssessTime = bb.AssessTime,
                              RenewDay = c.TypeValue,
                              ShiftStartTime = d.ShiftStartTime,
                              ShiftEndTime = d.ShiftEndTime,
                              NursingProcedureCode = bb.NursingProcedureCode
                          }).ToList();
            return result;
            //return query.GroupBy(m => m.InpatientID).Select(m => m.First()).ToList();
            //var query = await (from a in _dbContext.InpatientDatas
            //                   join bb in _dbContext.AssessMains on new { InpatientID = a.ID, a.DeleteFlag }
            //                   equals new { bb.InpatientID, bb.DeleteFlag }
            //                   join c in _dbContext.SettingDescriptions
            //                   on new { SettingTypeCode = "ProblemRenew" + (string.IsNullOrEmpty(a.NursingLevel) ? "" : a.NursingLevel), Language = langauge }
            //                   equals new { c.SettingTypeCode, c.Language }
            //                   join d in _dbContext.StationShifts on new { a.StationID, a.HospitalID }
            //                   equals new { d.StationID, d.HospitalID }
            //                   join e in _dbContext.SettingDescriptions on new { SettingTypeCode = "NursingPlanRenewShift", TypeValue = d.Shift, Language = langauge } 
            //                   equals new { e.SettingTypeCode, e.TypeValue, e.Language }
            //                   where a.StationID == stationID && a.DeleteFlag != "*"
            //                   && a.HospitalID == hospitalID && c.HospitalID == hospitalID
            //                   && d.HospitalID == hospitalID && e.HospitalID == hospitalID
            //                   && a.InHospitalStatus.HasValue
            //                   && Medical.Common.InHospitalStatus.INHOSPITALLIST.Contains(a.InHospitalStatus.Value)
            //                   select new InpatientLastAssessement
            //                   {
            //                       InpatientID = bb.InpatientID,
            //                       AssessDate = bb.AssessDate,
            //                       AssessTime = bb.AssessTime,
            //                       RenewDay = c.TypeValue,
            //                       ShiftStartTime = d.ShiftStartTime,
            //                       ShiftEndTime = d.ShiftEndTime,
            //                       NursingProcedureCode = a.NursingProcedureCode,
            //                   }).OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime).ToListAsync();

            //return query.GroupBy(m => m.InpatientID).Select(m => m.First()).ToList();
        }
        /// <summary>
        /// 根据科室获取信息
        /// </summary>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetListByStaionID(int stationID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && m.StationID == stationID).ToListAsync();
        }
        /// <summary>
        /// 根据科室获取信息
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetListByStaionID(int stationID, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && m.StationID == stationID && m.HospitalID == hospitalID).ToListAsync();
        }

        /// <summary>
        /// 获取起讫时间内该病区出院病人（是否评价）
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="stationID"></param>
        /// <param name="isEvaluate"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<OutpatientData>> GetDischargePatientByQuery(DateTime startTime, DateTime endTime, int stationID, bool isEvaluate, string hospitalID)
        {
            var list = await (from m in _dbContext.InpatientDatas
                              join o in _dbContext.PatientBasicDatas on m.PatientID equals o.PatientID
                              join p in _dbContext.Users on m.AttendingPhysicianID equals p.UserID
                              into user
                              from u in user.DefaultIfEmpty()
                              where m.DeleteFlag != "*" && m.DischargeDate.HasValue && m.DischargeTime.HasValue && m.HospitalID == hospitalID
                              && m.StationID == stationID && m.DischargeDate >= startTime && m.DischargeDate <= endTime
                              orderby m.DischargeDate descending
                              select new OutpatientData
                              {
                                  InpatientID = m.ID,
                                  PatientID = m.PatientID,
                                  ChartNo = m.ChartNo,
                                  CaseNumber = m.CaseNumber,
                                  PatientName = o.PatientName,
                                  BedNumber = m.BedNumber,
                                  DischargeTime = m.DischargeDate.Value,
                                  AdmissionDate = m.AdmissionDate,
                                  Gender = o.Gender,
                                  StationID = m.StationID,
                                  DepartmentListID = m.DepartmentListID,
                                  DateOfBirth = o.DateOfBirth.ToString(),
                                  NursingLevel = m.NursingLevel,
                                  PhysicianName = u.Name,
                                  LocalCaseNumber = m.LocalCaseNumber,
                                  IsEvaluate = false,
                                  Age = m.Age.HasValue ? m.Age.Value.ToString() : "",
                                  Diagnosis = m.Diagnosis
                              }).ToListAsync();

            var IDs = list.Select(m => m.InpatientID).ToList();
            var problems = await _dbContext.PatientProblems.Where(n => IDs.Any(m => m == n.InpatientID) && n.DeleteFlag != "*" && n.DiagnoseFlag != "O" && n.Status != 0 && !n.NursingOutcomeID.HasValue)
                .Select(m => new { m.InpatientID, m.NursingOutcomeID.HasValue, m.StationID, m.DepartmentListID }).ToListAsync();
            if (hospitalID == "1")
            {
                var assessInpatientIDs = await _dbContext.AssessMains.AsNoTracking().Where(m => IDs.Contains(m.InpatientID) && m.DeleteFlag != "*").Select(m => m.InpatientID).Distinct().ToListAsync();
                list = list.Where(m => assessInpatientIDs.Contains(m.InpatientID)).ToList();
            }

            foreach (var item in list)
            {
                var sucProblem = problems.Find(m => m.InpatientID == item.InpatientID && m.StationID == item.StationID && m.DepartmentListID == item.DepartmentListID);
                if (sucProblem != null)
                {
                    item.IsEvaluate = false;
                }
                else
                {
                    item.IsEvaluate = true;
                }
            }
            //存在待评价问题人员ID
            var unEvaluateInpatientIDs = list.Where(m => !m.IsEvaluate).Select(m => m.InpatientID).Distinct().ToList();
            var YEvaluate = new List<OutpatientData>();
            var NEvaluate = new List<OutpatientData>();
            //筛选出没有护理问题需要评价的人员
            var testList = list.Where(m => !unEvaluateInpatientIDs.Contains(m.InpatientID))
                .GroupBy(m => m.InpatientID).Select(m => m.First()).ToList();
            //筛选出及没有护理评价又有出院交班记录的人员为已评价人员
            var ids = testList.Select(m => m.InpatientID).ToList();
            var handOverListIds = await _dbContext.HandoverInfos.Where(m => ids.Contains(m.InpatientID) && m.HandoverType == "DischargeAssess").Select(n => n.InpatientID).ToListAsync();
            YEvaluate = testList.Where(m => handOverListIds.Contains(m.InpatientID)).GroupBy(m => m.InpatientID).Select(m => m.First()).ToList();

            //其他全部为未评价人员
            var YEvaluateIDs = YEvaluate.Select(m => m.InpatientID).ToList();
            NEvaluate = list.Where(m => !YEvaluateIDs.Contains(m.InpatientID)).GroupBy(m => m.InpatientID).Select(m => m.First()).ToList();
            //交班数据迁移
            if (NEvaluate.Count > 0)
            {
                var cloneNEvaluate = NEvaluate;
                var handoverList = await _dbContext.HandoverInfos.Where(m => IDs.Contains(m.InpatientID)).Select(n => n.InpatientID).ToListAsync();
                for (var i = 0; i < cloneNEvaluate.Count; i++)
                {
                    var zeroHandover = handoverList.Where(m => m == cloneNEvaluate[i].InpatientID).ToList();
                    //#bugfix:出院评价-出院小结，未评价患者出院后直接到已评价中,添加cloneNEvaluate[i].IsEvaluate判断,有护理问题未评价，就显示在未评价中，不会因为交班数据迁移改变
                    if (zeroHandover.Count == 0 && cloneNEvaluate[i].IsEvaluate)
                    {
                        YEvaluate.Add(cloneNEvaluate[i]);
                        NEvaluate.Remove(cloneNEvaluate[i]);
                        i--;
                    }
                }
            }
            return isEvaluate ? YEvaluate : NEvaluate;
        }

        /// <summary>
        /// 获取起讫时间内该病区出院病人
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="stationID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<OutpatientData>> GetDischargePatientByStartEndDay(DateTime startTime, DateTime endTime, int stationID, string hospitalID)
        {
            var query = from m in _dbContext.InpatientDatas
                        join n in _dbContext.PatientBasicDatas on m.PatientID equals n.PatientID
                        join o in _dbContext.HandoverInfos on new { InpatientID = m.ID, RecordsCode = "DischargeAssess" } equals new { o.InpatientID, o.RecordsCode }
                        into temp
                        from oo in temp.DefaultIfEmpty()
                        join p in _dbContext.Users on m.AttendingPhysicianID equals p.UserID into tempUser
                        from pp in tempUser.DefaultIfEmpty()
                        where m.DischargeDate.HasValue && m.DischargeTime.HasValue && m.HospitalID == hospitalID && m.StationID == stationID
                             && m.DischargeDate >= startTime && m.DischargeDate <= endTime
                        orderby m.DischargeDate descending, m.DischargeTime descending
                        select new OutpatientData
                        {
                            InpatientID = m.ID,
                            PatientID = n.PatientID,
                            ChartNo = n.ChartNo,
                            CaseNumber = m.CaseNumber,
                            PatientName = n.PatientName,
                            BedNumber = m.BedNumber,
                            Diagnosis = m.Diagnosis,
                            ICUFlag = m.ICUFlag,
                            AttendingPhysicianID = m.AttendingPhysicianID,
                            DischargeTime = m.DischargeDate.Value.Add(m.DischargeTime.Value),
                            AdmissionDate = m.AdmissionDate,
                            Gender = n.Gender,
                            StationID = m.StationID,
                            DateOfBirth = n.DateOfBirth.HasValue ? n.DateOfBirth.Value.ToString("yyyy-MM-dd") : "",
                            NursingLevel = m.NursingLevel,
                            PhysicianName = pp.Name,
                            HandoverID = oo.ID,
                            ShiftDate = oo.ShiftDate,
                            HandoverShift = oo.HandoverShift,
                            LocalCaseNumber = m.LocalCaseNumber
                        };
            return await query.ToListAsync();
        }

        /// <summary>
        /// 通过localCaseNumber获取患者数据（首条）
        /// </summary>
        /// <param name="localCaseNumber"></param>
        /// <returns></returns>
        public async Task<InpatientDataInfo> GetByLocalCaseNumber(string localCaseNumber)
        {
            return await _dbContext.InpatientDatas.Where(m => m.LocalCaseNumber == localCaseNumber && m.DeleteFlag != "*").OrderByDescending(t => t.AdmissionDate).ThenByDescending(t => t.AdmissionTime).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 通过localCaseNumber获取患者数据（首条）
        /// </summary>
        /// <param name="localCaseNumber"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<InpatientDataInfo> GetByLocalCaseNumber(string localCaseNumber, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.LocalCaseNumber == localCaseNumber && m.HospitalID == hospitalID && m.DeleteFlag != "*").OrderByDescending(t => t.AdmissionDate).ThenByDescending(t => t.AdmissionTime).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 通过chartNo获取患者数据（在院）
        /// </summary>
        /// <param name="chartNo"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<InpatientDataInfo> GetInpatientByChartNo(string chartNo, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.ChartNo == chartNo && m.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 通过chartNo获取患者数据（在院）, 不跟踪
        /// </summary>
        /// <param name="chartNo"></param>
        /// <returns></returns>
        public async Task<InpatientDataInfo> GetInpatientByChartNoNoTracking(string chartNo, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.ChartNo == chartNo && m.HospitalID == hospitalID && m.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).AsNoTracking().FirstOrDefaultAsync();
        }

        /// <summary>
        /// 通过chartNo获取患者数据（在院）
        /// </summary>
        /// <param name="chartNo"></param>
        /// <returns></returns>
        public async Task<InpatientDataInfo> GetInpatientByChartNo(string chartNo)
        {
            return await _dbContext.InpatientDatas.Where(m => m.ChartNo == chartNo && m.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 通过chartNo获取患者数据（包含出院）
        /// </summary>
        /// <param name="chartNo"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetListByChartNo(string chartNo, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.ChartNo == chartNo && m.HospitalID == hospitalID && m.DeleteFlag != "*").OrderBy(m => m.AdmissionDate).ToListAsync();
        }

        /// <summary>
        /// 通过chartNo获取患者数据（包含出院）
        /// </summary>
        /// <param name="chartNo"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetListByChartNo(string chartNo)
        {
            return await _dbContext.InpatientDatas.Where(m => m.ChartNo == chartNo && m.DeleteFlag != "*").OrderBy(m => m.AdmissionDate).ToListAsync();
        }
        public async Task<InpatientDataInfo> GetLastByChartNo(string chartNo)
        {
            return await _dbContext.InpatientDatas.Where(m => m.ChartNo == chartNo && m.DeleteFlag != "*").OrderBy(m => m.AdmissionDate).LastOrDefaultAsync();
        }
        /// <summary>
        /// 通过caseNumberList获取患者数据（在院）
        /// </summary>
        /// <param name="caseNumberList"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientListByCaseNumberListAsync(List<string> caseNumberList)
        {
            var InpatientDataList = new List<InpatientDataInfo>();
            foreach (var item in caseNumberList)
            {
                var tempList = await _dbContext.InpatientDatas.Where(m => m.CaseNumber == item && m.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).ToListAsync();
                InpatientDataList = InpatientDataList.Union(tempList).ToList();
            }
            return InpatientDataList;
        }
        /// <summary>
        /// 通过caseNumberList获取患者数据（在院）
        /// </summary>
        /// <param name="caseNumberList"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientListByCaseNumberListAsync(List<string> caseNumberList, string hospitalID)
        {
            var InpatientDataList = new List<InpatientDataInfo>();
            foreach (var item in caseNumberList)
            {
                var tempList = await _dbContext.InpatientDatas.Where(m => m.CaseNumber == item
                && m.HospitalID == hospitalID && m.DeleteFlag != "*"
                && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).ToListAsync();
                InpatientDataList = InpatientDataList.Union(tempList).ToList();
            }
            return InpatientDataList;
        }
        public async Task<List<InpatientDataInfo>> GetInpatientListByCaseNumbersAsync(List<string> caseNumbers, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => caseNumbers.Contains(m.CaseNumber) && m.HospitalID == hospitalID && m.DeleteFlag != "*").AsNoTracking().ToListAsync();
        }
        /// <summary>
        /// 通过inPatientID获取患者基本信息（在院）
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<PatientData> GetInpatientBasicData(string hospitalID, string inpatientID)
        {
            var query = from m in _dbContext.InpatientDatas
                        join n in _dbContext.PatientBasicDatas on m.PatientID equals n.PatientID
                        join b in _dbContext.BedListInfos on m.BedID equals b.ID
                        where m.ID == inpatientID && m.HospitalID == hospitalID && m.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)
                        orderby b.Sort
                        select new PatientData
                        {
                            ChartNo = n.ChartNo,
                            InpatientID = m.ID,
                            PatientID = n.PatientID,
                            CaseNumber = m.CaseNumber,
                            PatientName = n.PatientName,
                            Gender = n.Gender,
                            BedNumber = m.BedNumber,
                            NursingLevel = m.NursingLevel,
                            BedID = m.BedID,
                            DepartmentListID = m.DepartmentListID,
                            StationID = m.StationID,
                            Age = m.Age,
                            AgeDetail = m.AgeDetail,
                            DateOfBirth = n.DateOfBirth,
                            CurrentAge = n.DateOfBirth.HasValue ? AgeCalculat.GetAge(n.DateOfBirth.Value, DateTime.Now.Date) : m.Age
                        };
            return await query.FirstOrDefaultAsync();
        }

        /// <summary>
        /// 通过inpatientID获取患者基本信息
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<PatientData> GetInpatientBasicDataByInpatientID(string inpatientID)
        {
            return await (from m in _dbContext.InpatientDatas
                          join n in _dbContext.PatientBasicDatas on m.PatientID equals n.PatientID
                          where m.ID == inpatientID && m.DeleteFlag != "*"
                          select new PatientData
                          {
                              ChartNo = n.ChartNo,
                              InpatientID = m.ID,
                              PatientID = n.PatientID,
                              PatientName = n.PatientName,
                              Gender = n.Gender,
                              BedNumber = m.BedNumber,
                              NursingLevel = m.NursingLevel,
                              BedID = m.BedID,
                              DepartmentListID = m.DepartmentListID,
                              StationID = m.StationID,
                              Age = m.Age,
                              DateOfBirth = n.DateOfBirth,
                              CurrentAge = n.DateOfBirth.HasValue ? AgeCalculat.GetAge(n.DateOfBirth.Value, DateTime.Now.Date) : m.Age,
                              Diagnosis = m.Diagnosis,
                              AdmissionDate = m.AdmissionDate,
                              DischargeDate = m.DischargeDate,
                              DischargeTime = m.DischargeTime,
                              NumberOfAdmissions = m.NumberOfAdmissions
                          }).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 通过inpatientID获取患者基本信息(出院)
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<PatientData> GetDischargeInpatientBasicData(string hospitalID, string inpatientID)
        {
            var query = from m in _dbContext.InpatientDatas
                        join n in _dbContext.PatientBasicDatas on m.PatientID equals n.PatientID
                        where m.ID == inpatientID && m.HospitalID == hospitalID && m.DeleteFlag != "*"
                        select new PatientData
                        {
                            ChartNo = n.ChartNo,
                            InpatientID = m.ID,
                            PatientID = n.PatientID,
                            PatientName = n.PatientName,
                            Gender = n.Gender,
                            BedNumber = m.BedNumber,
                            NursingLevel = m.NursingLevel,
                            BedID = m.BedID,
                            DepartmentListID = m.DepartmentListID,
                            StationID = m.StationID,
                            Age = m.Age,
                            AgeDetail = m.AgeDetail,
                            DateOfBirth = n.DateOfBirth,
                            CurrentAge = n.DateOfBirth.HasValue ? AgeCalculat.GetAge(n.DateOfBirth.Value, DateTime.Now.Date) : m.Age,
                            Diagnosis = m.Diagnosis
                        };
            return await query.FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取单位病人资料（派班用）
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="stationID"></param>
        /// <returns></returns>

        public async Task<List<AttendancePatientList>> GetInPatientAttendanceAsync(string hospitalID, int stationID)
        {
            var query = from m in _dbContext.InpatientDatas
                        join n in _dbContext.PatientBasicDatas on m.PatientID equals n.PatientID
                        join b in _dbContext.BedListInfos on m.BedID equals b.ID
                        where m.StationID == stationID && b.HospitalID == hospitalID && n.HospitalID == hospitalID
                        && m.HospitalID == hospitalID && m.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)
                        orderby b.Sort
                        select new AttendancePatientList
                        {
                            ChartNo = n.ChartNo,
                            DateOfBirth = n.DateOfBirth.Value.ToString(),
                            InpatientID = m.ID,
                            PatientID = n.PatientID,
                            PatientName = n.PatientName,
                            Gender = n.Gender,
                            BedNumber = m.BedNumber,
                            ICUFlag = m.ICUFlag,
                            NursingLevel = m.NursingLevel,
                            Diagnosis = m.Diagnosis,
                            PhysicianName = "",
                            BedID = m.BedID,
                            AttendingPhysicianID = m.AttendingPhysicianID,
                            BillingPattern = m.BillingPattern,
                            AdmissionDate = m.AdmissionDate,
                            AdmissionTime = m.AdmissionTime,
                            StationID = m.StationID,
                            InpatientDays = (DateTime.Now - m.AdmissionDate).Days,
                            CaseNumber = m.CaseNumber,
                            AgeDetail = m.AgeDetail,
                            PrePayments = m.PrePayments,
                            TotalCharges = m.TotalCharges,
                            Level = m.NursingLevel,
                            ChiefComplaint = m.ChiefComplaint
                        };
            return await query.ToListAsync();
        }

        /// <summary>
        /// 通过stationID和bedID获取在院患者数据
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="bedID"></param>
        /// <returns></returns>
        public async Task<InpatientDataInfo> GetByBedID(int stationID, int bedID)
        {
            var list = await _dbContext.InpatientDatas.Where(m => m.StationID == stationID && m.BedID == bedID
            && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DeleteFlag != "*")
                .OrderByDescending(m => m.AdmissionDate).ThenByDescending(m => m.AdmissionTime).ToListAsync();
            if (list.Count > 0)
            {
                return list[0];
            }
            return null;
        }
        /// <summary>
        /// 通过stationID和bedID获取在院患者数据
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="bedID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<InpatientDataInfo> GetByBedID(int stationID, int bedID, string hospitalID)
        {
            var list = await _dbContext.InpatientDatas.Where(m => m.StationID == stationID && m.BedID == bedID && m.HospitalID == hospitalID
            && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DeleteFlag != "*")
                .OrderByDescending(m => m.AdmissionDate).ThenByDescending(m => m.AdmissionTime).ToListAsync();
            if (list.Count > 0)
            {
                return list[0];
            }
            return null;
        }

        /// <summary>
        /// 通过inpatientIDList获取患者数据（在院）
        /// </summary>
        /// <param name="inpatientIDList"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientListByInpatientIDListAsync(List<string> inpatientIDList)
        {
            return await _dbContext.InpatientDatas.Where(m => inpatientIDList.Contains(m.ID) && m.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).ToListAsync();
        }

        /// <summary>
        /// 通过inpatientIDList获取患者数据（包含出院）
        /// </summary>
        /// <param name="inpatientIDList"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetListByInpatientIDListAsync(List<string> inpatientIDList)
        {
            return await _dbContext.InpatientDatas.Where(m => inpatientIDList.Contains(m.ID) && m.DeleteFlag != "*").ToListAsync(); ;
        }

        public async Task<List<InpatientDataInfo>> GetNursingLevelByInpatientIDListAsync(List<string> inpatientIDList)
        {
            return await _dbContext.InpatientDatas.Where(m => inpatientIDList.Contains(m.ID) && m.DeleteFlag != "*")
                .Select(m => new InpatientDataInfo
                {
                    ID = m.ID,
                    NursingLevel = m.NursingLevel
                }).ToListAsync();
        }

        /// <summary>
        /// 通过病区的派班班次或及其责护获取患者信息（包含出院）
        /// </summary>
        /// <param name="shiftDate"></param>
        /// <param name="stationID"></param>
        /// <param name="stationShiftID"></param>
        /// <param name="nurseID"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientBysAttendanceAsync(DateTime shiftDate, int stationID, int stationShiftID, string nurseID)
        {
            var InpatientDataList = new List<InpatientDataInfo>();
            if (!string.IsNullOrEmpty(nurseID))
            {
                var result = (from a in _dbContext.InpatientDatas
                              where _dbContext.Attendance.Any(m => m.InpatientID == a.ID
                              && m.AttendanceDate == shiftDate
                              && m.StationID == stationID
                              && m.StationShiftID == stationShiftID
                              && m.NurseEmployeeID == nurseID
                              && m.DeleteFlag != "*")
                              && a.DeleteFlag != "*" && a.StationID == stationID
                              select a);
                InpatientDataList = await result.ToListAsync();
            }
            else
            {
                var result1 = (from a in _dbContext.InpatientDatas
                               where _dbContext.Attendance.Any(m => m.InpatientID == a.ID
                               && m.AttendanceDate == shiftDate
                               && m.StationID == stationID
                               && m.StationShiftID == stationShiftID
                               && m.DeleteFlag != "*")
                               && a.DeleteFlag != "*" && a.StationID == stationID
                               select a);
                InpatientDataList = await result1.ToListAsync();
            }
            return InpatientDataList;
        }
        /// <summary>
        /// 通过病区的派班班次或及其责护获取患者信息（包含出院）
        /// </summary>
        /// <param name="shiftDate"></param>
        /// <param name="stationID"></param>
        /// <param name="stationShiftID"></param>
        /// <param name="nurseID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientBysAttendanceAsync(DateTime shiftDate, int stationID, int stationShiftID, string nurseID, string hospitalID)
        {
            var InpatientDataList = new List<InpatientDataInfo>();
            if (!string.IsNullOrEmpty(nurseID))
            {
                var result = (from a in _dbContext.InpatientDatas
                              where _dbContext.Attendance.Any(m => m.InpatientID == a.ID
                              && m.AttendanceDate == shiftDate
                              && m.StationID == stationID
                              && m.StationShiftID == stationShiftID
                              && m.NurseEmployeeID == nurseID
                              && m.DeleteFlag != "*")
                              && a.DeleteFlag != "*" && a.StationID == stationID
                              && a.HospitalID == hospitalID
                              select a);
                InpatientDataList = await result.ToListAsync();
            }
            else
            {
                var result1 = (from a in _dbContext.InpatientDatas
                               where _dbContext.Attendance.Any(m => m.InpatientID == a.ID
                               && m.AttendanceDate == shiftDate
                               && m.StationID == stationID
                               && m.StationShiftID == stationShiftID
                               && m.DeleteFlag != "*")
                               && a.DeleteFlag != "*" && a.StationID == stationID
                               && a.HospitalID == hospitalID
                               select a);
                InpatientDataList = await result1.ToListAsync();
            }
            return InpatientDataList;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="inpatientIDList"></param>
        /// <returns></returns>
        public async Task<List<SimpleInpatient>> GetSimpleListAsync(List<string> inpatientIDList)
        {
            var set = new HashSet<string>(inpatientIDList);

            return await _dbContext.InpatientDatas.Where(m => set.Contains(m.ID) && m.DeleteFlag != "*").AsNoTracking()
                .Select(m => new SimpleInpatient
                {
                    ID = m.ID,
                    PatientID = m.PatientID,
                    CaseNumber = m.CaseNumber,
                    ChartNo = m.ChartNo,
                    DepartmentListID = m.DepartmentListID,
                    StationID = m.StationID,
                    ICDCode = m.ICDCode,
                    Diagnosis = m.Diagnosis,
                    AdmissionDate = m.AdmissionDate,
                    AdmissionTime = m.AdmissionTime,
                    DischargeDate = m.DischargeDate,
                    DischargeTime = m.DischargeTime,
                    Age = m.Age,
                    AgeDetail = m.AgeDetail
                }).AsNoTracking().ToListAsync();
        }

        /// <summary>
        /// 通过起止时间获取病区内精简患者数据（出院）
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<List<SimpleInpatient>> GetDischargeSimplePatientsAsync(DateTime startTime, DateTime endTime, int stationID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.DischargeDate.HasValue && m.StationID == stationID
            && m.DischargeDate >= startTime && m.DischargeDate <= endTime)
            .Join(_dbContext.PatientBasicDatas, u => u.PatientID, d => d.PatientID, (u, d) => new SimpleInpatient
            {
                ID = u.ID,
                PatientID = u.PatientID,
                Gender = d.Gender,
                CaseNumber = u.CaseNumber,
                ChartNo = u.ChartNo,
                DepartmentListID = u.DepartmentListID,
                StationID = u.StationID,
                ICDCode = u.ICDCode,
                Diagnosis = u.Diagnosis,
                AdmissionDate = u.AdmissionDate,
                AdmissionTime = u.AdmissionTime,
                DischargeDate = u.DischargeDate,
                DischargeTime = u.DischargeTime,
                Age = u.Age,
                AgeDetail = u.AgeDetail
            }).AsNoTracking().OrderByDescending(m => m.DischargeDate).ThenByDescending(m => m.DischargeTime).ToListAsync();
        }

        /// <summary>
        /// 根据住院号集合获取全部病人列表,带上删除的，统计使用-李帅
        /// </summary>
        /// <param name="inpatientIDs"></param>
        /// <returns></returns>
        public async Task<List<InpatientClusterView>> GetAllStatisticsByInpatientIDs(List<string> inpatientIDs)
        {
            var InpatientDataList = new List<InpatientClusterView>();
            foreach (var item in inpatientIDs)
            {
                var tempList = await _dbContext.InpatientDatas.Where(m => m.ID == item)
                .Select(m => new InpatientClusterView
                {
                    ID = m.ID,
                    NumberOfAdmissions = m.NumberOfAdmissions,
                    Age = m.Age,
                    DischargeDate = m.DischargeDate,
                    AdmissionDate = m.AdmissionDate,
                    StationID = m.StationID
                }).ToListAsync();
                InpatientDataList = InpatientDataList.Union(tempList).ToList();
            }
            return InpatientDataList;
        }

        /// <summary>
        /// 获取患者在院信息 姓名 主责医生
        /// </summary>
        /// <param name="inpatienID"></param>
        /// <returns></returns>
        public async Task<InPatientDataMarkView> GetInpatientInfo(string inpatienID)
        {
            var resultData = await (from a in _dbContext.InpatientDatas
                                    join b in _dbContext.PatientBasicDatas
                                          on a.PatientID equals b.PatientID
                                    where a.ID == inpatienID && InHospitalStatus.INHOSPITALLIST.Contains(a.InHospitalStatus ?? -1) && a.DeleteFlag != "*"
                                    select new InPatientDataMarkView
                                    {
                                        BedID = a.BedID,
                                        BedNumber = a.BedNumber,
                                        InpatientID = a.ID,
                                        PatientName = b.PatientName,
                                        StationID = a.StationID,
                                        AttendingPhysicianID = a.AttendingPhysicianID
                                    }).FirstOrDefaultAsync();
            return resultData;
        }

        /// <summary>
        /// 获取科室当前在院患者ID
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<List<string>> GetInPatientIDsAsync(string hospitalID, int stationID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.HospitalID == hospitalID && (m.StationID == stationID || stationID == 999999) && m.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).Select(m => m.ID).ToListAsync();
        }

        /// <summary>
        /// 根据医院获取全部在院患者StationID及BedNumber
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<List<InpatientNursingLevelView>> GetInpatientNursingLevelAsync(string hospitalID, int stationID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.HospitalID == hospitalID && m.StationID == stationID)
            .Select(m => new InpatientNursingLevelView
            {
                ID = m.ID,
                PatientID = m.PatientID,
                StationID = m.StationID,
                BedNumber = m.BedNumber,
                NursingLevel = m.NursingLevel,
                AttendingPhysicianID = m.AttendingPhysicianID
            }).ToListAsync();
        }

        /// <summary>
        /// 获取病人所在科室（获取不到数据默认为0）
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<int> GetDepartmentListIDByInpatientIDAsync(string inpatientID)
        {
            return await _dbContext.InpatientDatas.AsNoTracking().Where(m => m.ID == inpatientID).Select(m => m.DepartmentListID).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 通过inpatientIDList获取病人基本信息（包含出院）
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="inpatientIDList"></param>
        /// <returns></returns>
        public async Task<List<PatientData>> GetInpatientBasicDataByInpatientIDList(string hospitalID, List<string> inpatientIDList)
        {
            var query = from m in _dbContext.InpatientDatas
                        join n in _dbContext.PatientBasicDatas on m.PatientID equals n.PatientID
                        join o in _dbContext.BedListInfos on m.BedID equals o.ID
                        where inpatientIDList.Contains(m.ID) && m.HospitalID == hospitalID && m.DeleteFlag != "*" && n.DeleteFlag != "*"
                        && o.HospitalID == hospitalID
                        orderby o.Sort
                        select new PatientData
                        {
                            InpatientID = m.ID,
                            PatientName = n.PatientName,
                            BedNumber = m.BedNumber,
                            AdmissionDate = m.AdmissionDate,
                            AgeDetail = m.AgeDetail,
                            Diagnosis = m.Diagnosis,
                            Gender = n.Gender,
                            DischargeFlag = !InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1),
                            StationID = m.StationID,
                            CaseNumber = m.CaseNumber,
                            LocalCaseNumber = m.LocalCaseNumber,
                            Age = m.Age,
                            ChartNo = m.ChartNo,
                            NumberOfAdmissions = m.NumberOfAdmissions,
                            BedSort = o.Sort
                        };
            return await query.ToListAsync();
        }

        /// <summary>
        /// 通过起止时间及其stationID获取患者数据（出院）
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public Task<List<InpatientDataInfo>> GetDischargeAsync(DateTime startTime, DateTime endTime, int? stationID = null)
        {
            var query = _dbContext.InpatientDatas.Where(m => InHospitalStatus.DISCHARGEDHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)
            && m.DischargeDate.HasValue && m.DischargeTime.HasValue
            && m.DischargeDate.Value > startTime.Date
            && m.DischargeDate.Value < endTime.Date
            || (m.DischargeDate.Value == startTime.Date && m.DischargeTime.Value >= startTime.TimeOfDay)
            || (m.DischargeDate == endTime.Date && m.DischargeTime.Value <= endTime.TimeOfDay));
            if (stationID != null)
            {
                query = query.Where(m => m.StationID == stationID);
            }
            return query.Where(m => m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 通过起止时间及其stationID获取患者数据（入院）
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetAdmissionAsync(DateTime startTime, DateTime endTime, int stationID)
        {
            var list = await _dbContext.InpatientDatas.Where(m => m.AdmissionDate >= startTime.Date
                && m.AdmissionDate <= endTime.Date
                && m.StationID == stationID
                && m.DeleteFlag != "*").ToListAsync();
            return list.Where(m => m.AdmissionDate.Add(m.AdmissionTime) >= startTime && m.AdmissionDate.Add(m.AdmissionTime) <= endTime).ToList();
        }

        /// <summary>
        /// 通过日期及stationID获取患者数据（出院）
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="stationID">病区序号</param>
        /// <param name="hospitalID">医疗院所序号</param>
        /// <returns></returns>
        public async Task<List<InpatientQuery>> GetDischargePatient(DateTime date, int stationID, string hospitalID)
        {
            var query = await _dbContext.InpatientDatas.Where(m => m.HospitalID == hospitalID && m.StationID == stationID && m.DischargeDate == date)
                .Join(_dbContext.PatientBasicDatas, u => u.PatientID, d => d.PatientID, (u, d) => new InpatientQuery
                {
                    InpatientID = u.ID,
                    PatientID = u.PatientID,
                    CaseNumber = u.CaseNumber,
                    ChartNo = u.ChartNo,
                    DepartmentID = u.DepartmentListID,
                    StationID = u.StationID,
                    BedID = u.BedID,
                    BedNumber = u.BedNumber,
                    PatientName = d.PatientName,
                }).ToListAsync();
            return query;
        }

        /// <summary>
        /// 通过stationID获取病区在院病人(不可修改)
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<List<InPatientDataMarkView>> GetInPatientAsyncAsNoTracking(string hospitalID, int stationID)
        {
            var resultData = from a in _dbContext.InpatientDatas
                             join b in _dbContext.PatientBasicDatas
                                   on a.PatientID equals b.PatientID
                             where a.HospitalID == hospitalID && a.StationID == stationID
                             && a.InHospitalStatus.HasValue && InHospitalStatus.INHOSPITALLIST.Contains(a.InHospitalStatus ?? -1) && a.DeleteFlag != "*"
                             select new InPatientDataMarkView
                             {
                                 CaseNumber = a.CaseNumber,
                                 BedID = a.BedID,
                                 BedNumber = a.BedNumber,
                                 Diagnose = a.Diagnosis,
                                 ChartNo = a.ChartNo,
                                 Age = a.Age.ToString(),
                                 AgeDetail = a.AgeDetail,
                                 NursingLevel = a.NursingLevel.Trim(),
                                 ICUFlag = a.ICUFlag,
                                 AttendingPhysicianID = a.AttendingPhysicianID,
                                 BillingPattern = a.BillingPattern,
                                 PrePayments = a.PrePayments,
                                 TotalCharges = a.TotalCharges,
                                 InpatientID = a.ID,
                                 PatientName = b.PatientName,
                                 AdmissionDate = a.AdmissionDate,
                                 AdmissionTime = a.AdmissionTime,
                                 Gender = b.Gender,
                                 DateOfBirth = b.DateOfBirth.HasValue ? b.DateOfBirth.Value.ToString() : "",
                                 StationID = a.StationID,
                                 LocalCaseNumber = a.LocalCaseNumber,
                                 DepartmentListID = a.DepartmentListID,
                                 AdmWardDateTime = a.AdmWardDateTime.HasValue ? a.AdmWardDateTime.Value.ToString("yyyy-MM-dd HH:mm:ss") : "",
                             };
            return await resultData.AsNoTracking().ToListAsync();
        }

        /// <summary>
        /// 获取时间点在院病人
        /// </summary>
        /// <param name="dateTime"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientsByTime(DateTime dateTime)
        {
            return await _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*"
            && (m.AdmissionDate < dateTime.Date || (m.AdmissionDate == dateTime.Date && m.AdmissionTime <= dateTime.TimeOfDay))
            && (m.DischargeDate > dateTime.Date || (m.DischargeDate == dateTime.Date && m.DischargeTime > dateTime.TimeOfDay) || m.DischargeDate == null)
            ).ToListAsync();
        }

        public async Task<bool> AddAsync(InpatientDataInfo inpatientDataInfo)
        {
            if (inpatientDataInfo == null)
            {
                return false;
            }
            _dbContext.InpatientDatas.Add(inpatientDataInfo);
            return await _dbContext.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 通过chartNo和numberOfAdmissions获取患者数据（包含删除）
        /// </summary>
        /// <param name="chartNo"></param>
        /// <param name="numberOfAdmissions">住院次数</param>
        /// <returns></returns>
        public async Task<InpatientDataInfo> GetAsync(string chartNo, int numberOfAdmissions)
        {
            return await _dbContext.InpatientDatas.Where(m => m.ChartNo == chartNo && m.NumberOfAdmissions == numberOfAdmissions).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 透过CaseNumber获取在院病人信息
        /// </summary>
        /// <param name="caseNumber">住院序号</param>
        /// <returns></returns>
        public async Task<InpatientDataInfo> GetByCaseNumberAsync(string caseNumber)
        {
            return await _dbContext.InpatientDatas.Where(m => m.CaseNumber == caseNumber && m.DeleteFlag != "*"
            && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取当前在院病人（不包含删除）
        /// </summary>
        /// <returns></returns>

        public async Task<List<InpatientDataInfo>> GetInpatientData()
        {
            return await _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).ToListAsync();
        }

        /// <summary>
        /// 获取全院在院患者
        /// </summary>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientListAsync()
        {
            return await _dbContext.InpatientDatas.Where(m => InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).ToListAsync();
        }

        /// <summary>
        /// 根据CaseNumber集合获取在院病人数据(数据同步使用)（包含删除）
        /// </summary>
        /// <param name="ids">CaseNumber集合</param>
        /// <returns></returns>
        public List<InpatientDataInfo> GetByCaseNumbersAll(List<string> ids)
        {
            var InpatientDataList = new List<InpatientDataInfo>();
            foreach (var item in ids)
            {
                var tempList = _dbContext.InpatientDatas.Where(m => m.CaseNumber == item).ToList();
                InpatientDataList = InpatientDataList.Union(tempList).ToList();
            }
            return InpatientDataList;
        }

        /// <summary>
        /// 根据出院时间范围和科室ID获取病人列表
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="deptmentID"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetDischargePatientsByTimeAndDeptAsync(DateTime startTime, DateTime endTime, int stationID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.DischargeDate.HasValue && m.StationID == stationID
            && m.DischargeDate >= startTime && m.DischargeDate <= endTime)
            .OrderByDescending(m => m.DischargeDate).ThenByDescending(m => m.DischargeTime).ToListAsync();
        }

        public async Task<List<InpatientDataInfo>> GetForExternalAsync(string hospitalID)
        {
            var inpatients = await _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && m.HospitalID == hospitalID
            && (m.InHospitalStatus == 30 || m.InHospitalStatus == 40 || m.InHospitalStatus == 50)).ToListAsync();

            return inpatients;
        }

        /// <summary>
        /// 通过caseNumber获取患者数据（包含删除，包含出院）
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        public InpatientDataInfo GetInpatientIDByCaseNumber(string caseNumber)
        {
            return _dbContext.InpatientDatas.Where(m => m.CaseNumber == caseNumber).FirstOrDefault();
        }

        /// <summary>
        /// 通过日期获取该日期及之后入院的患者数据
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientsByDate(DateTime date)
        {
            return await _dbContext.InpatientDatas.Where(m => m.AdmissionDate >= date && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 获取起止日期内出院患者
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetDischargePatientsAsync(DateTime startDate, DateTime endDate)
        {
            return await _dbContext.InpatientDatas.Where(m => m.DischargeDate.HasValue
            && m.DischargeDate >= startDate.Date && m.DischargeDate <= endDate && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据CaseNumber查找全部记录
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="containsDelete"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetByCaseNumberAllAsync(string caseNumber, bool containsDelete)
        {
            var list = await _dbContext.InpatientDatas.Where(m => m.CaseNumber == caseNumber).ToListAsync();
            if (!containsDelete)
            {
                list = list.Where(m => m.DeleteFlag != "*").ToList();
            }
            return list;
        }

        /// <summary>
        /// 根据InpatientID获取患者信息（包括删除，同步用）
        /// </summary>
        /// <param name="inpatientID">住院序号</param>
        /// <returns></returns>
        public async Task<InpatientDataInfo> GetByInpatientIDAllAsync(string inpatientID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.ID == inpatientID).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据日期获取时间段内入院患者记录
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="stationID">病区ID（可空）</param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetAdmissionDateAsync(DateTime startDate, DateTime endDate, int? stationID)
        {
            var list = await _dbContext.InpatientDatas.Where(m => m.AdmissionDate >= startDate
            && m.AdmissionDate < endDate
            && m.DeleteFlag != "*").ToListAsync();
            if (stationID.HasValue)
            {
                list = list.Where(m => m.StationID == stationID).ToList();
            }
            return list;
        }

        /// <summary>
        /// 获取还在院病人（病人还在CCC上）
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetAllInpadientListAsync()
        {
            return await _dbContext.InpatientDatas.Where(m => m.StationID != 0 && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 查询病人InpatientID和CaseNumber的对应关系
        /// </summary>
        /// <param name="caseNumbers"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetAllInpatientIDByCaseNumbers(List<string> caseNumbers)
        {
            return await _dbContext.InpatientDatas.Where(m => caseNumbers.Contains(m.CaseNumber) && m.DeleteFlag != "*"
                && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1))
                .Select(m => new InpatientDataInfo
                {
                    ID = m.ID,
                    CaseNumber = m.CaseNumber,
                    DepartmentListID = m.DepartmentListID,
                    StationID = m.StationID,
                    BedNumber = m.BedNumber,
                    BedID = m.BedID,
                    PatientID = m.PatientID,
                    ChartNo = m.ChartNo,
                    HospitalID = m.HospitalID,
                    NumberOfAdmissions = m.NumberOfAdmissions
                }).ToListAsync();
        }

        public async Task<List<InpatientDataInfo>> GetInpatientByChartNos(List<string> chartNos)
        {
            return await _dbContext.InpatientDatas.Where(m => chartNos.Contains(m.ChartNo) && m.DeleteFlag != "*"
                && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).ToListAsync();
        }

        /// <summary>
        /// 根据医院获取全部在院患者charNo及NumberOfAdmissions
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<InpatientPartData>> GetAllInpatientAsync(string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1))
            .Select(m => new InpatientPartData
            {
                ChartNo = m.ChartNo,
                NumberOfAdmissions = m.NumberOfAdmissions,
                LocalCaseNumber = m.LocalCaseNumber,
                CaseNumber = m.CaseNumber,
                StationID = m.StationID
            }).ToListAsync();
        }

        /// <summary>
        /// 根据医院获取全部在院患者号码
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<InPatientNumberView>> GetAllInPatientAsyncAsNoTracking(string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.HospitalID == hospitalID)
            .Select(m => new InPatientNumberView
            {
                InpatientID = m.ID,
                PatientID = m.PatientID,
                CaseNumber = m.CaseNumber,
                ChartNo = m.ChartNo
            }).AsNoTracking().ToListAsync();
        }
        /// <summary>
        /// 获取患者信息根据CaseNumber或者StationID
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="stationID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetPatientByCaseNumberAndStationID(string caseNumber, int? stationID, string hospitalID)
        {
            var query = _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && m.HospitalID == hospitalID);

            if (!string.IsNullOrEmpty(caseNumber))
            {
                query = query.Where(m => m.CaseNumber == caseNumber);
            }
            if (stationID.HasValue)
            {
                query = query.Where(m => m.StationID == stationID.Value);
            }

            return await query.ToListAsync();
        }
        /// <summary>
        /// 获取在院病人CaseNumber集合
        /// </summary>
        /// <returns></returns>
        public async Task<List<string>> GetAllInpadientCaseNumberAsync()
        {
            return await _dbContext.InpatientDatas.Where(m => m.StationID != 0 && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DeleteFlag != "*").Select(m => m.CaseNumber).ToListAsync();
        }

        /// <summary>
        /// 根据病区ID,获取在院病人数据
        /// </summary>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<int> GetInpatientCountByStationID(int stationID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.StationID == stationID && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DeleteFlag != "*" && m.BedID != 0).CountAsync();
        }

        /// <summary>
        /// 获取所有在院病人病人信息
        /// </summary>
        /// <returns></returns>
        public async Task<List<ICCAInpatientView>> GetAllInpatientAsNoTrackAsync()
        {
            return await _dbContext.InpatientDatas.AsNoTracking().Where(m => m.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1))
                .Select(m => new ICCAInpatientView
                {
                    InpatientID = m.ID,
                    PatientID = m.PatientID,
                    CaseNumber = m.CaseNumber,
                    ChartNo = m.ChartNo,
                    StationID = m.StationID,
                    DepartmentListID = m.DepartmentListID,
                    BedID = m.BedID,
                    BedNumber = m.BedNumber,
                    NursingLevel = m.NursingLevel
                }).ToListAsync();
        }
        /// <summary>
        /// 获取所有在院病人病人信息
        /// </summary>
        /// <returns></returns>
        public async Task<List<StatisticInpatientView>> GetAllPatientAsNoTrackAsync()
        {
            return await _dbContext.InpatientDatas.AsNoTracking().Where(m => m.DeleteFlag != "*")
                .Select(m => new StatisticInpatientView
                {
                    InpatientID = m.ID,
                    ICDCode = m.ICDCode,
                    Diagnosis = m.Diagnosis,
                    HospitalID = m.HospitalID,
                    Age = m.Age,
                    ChiefComplaint = m.ChiefComplaint,
                    NumberOfAdmissions = m.NumberOfAdmissions,
                    AdmissionDate = m.AdmissionDate,
                    AdmissionTime = m.AdmissionTime,
                    DepartmentListID = m.DepartmentListID,
                    NursingLevel = m.NursingLevel,
                }).ToListAsync();
        }

        /// <summary>
        /// 获取包括该caseNumber的记录
        /// </summary>
        /// <param name="chartNO">病历号</param>
        /// <param name="visitID">住院次数</param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetByChartnoAndVisitID(string chartNO, int visitID, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.ChartNo == chartNO && m.NumberOfAdmissions == visitID && m.HospitalID == hospitalID).ToListAsync();
        }

        public async Task<T> GetFieldValueByCaseNumber<T>(string caseNumber, [DisallowNull] Expression<Func<InpatientDataInfo, T>> predicate)
        {
            var whereCondition = _dbContext.InpatientDatas.Where(m => m.CaseNumber == caseNumber && m.DeleteFlag != "*");
            if (predicate != null)
            {
                return await whereCondition.Select(predicate).FirstOrDefaultAsync();
            }

            return default;
        }
        public async Task<T> GetFieldValueByInpatientID<T>(string inpatientID, [DisallowNull] Expression<Func<InpatientDataInfo, T>> predicate)
        {
            var whereCondition = _dbContext.InpatientDatas.Where(m => m.ID == inpatientID && m.DeleteFlag != "*");
            if (predicate != null)
            {
                return await whereCondition.Select(predicate).FirstOrDefaultAsync();
            }

            return default;
        }
        /// <summary>
        /// 透过住院序号或取病人基本数据
        /// </summary>
        /// <param name="inpatientID">住院序号</param>
        /// <returns></returns>
        public async Task<InpatientDataInfo> GetByInpatientIDAsync(string inpatientID)
        {
            return await _dbContext.InpatientDatas.Where(m =>
              m.ID == inpatientID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<List<InpatientDataInfo>> GetSimpleByIDs(string[] inpatientIDs)
        {
            return await _dbContext.InpatientDatas.Where(m => inpatientIDs.Contains(m.ID)).AsNoTracking()
                .Select(m => new InpatientDataInfo
                {
                    ID = m.ID,
                    PatientID = m.PatientID,
                    ChartNo = m.ChartNo,
                    LocalCaseNumber = m.LocalCaseNumber,
                    StationID = m.StationID,
                    BedID = m.BedID,
                    BedNumber = m.BedNumber,
                    Age = m.Age,
                    AgeDetail = m.AgeDetail,
                    InHospitalStatus = m.InHospitalStatus,
                }).ToListAsync();
        }
        /// <summary>
        /// 获取移动端病区内需要进行巡视的病人列表
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="hospitalID"></param>
        /// <param name="eventAssessListID"></param>
        /// <param name="inpatentID"></param>
        /// <returns></returns>
        public async Task<List<PatientData>> GetNeedPatralInpatientDataList(int stationID, string hospitalID, int eventAssessListID, string inpatentID)
        {
            var resultData = (from a in _dbContext.InpatientDatas
                              join b in _dbContext.PatientBasicDatas on a.PatientID equals b.PatientID
                              join c in _dbContext.PatientEventInfos on a.ID equals c.InpatientID
                              where a.HospitalID == hospitalID && a.StationID == stationID && InHospitalStatus.INHOSPITALLIST.Contains(a.InHospitalStatus ?? -1) && a.DeleteFlag != "*"
                              && c.AssessListID == eventAssessListID && c.DeleteFlag != "*" && (string.IsNullOrEmpty(inpatentID) || a.ID == inpatentID)
                              select new PatientData
                              {
                                  InpatientID = a.ID,
                                  BedNumber = a.BedNumber,
                                  PatientName = b.PatientName
                              });
            return await resultData.AsNoTracking().ToListAsync();
        }
        /// <summary>
        /// 通过caseNumberList获取患者数据（银川市一同步使用(在院出院)）
        /// 2023-03-23 (宏力出院同步使用)
        /// </summary>
        /// <param name="caseNumberList"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientDataByCaseNumberListAsync(List<string> caseNumberList)
        {
            return await _dbContext.InpatientDatas.Where(m => caseNumberList.Contains(m.CaseNumber) && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取所有病人的CaseNumber
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<string>> GetAllPatientCaseNumberForExternalAsync(string hospitalID)
        {
            return await _dbContext.InpatientDatas.AsNoTracking().Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").Select(m => m.CaseNumber).ToListAsync();
        }
        public async Task<List<InpatientDataInfo>> GetInpatientByIDAsync(List<string> inpatientIDs)
        {
            return await _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && m.DischargeDate == null && inpatientIDs.Contains(m.ID))
                .Select(m => new InpatientDataInfo { ID = m.ID, CaseNumber = m.CaseNumber }).ToListAsync();
        }
        public async Task<List<InpatientDataInfo>> GetInpatientByStationListIDAsync(string hospitalID, List<int> stationListIDs)
        {
            return await _dbContext.InpatientDatas.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*" && m.DischargeDate == null && stationListIDs.Contains(m.StationID)).ToListAsync();
        }
        /// <summary>
        /// 根据ChartNO集合获取病人住院信息
        /// 索引：IX_InpatientData_ChartNo_DischargeDate20220416
        /// </summary>
        /// <param name="chartNOs"></param>
        /// <returns>{InpatientID,PatientID,StationID,BedID}</returns>
        public async Task<List<PatientData>> GetInpatientByChartNosAsNoTrackAsync(List<string> chartNOs)
        {
            return await _dbContext.InpatientDatas//.AsNoTracking()
                .Where(m => chartNOs.Contains(m.ChartNo) && m.DeleteFlag != "*")
                .Select(m => new PatientData
                {
                    InpatientID = m.ID,
                    PatientID = m.PatientID,
                    StationID = m.StationID,
                    BedID = m.BedID,
                    ChartNo = m.ChartNo
                })
                .ToListAsync();
        }

        public async Task<object> GetJoinPartDataByInpatientIDAsync(string inpatientID)
        {
            var query = from a in _dbContext.InpatientDatas.Where(m => m.ID == inpatientID && m.DeleteFlag != "*")
                        join b in _dbContext.PatientBasicDatas.Where(n => n.DeleteFlag != "*")
                        on a.PatientID equals b.PatientID
                        select new
                        {
                            InpatientID = a.ID,
                            b.IdentityID,
                            b.PatientName
                        };
            return await query.AsNoTracking().FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取有护理级别患者数据
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<PatientData>> GetLevelInpatientDataList(int stationID, string hospitalID)
        {
            var resultData = (from a in _dbContext.InpatientDatas
                              join b in _dbContext.PatientBasicDatas on a.PatientID equals b.PatientID
                              where a.HospitalID == hospitalID && a.StationID == stationID && InHospitalStatus.INHOSPITALLIST.Contains(a.InHospitalStatus ?? -1)
                              && !string.IsNullOrEmpty(a.NursingLevel) && a.DeleteFlag != "*"
                              select new PatientData
                              {
                                  InpatientID = a.ID,
                                  BedNumber = a.BedNumber,
                                  PatientName = b.PatientName
                              });
            return await resultData.AsNoTracking().ToListAsync();
        }
        /// <summary>
        /// 获取病人基本信息
        /// </summary>
        /// <param name="caseNumbers">住院号集合</param>
        /// <returns></returns>
        public async Task<List<PatientData>> GetInpatientDataByCaseNumbers(IEnumerable<string> caseNumbers)
        {
            var query = from m in _dbContext.InpatientDatas
                        join n in _dbContext.PatientBasicDatas on new { m.HospitalID, m.PatientID } equals new { n.HospitalID, n.PatientID }
                        join b in _dbContext.BedListInfos on new { m.HospitalID, m.BedID } equals new { b.HospitalID, BedID = b.ID }
                        where caseNumbers.Contains(m.CaseNumber) && m.HospitalID == _config.Value.HospitalID && m.DeleteFlag != "*"
                        && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)
                        orderby b.Sort
                        select new PatientData
                        {
                            InpatientID = m.ID,
                            PatientID = n.PatientID,
                            ChartNo = n.ChartNo,
                            CaseNumber = m.CaseNumber,
                            Age = m.Age,
                            DateOfBirth = n.DateOfBirth,
                            Gender = n.Gender,
                            NursingLevel = m.NursingLevel,
                            StationID = m.StationID,
                            DepartmentListID = m.DepartmentListID,
                            BedID = m.BedID,
                            BedNumber = m.BedNumber,
                        };
            return await query.ToListAsync();
        }
        /// <summary>
        /// 获取病人基本信息（包括出院）
        /// </summary>
        /// <param name="caseNumbers">住院号集合</param>
        /// <returns></returns>
        public async Task<List<PatientData>> GetPatientDataByCaseNumbersContainsDischarge(IEnumerable<string> caseNumbers)
        {
            var query = from m in _dbContext.InpatientDatas
                        join n in _dbContext.PatientBasicDatas on new { m.HospitalID, m.PatientID } equals new { n.HospitalID, n.PatientID }
                        join b in _dbContext.BedListInfos on new { m.HospitalID, m.BedID } equals new { b.HospitalID, BedID = b.ID }
                        where caseNumbers.Contains(m.CaseNumber) && m.HospitalID == _config.Value.HospitalID && m.DeleteFlag != "*"
                        orderby b.Sort
                        select new PatientData
                        {
                            InpatientID = m.ID,
                            PatientID = n.PatientID,
                            ChartNo = n.ChartNo,
                            CaseNumber = m.CaseNumber,
                            Age = m.Age,
                            DateOfBirth = n.DateOfBirth,
                            Gender = n.Gender,
                            NursingLevel = m.NursingLevel,
                            StationID = m.StationID,
                            DepartmentListID = m.DepartmentListID,
                            BedID = m.BedID,
                            BedNumber = m.BedNumber,
                        };
            return await query.ToListAsync();
        }
        /// <summary>
        /// 获取病人基本信息
        /// </summary>
        /// <param name="hospitalID">医院编号</param>
        /// <param name="stationID">病区ID</param>
        /// <returns></returns>
        public async Task<List<PatientData>> GetPatientBasicDataByStationID(string hospitalID, int stationID)
        {
            var query = from m in _dbContext.InpatientDatas
                        join n in _dbContext.PatientBasicDatas on m.PatientID equals n.PatientID
                        where m.StationID == stationID && m.HospitalID == hospitalID && m.DeleteFlag != "*" && n.DeleteFlag != "*"
                        && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)
                        orderby m.BedNumber
                        select new PatientData
                        {
                            InpatientID = m.ID,
                            PatientName = n.PatientName,
                            BedNumber = m.BedNumber,
                            AdmissionDate = m.AdmissionDate,
                            AdmissionTime = m.AdmissionTime,
                            ChartNo = m.ChartNo
                        };
            return await query.ToListAsync();
        }
        public async Task<List<RecordReviewView>> GetRecordReviewByStationID(int stationID, string hospitalID)
        {
            return await (from a in _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && m.HospitalID == hospitalID && m.StationID == stationID && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1))
                          join b in _dbContext.PatientBasicDatas.Where(m => m.DeleteFlag != "*" && m.HospitalID == hospitalID)
                          on a.PatientID equals b.PatientID
                          select new RecordReviewView
                          {
                              InpatientID = a.ID,
                              CaseNumber = a.CaseNumber,
                              ChartNo = a.ChartNo,
                              LocalCaseNumber = a.LocalCaseNumber,
                              HospitalID = a.HospitalID,
                              PatientName = b.PatientName,
                              BedNumber = a.BedNumber,
                              Gender = b.Gender,
                              AgeDetail = a.AgeDetail,
                              DateOfBirth = b.DateOfBirth,
                              AdmissionDate = a.AdmissionDate,
                              InHospitalDays = (DateTime.Now.Date - a.AdmissionDate.Add(a.AdmissionTime)).Days,
                              DepartmenID = a.DepartmentListID,
                              BedID = a.BedID,
                              StationID = a.StationID
                          }).ToListAsync();
        }

        public async Task<List<RecordReviewView>> GetRecordReviewByEvent(DateTime startDate, DateTime endDate, int stationID, string hospitalID, List<int> assessListIDs)
        {
            return await (from a in _dbContext.PatientEventInfos.Where(m => m.DeleteFlag != "*" && m.OccurDate >= startDate && m.OccurDate <= endDate && m.StationID == stationID && assessListIDs.Contains(m.AssessListID))
                          join b in _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && m.HospitalID == hospitalID)
                          on a.InpatientID equals b.ID
                          join c in _dbContext.PatientBasicDatas.Where(m => m.DeleteFlag != "*" && m.HospitalID == hospitalID)
                          on a.PatientID equals c.PatientID
                          select new RecordReviewView
                          {
                              InpatientID = b.ID,
                              CaseNumber = b.CaseNumber,
                              ChartNo = b.ChartNo,
                              LocalCaseNumber = b.LocalCaseNumber,
                              HospitalID = b.HospitalID,
                              PatientName = c.PatientName,
                              BedNumber = a.BedNumber,
                              Gender = c.Gender,
                              AgeDetail = b.AgeDetail,
                              DateOfBirth = c.DateOfBirth,
                              AdmissionDate = b.AdmissionDate,
                              TransferOutDate = a.OccurDate,
                              TransferDate = a.AssessListID == EVENTSETTING_TRANSFEROUT_ID ? a.OccurDate : (DateTime?)null,
                              DischargeDate = a.AssessListID == EVENTSETTING_DISCHARGE_ID ? a.OccurDate : (DateTime?)null,
                              InHospitalDays = b.DischargeDate.HasValue && b.DischargeTime.HasValue ? (b.DischargeDate.Value.Add(b.DischargeTime.Value) - b.AdmissionDate.Add(b.AdmissionTime)).Days : (DateTime.Now.Date - b.AdmissionDate.Add(b.AdmissionTime)).Days,
                              DepartmenID = a.DepartmentListID,
                              BedID = a.BedID,
                              StationID = a.StationID
                          }).ToListAsync();
        }

        public async Task<List<RecordReviewView>> GetRecordReviewByLocalCaseNumber(string localCaseNumber, int stationID, string hospitalID)
        {
            // 2873 出院 2875 出科
            var assessListIDs = new List<int> { EVENTSETTING_DISCHARGE_ID, EVENTSETTING_TRANSFEROUT_ID };
            var historyData = await (from a in _dbContext.PatientEventInfos.Where(m => m.DeleteFlag != "*" && assessListIDs.Contains(m.AssessListID))
                                     join b in _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && m.HospitalID == hospitalID && m.LocalCaseNumber == localCaseNumber)
                                     on a.InpatientID equals b.ID
                                     join c in _dbContext.PatientBasicDatas.Where(m => m.DeleteFlag != "*" && m.HospitalID == hospitalID)
                                     on a.PatientID equals c.PatientID
                                     select new RecordReviewView
                                     {
                                         InpatientID = b.ID,
                                         CaseNumber = b.CaseNumber,
                                         ChartNo = b.ChartNo,
                                         LocalCaseNumber = b.LocalCaseNumber,
                                         HospitalID = b.HospitalID,
                                         PatientName = c.PatientName,
                                         BedNumber = a.BedNumber,
                                         Gender = c.Gender,
                                         AgeDetail = b.AgeDetail,
                                         DateOfBirth = c.DateOfBirth,
                                         AdmissionDate = b.AdmissionDate,
                                         TransferOutDate = a.OccurDate,
                                         InHospitalDays = b.DischargeDate.HasValue && b.DischargeTime.HasValue ? (b.DischargeDate.Value.Add(b.DischargeTime.Value) - b.AdmissionDate.Add(b.AdmissionTime)).Days : (DateTime.Now.Date - b.AdmissionDate.Add(b.AdmissionTime)).Days,
                                         DepartmenID = a.DepartmentListID,
                                         BedID = a.BedID,
                                         StationID = a.StationID
                                     }).ToListAsync();
            var currentData = await (from a in _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && m.HospitalID == hospitalID && m.LocalCaseNumber == localCaseNumber)
                                     join b in _dbContext.PatientBasicDatas.Where(m => m.DeleteFlag != "*" && m.HospitalID == hospitalID)
                                     on a.PatientID equals b.PatientID
                                     select new RecordReviewView
                                     {
                                         InpatientID = a.ID,
                                         CaseNumber = a.CaseNumber,
                                         ChartNo = a.ChartNo,
                                         LocalCaseNumber = a.LocalCaseNumber,
                                         HospitalID = a.HospitalID,
                                         PatientName = b.PatientName,
                                         BedNumber = a.BedNumber,
                                         Gender = b.Gender,
                                         AgeDetail = a.AgeDetail,
                                         DateOfBirth = b.DateOfBirth,
                                         AdmissionDate = a.AdmissionDate,
                                         TransferOutDate = a.DischargeDate,
                                         InHospitalDays = a.DischargeDate.HasValue && a.DischargeTime.HasValue ? (a.DischargeDate.Value.Add(a.DischargeTime.Value) - a.AdmissionDate.Add(a.AdmissionTime)).Days : (DateTime.Now.Date - a.AdmissionDate.Add(a.AdmissionTime)).Days,
                                         DepartmenID = a.DepartmentListID,
                                         BedID = a.BedID,
                                         StationID = a.StationID
                                     }).ToListAsync();
            return currentData.Union(historyData).Where(m => m.StationID == stationID).ToList();
        }
        /// <summary>
        /// 获取所有患者数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<InpatientDataView>> GetAsyncData()
        {
            var session = await _sessionCommonServer.GetSession();
            var query = from m in _dbContext.InpatientDatas
                        join n in _dbContext.PatientProfileMark
                        on m.ChartNo equals n.ChartNo
                        where InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DeleteFlag != "*" && m.HospitalID == session.HospitalID
                        select new InpatientDataView
                        {
                            InpatientID = m.ID,
                            StationID = m.StationID,
                            BedID = m.BedID,
                            BedNumber = m.BedNumber,
                            ChartNo = m.ChartNo,
                            MarkListJson = n.MarkList,
                            CaseNumber = m.CaseNumber,
                            NursingLevel = m.NursingLevel,
                            AdmissionDate = m.AdmissionDate
                        };
            return await query.ToListAsync();
        }
        /// <summary>
        /// 根据患者主键获取患者基本信息
        /// </summary>
        /// <param name="inpatientIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientBasicData>> GetPatientDataByIDs(DateTime startDate, DateTime endDate)
        {
            return await (from inpatientData in _dbContext.InpatientDatas
                          join patientBasic in _dbContext.PatientBasicDatas
                          on inpatientData.PatientID equals patientBasic.PatientID
                          where inpatientData.DeleteFlag != "*" && patientBasic.DeleteFlag != "*"
                          && inpatientData.DischargeDate >= startDate
                          && inpatientData.DischargeDate <= endDate
                          select new PatientBasicData
                          {
                              InpatientID = inpatientData.ID,
                              PatientName = patientBasic.PatientName,
                              Gender = patientBasic.Gender,
                              StationID = inpatientData.StationID,
                              Age = inpatientData.Age,
                              DateOfBirth = patientBasic.DateOfBirth,
                              ChartNo = inpatientData.ChartNo
                          }).ToListAsync();
        }
        /// <summary>
        /// 根据病区id和医院序号获取数据(部分字段)
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataView>> GetInpatientDataByStationID(int stationID, string hospitalID)
        {
            return await (from m in _dbContext.InpatientDatas
                          join n in _dbContext.BedListInfos on m.BedID equals n.ID
                          join b in _dbContext.PatientBasicDatas on new { m.HospitalID, m.PatientID } equals new { b.HospitalID, b.PatientID }
                          where m.StationID == stationID && n.StationID == stationID && b.HospitalID == hospitalID && m.HospitalID == hospitalID && n.HospitalID == hospitalID
                          && m.DeleteFlag != "*" && n.DeleteFlag != "*" && b.DeleteFlag != "*"
                          && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)
                          select new InpatientDataView
                          {
                              InpatientID = m.ID,
                              BedNumber = m.BedNumber,
                              BedID = m.BedID,
                              BedSort = n.Sort,
                              PatientName = b.PatientName,
                              NursingProcedureCode = m.NursingProcedureCode,
                              ChartNo = m.ChartNo,
                              DepartmentListID = m.DepartmentListID,
                              StationID = m.StationID,
                              NursingLevel = m.NursingLevel
                          }).ToListAsync();
        }
        /// <summary>
        /// 根据主键判断当前病人是否存在（不判断出院）
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<bool> ExistAsync(string inpatientID)
        {
            return await _dbContext.InpatientDatas.AsNoTracking().Where(m => m.ID == inpatientID && m.DeleteFlag != "*").CountAsync() > 0;
        }
        /// <summary>
        /// 根据ChartNo获取患者住院次数
        /// </summary>
        /// <param name="chartNo"></param>
        /// <returns></returns>
        public async Task<int?> GetPatientDataCountByChartNo(string chartNo)
        {
            if (string.IsNullOrEmpty(chartNo))
            {
                return null;
            }
            return await _dbContext.InpatientDatas.Where(m => m.ChartNo == chartNo.Trim() && m.DeleteFlag != "*").CountAsync();
        }
        /// <summary>
        /// 根据CaseNumber集合获取指定InpatientID和CaseNumber对应关系集合
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="caseNumbers"></param>
        /// <param name="predicate"></param>
        /// <returns></returns>
        public async Task<List<T>> GetFieldValueByCaseNumbers<T>(List<string> caseNumbers, [DisallowNull] Expression<Func<InpatientDataInfo, T>> predicate)
        {
            var whereCondition = _dbContext.InpatientDatas.Where(m => caseNumbers.Contains(m.CaseNumber) && m.DeleteFlag != "*");
            if (predicate != null)
            {
                return await whereCondition.Select(predicate).ToListAsync();
            }

            return default;
        }
        /// <summary>
        /// 获取患者信息集合
        /// </summary>
        /// <param name="inpatientIDs">病人住院序号集合</param>
        /// <returns></returns>
        public async Task<List<InpatientDataView>> GetInpatientDataViewsByInpatientIDs(string[] inpatientIDs)
        {
            return await (from m in _dbContext.InpatientDatas
                          join n in _dbContext.PatientBasicDatas on m.PatientID equals n.PatientID
                          where inpatientIDs.Contains(m.ID) && m.DeleteFlag != "*" && n.DeleteFlag != "*"
                          select new InpatientDataView
                          {
                              InpatientID = m.ID,
                              CaseNumber = m.CaseNumber,
                              ChartNo = m.ChartNo,
                              BedID = m.BedID,
                              BedNumber = m.BedNumber,
                              PatientName = n.PatientName,
                              Diagnosis = m.Diagnosis,
                              Gender = n.Gender,
                              Age = m.Age,
                              AdmissionDate = m.AdmissionDate,
                              AdmissionTime = m.AdmissionTime,
                              DischargeTime = m.DischargeTime,
                              DischargeDate = m.DischargeDate,
                              StationID = m.StationID
                          }).ToListAsync();
        }

        public async Task<List<string>> GetInHospitalInpatientIDsByNursingLevelAsync(int stationID, string nursingLevelCode)
        {
            var session = await _sessionCommonServer.GetSession();
            return await _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*"
                && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)
                && m.HospitalID == session.HospitalID && m.StationID == stationID
                && m.NursingLevel == nursingLevelCode)
                .Select(m => m.ID).ToListAsync();
        }
        /// <summary>
        /// 根据病区获取当前在院病人主键ID
        /// </summary>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<List<string>> GetCurrInpatientIDsByStationIDAsync(int stationID)
        {
            var session = await _sessionCommonServer.GetSession();

            return await _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)
                && m.HospitalID == session.HospitalID && m.StationID == stationID).Select(m => m.ID).ToListAsync();
        }

        public async Task<List<InpatientDataInfo>> GetInPatientInfoAsync(List<string> inpatientIDs)
        {
            return await (from a in _dbContext.InpatientDatas.Where(m => inpatientIDs.Contains(m.ID) && m.DeleteFlag != "*")
                          join b in _dbContext.PatientBasicDatas.Where(m => m.DeleteFlag != "*") on a.PatientID equals b.PatientID
                          select new InpatientDataInfo
                          {
                              ID = a.ID,
                              StationID = a.StationID,
                              ChartNo = a.ChartNo,
                              NumberOfAdmissions = a.NumberOfAdmissions,
                              LocalCaseNumber = a.LocalCaseNumber,
                              BedNumber = a.BedNumber,
                              AdmissionDate = a.AdmissionDate,
                              DischargeDate = a.DischargeDate,
                              EMRArchivingFlag = b.PatientName
                          }).ToListAsync();
        }

        /// <summary>
        /// 通过stationID获取患者数据（在院）
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<NursingBoardPatientView>> GetByStationID(int stationID, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.StationID == stationID && m.HospitalID == hospitalID && m.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1))
                .Select(m => new NursingBoardPatientView
                {
                    BedNumber = m.BedNumber,
                    InpatientID = m.ID,
                    StationID = m.StationID,
                    BedID = m.BedID,
                }).ToListAsync();
        }
        /// <summary>
        /// 获取当前在院患者
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientsByStationID(int stationID, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.StationID == stationID && m.HospitalID == hospitalID && m.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1))
                                .Select(m => new InpatientDataInfo
                                {
                                    ID = m.ID,
                                    CaseNumber = m.CaseNumber,
                                    DepartmentListID = m.DepartmentListID,
                                    StationID = m.StationID,
                                    BedNumber = m.BedNumber,
                                    BedID = m.BedID,
                                    PatientID = m.PatientID,
                                    ChartNo = m.ChartNo
                                }).ToListAsync();
        }
        /// <summary>
        /// 根据患者ID取得患者数据(包含出院)
        /// </summary>
        /// <param name="inpatientIDs"></param>
        /// <returns></returns>
        public async Task<List<InpationDataView>> GetPatientDataViewByInpatientIDs(List<string> inpatientIDs)
        {
            return await _dbContext.InpatientDatas.Where(m => inpatientIDs.Contains(m.ID) && m.DeleteFlag != "*")
             .Select(n => new InpationDataView
             {
                 StationID = n.StationID,
                 ID = n.ID,
                 BedID = n.BedID,
                 BedNumber = n.BedNumber,
                 DischargeDate = n.DischargeDate,
                 NursingProcedureCode = n.NursingProcedureCode,
                 InHospitalStatus = n.InHospitalStatus
             }).ToListAsync();
        }
        public async Task<List<InpatientDataInfo>> GetPatientByBedID(List<int> bedIDs, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*" && m.DischargeDate == null
            && bedIDs.Contains(m.BedID) && (m.InHospitalStatus == 30 || m.InHospitalStatus == 40)).ToListAsync();
        }
        /// <summary>
        /// 通过病人ID集合获取病人信息
        /// </summary>
        /// <param name="hospitalID">医院序号</param>
        /// <param name="inpatientIDs">病人ID集合</param>
        /// <returns></returns>
        public async Task<PatientData[]> GetInpatientViewByIDs(string hospitalID, IEnumerable<string> inpatientIDs)
        {
            var ids = inpatientIDs.ToHashSet();
            var query = from m in _dbContext.InpatientDatas
                        join n in _dbContext.PatientBasicDatas on m.PatientID equals n.PatientID
                        where ids.Contains(m.ID) && m.HospitalID == hospitalID && m.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)
                        select new PatientData
                        {
                            InpatientID = m.ID,
                            PatientName = n.PatientName,
                            Gender = n.Gender,
                            Age = m.Age,
                            BedID = m.BedID,
                            BedNumber = m.BedNumber,
                            StationID = m.StationID,
                            CurrentAge = n.DateOfBirth.HasValue ? AgeCalculat.GetAge(n.DateOfBirth.Value, DateTime.Now.Date) : m.Age
                        };
            return await query.ToArrayAsync();
        }

        public async Task<List<InpatientView>> GetPatientInfoByInpatientIDsAndHospitalID(string[] inpatientIDs, string hospitalID)
        {
            var query = await (from a in _dbContext.InpatientDatas.Where(m => inpatientIDs.Contains(m.ID) && m.DeleteFlag != "*" && m.StationID != 0 && m.HospitalID == hospitalID)
                               join b in _dbContext.PatientBasicDatas.Where(m => m.DeleteFlag != "*") on a.PatientID equals b.PatientID
                               select new InpatientView
                               {
                                   InpatientID = a.ID,
                                   StationID = a.StationID,
                                   LocalCaseNumber = a.LocalCaseNumber,
                                   BedNumber = a.BedNumber,
                                   PatientName = b.PatientName,
                                   ChartNo = a.ChartNo,
                                   CaseNumber = a.CaseNumber,
                                   Age = a.Age,
                                   AgeDetail = a.AgeDetail,
                                   Gender = b.Gender,
                                   ICDCode = a.ICDCode,
                                   Diagnosis = a.Diagnosis,
                                   AdmissionDate = a.AdmissionDate,
                                   AdmissionTime = a.AdmissionTime,
                                   DateOfBirth = b.DateOfBirth
                               }).ToListAsync();
            return query;
        }
        /// <summary>
        /// 获取患者年龄
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<int?> GetAgeByInpatientIDAsync(string inpatientID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.ID == inpatientID && m.DeleteFlag != "*").Select(
                m => m.Age).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取当前病区患者Id
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<string>> GetInpatientIdsByStationID(int stationID, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.StationID == stationID && m.HospitalID == hospitalID && m.DeleteFlag != "*")
                .Select(m => m.ID).ToListAsync();
        }
        /// <summary>
        /// 获取病区在用床位
        /// </summary>
        /// <returns>StationID,BedID</returns>
        public async Task<List<Tuple<int, int>>> GetUseStationBedAsync()
        {
            return await _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && !m.DischargeDate.HasValue && (!m.InHospitalStatus.HasValue || m.InHospitalStatus.Value < 60))
                .Select(m => Tuple.Create(m.StationID, m.BedID)).ToListAsync();
        }
        /// <summary>
        /// 判断在病区内的病人ID集合
        /// </summary>
        /// <param name="stationID">病区ID</param>
        /// <param name="carePatientIDs">病人ID集合</param>
        /// <returns></returns>
        public async Task<List<string>> GetInpatienIDInStationAsync(int stationID, List<string> carePatientIDs)
        {
            return await _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && carePatientIDs.Contains(m.ID))
                .Select(m => m.ID).ToListAsync();
        }
        /// <summary>
        /// 动态参数查询病案数据
        /// </summary>
        /// <param name="func">linq查询方法</param>
        /// <returns></returns>
        public async Task<InpatientDataInfo> GetPatientByDynamicParamAsync(Expression<Func<InpatientDataInfo, bool>> func)
        {
            return await _dbContext.InpatientDatas.Where(func).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据指定的归档天数获取归档患者
        /// </summary>
        /// <param name="days"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetEMRArchivingByDays(int days)
        {
            var dateTimeNow = DateTime.Now.AddDays(-days);
            return await _dbContext.InpatientDatas.Where(m => m.DischargeDate.HasValue
            && m.DischargeDate.Value.Date >= dateTimeNow.Date
            && InHospitalStatus.DISCHARGEDHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)
            && m.DeleteFlag != "*" && m.EMRArchivingFlag == "*"
            ).ToListAsync();
        }
        /// <summary>
        /// 获取病区住院病人的ID与床号信息
        /// </summary>
        /// <param name="stationID">病区ID</param>
        /// <returns></returns>
        public async Task<Dictionary<string, int>> GetInpatientIDAndBedID(int stationID)
        {
            var result = await _dbContext.InpatientDatas.Where(m => m.StationID == stationID && m.HospitalID == _config.Value.HospitalID &&
            m.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1))
                .Select(m => new { m.ID, m.BedID }).ToListAsync();
            return result.ToDictionary(m => m.ID, m => m.BedID);
        }

        public async Task<DateTime> GetAdmissionDateTime(string inpatientID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.ID == inpatientID && m.DeleteFlag != "*")
                .Select(m => m.AdmissionDate.Date.Add(m.AdmissionTime)).FirstOrDefaultAsync();
        }
        /// 根据住院序号判断当前病人是否在院
        /// </summary>
        /// <param name="inpatientID">住院序号</param>
        /// <returns></returns>
        public async Task<bool> GetInpatientInHospital(string inpatientID)
        {
            return await _dbContext.InpatientDatas.AnyAsync(m => m.ID == inpatientID && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1));
        }
        /// 根据日期获取相关患者的住院ID
        /// </summary>
        /// <param name="starDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <param name="hospitalID">医院编号</param>
        /// <returns></returns>
        public async Task<List<InpatientView>> GetInpatientIDsByDate(DateTime starDate, DateTime endDate, string hospitalID)
        {
            return await (from a in _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && m.AdmissionDate >= starDate && m.AdmissionDate <= endDate && m.HospitalID == hospitalID)
                          join b in _dbContext.PatientBasicDatas.Where(m => m.DeleteFlag != "*") on a.PatientID equals b.PatientID
                          select new InpatientView
                          {
                              InpatientID = a.ID,
                              StationID = a.StationID,
                              DepartmentListID = a.DepartmentListID,
                              PatientName = b.PatientName,
                              ChartNo = a.ChartNo,
                              Gender = b.Gender,
                              LocalChartNO = b.LocalChartNO,
                              CaseNumber = a.CaseNumber,
                              Age = a.Age,
                              BedNumber = a.BedNumber,
                          }).ToListAsync();
        }
        public async Task<List<InpatientDataInfo>> GetUnfiledInpatientByDateTime(DateTime archivingDateTime)
        {
            var session = await _sessionCommonServer.GetSession();
            var list = await _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && m.EMRArchivingFlag != "*"
            && m.HospitalID == session.HospitalID && m.DischargeDate.HasValue && m.DischargeTime.HasValue
            && m.DischargeDate.Value <= archivingDateTime.Date).ToListAsync();
            return list.Where(m => m.DischargeDate.Value.Add(m.DischargeTime.Value) <= archivingDateTime).ToList();
        }

        public async Task<List<InpatientDataInfo>> GetUnfiledInpatientByDateTimeRange(DateTime startDateTime, DateTime endDateTime)
        {
            var session = await _sessionCommonServer.GetSession();
            var list = await _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && m.EMRArchivingFlag != "*"
            && m.HospitalID == session.HospitalID && m.DischargeDate.HasValue && m.DischargeTime.HasValue
            && m.DischargeDate.Value >= startDateTime.Date
            && m.DischargeDate.Value <= endDateTime.Date).ToListAsync();
            return list.Where(m => m.DischargeDate.Value.Add(m.DischargeTime.Value) >= startDateTime
            && m.DischargeDate.Value.Add(m.DischargeTime.Value) <= endDateTime).ToList();
        }
    }
}