﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("NursingRecord_OutflowWater")]
    public class NROutflowWater
    {
        /// <summary>
        /// 自增长编号
        /// </summary>
        [Key]
        public int? SN { get; set; }
        /// <summary>
        /// Main记录的编号
        /// </summary>
        public string MainSN { get; set; }

        /// <summary>
        ///住院就诊序号
        /// </summary>
        public string CureNo { get; set; }
        /// <summary>
        /// 分类，如：导尿管，胃管，伤口引流管，其他出水量
        /// </summary>
        public string Category { get; set; }
        /// <summary>
        /// 子类名称，如：鼻胃管，PTCD
        /// </summary>
        public string SubCategory { get; set; }
        /// <summary>
        /// 出水量值
        /// </summary>
        [Column(TypeName = "decimal(10,2)")]
        public decimal Outflow { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        ///日期时间
        /// </summary>
        public DateTime WaterDateTime { get; set; }

        /// <summary>
        /// 病人排程编号
        /// </summary>
        public string PatientScheduleMainID { get; set; }
    }
}