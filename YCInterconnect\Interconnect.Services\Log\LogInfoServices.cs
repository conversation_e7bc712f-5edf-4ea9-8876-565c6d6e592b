﻿using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Medical.Common;
using Medical.Data.Interface;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;

namespace Interconnect.Services
{
    public class LogInfoServices : ILogInfoServices
    {

        private readonly ILogInfoRepository _ILogInfoRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly IOptions<SystemConfig> _config;
        public LogInfoServices(
            ILogInfoRepository LogInfoRepository
            , IOptions<SystemConfig> config
            , IAppConfigSettingRepository  appConfigSettingRepository)
        {
            _ILogInfoRepository = LogInfoRepository;
            _config = config;
            _appConfigSettingRepository = appConfigSettingRepository;
        }

        public LogInfo InnsertLogAsync(string tablenames, string logs)
        {
            var TempLog = new LogInfo
            {
                TableNames = tablenames,
                Logs = logs,
                Datetimes = DateTime.Now
            };
            return TempLog;
        }

        /// <summary>
        /// 删除日志
        /// </summary>
        /// <returns></returns>
        public bool DelLog()
        {
            //从配置档中获取数据 梁宝华 2020-04-29
            var logSaveDays = _appConfigSettingRepository.GetConfigSettingValue( "Configs", "LogSaveDays").Result;
            return _ILogInfoRepository.DelAsync(StringCheck.IsNumeric(logSaveDays)?int.Parse(logSaveDays):0);
        }

        /// <summary>
        /// 获取日志
        /// </summary>
        /// <returns></returns>
        public List<LogInfo> GetLog(string guid)
        {
            return _ILogInfoRepository.GetLog(guid);
        }

    }
}
