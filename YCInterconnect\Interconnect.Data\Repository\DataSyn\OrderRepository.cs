﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;


namespace Interconnect.Data.Repository

{
    public class OrderRepository : IOrderRepository
    {
        private DataOutConnection _DataOutConnection = null;

        public OrderRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }
        /// <summary>
        /// 获取所有没有抽取的数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<OrderInfo>> GetAsync(int takeRows)
        {
            return await _DataOutConnection.Orders.Where(m => m.DataPumpFlag != "*").Take(takeRows).ToListAsync();
        }    

        
    }
}
