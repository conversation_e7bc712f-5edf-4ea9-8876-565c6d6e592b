﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientCINVCareMainRepository : IPatientCINVCareMainRepository
    {
        private MedicalDbContext _medicalDbContext;

        public PatientCINVCareMainRepository(
              MedicalDbContext medicalDbContext)
        {
            _medicalDbContext = medicalDbContext;
        }

        public async Task<List<PatientCINVCareMainInfo>> GetByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientCINVCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime).ToListAsync();
        }

        public async Task<PatientCINVCareMainInfo> GetByCareMainID(string careMainID)
        {
            return await _medicalDbContext.PatientCINVCareMainInfos.Where(m => m.PatientCINVCareMainID == careMainID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<List<PatientCINVCareMainInfo>> GetByInpatientIDs(List<string> inpatientIDs)
        {
            return await _medicalDbContext.PatientCINVCareMainInfos.AsNoTracking().Where(m => inpatientIDs.Contains(m.InpatientID) && m.DeleteFlag != "*")
                .Select(m => new PatientCINVCareMainInfo
                {
                    PatientCINVCareMainID = m.PatientCINVCareMainID,
                    InpatientID = m.InpatientID,
                    PatientID = m.PatientID,
                    CaseNumber = m.CaseNumber,
                    BedNumber = m.BedNumber,
                    RecordsCode = m.RecordsCode,
                    AssessDate = m.AssessDate,
                    AssessTime = m.AssessTime,
                    NauseaLevel = m.NauseaLevel,
                    VomitingLevel = m.VomitingLevel,
                    RefuseTreatment = m.RefuseTreatment,
                    AddEmployeeID = m.AddEmployeeID,
                    Measures = m.Measures,
                }).OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime).ToListAsync();
        }

        public async Task<List<PatientCINVCareMainInfo>> GetHandoverViews(string inpatientID, DateTime startDate, TimeSpan startTime, DateTime endDate, TimeSpan endTime)
        {
            var startDateTime = startDate.Add(startTime);
            var endDateTime = endDate.Add(endTime);

            var date = await _medicalDbContext.PatientCINVCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*"
                && m.AssessDate >= startDate && m.AssessDate <= endDate).ToListAsync();

            return date.Where(m => m.AssessDate.Add(m.AssessTime) >= startDateTime && m.AssessDate.Add(m.AssessTime) <= endDateTime).OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).ToList();
        }
    }
}