﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientAssessMainHistoryRepository : IPatientAssessMainHistoryRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientAssessMainHistoryRepository(MedicalDbContext medicalDbContext)
        {
            this._medicalDbContext = medicalDbContext;
        }

        public async Task<List<PatientAssessMainHistoryInfo>> GetAsync(string patientAssessMainID)
        {
            return await _medicalDbContext.PatientAssessMainHistoryInfos.Where(m => m.PatientAssessMainID == patientAssessMainID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<AssessMainSealView>> GetSealViewAsync(string patientAssessMainID)
        {
            return await _medicalDbContext.PatientAssessMainHistoryInfos.Where(m => m.PatientAssessMainID == patientAssessMainID && m.DeleteFlag != "*")
                .Select(m => new AssessMainSealView
                {
                    PatientAssessMainID = m.PatientAssessMainID,
                    InpatientID = m.InpatientID,
                    CaseNumber = m.CaseNumber,
                    GroupContent = m.GroupContent
                }).ToListAsync();
        }

        public async Task<List<PatientAssessMainHistoryInfo>> GetListAsync(string inpatientID)
        {
            return await _medicalDbContext.PatientAssessMainHistoryInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();
        }
        public async Task<PatientAssessMainHistoryInfo> GetListInfoAsync(string inpatientID, int numberOfAssessment, string groupID)
        {
            List<PatientAssessMainHistoryInfo> list = await _medicalDbContext.PatientAssessMainHistoryInfos.Where(m => m.InpatientID == inpatientID && m.NumberOfAssessment == numberOfAssessment && m.GroupID == groupID && m.DeleteFlag != "*").OrderByDescending(m => m.ModifyDate).ToListAsync();
            if (list.Count == 0)
            {
                return null;
            }
            return list[0];
        }

        public async Task<List<AssessHistory>> GetVerifyData(string patientAssessMainID)
        {
            var d = new List<AssessHistory>();
            var query = await (from a in _medicalDbContext.PatientAssessMainHistoryInfos
                               join b in _medicalDbContext.AssessMains on a.PatientAssessMainID equals b.ID
                               where a.PatientAssessMainID == patientAssessMainID && a.DeleteFlag != "*"
                               select new AssessHistory
                               {
                                   RecordsCode = b.RecordsCode,
                                   GroupID = a.GroupID,
                                   Title = "",
                                   GroupContent = a.GroupContent,
                                   Sort = 0
                               }).ToListAsync();

            return query;
        }
        /// <summary>
        /// 根据inpatientID和groupID获取组装数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="groupID"></param>
        /// <returns></returns>
        public async Task<string> GetListInfoAsyncByGroupID(string inpatientID, string groupID)
        {
            List<PatientAssessMainHistoryInfo> list = await _medicalDbContext.PatientAssessMainHistoryInfos.Where(m => m.InpatientID == inpatientID && m.GroupID == groupID && m.DeleteFlag != "*").OrderByDescending(m => m.ModifyDate).ToListAsync();
            if (list.Count == 0)
            {
                return "";
            }
            return list[0].GroupContent;
        }
        /// <summary>
        /// 获取所有数据（包含逻辑删除）
        /// </summary>
        /// <param name="patientAssessMainID"></param>
        /// <returns></returns>
        public async Task<List<PatientAssessMainHistoryInfo>> GetAsyncNoDelete(string patientAssessMainID)
        {
            return await _medicalDbContext.PatientAssessMainHistoryInfos.Where(m => m.PatientAssessMainID == patientAssessMainID).ToListAsync();
        }
        /// <summary>
        /// 获取患者护理评估主ID
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        /// <exception cref="System.NotImplementedException"></exception>
        public async Task<List<string>> GetMainIDAsync(string inpatientID)
        {
            return await _medicalDbContext.PatientAssessMainHistoryInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").Select(t => t.PatientAssessMainID).ToListAsync(); ;
        }
    }
}