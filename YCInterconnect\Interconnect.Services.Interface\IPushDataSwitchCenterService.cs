﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;


namespace Interconnect.Services.Interface
{
  public  interface IPushDataSwitchCenterService
    {
        /// <summary>
        /// CCC系统向数据中心推送数据
        /// </summary>
        /// <returns></returns>
       // Task<List<string>> GetPushDataSwitchCenter();
        /// <summary>
        /// CCC系统评量表向数据中心推送数据
        /// </summary>
        /// <returns></returns>
       // Task<List<string>> PatientScoreJobMain();
    }
}
