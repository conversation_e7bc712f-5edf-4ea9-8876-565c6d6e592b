﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class OrderToNursingRepository : IOrderToNursingRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;

        public OrderToNursingRepository(MedicalDbContext db, IMemoryCache memoryCache)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
        }

        public async Task<List<OrderToNursingInfo>> AllSyncAsync(string hospitalCode = null)
        {
            string key = "OrderToNursing";
            if (hospitalCode != null)
            {
                key += "@" + hospitalCode;
            }
            var datas = await this._memoryCache.GetOrCreateAsync<List<OrderToNursingInfo>>(key, async entry =>
            {
                return await _medicalDbContext.Set<OrderToNursingInfo>().ToListAsync();
            });
            if (hospitalCode != null)
            {
                return datas.Where(t => t.PhysicianOrder.HospitalID == hospitalCode && t.DeleteFlag != "*").ToList();
            }
            return datas;
        }
    }
}