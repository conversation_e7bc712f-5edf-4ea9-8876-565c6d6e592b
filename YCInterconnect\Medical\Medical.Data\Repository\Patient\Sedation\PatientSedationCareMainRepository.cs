﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientSedationCareMainRepository : IPatientSedationCareMainRepository
    {
        private MedicalDbContext _medicalDbContext;

        public PatientSedationCareMainRepository(
              MedicalDbContext medicalDbContext)
        {
            _medicalDbContext = medicalDbContext;
        }

        public async Task<List<PatientSedationCareMainInfo>> GetByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientSedationCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime).ToListAsync();
        }

        public async Task<PatientSedationCareMainInfo> GetByCareMainID(string careMainID)
        {
            return await _medicalDbContext.PatientSedationCareMainInfos.Where(m => m.PatientSedationCareMainID == careMainID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        public async Task<List<PatientSedationCareMainInfo>> GetHandoverViews(string inpatientID, DateTime startDate, TimeSpan startTime, DateTime endDate, TimeSpan endTime)
        {
            var startDateTime = startDate.Add(startTime);
            var endDateTime = endDate.Add(endTime);

            var date = await _medicalDbContext.PatientSedationCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*"
                && m.AssessDate >= startDate && m.AssessDate <= endDate).ToListAsync();

            return date.Where(m => m.AssessDate.Add(m.AssessTime) >= startDateTime && m.AssessDate.Add(m.AssessTime) <= endDateTime).OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).ToList();
        }
    }
}