﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    /// <summary>
    /// 手术字典
    /// </summary>
    public class OperationListRepository : IOperationListRepository
    {
        private readonly MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public OperationListRepository(MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<OperationListInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("language", out var language);
            return await _medicalDbContext.OperationListInfos.Where(m => m.Language == (Int32)language && m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.OperationList.GetKey(_sessionCommonServer);
        }

        /// <summary>
        /// 根据手术码获得手术类别
        /// </summary>
        /// <param name="operationCode">手术码</param>
        /// <returns></returns>
        public async Task<OperationListInfo> GetOperationTypeByOperationCodeAsync(string operationCode)
        {
            if (string.IsNullOrEmpty(operationCode))
            {
                return null;
            }
            var data = await GetCacheAsync() as List<OperationListInfo>;
            return data.Where(m => m.LocalCode == operationCode).FirstOrDefault();
        }

        /// <summary>
        /// 获得所有的手术字典记录
        /// </summary>
        /// <returns></returns>
        public async Task<List<OperationListInfo>> GetAllOpeationListAsync()
        {
            return await GetCacheAsync() as List<OperationListInfo>;
        }
    }
}