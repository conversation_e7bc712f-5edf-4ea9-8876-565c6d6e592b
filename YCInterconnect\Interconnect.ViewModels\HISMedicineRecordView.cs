﻿namespace Interconnect.ViewModels
{
    public class HISMedicineRecordView
    {
        public string PatientOrderMainID { get; set; }
        public string PatientOrderSubID { get; set; }
        public string OrderType { get; set; }
        public string OrderPattern { get; set; }
        public string CaseNumber { get; set; }
        public string ChartNo { get; set; }
        public string StationCode { get; set; }
        public string BedNumber { get; set; }
        public string OrderCode { get; set; }
        public string OrderContent { get; set; }
        public string OrderDescription { get; set; }
        public string Frequency { get; set; }
        public string OrderDose { get; set; }
        public string OrderRuleCode { get; set; }
        public string OrderRuleName { get; set; }
        public string Unit { get; set; }
        /// <summary>
        /// 预计执行时间
        /// </summary>
        public string PerformDate { get; set; }
        public string PerformTime { get; set; }
        public string PerformEmployeeID { get; set; }
        /// <summary>
        /// 实际执行时间
        /// </summary>
        public string StartDate { get; set; }
        public string StartEmployeeID { get; set; }
        public string Enddate { get; set; }
        public string EndEmployeeID { get; set; }
        public string OrderStatus { get; set; }
        public string OrderStatusName { get; set; }
    }
}