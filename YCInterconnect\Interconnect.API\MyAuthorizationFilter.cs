﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Hangfire.Dashboard;

namespace Interconnect.API
{
    /// <summary>
    /// 
    /// </summary>
    public class MyAuthorizationFilter: IDashboardAuthorizationFilter
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool Authorize(DashboardContext context)
        {
            var httpContext = context.GetHttpContext();
            // Allow all authenticated users to see the Dashboard (potentially dangerous).

            return true;
            //return httpContext.User.Identity.IsAuthenticated;
        }
    }
}
