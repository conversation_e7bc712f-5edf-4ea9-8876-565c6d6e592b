﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientBabyFeedingRecordRepository : IPatientBabyFeedingRecordRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientBabyFeedingRecordRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }
        /// <summary>
        /// 根据D获取对应数据
        /// </summary>
        /// <param name="id">婴儿喂养记录ID</param>
        /// <returns></returns>
        public async Task<PatientBabyFeedingRecordInfo> GetByID(string id)
        {
            return await _medicalDbContext.PatientBabyFeedingRecordInfos.Where(t => t.BabyFeedingRecordID == id && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取所有喂养记录
        /// </summary>
        /// <param name="inpatientID">患者在院ID</param>
        /// <returns></returns>
        public async Task<List<PatientBabyFeedingRecordInfo>> GetByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientBabyFeedingRecordInfos.Where(t => t.InpatientID == inpatientID && t.DeleteFlag != "*").OrderBy(m => m.StartDate).ThenBy(m => m.StartTime).ToListAsync();
        }
        /// <summary>
        /// 获取交班使用数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="startDate"></param>
        /// <param name="startTime"></param>
        /// <param name="endDate"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public async Task<List<HandoverBabyFeedingCareIntervention>> GetPatientBabyFeedingIntervention(string inpatientID, DateTime startDate, TimeSpan startTime, DateTime endDate, TimeSpan endTime)
        {
            var datas = await (from a in _medicalDbContext.PatientBabyFeedingCareMainInfos
                               join b in _medicalDbContext.PatientBabyFeedingRecordInfos on a.BabyFeedingRecordID equals b.BabyFeedingRecordID

                               //TODO 由于缺BringToShift字段先注释了
                               //where a.InpatientID == inpatientID && a.AssessDate >= startDate && a.AssessDate <= endDate && a.BringToShift == true && a.DeleteFlag != "*" && b.DeleteFlag != "*"


                               select new HandoverBabyFeedingCareIntervention
                               {
                                   InpatientID = a.InpatientID,
                                   AssessDate = a.AssessDate,
                                   AssessTime = a.AssessTime,
                                   BabyFeedingRecordID = a.BabyFeedingRecordID
                               }).ToListAsync();

            if (datas.Count == 0)
            {
                return datas;
            }

            datas = datas.Where(m => m.AssessDate.Add(m.AssessTime) >= startDate.Date.Add(startTime) && m.AssessDate.Add(m.AssessTime) <= endDate.Date.Add(endTime))
                .GroupBy(m => m.BabyFeedingRecordID)
                .Select(m => m.OrderByDescending(n => n.AssessDate.Date.Add(n.AssessTime)).FirstOrDefault()).ToList();

            return datas;
        }
    }
}
