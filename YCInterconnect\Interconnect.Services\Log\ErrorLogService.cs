﻿using System;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Microsoft.EntityFrameworkCore;
using NLog;
using Interconnect.Data.Context;
using Arch.EntityFrameworkCore.UnitOfWork;

namespace Interconnect.Services
{
    public class ErrorLogService : IErrorLogService
    {
        private readonly IUnitOfWork<DataOutConnection> _unitOfWorkOut;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        public ErrorLogService(IUnitOfWork<DataOutConnection> unitOfWork)
        {
            _unitOfWorkOut = unitOfWork;
        }

        //写错误日志
        public bool InsertErrorLog(string logType, string logStr, int severityLevel)
        {
            var errorLog = new ErrorLogInfo()
            {
                SeverityLevel = severityLevel
                ,
                Logs = logStr
                ,
                LogType = logType
                ,
                DateTimes = DateTime.Now
            };
            try
            {
                _unitOfWorkOut.GetRepository<ErrorLogInfo>().Insert(errorLog);
                _unitOfWorkOut.SaveChanges();
                _logger.Info("增加错误日志成功：" + logStr);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Info("增加错误日志失败：" + logStr + "错误原因：" + ex.ToString());
                return false;
            }
        }
    }
}
