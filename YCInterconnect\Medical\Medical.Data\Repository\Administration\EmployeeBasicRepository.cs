﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class EmployeeBasicRepository : IEmployeeBasicRepository
    {
        private readonly MedicalDbContext _medicalDbContext;

        public EmployeeBasicRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<List<EmployeeBasicInfo>> GetList()
        {
            return await _medicalDbContext.EmployeeBasicInfos.Where(
                m => m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<EmployeeBasicInfo> GetByIDAsync(int ID)
        {
            return await _medicalDbContext.EmployeeBasicInfos.Where(
                m => m.DeleteFlag != "*" && m.EmployeeBasicID == ID).SingleOrDefaultAsync();
        }

        public async Task<EmployeeBasicInfo> GetByEmployeeIDAsync(string EmployeeID)
        {
            //return await _medicalDbContext.EmployeeBasicInfos.Where(
            //    m => m.DeleteFlag != "*" && m.EmployeeID == EmployeeID).SingleOrDefaultAsync();

            return await _medicalDbContext.EmployeeBasicInfos.Where(m => m.EmployeeID == EmployeeID).SingleOrDefaultAsync();
        }
    }
}