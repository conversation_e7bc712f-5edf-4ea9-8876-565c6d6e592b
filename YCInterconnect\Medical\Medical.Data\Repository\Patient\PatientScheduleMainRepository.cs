﻿/*
 * 2021-12-28 2291 护理计划需要可以调整时间,重构时新增取得前病人现有护理问题排程(GetNowProblemSchedules) -正元
 */
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.Models.Interconnect;
using Medical.ViewModels;
using Medical.ViewModels.Interface;
using Medical.ViewModels.View;
using Medical.ViewModels.View.Schedule;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientScheduleMainRepository : IPatientScheduleMainRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        /// <summary>
        /// 完成注记
        /// </summary>
        private const string PATIENTSCHEDULEMAIN_COMPLETEMARK_1 = "1";
        private readonly IUserRepository _userRepository;
        public PatientScheduleMainRepository(
            IUserRepository userRepository,
              MedicalDbContext db
            )
        {
            _userRepository = userRepository;
            _medicalDbContext = db;
        }

        /// <summary>
        /// 获取护理记录排程数据
        /// </summary>
        /// <param name="getNursingRecord">获取记录条件</param>
        /// <param name="shiftStartTime">班别开始日期</param>
        /// <param name="shiftEndTime">班别开始时间</param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetPatientScheudleForRecord(PatietnNursingRecordInfo getNursingRecord, TimeSpan shiftStartTime, TimeSpan shiftEndTime)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m =>
           m.InpatientID == getNursingRecord.InpatientID &&
           m.StationID == getNursingRecord.StationID &&
           m.ScheduleDate == getNursingRecord.RecordDate &&
           m.ScheduleTime >= shiftStartTime &&
           m.ScheduleTime < shiftEndTime && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientScheduleMainInfo>> GetByPatientProblemID(string inpatientID, string[] patientProblemIDs)
        {
            if (patientProblemIDs.Length == 0)
            {
                return new List<PatientScheduleMainInfo>();
            }
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
                            && patientProblemIDs.Contains(m.PatientProblemID)
                            && m.DeleteFlag != "*").ToListAsync();
        }
        //根据inpatientID和patientProblemID获取措施集合
        public async Task<List<PatientScheduleMainInfo>> GetByPatientProblemID(string inpatientID, string patientProblemID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m =>
                          m.InpatientID == inpatientID
                       && m.PatientProblemID == patientProblemID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 透过病人护理问题序号取得排程数据
        /// </summary>
        /// <param name="patientProblemID">透过病人护理问题序号取得排程数据</param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetByPatientProblemID(string patientProblemID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.PatientProblemID == patientProblemID && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 透过病人护理问题序号取得排程数据,包含删除的数据
        /// </summary>
        /// <param name="patientProblemID">透过病人护理问题序号取得排程数据</param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetAllByPatientProblemID(string patientProblemID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.PatientProblemID == patientProblemID).ToListAsync();
        }

        /// <summary>
        /// 透过病人护理问题序号取得排程数据数量，(中山同步在使用)
        /// </summary>
        /// <param name="patientProblemID">透过病人护理问题序号取得排程数据</param>
        /// <returns></returns>
        public async Task<int> GetCountByPatientProblemID(string inpatientID, string patientProblemID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID && m.PatientProblemID == patientProblemID).CountAsync();
        }

        /// <summary>
        /// 根据InterventID获取病人护理记录记录
        /// </summary>
        /// <param name="InpatientID"></param>
        /// <param name="InterventID"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetByPatientIDAndInterventID(string InpatientID, int InterventID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == InpatientID
            && m.InterventionID == InterventID && m.DeleteFlag != "*").OrderByDescending(m => m.ScheduleDate).ThenByDescending(m => m.ScheduleTime).ToListAsync();
        }

        public async Task<List<PatientScheduleMainInfo>> GetPerformedByPatientIDAndInterventID(string InpatientID, int[] InterventID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == InpatientID && m.PerformDate != null && m.CompleteMark == ScheduleCompleteMark.Completed
            && InterventID.Contains(m.InterventionID) && m.DeleteFlag != "*").OrderByDescending(m => m.ScheduleDate).ThenByDescending(m => m.ScheduleTime).ToListAsync();
        }

        public async Task<PatientScheduleMainInfo> GetByPatientIDAndInterventID(string InpatientID, int[] InterventID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == InpatientID
            && InterventID.Contains(m.InterventionID) && m.DeleteFlag != "*").OrderByDescending(m => m.ScheduleDate).ThenByDescending(m => m.ScheduleTime).FirstOrDefaultAsync();
        }

        public async Task<List<PatientScheduleMainInfo>> GetInfoByParmAsync(string InpatientID, string ScheduleDate, string ScheduleTime, string InterventionID)
        {
            DateTime scheduleDate = Convert.ToDateTime(ScheduleDate);

            TimeSpan scheduleTime = Convert.ToDateTime(ScheduleTime).TimeOfDay;

            int interventionID = int.Parse(InterventionID);

            return await _medicalDbContext.PatientScheduleMain.Where(t => t.InpatientID == InpatientID && t.ScheduleDate == scheduleDate && t.ScheduleTime == scheduleTime
            && t.InterventionID == interventionID && t.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 透过日期取得病人排程数据
        /// </summary>
        /// <param name="inpatientID">病人住院序号</param>
        /// <param name="stationID">病区序号</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetPatientScheduleByDates(string inpatientID, int stationID, DateTime startDate, DateTime endDate, TimeSpan startTime, TimeSpan endTime, string daySpan)
        {
            var PatientScheduleMainInfo = await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
            && m.StationID == stationID && m.DeleteFlag != "*").ToListAsync();
            if (daySpan == "*")
            {
                var tmp1 = PatientScheduleMainInfo.Where(m => m.ScheduleDate == startDate && m.ScheduleTime >= startTime && m.ScheduleTime <= new TimeSpan(23, 59, 59)).ToList();
                var tmp2 = PatientScheduleMainInfo.Where(m => m.ScheduleDate == endDate && m.ScheduleTime >= TimeSpan.Parse("00:00") && m.ScheduleTime < endTime).ToList();
                tmp1.AddRange(tmp2);
                PatientScheduleMainInfo = tmp1;
            }
            else
            {
                PatientScheduleMainInfo = PatientScheduleMainInfo.Where(m => m.ScheduleDate == startDate && m.ScheduleTime >= startTime && m.ScheduleTime < endTime).ToList();
            }
            return PatientScheduleMainInfo;
        }

        public async Task<List<PatientScheduleMainInfo>> GetOnePatientSchedule(string inpatientID, DateTime startDate, DateTime endDate,
           TimeSpan startTime, TimeSpan endTime)
        {
            var PatientScheduleMainInfo = await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
            && m.ScheduleDate >= startDate && m.ScheduleDate <= endDate && m.DeleteFlag != "*").ToListAsync();

            return PatientScheduleMainInfo.Where(m => m.ScheduleDate.Add(m.ScheduleTime) >= startDate.Add(startTime)
             && m.ScheduleDate.Add(m.ScheduleTime) < endDate.Add(endTime)).ToList();
        }

        /// <summary>
        /// 透过排程序号取得排程数据
        /// </summary>
        /// <param name="patientScheduleMainID"></param>
        /// <returns></returns>
        public async Task<PatientScheduleMainInfo> GetByID(string patientScheduleMainID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.PatientScheduleMainID == patientScheduleMainID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<string> GetPatientInterventionIDByID(string patientScheduleMainID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.PatientScheduleMainID == patientScheduleMainID && m.DeleteFlag != "*")
                .Select(m => m.PatientInterventionID).FirstOrDefaultAsync();
        }

        public async Task<PatientScheduleMainInfo> GetViewByID(string patientScheduleMainID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.PatientScheduleMainID == patientScheduleMainID && m.DeleteFlag != "*")
                .Select(m => new PatientScheduleMainInfo
                {
                    ScheduleDate = m.ScheduleDate,
                    ScheduleTime = m.ScheduleTime,
                    InpatientID = m.InpatientID,
                    InterventionID = m.InterventionID,
                    InterventionCode = m.InterventionCode,
                    StationID = m.StationID,
                    Content = m.Content,
                })
                .FirstOrDefaultAsync();
        }
        /// <summary>
        /// 透过排程序号取得排程数据
        /// </summary>
        /// <param name="ScheduleMainID"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetByID(string[] ScheduleMainID)
        {
            var PatientScheduleMainList = new List<PatientScheduleMainInfo>();
            for (int i = 0; i < ScheduleMainID.Length; i++)
            {
                var tempList = await _medicalDbContext.PatientScheduleMain.Where(m => m.PatientScheduleMainID == ScheduleMainID[i] && m.DeleteFlag != "*").ToListAsync();
                PatientScheduleMainList = PatientScheduleMainList.Union(tempList).ToList();
            }
            return PatientScheduleMainList;
        }

        public async Task<PatientScheduleMainInfo> GetLastData(string inpatientID, int interventionID, DateTime dateTime, TimeSpan timeSpan)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
                 && ((m.ScheduleDate == dateTime && m.ScheduleTime < timeSpan) || (m.ScheduleDate < dateTime))
                 && m.InterventionID == interventionID
                 && m.DeleteFlag != "*")
                .OrderByDescending(m => m.ScheduleDate)
                .ThenByDescending(m => m.ScheduleTime)
                .FirstOrDefaultAsync();
        }

        //todo待优化
        /// <summary>
        /// 透过措施码取得病人排程
        /// </summary>
        /// <param name="patientInverventionID">病人措施序号</param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetPatientScheduleByPatientInterventionID(string inpatientid, string[] patientInverventionID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientid && patientInverventionID.Any(a => a == m.PatientInterventionID) && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientScheduleMainInfo>> GetPatientScheduleByPatientInterventionID(string[] patientInverventionID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => patientInverventionID.Any(a => a == m.PatientInterventionID) && m.DeleteFlag != "*").ToListAsync();
        }
        //todo待优化
        /// <summary>
        /// 透过措施码取得病人排程
        /// </summary>
        /// <param name="patientInverventionID">病人措施序号</param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetPatientScheduleByPatientInterventionID(string patientInverventionID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m =>
                m.PatientInterventionID == patientInverventionID
             && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientScheduleMainInfo>> GetPatientSchedulesByInterventionID(string inpatientID, string patientInverventionID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m =>
               m.InpatientID == inpatientID && m.PatientInterventionID == patientInverventionID
             && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<PatientScheduleMainInfo> GetPatientScheduleByInterventionID(string inpatientID, string patientInverventionID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m =>
               m.InpatientID == inpatientID && m.PatientInterventionID == patientInverventionID
             && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<PatientScheduleMainInfo> GetPatientScheduleContentView(string inpatientID, string patientInverventionID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m =>
               m.InpatientID == inpatientID && m.PatientInterventionID == patientInverventionID
             && m.DeleteFlag != "*").Select(m => new PatientScheduleMainInfo
             {
                 PatientScheduleMainID = m.PatientScheduleMainID,
                 Content = m.Content
             }).FirstOrDefaultAsync();
        }

        public async Task<List<PatientScheduleMainInfo>> GetPatientScheduleByShift(int stationID, DateTime shiftDate, string shift)
        {
            DateTime tempDate = shiftDate.Date;

            return await _medicalDbContext.PatientScheduleMain.Where(m =>
               m.StationID == stationID
            && m.ShiftDate == tempDate
            && m.Shift == shift
            && m.DeleteFlag != "*").ToListAsync();
        }
        public async Task<List<PatientScheduleMainInfo>> GetPatientScheduleViewByShift(int stationID, DateTime shiftDate, string shift)
        {
            DateTime tempDate = shiftDate.Date;
            return await _medicalDbContext.PatientScheduleMain.Where(m =>
               m.StationID == stationID
            && m.ShiftDate == tempDate
            && m.Shift == shift
            && m.DeleteFlag != "*").Select(m => new PatientScheduleMainInfo
            {
                ScheduleDate = m.ScheduleDate,
                ScheduleTime = m.ScheduleTime,
                InpatientID = m.InpatientID,
                InterventionID = m.InterventionID,
                CompleteMark = m.CompleteMark
            }
            ).ToListAsync();
        }

        public async Task<List<PatientScheduleMainInfo>> GetPatientScheduleByShift(int stationID, DateTime shiftDate, string shift, string inpatientID)
        {
            DateTime tempDate = shiftDate.Date;

            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
            && m.StationID == stationID
            && m.ShiftDate == tempDate
            && m.Shift == shift
            && m.DeleteFlag != "*").ToListAsync();
        }
        //todo待优化
        /// <summary>
        /// 获取病人排程
        /// </summary>
        /// <param name="stationID">病区序号</param>
        /// <param name="shiftDate">班别日期</param>
        /// <param name="shift">班别</param>
        /// <param name="inpatientID">病人住院序号</param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetPatientSchedule(int stationID, DateTime shiftDate, string shift, string inpatientID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
            && m.StationID == stationID
            && m.ScheduleDate >= shiftDate
            && m.Shift == shift
            && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 获取相同时间内相同排程的措施
        /// </summary>
        /// <param name="scheduleDate">排程日期</param>
        /// <param name="scheduleTime">排程时间</param>
        /// <param name="inpatientID">病人住院序号</param>
        /// <param name="interventionID">措施序号</param>
        /// <param name="interventionCode">措施码/医嘱码</param>
        /// <param name="stationID">病区序号</param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetPatientSameSchedule(DateTime scheduleDate, TimeSpan scheduleTime, string inpatientID, int interventionID, string interventionCode, int stationID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
                      && m.StationID == stationID
                      && m.ScheduleDate == scheduleDate
                      && m.ScheduleTime == scheduleTime
                      && m.InterventionID == interventionID
                      && m.InterventionCode == interventionCode
                      && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 获取相同时间内相同排程的措施
        /// </summary>
        /// <param name="inpatientID">病人住院序号</param>
        /// <param name="patientInterventionID">病人措施序号</param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetByPatientInterventionID(string inpatientID, string patientInterventionID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
          && m.PatientInterventionID == patientInterventionID && m.DeleteFlag != "*").ToListAsync();
        }
        public async Task<List<PatientScheduleMainInfo>> GetByCasenumberAndPatientInterventionID(string casenumber, string patientInterventionID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.CaseNumber == casenumber
          && m.PatientInterventionID == patientInterventionID && m.DeleteFlag != "*").ToListAsync();
        }

        //根据casenumber及patientInterventionID获取指定时间之后未执行的排程
        public async Task<List<PatientScheduleMainInfo>> GetByPatientInterventionIDAndScheDateTime(string casenumber
            , string patientInterventionID, DateTime dateTime)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.CaseNumber == casenumber
         && m.PatientInterventionID == patientInterventionID && m.DeleteFlag != "*"
         && m.PerformDate == null
         && ((m.ScheduleDate == dateTime.Date && m.ScheduleTime > dateTime.TimeOfDay) || (m.ScheduleDate > dateTime.Date))
         ).ToListAsync();
        }

        /// <summary>
        /// 透过措施序号取得未执行的措施
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="patientInterventionID"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetUnPerformByPatientInterventionID(string inpatientID, string[] patientInterventionID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(
                   m => m.InpatientID == inpatientID
                && patientInterventionID.Contains(m.PatientInterventionID)
                && m.PerformDate == null
                && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 获得一段时间内病人的排程措施
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetByPatientIntervention(string inpatientID, DateTime startDate, DateTime endDate)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
             && m.ScheduleDate >= startDate && m.ScheduleDate <= endDate && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientScheduleMainInfo>> GetAsync(string inpatientID, DateTime dateTime)
        {
            var tempDate = dateTime.Date;

            var tempTime = dateTime.TimeOfDay;

            var list = await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
            && ((m.ScheduleDate == tempDate && m.ScheduleTime >= tempTime) || m.ScheduleDate > tempDate)
            && m.CompleteMark == ScheduleCompleteMark.UnPerform && m.DeleteFlag != "*").ToListAsync();

            return list;
        }

        public async Task<List<PatientScheduleMainInfo>> GetUnPerformBypatientProblemIDsAsync(string inpatientID, string[] patientProblemIDs, DateTime dateTime)
        {
            var patientScheduleMains = await _medicalDbContext.PatientScheduleMain
                .Where(m => m.InpatientID == inpatientID
                && patientProblemIDs.Contains(m.PatientProblemID) && m.ScheduleDate >= dateTime.Date && m.PerformDate == null
                && m.DeleteFlag != "*" && m.CompleteMark == ScheduleCompleteMark.UnPerform).ToListAsync();
            return patientScheduleMains = patientScheduleMains.Where(m => m.ScheduleDate.Add(m.ScheduleTime) > dateTime).ToList();
        }

        public async Task<List<PatientScheduleMainInfo>> GetAsync(string[] inpatientIDs, int stationID, DateTime shiftDate, string shift)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => inpatientIDs.Contains(m.InpatientID)
            && m.StationID == stationID
            && m.ShiftDate == shiftDate.Date
            && m.Shift == shift
            && m.DeleteFlag != "*").ToListAsync();
        }

        //根据病人ID获取相关信息
        public async Task<List<PatientScheduleMainInfo>> GetInfoByPaitientIDAsync(string inpatientID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientScheduleMainInfo>> GetPatientScheduleByShift(string inpatientID, int stationID, string shift, DateTime shiftDate)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID && m.StationID == stationID && m.ShiftDate == shiftDate && m.Shift == shift
             && m.CompleteMark != ScheduleCompleteMark.UnPerform && m.DeleteFlag != "*").OrderBy(m => m.ScheduleDate).ThenBy(m => m.ScheduleTime).ToListAsync();
        }

        public async Task<List<PatientScheduleMainInfo>> GetByInpatientIDAndInterventionIDs(string inpatientID, string[] ids)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
               && ids.Contains(m.PatientInterventionID)
               && m.DeleteFlag != "*").ToListAsync();
        }
        //todo待优化
        public async Task<List<PatientScheduleMainInfo>> GetByInpatientIDsAndInterventionIDs(string[] inpatientID, string[] ids)
        {
            //此方法是目前获取数据最优的写法，如果需要改变，与xml 沟通。或者调整此方法的调用逻辑
            var patientScheduleMainList = new List<PatientScheduleMainInfo>();
            foreach (var item in inpatientID)
            {
                var data = await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == item
                 && ids.Contains(m.PatientInterventionID)
                 && m.DeleteFlag != "*").ToListAsync();
                patientScheduleMainList.AddRange(data);
            }
            return patientScheduleMainList;
        }

        //根据PatientAttachedIntervention 的PatientInterventionID,获取病人对应的PatientScheduleMain的PatientScheduleMainID
        public async Task<List<string>> GetByJoinPatientAttachedIntervention(string inpatientID)
        {
            var result = (from a in _medicalDbContext.PatientScheduleMain
                          join b in _medicalDbContext.PatientAttachedInterventions
                          on new { a.PatientInterventionID, InpatientID = inpatientID }
                          equals new { PatientInterventionID = b.PatientAttachedInterventionID, b.InpatientID }
                          select a.PatientScheduleMainID
                          );

            return await result.ToListAsync();
        }

        /// <summary>
        /// 获取问题未执行措施（查询用，不能修改）
        /// </summary>
        /// <param name="patientProblemIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetUnEndByProblemIDs(List<string> patientProblemIDs, string inpatientID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
                && patientProblemIDs.Contains(m.PatientProblemID) && m.DeleteFlag != "*" && m.CompleteMark == ScheduleCompleteMark.UnPerform).ToListAsync();
        }
        //todo待优化
        public async Task<List<PatientScheduleMainInfo>> GetByStartAndEndTime(string inpatientID, int stationID, DateTime startDate, DateTime endDate)
        {
            var tempStart = startDate.Date;

            var tempEnd = endDate.Date;

            var datas = await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
            && m.StationID == stationID
            && m.ScheduleDate >= tempStart && m.ScheduleDate <= tempEnd
            && m.DeleteFlag != "*").ToListAsync();

            return datas.Where(m => m.ScheduleDate.Add(m.ScheduleTime) >= startDate && m.ScheduleDate.Add(m.ScheduleTime) <= endDate).ToList();
        }

        /// <summary>
        /// 获取某一类措施已完成记录
        /// </summary>
        /// <param name="inpatientID">患者ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="actiontype">措施类型</param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetAsync(string inpatientID, DateTime startDate, DateTime endDate, string actiontype)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID && m.ScheduleDate >= startDate &&
            m.ScheduleDate <= endDate && m.ActionType == actiontype &&
            m.CompleteMark != ScheduleCompleteMark.UnPerform && m.DeleteFlag != "*")
            .OrderBy(m => m.PerformDate).ThenBy(m => m.PerformTime).ToListAsync();
        }

        //todo待优化
        public async Task<List<PatientScheduleMainInfo>> GetByAssessMainID(string inpatientid, string assessMainID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientid && m.PatientAssessMainID == assessMainID
                           && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientScheduleMainInfo>> GetNoAssessMainIDData(string inpatientID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
                            && (m.PatientAssessMainID == null || m.PatientAssessMainID == "") && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据InpatientID、scheduleDate、interventionID获取血糖监测的排程数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="scheduleDate"></param>
        /// <param name="interventionID"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetGlucoseScheduleListAsync(string inpatientID, string scheduleDate, int interventionID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(t => t.InpatientID == inpatientID
            && t.ScheduleDate == Convert.ToDateTime(scheduleDate)
            && t.InterventionID == interventionID
            && t.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据InpatientID、scheduleDate、interventionID获取未执行的排程数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="scheduleDate"></param>
        /// <param name="interventionIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetNoExecutionScheduleByDateAsync(string inpatientID, DateTime scheduleDate, List<int> interventionIDs)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(t => t.InpatientID == inpatientID
            && t.ScheduleDate == scheduleDate
            && interventionIDs.Contains(t.InterventionID)
            && t.PerformDate == null
            && t.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 获取已执行的措施记录(费用查核获取记录使用)
        /// </summary>
        /// <param name="interventionID">措施ID</param>
        /// <param name="nowTime">当前时间</param>
        /// <param name="time">上次同步时间</param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetAllByInterventionIDAsync(int interventionID, DateTime nowTime, DateTime time)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(t => t.ModifyDate <= nowTime && t.ModifyDate >= time && t.InterventionID == interventionID
            && t.PerformDate.HasValue && t.PerformTime.HasValue
            && t.CompleteMark == ScheduleCompleteMark.Completed && t.DeleteFlag != "*").AsNoTracking().ToListAsync();
        }
        //todo待优化
        public async Task<List<KeyValue>> GetScheduleIntervention(DateTime shiftDate, string shift, TimeSpan startTime, TimeSpan endTime, int stationID, int language, IEnumerable<string> inpatientIDs)
        {
            var query = await (from a in _medicalDbContext.PatientScheduleMain.Where(m => inpatientIDs.Contains(m.InpatientID) && m.DeleteFlag != "*")
                               join b in _medicalDbContext.NursingInterventionMain on new { ID = a.InterventionID, Language = language } equals new { b.ID, b.Language }
                               where a.StationID == stationID && a.ShiftDate == shiftDate && a.Shift == shift
                                  && a.ScheduleTime >= startTime && a.ScheduleTime < endTime
                                  && a.CompleteMark == ScheduleCompleteMark.UnPerform && (a.ActionType == "2" || a.ActionType == "3" || a.ActionType == "4")
                                  && !(b.InterventionType == "L" || b.InterventionType == "A" || b.InterventionType == "S") && b.DeleteFlag != "*"
                               select new KeyValue
                               {
                                   ID = b.ID,
                                   Value = b.Intervention
                               }).Distinct().ToListAsync();
            return query;
        }
        //todo待优化
        public async Task<List<string>> GetHasPlanSchedulePatient(DateTime shiftDate, string shift, int stationID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.StationID == stationID
                              && m.ShiftDate == shiftDate && m.Shift == shift && m.SourceFlag == "S" && m.DeleteFlag != "*")
                              .Select(m => m.InpatientID).Distinct().ToListAsync();
        }
        //todo待优化
        public async Task<List<MultiPatientPerform>> GetMultiPatientPerformList(DateTime shiftDate, string shift, int stationID, int interventionID, TimeSpan startTime, TimeSpan endTime, string employeeID, string hospitalID, int language)
        {
            var query = await (from a in _medicalDbContext.PatientScheduleMain
                               join b in _medicalDbContext.InpatientDatas on new { ID = a.InpatientID, HospitalID = hospitalID } equals new { b.ID, b.HospitalID }
                               join c in _medicalDbContext.PatientBasicDatas on a.PatientID equals c.PatientID
                               join d in _medicalDbContext.Attendance on new { AttendanceDate = a.ShiftDate, a.InpatientID, a.StationID } equals new { d.AttendanceDate, d.InpatientID, d.StationID }
                               join e in _medicalDbContext.BedListInfos on new { ID = a.BedID, HospitalID = hospitalID } equals new { e.ID, e.HospitalID }
                               where a.ShiftDate == shiftDate && a.Shift == shift && a.StationID == stationID && a.InterventionID == interventionID && a.CompleteMark == ScheduleCompleteMark.UnPerform
                                  && a.ScheduleTime >= startTime && a.ScheduleTime <= endTime && a.DeleteFlag != "*"
                                  && a.ScheduleTime >= startTime && a.ScheduleTime <= endTime && a.DeleteFlag != "*"
                                  && InHospitalStatus.INHOSPITALLIST.Contains(b.InHospitalStatus ?? -1)
                                  && d.NurseEmployeeID == employeeID && d.DeleteFlag != "*" && b.StationID == stationID
                               orderby e.Sort
                               select new MultiPatientPerform
                               {
                                   InterventionID = a.InterventionID,
                                   PatientInterventionID = a.PatientInterventionID,
                                   PatientAssessMainID = a.PatientAssessMainID,
                                   ScheduleDate = a.ScheduleDate,
                                   ScheduleTime = a.ScheduleTime,
                                   PatientScheduleMainID = a.PatientScheduleMainID,
                                   InpatientID = a.InpatientID,
                                   BedNumber = a.BedNumber,
                                   NursingLevel = b.NursingLevel,
                                   PatientName = c.PatientName,
                                   Gender = c.Gender,
                                   Record = a.BringToNursingRecords == "1"
                               }).ToListAsync();

            query = query.GroupBy(m => new { m.InpatientID, m.ScheduleDate, m.ScheduleTime }).Select(m => m.First()).ToList();

            return query;
        }
        //todo待优化
        public async Task<List<MultiPatientPerform>> GetAllMultiPatientPerformList(DateTime shiftDate, string shift, int stationID, int interventionID, TimeSpan startTime, TimeSpan endTime, string hospitalID, int language)
        {
            var query = await (from a in _medicalDbContext.PatientScheduleMain
                               join b in _medicalDbContext.InpatientDatas on new { ID = a.InpatientID, HospitalID = hospitalID } equals new { b.ID, b.HospitalID }
                               join c in _medicalDbContext.PatientBasicDatas on a.PatientID equals c.PatientID
                               join e in _medicalDbContext.BedListInfos on new { ID = a.BedID, HospitalID = hospitalID } equals new { e.ID, e.HospitalID }
                               where a.ShiftDate == shiftDate && a.Shift == shift && a.StationID == stationID && a.InterventionID == interventionID && a.CompleteMark == ScheduleCompleteMark.UnPerform
                                  && a.ScheduleTime >= startTime && a.ScheduleTime <= endTime && a.DeleteFlag != "*" && b.StationID == stationID
                                   && InHospitalStatus.INHOSPITALLIST.Contains(b.InHospitalStatus ?? -1)
                               orderby e.Sort
                               select new MultiPatientPerform
                               {
                                   InterventionID = a.InterventionID,
                                   PatientInterventionID = a.PatientInterventionID,
                                   PatientAssessMainID = a.PatientAssessMainID,
                                   ScheduleDate = a.ScheduleDate,
                                   ScheduleTime = a.ScheduleTime,
                                   PatientScheduleMainID = a.PatientScheduleMainID,
                                   InpatientID = a.InpatientID,
                                   BedNumber = a.BedNumber,
                                   PatientName = c.PatientName,
                                   NursingLevel = b.NursingLevel,
                                   Gender = c.Gender,
                                   Record = a.BringToNursingRecords == "1"
                               }).ToListAsync();

            query = query.GroupBy(m => new { m.InpatientID, m.ScheduleDate, m.ScheduleTime }).Select(m => m.First()).ToList();

            return query;
        }

        public async Task<List<PatientScheduleMainInfo>> GetTrigger(string inpatientID, DateTime date)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m =>
                      m.InpatientID == inpatientID
                   && m.ScheduleDate >= date
                   && m.SourceFlag == "T" && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<PatientScheduleMainView> GetViewDataByID(string patientScheduleMainID)
        {
            var result = (from m in _medicalDbContext.PatientScheduleMain
                          where m.PatientScheduleMainID == patientScheduleMainID && m.DeleteFlag != "*"
                          select new PatientScheduleMainView()
                          {
                              InpatientID = m.InpatientID,
                              ActionTypeName = m.ActionTypeName,
                              ActionType = m.ActionType,
                              PatientInterventionID = m.PatientInterventionID,
                              InterventionID = m.InterventionID,
                              ScheduleDate = m.ScheduleDate,
                              ScheduleTime = m.ScheduleTime,
                              PatientScheduleMainID = m.PatientScheduleMainID,
                              PerformDate = m.PerformDate,
                              PerformTime = m.PerformTime,
                              CompleteMark = m.CompleteMark,
                          }
                    );
            return await result.FirstOrDefaultAsync();
        }
        //todo待优化
        public async Task<List<HandoverSchedule>> GetOnePatientHandoverSchedule(HandoverQueryView query)
        {
            var datas = await _medicalDbContext.PatientScheduleMain
                .Where(m => m.InpatientID == query.InpatientID && m.StationID == query.StationID
                         && m.ScheduleDate >= query.StartDate && m.ScheduleDate <= query.EndDate
                         && m.BringToShift == "1" && m.DeleteFlag != "*")
                .Select(n => new HandoverSchedule
                {
                    ScheduleDate = n.ScheduleDate,
                    ScheduleTime = n.ScheduleTime,
                    PerformDate = n.PerformDate,
                    PerformTime = n.PerformTime,
                    ActionType = n.ActionType,
                    CompleteMark = n.CompleteMark,
                    BringToShift = n.BringToShift,
                    Content = n.Content
                }).ToListAsync();

            datas = datas.Where(m => m.ScheduleDate.Add(m.ScheduleTime) >= query.StartDate.Add(query.StartTime)
             && m.ScheduleDate.Add(m.ScheduleTime) < query.EndDate.Add(query.EndTime)).ToList();

            return datas;
        }

        public async Task<List<ProblemRecordView>> GetProblemRecord(string inpateintID, int stationID, DateTime shiftDate, string shift)
        {
            var data = await (from a in _medicalDbContext.PatientScheduleMain
                              join b in _medicalDbContext.PatientProblems on a.PatientProblemID equals b.ID
                              where a.InpatientID == inpateintID && a.StationID == stationID && a.ShiftDate == shiftDate && a.Shift == shift && a.DeleteFlag != "*"
                              && a.Content != null
                              && (a.BringToNursingRecords == "1" || a.DelayPerformReason != null || a.NotPerformReason != null)
                              select new ProblemRecordView
                              {
                                  ScheduleDate = a.ScheduleDate,
                                  ScheduleTime = a.ScheduleTime,
                                  PerformDate = a.PerformDate,
                                  PerformTime = a.PerformTime,
                                  PatientProblemID = a.PatientProblemID,
                                  ProblemID = b.ProblemID,
                                  Content = a.Content,
                                  AddEmployeeID = a.AddEmployeeID,
                                  NurseEmployeeID = a.NurseEmployeeID,
                              }).ToListAsync();

            var query = data.GroupBy(m => new { m.PerformDate, m.PerformTime, m.Content })
                            .Select(m => m.First()).OrderBy(m => m.PerformDate).ThenBy(m => m.PerformTime)
                            .ToList();

            return query;
        }

        public async Task<List<SchedulePerformDetail>> GetPerformDetailData(string inpateintID, int stationID, DateTime shiftDate, string shift)
        {
            var data = await (from b in _medicalDbContext.PatientScheduleMain
                              join c in _medicalDbContext.PatientScheduleDetail on new { b.PatientScheduleMainID, b.InterventionID } equals new { c.PatientScheduleMainID, c.InterventionID }
                              where b.InpatientID == inpateintID && b.StationID == stationID && b.ShiftDate == shiftDate && b.Shift == shift
                              && b.CompleteMark == ScheduleCompleteMark.Completed && b.PerformDate != null && c.AssessListID != 0
                              select new SchedulePerformDetail
                              {
                                  PatientScheduleMainID = b.PatientScheduleMainID,
                                  PerformDate = b.PerformDate,
                                  PerformTime = b.PerformTime,
                                  AssessListID = c.AssessListID,
                                  ScheduleData = c.ScheduleData,
                                  InterventionDetailID = c.InterventionDetailID,
                                  SourceFlag = b.SourceFlag,
                                  InterventionID = b.InterventionID,
                                  PatientProblemID = b.PatientProblemID,
                                  BringToShift = b.BringToShift
                              }).ToListAsync();

            // 取每个项目最近一条数据
            data = data.GroupBy(m => new { m.PerformDate, m.PerformTime, m.AssessListID })
                              .Select(m => m.First()).OrderByDescending(m => m.PerformDate).ThenByDescending(m => m.PerformTime)
                              .ToList();

            return data;
        }

        public async Task<List<SchedulePerformDetail>> GetDetailByShiftAndAssessListIDs(string inpateintID, int stationID, DateTime shiftDate, string shift, int[] assesslistIDs)
        {
            var data = await (from m in _medicalDbContext.PatientScheduleMain
                              join n in _medicalDbContext.PatientScheduleDetail on new { m.PatientScheduleMainID } equals new { n.PatientScheduleMainID }
                              where m.InpatientID == inpateintID && m.StationID == stationID && m.ShiftDate == shiftDate && m.Shift == shift
                              && m.DeleteFlag != "*" && m.CompleteMark != ScheduleCompleteMark.UnPerform
                              && assesslistIDs.Contains(n.AssessListID) && n.DeleteFlag != "*"
                              select new SchedulePerformDetail
                              {
                                  PerformDate = m.ScheduleDate,
                                  PerformTime = m.ScheduleTime,
                                  AssessListID = n.AssessListID,
                                  ScheduleData = n.ScheduleData,
                                  PatientScheduleMainID = m.PatientScheduleMainID,
                              }).ToListAsync();
            return data;
        }
        /// <summary>
        /// 根据时间获取排程数据
        /// </summary>
        /// <param name="inpateintID"></param>
        /// <param name="stationID"></param>
        /// <param name="starDate"></param>
        /// <param name="endDate"></param>
        /// <param name="assesslistIDs"></param>
        /// <returns></returns>
        public async Task<List<SchedulePerformDetail>> GetDetailByDateAndAssessListIDs(string inpateintID, int stationID, DateTime startDate, DateTime endDate, int[] assesslistIDs)
        {
            return await (from m in _medicalDbContext.PatientScheduleMain
                          join n in _medicalDbContext.PatientScheduleDetail on m.PatientScheduleMainID equals n.PatientScheduleMainID
                          where m.InpatientID == inpateintID
                              && m.StationID == stationID
                              && m.DeleteFlag != "*"
                              && m.CompleteMark != ScheduleCompleteMark.UnPerform
                              && assesslistIDs.Contains(n.AssessListID)
                              && n.DeleteFlag != "*"
                          where m.ScheduleDate >= startDate && m.ScheduleDate <= endDate
                          select new SchedulePerformDetail
                          {
                              PerformDate = m.ScheduleDate,
                              PerformTime = m.ScheduleTime,
                              AssessListID = n.AssessListID,
                              ScheduleData = n.ScheduleData,
                              PatientScheduleMainID = m.PatientScheduleMainID,
                          }).ToListAsync();
        }

        public async Task<List<string>> GetScheduleMainIDs(List<string> patientInterventionIDs)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => patientInterventionIDs.Contains(m.PatientInterventionID)).AsNoTracking()
                .Select(m => m.PatientScheduleMainID).ToListAsync();
        }
        /// <summary>
        /// 获取耨个时间点之后未执行的排程
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="handoverDateTime">日期</param>
        /// <param name="handonTime">时间</param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetUnPerformByHandoverDateTimeAsync(string inpatientID, DateTime handoverDateTime, TimeSpan handonTime)
        {
            var result = await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
                        && m.DeleteFlag != "*" && m.PerformDate == null
                    && ((m.ScheduleDate == handoverDateTime.Date && m.ScheduleTime >= handonTime) || m.ScheduleDate > handoverDateTime.Date)).ToListAsync();
            return result;
        }
        //todo待优化
        public async Task<PatientScheduleMainInfo> GetByPatientInterventionID(string patientInterventionID)
        {
            var result = await _medicalDbContext.PatientScheduleMain.Where(m => m.PatientInterventionID == patientInterventionID && m.DeleteFlag != "*")
                .OrderByDescending(m => m.ScheduleDate)
                .ThenByDescending(m => m.ScheduleTime).FirstOrDefaultAsync();
            return result;
        }

        public async Task<int> GetUnPerformOrderSchedule(string inpatientID, int stationID, DateTime shiftDate, string shift)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
                        && m.StationID == stationID
                        && m.ShiftDate == shiftDate
                        && m.Shift == shift
                        && m.SourceFlag == "O"
                        && m.CompleteMark == ScheduleCompleteMark.UnPerform && m.DeleteFlag != "*").CountAsync();
        }

        //获取班内排程执行数量，同步接口使用
        public async Task<List<PatientScheduleCountView>> GetPatientScheduleNumberByDateTime(string inpatientID, int stationID, DateTime shiftDate, string shift)
        {
            var data = await (from m in _medicalDbContext.PatientScheduleMain
                              where m.InpatientID == inpatientID
                                   && m.StationID == stationID
                                   && m.ShiftDate == shiftDate
                                   && m.Shift == shift
                                   && m.DeleteFlag != "*"
                              select new PatientScheduleCountView
                              {
                                  CaseNumber = m.CaseNumber,
                                  InterventionID = m.InterventionID,
                                  ActionType = m.ActionType,
                                  ActionTypeName = m.ActionTypeName,
                                  ScheduleDateTime = m.ScheduleDate.Add(m.ScheduleTime),
                                  PerformDate = m.PerformDate,
                                  PerformTime = m.PerformTime,
                                  CompleteMark = m.CompleteMark,
                                  Shift = m.Shift
                              }).ToListAsync();

            data = data.GroupBy(m => new
            {
                m.CaseNumber,
                m.InterventionID,
                m.ActionType,
                m.ActionTypeName,
                m.ScheduleDateTime,
                m.CompleteMark,
                m.Shift
            }).Select(m => m.First()).ToList();

            return data;
        }
        //获取班内排程执行数量，同步接口使用
        public async Task<List<PatientScheduleCountView>> GetStationScheduleNumberByDateTime(int stationID, DateTime shiftDate, string shift)
        {
            var data = await (from m in _medicalDbContext.PatientScheduleMain
                              where m.StationID == stationID
                                   && m.ShiftDate == shiftDate
                                   && m.Shift == shift
                                   && m.DeleteFlag != "*"
                              select new PatientScheduleCountView
                              {
                                  CaseNumber = m.CaseNumber,
                                  InterventionID = m.InterventionID,
                                  ActionType = m.ActionType,
                                  ActionTypeName = m.ActionTypeName,
                                  ScheduleDateTime = m.ScheduleDate.Add(m.ScheduleTime),
                                  PerformDate = m.PerformDate,
                                  PerformTime = m.PerformTime,
                                  CompleteMark = m.CompleteMark,
                                  Shift = m.Shift
                              }).ToListAsync();

            data = data.GroupBy(m => new
            {
                m.CaseNumber,
                m.InterventionID,
                m.ActionType,
                m.ActionTypeName,
                m.ScheduleDateTime,
                m.CompleteMark,
                m.Shift
            }).Select(m => m.First()).ToList();
            return data;
        }
        //todo待优化
        /// <summary>
        /// 获取相同时间内相同排程的措施
        /// </summary>
        /// <param name="scheduleDate">排程日期</param>
        /// <param name="scheduleTime">排程时间</param>
        /// <param name="inpatientID">病人住院序号</param>
        /// <param name="interventionID">措施序号</param>
        /// <param name="interventionCode">措施码/医嘱码</param>
        /// <param name="linkForm"></param>
        /// <param name="completeFlag">状态</param>
        /// <param name="language">语言</param>
        /// <param name="stationID">病区序号</param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetPatientSameInterventionSchedule(DateTime scheduleDate, TimeSpan scheduleTime, string inpatientID, int interventionID, string interventionCode, string linkForm, bool completeFlag, int language, int stationID)
        {
            var dataList = await (from m in _medicalDbContext.PatientScheduleMain
                                  join n in _medicalDbContext.NursingInterventionMain on m.InterventionID equals n.ID
                                  where m.InpatientID == inpatientID
                                     && m.StationID == stationID
                                     && m.ScheduleDate == scheduleDate
                                     && m.ScheduleTime == scheduleTime
                                     && ((m.InterventionID == interventionID && m.InterventionCode == interventionCode)
                                     || (n.LinkForm == linkForm))
                                     && m.DeleteFlag != "*"
                                     && n.DeleteFlag != "*"
                                     && n.Language == language
                                  select m).ToListAsync();
            if (completeFlag)
            {
                dataList = dataList.Where(m => m.CompleteMark != ScheduleCompleteMark.UnPerform).ToList();
            }
            else
            {
                dataList = dataList.Where(m => m.CompleteMark == ScheduleCompleteMark.UnPerform).ToList();
            }
            return dataList;
        }

        public async Task<List<ScheduleDetailDataView>> GetScheduleDetailDatas(string inpateintID, int stationID, DateTime shiftDate, string hospitalID)
        {
            var scheduleDetailDataViews = await (from m in _medicalDbContext.PatientScheduleMain
                                                 join n in _medicalDbContext.PatientScheduleDetail on m.PatientScheduleMainID equals n.PatientScheduleMainID
                                                 where m.InpatientID == inpateintID && m.StationID == stationID && m.ShiftDate == shiftDate
                                                       && m.DeleteFlag != "*" && n.DeleteFlag != "*"
                                                 select new ScheduleDetailDataView
                                                 {
                                                     ScheduleDate = m.ScheduleDate,
                                                     ScheduleTime = m.ScheduleTime,
                                                     InterventionID = m.InterventionID,
                                                     InterventionDetailID = n.InterventionDetailID,
                                                     AssessListID = n.AssessListID,
                                                     ScheduleData = n.ScheduleData,
                                                     EmployeeID = m.NurseEmployeeID
                                                 }).ToListAsync();

            foreach (var item in scheduleDetailDataViews)
            {
                item.UserName = await _userRepository.GetUserNameByEmployeeID(item.EmployeeID);
            }
            scheduleDetailDataViews = scheduleDetailDataViews.OrderBy(m => m.ScheduleDate).ThenBy(m => m.ScheduleTime).ThenBy(m => m.InterventionID).ToList();
            return scheduleDetailDataViews;
        }

        public async Task<List<PatientScheduleMainInfo>> GetPatientScheduleByShiftDate(string inpatientID, DateTime shiftDate)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
                          && m.ShiftDate == shiftDate && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据ID获取排程（不过滤删除标记）
        /// </summary>
        /// <param name="patientScheduleMainID"></param>
        /// <returns></returns>
        public async Task<PatientScheduleMainInfo> GetAllByID(string patientScheduleMainID)
        {
            if (string.IsNullOrEmpty(patientScheduleMainID))
            {
                return null;
            }
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.PatientScheduleMainID == patientScheduleMainID).FirstOrDefaultAsync();
        }
        //todo待优化
        /// <summary>
        /// 透过病人护理问题序号取得排程数据(输血专项使用,包含被删除的)
        /// </summary>
        /// <param name="patientProblemID">透过病人护理问题序号取得排程数据</param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetByTransfusionID(string patientProblemID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.PatientProblemID == patientProblemID).ToListAsync();
        }

        public async Task<List<PatientScheduleMainInfo>> GetSchedule(string inpatientID, int interventionID, DateTime startDate, DateTime endDate)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m =>
                     m.InpatientID == inpatientID
                  && m.InterventionID == interventionID
                  && m.ScheduleDate >= startDate
                  && m.ScheduleDate <= endDate).ToListAsync();
        }
        /// <summary>
        /// 根据患者唯一ID获取病区内班别日期内执行的排程明细
        /// </summary>
        /// <param name="inpatientID">病人住院唯一ID</param>
        /// <param name="stationID">病区ID</param>
        /// <param name="shiftDate">班别日期</param>
        /// <returns></returns>
        public async Task<List<SchedulePerformDetail>> GetPerformDetailData(string inpatientID, int stationID, DateTime shiftDate)
        {
            var query = await (from b in _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID && m.StationID == stationID
                    && m.ShiftDate == shiftDate && m.CompleteMark == ScheduleCompleteMark.Completed && m.PerformDate != null)
                               join c in _medicalDbContext.PatientScheduleDetail on new { b.PatientScheduleMainID, b.InterventionID } equals new { c.PatientScheduleMainID, c.InterventionID }
                               select new SchedulePerformDetail
                               {
                                   PerformDate = b.PerformDate,
                                   PerformTime = b.PerformTime,
                                   AssessListID = c.AssessListID,
                                   ScheduleData = c.ScheduleData,
                                   InterventionDetailID = c.InterventionDetailID
                               }).ToListAsync();

            var data = query.GroupBy(m => new { m.PerformDate, m.PerformTime, m.AssessListID, m.InterventionDetailID })
                .Select(m => m.First()).OrderByDescending(m => m.PerformDate).ThenByDescending(m => m.PerformTime)
                            .ToList();
            return data;
        }
        //todo待优化
        /// <summary>
        /// 获取科室巡视排程
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="shiftDate"></param>
        /// <param name="shift"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetPatientPatrolScheduleByStationID(int stationID, DateTime shiftDate, string shift, TimeSpan startTime, TimeSpan endTime, string inpatientID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m =>
            m.ShiftDate == shiftDate.Date
            && m.Shift == shift
            && m.StationID == stationID
            && m.ScheduleTime >= startTime
            && m.ScheduleTime <= endTime
            && (m.PatrolFlag == "P" || m.PatrolFlag == "A")
            && m.DeleteFlag != "*"
            && (string.IsNullOrEmpty(inpatientID) || m.InpatientID == inpatientID))
            .Select(m => new PatientScheduleMainInfo
            {
                InpatientID = m.InpatientID,
                PatientID = m.PatientID,
                BedNumber = m.BedNumber,
                CompleteMark = m.CompleteMark
            }).ToListAsync();
        }

        /// <summary>
        ///  获取单病人巡视排程
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="stationID"></param>
        /// <param name="shiftDate"></param>
        /// <param name="shift"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetPatientPatrolScheduleByInpatientID(string inpatientID, int stationID, DateTime shiftDate, string shift, TimeSpan startTime, TimeSpan endTime)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m =>
            m.InpatientID == inpatientID
            && m.StationID == stationID
            && m.ShiftDate == shiftDate
            && m.ScheduleTime >= startTime
            && m.ScheduleTime <= endTime
            && m.Shift == shift
            && (m.PatrolFlag == "P" || m.PatrolFlag == "A")
            && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<string> GetLastMainID(string inpatientID, int interventionID, DateTime dateTime, TimeSpan timeSpan)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
             && ((m.ScheduleDate == dateTime && m.ScheduleTime < timeSpan) || (m.ScheduleDate < dateTime))
             && m.InterventionID == interventionID
             && m.DeleteFlag != "*")
             .OrderByDescending(m => m.ScheduleDate)
             .ThenByDescending(m => m.ScheduleTime)
             .Select(m => m.PatientScheduleMainID)
             .FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取某个措施最先执行的排程数据
        /// </summary>
        /// <param name="InpatientID"></param>
        /// <param name="InterventID"></param>
        /// <returns></returns>
        public async Task<PatientScheduleMainInfo> GetByPatientIDAndInterventIDFirstData(string InpatientID, int InterventID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == InpatientID
            && m.InterventionID == InterventID && m.CompleteMark != ScheduleCompleteMark.UnPerform && m.DeleteFlag != "*").OrderBy(m => m.PerformDate).ThenBy(m => m.PerformTime).FirstOrDefaultAsync();
        }
        //todo待优化
        public async Task<List<PatientScheduleMainInfo>> GetPatientScheduleByShift(int stationID, DateTime shiftDate1, string shift1, DateTime shiftDate2, string shift2)
        {
            DateTime tempDate1 = shiftDate1.Date;

            DateTime tempDate2 = shiftDate2.Date;

            return await _medicalDbContext.PatientScheduleMain.Where(m =>
                 (m.StationID == stationID && m.ShiftDate == tempDate1 && m.Shift == shift1 && m.DeleteFlag != "*")
              || (m.StationID == stationID && m.ShiftDate == tempDate2 && m.Shift == shift2 && m.DeleteFlag != "*")
              ).ToListAsync();
        }
        /// <summary>
        /// 获取因手术暂停的排程
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetPauseScheduleAsync(string inpatientID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
            && m.DeleteFlag != "*" && m.NotPerformReason == "OP").ToListAsync();
        }

        /// <summary>
        /// 根据时间段获取排程数据（护理看板使用）
        /// </summary>
        /// <param name="stationID">病区ID</param>
        /// <param name="shiftDate">班次日期</param>
        /// <param name="shift">班别</param>
        /// <param name="startTime">时间段开始</param>
        /// <param name="endTime">时间段结束</param>
        /// <returns>排程之措施信息、执行状态与患者床号</returns>
        public async Task<List<ScheduleTreatmentView>> GetPatientScheduleByTimeLine(int stationID, DateTime shiftDate, string shift, TimeSpan startTime, TimeSpan endTime)
        {
            DateTime tempDate = shiftDate.Date;

            return await _medicalDbContext.PatientScheduleMain.Where(m =>
               m.StationID == stationID
            && m.ShiftDate == tempDate
            && m.Shift == shift
            && (m.ScheduleTime >= startTime && m.ScheduleTime <= endTime)
            && m.DeleteFlag != "*")
                .Select(m => new ScheduleTreatmentView
                {
                    InterventionID = m.InterventionID,
                    BedNumber = m.BedNumber,
                    BedID = m.BedID,
                    ScheduleStatus = m.CompleteMark != ScheduleCompleteMark.UnPerform
                })
                .ToListAsync();
        }
        //todo待优化
        /// <summary>
        ///  获取班别日期的排程数据
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="shiftDate"></param>
        /// <returns></returns>
        public async Task<List<ScheduleTreatmentView>> GetPatientScheduleByShiftDate(int stationID, DateTime shiftDate)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m =>
               m.StationID == stationID
                && m.ShiftDate == shiftDate
            && m.DeleteFlag != "*").Select(m => new ScheduleTreatmentView
            {
                InterventionID = m.InterventionID,
                BedNumber = m.BedNumber,
                BedID = m.BedID,
                ScheduleStatus = m.CompleteMark != ScheduleCompleteMark.UnPerform
            }).ToListAsync();
        }
        public async Task<List<PatientScheduleMainInfo>> GetScheduleBySourceFlagDetail(string inpatientID, int interventionID
            , string interventionCode, string[] scheduleMainIDs)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
                  && scheduleMainIDs.Contains(m.SourceFlagDetail)
                  && m.InterventionID == interventionID
                  && m.InterventionCode == interventionCode
                  && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据来源ID获取通知医师排程
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="interventionID"></param>
        /// <param name="interventionCode"></param>
        /// <param name="sourceID"></param>
        /// <returns></returns>
        public async Task<PatientScheduleMainInfo> GetInformPhysicianScheduleBySourceIDl(string inpatientID, int interventionID
            , string interventionCode, string sourceID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
                  && m.SourceFlagDetail == sourceID
                  && m.InterventionID == interventionID
                  && m.InterventionCode == interventionCode
                  && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据interventionID获取对应排程执行明细
        /// </summary>
        /// <param name="inpateintID"></param>
        /// <param name="interventionID"></param>
        /// <returns></returns>
        public async Task<List<SchedulePerformDetail>> GetPerformDetailByInterventionID(string inpateintID, int interventionID)
        {
            var query = await (from b in _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpateintID
                               && m.InterventionID == interventionID && m.CompleteMark == ScheduleCompleteMark.Completed && m.PerformDate.HasValue && m.DeleteFlag != "*")
                               join c in _medicalDbContext.PatientScheduleDetail
                               on new { b.PatientScheduleMainID, b.InterventionID }
                               equals new { c.PatientScheduleMainID, c.InterventionID }
                               where c.DeleteFlag != "*"
                               select new SchedulePerformDetail
                               {
                                   StationID = b.StationID,
                                   PerformDate = b.PerformDate,
                                   PerformTime = b.PerformTime,
                                   PerformEmployeeID = b.NurseEmployeeID,
                                   AssessListID = c.AssessListID,
                                   InterventionDetailID = c.InterventionDetailID,
                                   ScheduleData = c.ScheduleData
                               }).ToListAsync();
            var data = query.GroupBy(m => new { m.PerformDate, m.PerformTime, m.AssessListID, m.InterventionDetailID })
                            .Select(m => m.First())
                            .ToList();
            return data;
        }
        //取得现有问题排程
        public async Task<List<PatientScheduleMainInfo>> GetNowProblemSchedules(string inpatientID, int stationID)
        {
            return await (from a in _medicalDbContext.PatientProblems
                          join b in _medicalDbContext.PatientScheduleMain on new { a.InpatientID, PatientProblemID = a.ID } equals new { b.InpatientID, b.PatientProblemID }
                          where a.InpatientID == inpatientID && a.StationID == stationID && a.EndDate == null && a.DeleteFlag != "O" && a.DeleteFlag != "*"
                             && b.StationID == stationID && b.DeleteFlag != "*"
                          select b
                          ).ToListAsync();
        }

        public async Task<List<PatientScheduleMainInfo>> GetObservationScheudle(string patientScheduleMainID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.PatientInterventionID == patientScheduleMainID && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 获取措施执行日期
        /// </summary>
        /// <param name="inpatientID">患者在院ID</param>
        /// <param name="interventID">措施ID</param>
        /// <returns>措施执行日期集合</returns>
        public async Task<List<DateTime>> GetDates(string inpatientID, int interventID)
        {
            return await _medicalDbContext.PatientScheduleMain.AsNoTracking().Where(m => m.InpatientID == inpatientID
            && m.InterventionID == interventID && m.CompleteMark == ScheduleCompleteMark.Completed && m.PerformDate != null)
                .Select(m => m.PerformDate.Value).Distinct().ToListAsync();
        }

        public async Task<List<PatientScheduleMainInfo>> GetPatientScheduleToHealthEdu(string inpatientID, string actiontype)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID &&
            m.ActionType == actiontype && m.Content != null && m.DeleteFlag != "*").ToListAsync();
        }
        public async Task<List<PatientScheduleViews>> GetTreatScheduleByDate(string inpatientID, DateTime date)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID &&
            m.ActionType == "2" && m.ScheduleDate == date.Date && m.DeleteFlag != "*")
                .Select(m => new PatientScheduleViews
                {
                    CaseNumber = m.CaseNumber,
                    InterventionID = m.InterventionID,
                    ScheduleDate = m.ScheduleDate,
                    ScheduleTime = m.ScheduleTime,
                    Status = m.CompleteMark
                })
                .ToListAsync();
        }

        public async Task<List<string>> GetPatientInterventionCodeByActionType(string inpatientID, string actiontype)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID && m.ActionType == actiontype && m.DeleteFlag != "*" && m.InterventionCode != "")
                .Select(m => m.InterventionCode
                ).ToListAsync();
        }
        public async Task<List<int>> GetPatientInterventionIDByTime(string inpatientID, DateTime scheduleDate, TimeSpan scheduleTime, int stationID, int departmentListID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID &&
            m.ScheduleDate == scheduleDate && m.ScheduleTime == scheduleTime && m.DeleteFlag != "*"
            && m.StationID == stationID && m.DepartmentListID == departmentListID)
                .Select(m => m.InterventionID).ToListAsync();
        }
        //获取指定日期的最新一笔数据
        public async Task<PatientScheduleMainInfo> GetLastPatientScheduleMainByDateTime(string inpatientID, DateTime dateTime)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
                 && m.PerformDate == dateTime.Date
                 && m.DeleteFlag != "*")
                .OrderByDescending(m => m.PerformDate)
                .ThenByDescending(m => m.PerformTime)
                .Select(m => new PatientScheduleMainInfo
                {
                    InpatientID = m.InpatientID,
                    StationID = m.StationID,
                    ShiftDate = m.ShiftDate,
                    Shift = m.Shift,
                    NurseEmployeeID = m.NurseEmployeeID
                })
                .FirstOrDefaultAsync();
        }

        public async Task<List<SchedulePerformDetail>> GetScheduleDetailByInterventionID(string inpateintID, int interventionID)
        {
            var query = await (from b in _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpateintID
                               && m.InterventionID == interventionID && m.CompleteMark == ScheduleCompleteMark.Completed && m.PerformDate.HasValue)
                               join c in _medicalDbContext.PatientScheduleDetail
                               on new { b.PatientScheduleMainID, b.InterventionID }
                               equals new { c.PatientScheduleMainID, c.InterventionID }
                               select new SchedulePerformDetail
                               {
                                   PerformDate = b.PerformDate,
                                   PerformTime = b.PerformTime,
                                   PerformEmployeeID = b.NurseEmployeeID,
                                   AssessListID = c.AssessListID,
                                   InterventionDetailID = c.InterventionDetailID,
                                   ScheduleData = c.ScheduleData,
                                   PatientProblemID = b.PatientInterventionID
                               }).ToListAsync();
            var data = query.GroupBy(m => new { m.PerformDate, m.PerformTime, m.AssessListID, m.InterventionDetailID })
                            .Select(m => m.First())
                            .ToList();
            return data;
        }

        /// <summary>
        /// 获取一位病人一段时间内所执行的最后一条指定措施的执行明细
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="interventionID">措施ID</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns></returns>
        public async Task<List<PatientScheduleDetailView>> GetLastPerformDetailByInterventionIDAndDate(string inpatientID, int interventionID, DateTime startTime, DateTime endTime)
        {
            var data = await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
             && m.InterventionID == interventionID && m.DeleteFlag != "*" && m.CompleteMark != ScheduleCompleteMark.UnPerform
             && m.ScheduleDate >= startTime.Date && m.ScheduleDate <= endTime.Date).ToListAsync();
            var lastScheduleID = data.Where(m => m.ScheduleDate.Add(m.ScheduleTime) >= startTime && m.ScheduleDate.Add(m.ScheduleTime) <= endTime)
                .OrderByDescending(m => m.PerformDate)
                .ThenByDescending(m => m.PerformTime)
                .Select(m => m.PatientScheduleMainID)
                .FirstOrDefault();
            if (string.IsNullOrEmpty(lastScheduleID))
            {
                return new List<PatientScheduleDetailView>();
            }

            return await _medicalDbContext.PatientScheduleDetail.Where(m => m.PatientScheduleMainID == lastScheduleID)
                .Select(m => new PatientScheduleDetailView
                {
                    AssessListID = m.AssessListID,
                    InterventionID = m.InterventionDetailID,
                    ScheduleData = m.ScheduleData
                }).ToListAsync();
        }
        /// <summary>
        /// 获取排程描述内容
        /// </summary>
        /// <param name="scheduleMainID"></param>
        /// <returns></returns>
        public async Task<string> GetContentByIDAsync(string scheduleMainID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.PatientScheduleMainID == scheduleMainID && m.DeleteFlag != "*")
                 .Select(m => m.Content).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取时间范围内的所有排程
        /// </summary>
        /// <param name="inpatientIDs"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetAllScheduleByQuery(List<string> inpatientIDs, DateTime startDate, DateTime endDate)
        {
            var data = await _medicalDbContext.PatientScheduleMain.Where(m => inpatientIDs.Contains(m.InpatientID)
                        && m.ScheduleDate >= startDate && m.ScheduleDate <= endDate && m.DeleteFlag != "*").ToListAsync();

            return data;
        }

        /// <summary>
        /// 获取病人scheduleDate时间点之后的所有排程
        /// </summary>
        /// <param name="scheduleDate">排程时间</param>
        /// <param name="inpatientID">病人CCC在院一次的主键标识</param>
        /// <param name="allFlag">true:查询包括被逻辑删除的排程</param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetScheduleAfterDateAsync(DateTime scheduleDate, string inpatientID, bool deletedFlag = false)
        {
            if (deletedFlag)
            {
                return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID && m.ScheduleDate >= scheduleDate.Date && m.DeleteFlag == "*").ToListAsync();
            }
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID && m.ScheduleDate >= scheduleDate.Date && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取班内已执行的措施内容
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="stationID">病区ID</param>
        /// <param name="interventionID">措施ID</param>
        /// <param name="shift">班别</param>
        /// <param name="shiftDate">班别日期</param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainView>> GetPerformScheduleViewByInterventionAndShift(string inpatientID, int stationID, int interventionID, string shift, DateTime shiftDate)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID && m.StationID == stationID && m.InterventionID == interventionID
                && m.Shift == shift &&
            m.ShiftDate == shiftDate && m.CompleteMark == ScheduleCompleteMark.Completed && m.DeleteFlag != "*").Select(m => new PatientScheduleMainView
            {
                PatientScheduleMainID = m.PatientScheduleMainID,
                Contents = m.Content,
                PerformDate = m.PerformDate,
                PerformTime = m.PerformTime,
                BringToShift = m.BringToShift == "1"
            }).ToListAsync();
        }

        /// <summary>
        /// 取得班内指定措施最新的已执行排程ID
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="interventionID">措施ID</param>
        /// <param name="shift">班别</param>
        /// <param name="shiftDate">班别日期</param>
        /// <returns></returns>
        public async Task<string> GetLatestPerformScheduleIDByInterventionAndShift(string inpatientID, int interventionID, string shift, DateTime shiftDate)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" && m.InterventionID == interventionID &&
            m.Shift == shift && m.ShiftDate == shiftDate && m.CompleteMark != ScheduleCompleteMark.UnPerform).OrderByDescending(m => m.PerformDate).ThenByDescending(m => m.PerformTime)
            .Select(m => m.PatientScheduleMainID).FirstOrDefaultAsync();
        }

        public async Task<List<PatientScheduleMainView>> GetPerformScheduleIDByTimeRange(string inpatientID, int interventionID, DateTime startDateTime, DateTime endDateTime)
        {
            var data = await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" && m.InterventionID == interventionID && m.CompleteMark == ScheduleCompleteMark.Completed
            && m.PerformDate.Value >= startDateTime.Date && m.PerformDate.Value <= endDateTime.Date).ToListAsync();

            return data.Where(m => m.PerformDate.Value.Add(m.PerformTime.Value) >= startDateTime && m.PerformDate.Value.Add(m.PerformTime.Value) <= endDateTime)
                .OrderBy(m => m.PerformDate.Value.Add(m.PerformTime.Value)).Select(m => new PatientScheduleMainView
                {
                    PatientScheduleMainID = m.PatientScheduleMainID,
                    Contents = m.Content,
                    PerformDate = m.PerformDate,
                    PerformTime = m.PerformTime,
                    BringToShift = m.BringToShift == "1"
                }).ToList();
        }

        /// <summary>
        /// 妇幼重症护理记录单,取一段时间内的排程
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="stationID"></param>
        /// <param name="startDateTime"></param>
        /// <param name="endDateTime"></param>
        /// <returns></returns>
        public async Task<List<SchedulePerformDetail>> GetPerformDetailDataByTimeSpan(string inpatientID, int stationID, DateTime startDateTime, DateTime endDateTime)
        {
            var query = await (from b in _medicalDbContext.PatientScheduleMain.Where(m => m.DeleteFlag != "*"
                               && m.InpatientID == inpatientID && m.StationID == stationID
                               && m.PerformDate.HasValue && m.PerformTime.HasValue
                               && m.PerformDate.Value >= startDateTime.Date
                               && m.PerformDate.Value <= endDateTime.Date
                               && m.CompleteMark == ScheduleCompleteMark.Completed)
                               join c in _medicalDbContext.PatientScheduleDetail.Where(m => m.DeleteFlag != "*")
                               on new { b.PatientScheduleMainID, b.InterventionID } equals new { c.PatientScheduleMainID, c.InterventionID }
                               select new SchedulePerformDetail
                               {
                                   PerformDate = b.PerformDate,
                                   PerformTime = b.PerformTime,
                                   AssessListID = c.AssessListID,
                                   ScheduleData = c.ScheduleData,
                                   InterventionDetailID = c.InterventionDetailID
                               }).ToListAsync();
            query = query.Where(m => m.PerformDate.Value.Add(m.PerformTime.Value) >= startDateTime && m.PerformDate.Value.Add(m.PerformTime.Value) <= endDateTime).ToList();
            var data = query.GroupBy(m => new { m.PerformDate, m.PerformTime, m.AssessListID, m.InterventionDetailID })
                            .Select(m => m.FirstOrDefault())
                            .ToList();
            return data;
        }

        /// <summary>
        /// 透过排程序号取得排程数据(可取SaveChange前的数据)
        /// </summary>
        /// <param name="patientScheduleMainID"></param>
        /// <returns></returns>
        public PatientScheduleMainInfo FindByID(string patientScheduleMainID)
        {
            PatientScheduleMainInfo patientScheduleMainInfo = null;
            //ChangeTracker是EF/EF Core中的核心对象，在这个对象中记录了当前EF上下文，操作过的所有实体，实体状态及实体属性的变更。
            var scheduleChangeTracker = _medicalDbContext.ChangeTracker.Entries<PatientScheduleMainInfo>();
            if (scheduleChangeTracker.Any(p => p.Entity.PatientScheduleMainID == patientScheduleMainID))
            {
                patientScheduleMainInfo = scheduleChangeTracker.First(m => m.Entity.PatientScheduleMainID == patientScheduleMainID).Entity;
            }
            else if (_medicalDbContext.PatientScheduleMain.Any(p => p.PatientScheduleMainID == patientScheduleMainID))
            {
                patientScheduleMainInfo = _medicalDbContext.PatientScheduleMain.First(m => m.PatientScheduleMainID == patientScheduleMainID && m.DeleteFlag != "*");
            }
            return patientScheduleMainInfo;
        }

        /// <summary>
        /// 获取特定执行时间特定措施的数据，中山同步使用
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="interventionID"></param>
        /// <param name="patientschedulemainID"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetScheduleMainByTimeAndIntervenTionID(string inpatientID, DateTime date, TimeSpan time, int interventionID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" && m.InterventionID == interventionID && m.PerformDate == date && m.PerformTime == time).ToListAsync();
        }

        /// <summary>
        /// 获取患者特定时间段内的特定类型排程
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="inpatientID">患者ID</param>
        /// <param name="interventionIDs">措施ID集合</param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetPatientScheduleByinterventionIDsAsync(DateTime startTime, DateTime endTime, string inpatientID, List<int> interventionIDs)
        {
            var query = await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID && interventionIDs.Contains(m.InterventionID) && m.CompleteMark == ScheduleCompleteMark.UnPerform && m.ScheduleDate == startTime.Date).ToListAsync();
            return query.Where(m => m.ScheduleDate.Add(m.ScheduleTime) >= startTime && m.ScheduleDate.Add(m.ScheduleTime) <= endTime).ToList();
        }

        public async Task<List<PatientScheduleMainInfo>> GetScheduleByinterventionIDsAsync(DateTime startDateTime, DateTime endDateTime, string inpatientID, List<int> interventionIDs, string souceFlag = null)
        {
            var startDate = startDateTime.Date;
            var startTime = startDateTime.TimeOfDay;
            var endDate = endDateTime.Date;
            var endTime = endDateTime.TimeOfDay;

            var query = await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID && interventionIDs.Contains(m.InterventionID)
            && m.CompleteMark == ScheduleCompleteMark.UnPerform && m.DeleteFlag != "*" && (souceFlag == null || m.SourceFlag == souceFlag)
            && m.ScheduleDate >= startDate && m.ScheduleDate <= endDate).ToListAsync();
            return query.Where(m => (m.ScheduleDate == startDate && m.ScheduleTime >= startTime)
            || (m.ScheduleDate == endDate && m.ScheduleTime <= endTime)).ToList();
        }

        public async Task<List<PatientScheduleMainInfo>> GetSubNoTrackingByContatinsDelete(string inpatientID, List<string> patientInterventionIDs)
        {
            return await _medicalDbContext.PatientScheduleMain.AsNoTracking().Where(m => m.InpatientID == inpatientID
            && patientInterventionIDs.Contains(m.PatientInterventionID)).Select(m => new PatientScheduleMainInfo
            {
                ScheduleDate = m.ScheduleDate,
                ScheduleTime = m.ScheduleTime,
                InpatientID = m.InpatientID,
                PatientInterventionID = m.PatientInterventionID,
                PatientProblemID = m.PatientProblemID
            }).ToListAsync();
        }

        /// <summary>
        /// 根据措施ID和时间获取患者排程，中山同步使用
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="interventionID"></param>
        /// <param name="startDateTime"></param>
        /// <param name="endDateTime"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetScheduleByTimeAndInterventionID(string inpatientID, int interventionID, DateTime startDateTime, DateTime endDateTime)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(p => p.InpatientID == inpatientID && p.InterventionID == interventionID && p.CompleteMark == ScheduleCompleteMark.UnPerform && p.PerformDate == null
                                                                       && (p.DeleteFlag != "*" || p.DeleteFlag == null)
                                                                       && ((p.ScheduleDate == startDateTime.Date && p.ScheduleTime >= startDateTime.TimeOfDay) || p.ScheduleDate > startDateTime.Date)
                                                                       && ((p.ScheduleDate == endDateTime.Date && p.ScheduleTime <= endDateTime.TimeOfDay) || p.ScheduleDate < endDateTime.Date
                                                                     )).ToListAsync();
        }
        /// <summary>
        /// 获取某段时间某个措施的排程
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="stationID"></param>
        /// <param name="shiftDate"></param>
        /// <param name="shift"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="interventionID"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetPatientTimePeriodScheduleByInpatientID(string inpatientID, int stationID, DateTime shiftDate, string shift, TimeSpan startTime, TimeSpan endTime, int interventionID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m =>
            m.InpatientID == inpatientID
            && m.StationID == stationID
            && m.ShiftDate == shiftDate
            && m.ScheduleTime >= startTime
            && m.ScheduleTime <= endTime
            && m.Shift == shift
            && m.InterventionID == interventionID
            && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据多个措施ID和时间获取患者排程数据
        /// </summary>
        /// <param name="interventionIDs"></param>
        /// <param name="inpatientID"></param>
        /// <param name="scheduleDate"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetScheduleListByTimeAndInterventionIDArr(int[] interventionIDs, string inpatientID, DateTime scheduleDate, TimeSpan startTime, TimeSpan endTime)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => interventionIDs.Contains(m.InterventionID) && m.InpatientID == inpatientID && m.ScheduleDate == scheduleDate
            && m.ScheduleTime >= startTime && m.ScheduleTime <= endTime).ToListAsync();
        }
        /// <summary>
        /// 根据排程ID获取执行时间
        /// </summary>
        /// <param name="scheduleMainID">排程ID</param>
        /// <returns></returns>
        public async Task<DateTime> GetPerformDateTimeByMainID(string scheduleMainID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.PatientScheduleMainID == scheduleMainID && m.DeleteFlag != "*" && m.PerformDate.HasValue
            && m.PerformTime.HasValue)
                .Select(m => m.PerformDate.Value.Add(m.PerformTime.Value)).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 检查是否存在对应的记录
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="patientInterventionID"></param>
        /// <param name="patientProblemID"></param>
        /// <returns></returns>
        public async Task<bool> CheckExistOrNotByPatientInterventionID(string inpatientID, string patientInterventionID, string patientProblemID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
                && m.PatientInterventionID == patientInterventionID && m.PatientProblemID == patientProblemID && m.DeleteFlag != "*").CountAsync() > 0;
        }

        public async Task<List<KeyValueString>> GetAttachedScheduleMainIDs(List<string> patientScheduleMainIDs, int assessListID, int interventionID)
        {
            return await (from b in _medicalDbContext.PatientAttachedInterventions
                          join c in _medicalDbContext.PatientScheduleMain on b.PatientAttachedInterventionID equals c.PatientInterventionID
                          join d in _medicalDbContext.PatientScheduleDetail on c.PatientScheduleMainID equals d.PatientScheduleMainID
                          where patientScheduleMainIDs.Contains(b.PatientScheduleMainID) && b.DeleteFlag != "*" && c.DeleteFlag != "*"
                          && b.InterventionID == interventionID && d.AssessListID == assessListID
                          select new KeyValueString
                          {
                              Key = d.PatientScheduleMainID,
                              Value = d.PatientScheduleDetailID
                          }).ToListAsync();
        }

        /// <summary>
        /// 获取暂停的排程
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="notPerformReason"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetPauseScheduleAsync(string inpatientID, string notPerformReason)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
            && m.DeleteFlag != "*" && m.NotPerformReason == notPerformReason).ToListAsync();
        }
        /// <summary>
        /// 根据病人住院序号和措施ID集合查找排程
        /// </summary>
        /// <param name="inpatientID">病人住院序号</param>
        /// <param name="interventionIDs">措施ID集合</param>
        /// <returns></returns>
        public async Task<List<SimpleSchedule>> GetSimpleSchedulesByInpatientIDAndInterventionIDs(string inpatientID, int[] interventionIDs)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID && interventionIDs.Contains(m.InterventionID) && m.CompleteMark == ScheduleCompleteMark.Completed && m.PerformDate.HasValue && m.DeleteFlag != "*")
                .Select(m => new SimpleSchedule
                {
                    ScheduleDate = m.PerformDate.Value,
                    ScheduleTime = m.PerformTime.Value,
                    PerformDate = m.PerformDate.Value,
                    PerformTime = m.PerformTime.Value,
                    StationID = m.StationID,
                }).ToListAsync();
        }
        /// <summary>
        /// 根据patientScheduleMainID集合获取数据
        /// </summary>
        /// <param name="patientScheduleMainIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetByIDs(List<string> patientScheduleMainIDs)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => patientScheduleMainIDs.Contains(m.PatientScheduleMainID) && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据患者住院号和措施ID获取排程数据
        /// </summary>
        /// <param name="inpatientIDs">患者住院号集合</param>
        /// <param name="stationID">病区ID</param>
        /// <param name="ids">措施ID集合</param>
        /// <param name="shiftDate">班别时间</param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetScheduleByInpatientIDsAndInterventionIDs(List<string> inpatientIDs, int stationID, int[] ids, DateTime shiftDate)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => inpatientIDs.Contains(m.InpatientID)
               && m.StationID == stationID && ids.Contains(m.InterventionID) && m.ShiftDate == shiftDate && m.DeleteFlag != "*")
                .Select(m => new PatientScheduleMainInfo
                {
                    PatientScheduleMainID = m.PatientScheduleMainID,
                    ScheduleDate = m.ScheduleDate,
                    ScheduleTime = m.ScheduleTime,
                    InpatientID = m.InpatientID,
                    CompleteMark = m.CompleteMark,
                }).ToListAsync();
        }

        public async Task<List<PatientScheduleMainInfo>> GetScheduleByReason(string inpatientID, string reason)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
            && m.DeleteFlag != "*" && m.NotPerformReason == reason).ToListAsync();
        }
        public async Task<ScheduleMainSealView> GetSealViewByID(string patientScheduleMainID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.PatientScheduleMainID == patientScheduleMainID && m.PerformDate.HasValue && m.PerformTime.HasValue && m.DeleteFlag != "*")
                                .Select(m => new ScheduleMainSealView
                                {
                                    PatientScheduleMainID = m.PatientScheduleMainID,
                                    InpatientID = m.InpatientID,
                                    CaseNumber = m.CaseNumber,
                                    PerformDate = m.PerformDate.Value,
                                    PerformTime = m.PerformTime.Value,
                                    PerformComment = m.PerformComment,
                                }).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取特定已执行排程明细
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="interventionID"></param>
        /// <returns></returns>
        public async Task<List<SchedulePerformDetail>> GetScheduleMainAndDetails(string inpatientID, int interventionID)
        {
            return await (from a in _medicalDbContext.PatientScheduleMain
                          join b in _medicalDbContext.PatientScheduleDetail
                          on a.PatientScheduleMainID equals b.PatientScheduleMainID
                          where a.InpatientID == inpatientID && a.InterventionID == interventionID && a.DeleteFlag != "*" && a.CompleteMark == ScheduleCompleteMark.Completed && a.PerformDate != null
                          && a.PerformTime != null && b.DeleteFlag != "*"
                          select new SchedulePerformDetail
                          {
                              PatientScheduleMainID = a.PatientScheduleMainID,
                              PerformEmployeeID = a.NurseEmployeeID,
                              PerformDate = a.PerformDate,
                              PerformTime = a.PerformTime,
                              AssessListID = b.AssessListID,
                              InterventionDetailID = b.InterventionDetailID,
                              ScheduleData = b.ScheduleData
                          }).ToListAsync();
        }
        /// <summary>
        /// 获取排程View集合
        /// </summary>
        /// <param name="shiftDate">班别日期</param>
        /// <param name="shift">班别</param>
        /// <param name="stationID">病区ID</param>
        /// <param name="inpatientID">病人ID</param>
        /// <returns></returns>
        public async Task<MultiPerformScheduleView[]> GetScheduleViews(DateTime shiftDate, string shift, int stationID, string inpatientID)
        {
            return await (from m in _medicalDbContext.PatientScheduleMain
                          .Where(m => m.ShiftDate == shiftDate && m.Shift == shift && m.StationID == stationID && m.DeleteFlag != "*")
                          .Where(!string.IsNullOrEmpty(inpatientID), m => m.InpatientID == inpatientID)
                          join n in _medicalDbContext.PatientScheduleDetail.Where(m => m.DeleteFlag != "*")
                          on m.PatientScheduleMainID equals n.PatientScheduleMainID
                          into scheduleGroup
                          from n in scheduleGroup.DefaultIfEmpty()
                          select new MultiPerformScheduleView
                          {
                              PatientScheduleMainID = m.PatientScheduleMainID,
                              InpatientID = m.InpatientID,
                              BedID = m.BedID,
                              StationID = m.StationID,
                              DepartmentListID = m.DepartmentListID,
                              InterventionID = m.InterventionID,
                              ActionType = m.ActionType,
                              ActionTypeName = m.ActionTypeName,
                              ScheduleDate = m.ScheduleDate,
                              ScheduleTime = m.ScheduleTime,
                              PerformDate = m.PerformDate,
                              PerformTime = m.PerformTime,
                              InformPhysician = m.InformPhysician ?? false,
                              BringToShift = m.BringToShift == "1",
                              IsComplete = m.CompleteMark != ScheduleCompleteMark.UnPerform,
                              NotPerformReason = m.NotPerformReason,
                              AssessListID = n.AssessListID,
                              InterventionDetailID = n.InterventionDetailID,
                              ScheduleData = n.ScheduleData,
                          }).ToArrayAsync();
        }
        /// <summary>
        /// 获取一段时间范围内的对应措施的排程数据
        /// </summary>
        /// <param name="stationID">病区ID</param>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <param name="interventionIDs">措施ID集合</param>
        /// <param name="inpatientIDs">患者住院序号ID集合</param>
        /// <returns></returns>
        public async Task<List<InterventionScheduleView>> GetTimeRangePatientSchedulesByInterventionID(int stationID, DateTime startDateTime, DateTime endDateTime, List<int> interventionIDs, List<string> inpatientIDs)
        {
            var querySchedules = await _medicalDbContext.PatientScheduleMain.Where(m => inpatientIDs.Contains(m.InpatientID) && m.ScheduleDate >= startDateTime.Date && m.ScheduleDate <= endDateTime.Date
                                    && m.PerformDate.HasValue && m.PerformTime.HasValue && interventionIDs.Contains(m.InterventionID)
                                    && (m.StationID == stationID || stationID == 999999) && m.DeleteFlag != "*")
                                    .Select(m => new InterventionScheduleView
                                    {
                                        PatientScheduleMainID = m.PatientScheduleMainID,
                                        InpatientID = m.InpatientID,
                                        StationID = m.StationID,
                                        DepartmentListID = m.DepartmentListID,
                                        BedNumber = m.BedNumber,
                                        ChartNo = m.ChartNo,
                                        InterventionID = m.InterventionID,
                                        ScheduleDateTime = m.ScheduleDate.Add(m.ScheduleTime),
                                        NurseEmployeeID = m.NurseEmployeeID,
                                        PerformDateTime = m.PerformDate.Value.Add(m.PerformTime.Value),
                                        Content = m.Content,
                                    }).ToListAsync();
            return querySchedules.Where(m => m.ScheduleDateTime >= startDateTime && m.ScheduleDateTime <= endDateTime).ToList();
        }
        /// <summary>
        /// 判断排程是否已经存在
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="patientProblemID"></param>
        /// <returns></returns>
        public async Task<bool> CheckExistOrNotByPatientProblemIDAsync(string inpatientID, string patientProblemID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID && m.PatientProblemID == patientProblemID && m.DeleteFlag != "*").CountAsync() > 0;
        }

        public async Task<List<PatientScheduleMainInfo>> GetByInpatientIDUnStation(string inpatientID, int stationID, DateTime startDateTime)
        {
            var list = await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID
                && m.SourceFlag == "O"
               && m.ScheduleDate >= startDateTime.Date
               && m.StationID != stationID && !m.PerformDate.HasValue
               && m.DeleteFlag != "*").ToListAsync();
            list = list.Where(m => m.ScheduleDate.Add(m.ScheduleTime) >= startDateTime).ToList();
            return list;
        }
        /// <summary>
        /// 查询是否存在排程
        /// </summary>
        /// <param name="patientInterventionID">病人措施序号</param>
        /// <returns></returns>
        public async Task<bool> GetScheduleExistByPatientInterventionID(string patientInterventionID)
        {
            if (string.IsNullOrEmpty(patientInterventionID))
            {
                return false;
            }
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.PatientInterventionID == patientInterventionID && m.DeleteFlag != "*").AnyAsync();
        }

        /// <summary>
        /// 获取耨个时间点之后未执行的排程
        /// </summary>
        /// <param name="inpatientID">病人唯一ID</param>
        /// <param name="stationID">病区ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="startTime">开始时间</param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMainInfo>> GetUnPerformByDateTimeAsync(string inpatientID, int stationID, DateTime startDate, TimeSpan startTime)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.InpatientID == inpatientID && m.StationID == stationID
                         && m.DeleteFlag != "*" && m.CompleteMark == ScheduleCompleteMark.UnPerform
                         && ((m.ScheduleDate == startDate && m.ScheduleTime >= startTime) || m.ScheduleDate > startDate)).ToListAsync();
        }
    }
}