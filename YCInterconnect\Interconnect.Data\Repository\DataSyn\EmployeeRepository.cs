﻿using Microsoft.EntityFrameworkCore;
using Interconnect.Data.Context;
using Interconnect.Data.Interface;
using Interconnect.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Options;

namespace Interconnect.Data.Repository
{
    public class EmployeeRepository : IEmployeeRepository
    {
        private DataOutConnection _DataOutConnection = null;

        public EmployeeRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }

        public  List<EmployeeInfo> GetAsync()
        {
            return  _DataOutConnection.Employees.ToList();
        }

        /// <summary>
        /// 获取员工基本信息
        /// </summary>
        /// <param name="tongbuCount"></param>
        /// <param name="takeRows"></param>
        /// <returns></returns>
        public List<EmployeeInfo> GetDataPump(int tongbuCount, int takeRows)
        {
            return  _DataOutConnection.Employees.Where(m => m.DataPumpFlag != "*"
            && ((m.Counts ?? 0) < tongbuCount)
           ).OrderBy(m=>m.ID).Take(takeRows).ToList();
        }
    }
}
