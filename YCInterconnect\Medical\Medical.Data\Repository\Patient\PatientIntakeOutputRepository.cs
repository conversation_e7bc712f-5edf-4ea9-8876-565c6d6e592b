﻿//2021-11-15 修复入出量统计无法取到数据 En

using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientIntakeOutputRepository : IPatientIntakeOutputRepository
    {
        private MedicalDbContext _dbContext = null;
        private IIntakeOutputSettingRepository _intakeOutputSettingRepository;

        public PatientIntakeOutputRepository(MedicalDbContext db,
            IIntakeOutputSettingRepository intakeOutputSettingRepository
            )
        {
            _dbContext = db;
            _intakeOutputSettingRepository = intakeOutputSettingRepository;
        }

        public async Task<PatientIntakeOutputInfo> GetAsync(string id)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.ID == id && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据导管获取出入量数据
        /// </summary>
        /// <param name="ipatientTubeRecordID"></param>
        /// <returns></returns>
        public async Task<List<PatientIntakeOutputInfo>> GetByTubeAsync(string ipatientTubeRecordID)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.PatientTubeRecordID == ipatientTubeRecordID && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据InpatientID判断io表是否有数据
        /// </summary>
        /// <param name="InpatientID"></param>
        /// <returns></returns>
        public async Task<bool> GetByInpatientID(string InpatientID)
        {
            return await _dbContext.PatientIntakeOutputs.AnyAsync(m => m.InpatientID == InpatientID && m.DeleteFlag != "*");
        }

        /// <summary>
        /// 获取一段时间内病人的io记录
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="InpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientIntakeOutputInfo>> GetByioDate(DateTime startDate, DateTime endDate, string InpatientID)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.IODate.Date >= startDate.Date
             && m.IODate.Date <= endDate.Date && m.InpatientID == InpatientID && m.DeleteFlag != "*").ToListAsync(); ;
        }
        ///2021-11-15 修复入出量统计无法取到数据 En
        /// <summary>
        /// 获取详细时间内病人的io记录
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="InpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientIntakeOutputInfo>> GetByioDateTime(DateTime startDate, DateTime endDate, string InpatientID)
        {
            var list = await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == InpatientID && m.IODate.Date >= startDate.Date
            && m.IODate.Date <= endDate.Date && m.DeleteFlag != "*").ToListAsync();

            //2021-11-15 修复入出量统计无法取到数据 En
            list = list.Where(m => m.IODate.Add(m.IOTime) >= startDate && m.IODate.Add(m.IOTime) <= endDate).ToList();
            return list;
        }

        public async Task<List<PatientIntakeOutputInfo>> GetByioDateTimeAsNoTracking(string InpatientID, int stationID, DateTime startDate, DateTime endDate)
        {
            var list = await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == InpatientID && m.StationID == stationID && m.IODate.Date >= startDate.Date
            && m.IODate.Date <= endDate.Date && m.DeleteFlag != "*")
                .ToListAsync();
            list = list.Where(m => m.IODate.Add(m.IOTime) >= startDate && m.IODate.Add(m.IOTime) <= endDate).ToList();
            return list;
        }

        public async Task<List<PatientIntakeOutputInfo>> GetByIDS(List<string> ids)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => ids.Contains(m.ID) && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取出入量记录
        /// </summary>
        /// <param name="inpatientID">病人住院序号</param>
        /// <param name="stationID">科室序号</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns></returns>
        public async Task<List<PatientIntakeOutputInfo>> GetRecordAsync(string inpatientID, int? stationID, DateTime startTime, DateTime endTime)
        {
            var newSartTime = startTime.Date;

            var newEndTime = endTime.Date;

            var query = await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID
                          && m.IODate >= startTime.Date && m.IODate <= endTime.Date
                          && m.DeleteFlag != "*").ToListAsync();

            var data = query.Where(m => m.IODate.Add(m.IOTime) >= startTime && m.IODate.Add(m.IOTime) <= endTime);

            if (stationID != null)
            {
                data = data.Where(t => t.StationID == stationID.Value);
            }

            return data.ToList();
        }
        /// <summary>
        /// 获取出入量记录by日期
        /// </summary>
        /// <param name="inpatientID">病人住院序号</param>
        /// <param name="stationID">科室序号</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns></returns>
        public async Task<List<PatientIntakeOutputInfo>> GetRecordByDateAsync(string inpatientID, int? stationID, DateTime startTime, DateTime endTime)
        {
            var query = _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID
                    && m.IODate >= startTime && m.IODate <= endTime
                    && m.DeleteFlag != "*");
            if (stationID != null)
            {
                query = query.Where(t => t.StationID == stationID.Value);
            }
            return await query.ToListAsync();
        }

        public async Task<List<PatientIntakeOutputInfo>> GetNoPumpAsync()
        {
            return await _dbContext.PatientIntakeOutputs.Where(t => t.DataPumpFlag != "*").ToListAsync();
        }

        public async Task<List<PatientIntakeOutputInfo>> GetByStartEndDateTime(string inpatientID, DateTime startDate, DateTime endDate)
        {
            var start = startDate.Date;

            var end = endDate.Date;

            var datas = await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.IODate >= start && m.IODate <= end
                              && m.DeleteFlag != "*").ToListAsync();

            return datas.Where(m => m.IODate.Date.Add(m.IOTime) >= startDate && m.IODate.Date.Add(m.IOTime) <= endDate).ToList();
        }
        public async Task<PatientIntakeOutputInfo> GetByScheduleID(string inpatientID, string patientScheduleMainID)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID &&
            m.PatientScheduleMainID == patientScheduleMainID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<List<PatientIntakeOutputInfo>> GetListByScheduleID(string inpatientID, string patientScheduleMainID)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID &&
            m.PatientScheduleMainID == patientScheduleMainID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientIntakeOutputInfo>> Get24HoursIntakeOutput(string inpatientID, DateTime shiftDate, int[] settingIDs)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID &&
            m.ShiftDate == shiftDate && settingIDs.Contains(m.IntakeOutputSettingID) && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 获取24Hour出入量小结
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="shiftDate"></param>
        /// <returns></returns>
        public async Task<List<PatientIntakeOutputInfo>> GetIOByShiftDate(string inpatientID, DateTime shiftDate)
        {
            var datas = await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID &&
            m.ShiftDate == shiftDate && m.DeleteFlag != "*").ToListAsync();
            datas.Where(m => m.IntakeOutputKind == "190" || m.IntakeOutputKind == "290");
            return datas;
        }

        public async Task<List<PatientIntakeOutputInfo>> GetNoAssessMainIDData(string inpatientID)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID
                            && (m.PatientAssessMainID == null || m.PatientAssessMainID == "") && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientIntakeOutputInfo>> GetByAssessMainID(string intakeOutputsMainID)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.PatientAssessMainID == intakeOutputsMainID
                           && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientOutputData>> GetPatientOutputDatas(string inpatientID, string patientScheduleMainID)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID &&
                                 m.PatientScheduleMainID == patientScheduleMainID && m.DeleteFlag != "*")
                                 .Select(m => new PatientOutputData
                                 {
                                     PatientIntakeOutputID = m.ID,
                                     PatientTubeCareMainID = m.PatientTubeCareMainID,
                                     PatientTubeRecordID = m.PatientTubeRecordID,
                                     AddDate = m.AddDate,
                                     SmellID = m.SmellID,
                                     CharacteristicID = m.CharacteristicID,
                                     Color = m.Color,
                                     IntakeOutputVolume = m.IntakeOutputVolume
                                 }).ToListAsync();
        }

        /// <summary>
        /// 获得排程对应的出入量数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="patientScheduleMainID"></param>
        /// <returns></returns>
        public async Task<List<PatientOutputData>> GetOutputDatasByInpatientIDAndScheduleID(string inpatientID, string patientScheduleMainID)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID &&
                                 m.PatientScheduleMainID == patientScheduleMainID && m.DeleteFlag != "*")
                                 .Select(m => new PatientOutputData
                                 {
                                     PatientIntakeOutputID = m.ID,
                                     PatientTubeCareMainID = m.PatientTubeCareMainID,
                                     PatientTubeRecordID = m.PatientTubeRecordID,
                                     AddDate = m.AddDate,
                                     SmellID = m.SmellID,
                                     CharacteristicID = m.CharacteristicID,
                                     Color = m.Color,
                                     IntakeOutputVolume = m.IntakeOutputVolume,
                                     IoDate = m.IODate,
                                     IoTime = m.IOTime,
                                     IntakeOutputNote = m.IntakeOuputNote
                                 }).ToListAsync();
        }

        public async Task<List<PatientIntakeOutputView>> GetIntakeOutputView(DateTime startDate, DateTime endDate, string inpatientID, int language)
        {
            var query = await (from a in _dbContext.PatientIntakeOutputs
                               join c in _dbContext.IntakeOutputSettings on new { ID = a.IntakeOutputSettingID, Language = language } equals new { c.ID, c.Language }
                               where a.InpatientID == inpatientID && a.IODate >= startDate && a.IODate <= endDate && a.IntakeOutputVolume != null
                                            && a.DeleteFlag != "*" && c.DeleteFlag != "*"
                               select new PatientIntakeOutputView
                               {
                                   InpatientID = a.InpatientID,
                                   IOType = c.IOType,
                                   IODate = a.IODate,
                                   IOTime = a.IOTime,
                                   IntakeOutputVolume = a.IntakeOutputVolume.Value,
                                   IOKind = a.IntakeOutputKind,
                                   Code = c.Code
                               }).ToListAsync();
            return query;
        }

        public async Task<List<PatientIntakeOutputInfo>> GetTotalData(DateTime shiftDate, int intakeID, int outputID, string InpatientID)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == InpatientID && m.ShiftDate == shiftDate
                                                    && (m.IntakeOutputSettingID == intakeID || m.IntakeOutputSettingID == outputID) && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientIntakeOutputInfo>> GetAsync(string inpatientID, DateTime date, int[] intakeOutputID)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.IODate == date.Date
                                                   && intakeOutputID.Contains(m.IntakeOutputSettingID) && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientIntakeOutputInfo>> GetAsync(string inpatientID, DateTime date, TimeSpan time, int[] intakeOutputID)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.IODate == date.Date
                                                   && m.IOTime == time
                                                   && intakeOutputID.Contains(m.IntakeOutputSettingID) && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientIntakeOutputInfo>> GetAsync(string inpatientID, DateTime date, TimeSpan time, int intakeOutputID)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.IODate == date.Date
                                                   && m.IOTime == time
                                                   && m.IntakeOutputSettingID == intakeOutputID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientIntakeOutputInfo>> GetByShiftDate(string inpatientID, DateTime shiftDate)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.ShiftDate == shiftDate && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<PatientIntakeOutputInfo> GetByInpatientID(string inpatientID, string patientScheduleMainID, string intakeOutputKind)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.PatientScheduleMainID == patientScheduleMainID &&
           m.IntakeOutputKind == intakeOutputKind && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 取第一笔出入量数据
        /// </summary>
        /// <returns></returns>
        public async Task<DateTime> GetFirstModifyDateAsync()
        {
            var data = await _dbContext.PatientIntakeOutputs.Where(m => m.DeleteFlag != "*").OrderBy(m => m.AddDate).FirstOrDefaultAsync();

            if (data == null)
            {
                return DateTime.Now;
            }

            if (data.ModifyDate == null)
            {
                return DateTime.Now;
            }
            return data.ModifyDate.Value;
        }

        /// <summary>
        /// 根据异动时间取得需要CDA转换的数据
        /// </summary>
        /// <param name="value"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>

        public async Task<List<CDA_DiscrepancyRecordView>> GetByModifyDate(DateTime dateTime, string hospitalID)
        {
            var result = await (from intakeOutputs in _dbContext.PatientIntakeOutputs
                                join inpatient in _dbContext.InpatientDatas on intakeOutputs.InpatientID equals inpatient.ID
                                join patientBasic in _dbContext.PatientBasicDatas on intakeOutputs.PatientID equals patientBasic.PatientID
                                join station in _dbContext.StationList on new { ID = intakeOutputs.StationID, hospitalID } equals new { station.ID, hospitalID = station.HospitalID }
                                join departmnt in _dbContext.DepartmentListInfos on new { ID = intakeOutputs.DepartmentID, hospitalID } equals new { departmnt.ID, hospitalID = departmnt.HospitalID }
                                join employee in _dbContext.Users on intakeOutputs.AddEmployeeID equals employee.UserID
                                where intakeOutputs.ModifyDate >= dateTime && intakeOutputs.DeleteFlag != "*"
                                && inpatient.HospitalID == hospitalID && patientBasic.HospitalID == hospitalID
                                && employee.HospitalID == hospitalID
                                select new CDA_DiscrepancyRecordView
                                {
                                    DCID = intakeOutputs.ID,
                                    InpatientID = intakeOutputs.InpatientID,
                                    ChartNo = intakeOutputs.ChartNo,
                                    CaseNumber = intakeOutputs.CaseNumber,
                                    PatientType = "04",
                                    DeptName = departmnt.Department,
                                    WardAreaName = station.StationName,
                                    SickbedId = intakeOutputs.BedNumber,
                                    Name = patientBasic.PatientName,
                                    Sex = patientBasic.Gender,
                                    Age = inpatient.Age.ToString(),
                                    MonthAge = "",
                                    DiagnoseCode = inpatient.ICDCode,
                                    DiagnoseName = inpatient.Diagnosis,
                                    NursingLevel = intakeOutputs.NursingLevel,
                                    NursingTypeCode = "",
                                    RecordDateTime = intakeOutputs.IODate.Add(intakeOutputs.IOTime),
                                    UrinationDifficultySign = "",
                                    NurseSign = employee.Name,
                                    SignDateTime = intakeOutputs.ModifyDate.Value,
                                    TimeStamp = intakeOutputs.ModifyDate.Value,
                                    DeptCode = departmnt.DepartmentCode,
                                    WardAreaCode = station.StationCode,

                                    IntakeOutputSettingID = intakeOutputs.IntakeOutputSettingID,
                                    IntakeOutputKind = intakeOutputs.IntakeOutputKind,
                                    IntakeOuputNote = intakeOutputs.IntakeOuputNote,
                                    IntakeOutputVolume = intakeOutputs.IntakeOutputVolume,
                                    Color = intakeOutputs.Color,
                                    Characteristic = intakeOutputs.Characteristic,
                                    Smell = intakeOutputs.Smell,
                                    IntakeOutputTimes = intakeOutputs.IntakeOutputTimes,
                                    InPutType = intakeOutputs.InPutType,
                                    InPutContent = intakeOutputs.InPutContent,
                                    DeleteFlag = intakeOutputs.DeleteFlag,
                                }).ToListAsync();
            return result;
        }

        public async Task<List<string>> GetInpatientListByTranslateModifyDateAsync(DateTime? modifyDate)
        {
            List<string> result;
            if (modifyDate == null)
            {
                result = await _dbContext.PatientIntakeOutputs.Select(m => m.InpatientID).Distinct().ToListAsync();
            }
            else
            {
                result = await _dbContext.PatientIntakeOutputs.Where(m => m.ModifyDate > modifyDate).Select(m => m.InpatientID).Distinct().ToListAsync();
            }
            return result;
        }

        public async Task<List<CDA_DiscrepancyRecordView>> GetFullIntakeOutputModifyByInpatientID(string hospitalID
            , DateTime modifyDate)
        {
            var result = await (from intakeOutputs in _dbContext.PatientIntakeOutputs
                                join inpatient in _dbContext.InpatientDatas on intakeOutputs.InpatientID equals inpatient.ID
                                join patientBasic in _dbContext.PatientBasicDatas on intakeOutputs.PatientID equals patientBasic.PatientID
                                join station in _dbContext.StationList on new { ID = intakeOutputs.StationID, hospitalID } equals new { station.ID, hospitalID = station.HospitalID }
                                join departmnt in _dbContext.DepartmentListInfos on new { ID = intakeOutputs.DepartmentID, hospitalID } equals new { departmnt.ID, hospitalID = departmnt.HospitalID }
                                join employee in _dbContext.Users on intakeOutputs.AddEmployeeID equals employee.UserID
                                where intakeOutputs.ModifyDate > modifyDate && intakeOutputs.ModifyDate <= modifyDate.AddDays(1) && employee.HospitalID == hospitalID
                                && inpatient.HospitalID == hospitalID && patientBasic.HospitalID == hospitalID
                                select new CDA_DiscrepancyRecordView
                                {
                                    DCID = intakeOutputs.ID,
                                    InpatientID = intakeOutputs.InpatientID,
                                    ChartNo = intakeOutputs.ChartNo,
                                    LocalChartNO = patientBasic.LocalChartNO,
                                    CaseNumber = intakeOutputs.CaseNumber,
                                    PatientType = "04",
                                    DeptName = departmnt.Department,
                                    WardAreaName = station.StationName,
                                    SickbedId = intakeOutputs.BedNumber,
                                    Name = patientBasic.PatientName,
                                    Sex = patientBasic.Gender,
                                    Age = inpatient.Age.ToString(),

                                    MonthAge = "",
                                    DiagnoseCode = inpatient.ICDCode,
                                    DiagnoseName = inpatient.Diagnosis,
                                    NursingLevel = intakeOutputs.NursingLevel,
                                    NursingTypeCode = "",
                                    RecordDateTime = intakeOutputs.IODate.Add(intakeOutputs.IOTime),
                                    UrinationDifficultySign = "",
                                    NurseSign = employee.Name,
                                    SignDateTime = intakeOutputs.ModifyDate.Value,
                                    TimeStamp = intakeOutputs.ModifyDate.Value,
                                    DeptCode = departmnt.DepartmentCode,
                                    WardAreaCode = station.StationCode,

                                    IntakeOutputSettingID = intakeOutputs.IntakeOutputSettingID,
                                    IntakeOutputKind = intakeOutputs.IntakeOutputKind,
                                    IntakeOuputNote = intakeOutputs.IntakeOuputNote,
                                    IntakeOutputVolume = intakeOutputs.IntakeOutputVolume,
                                    Color = intakeOutputs.Color,
                                    Characteristic = intakeOutputs.Characteristic,
                                    Smell = intakeOutputs.Smell,
                                    IntakeOutputTimes = intakeOutputs.IntakeOutputTimes,
                                    InPutType = intakeOutputs.InPutType,
                                    InPutContent = intakeOutputs.InPutContent,
                                    DeleteFlag = intakeOutputs.DeleteFlag,
                                }).ToListAsync();
            return result;
        }
        public async Task<List<CDA_DiscrepancyRecordView>> GetFullIntakeOutputModifyByInpatientID(string hospitalID
            , DateTime startDate, DateTime endDate)
        {
            var result = await (from intakeOutputs in _dbContext.PatientIntakeOutputs
                                join inpatient in _dbContext.InpatientDatas on intakeOutputs.InpatientID equals inpatient.ID
                                join patientBasic in _dbContext.PatientBasicDatas on intakeOutputs.PatientID equals patientBasic.PatientID
                                join station in _dbContext.StationList on new { ID = intakeOutputs.StationID, hospitalID } equals new { station.ID, hospitalID = station.HospitalID }
                                join departmnt in _dbContext.DepartmentListInfos on new { ID = intakeOutputs.DepartmentID, hospitalID } equals new { departmnt.ID, hospitalID = departmnt.HospitalID }
                                join employee in _dbContext.Users on intakeOutputs.AddEmployeeID equals employee.UserID
                                where intakeOutputs.ModifyDate >= startDate && intakeOutputs.ModifyDate < endDate && intakeOutputs.DeleteFlag != "*"
                                && employee.HospitalID == hospitalID && inpatient.HospitalID == hospitalID && patientBasic.HospitalID == hospitalID
                                && employee.HospitalID == hospitalID

                                select new CDA_DiscrepancyRecordView
                                {
                                    DCID = intakeOutputs.ID,
                                    InpatientID = intakeOutputs.InpatientID,
                                    ChartNo = intakeOutputs.ChartNo,
                                    LocalChartNO = patientBasic.LocalChartNO,
                                    CaseNumber = intakeOutputs.CaseNumber,
                                    PatientType = "04",
                                    DeptName = departmnt.Department,
                                    WardAreaName = station.StationName,
                                    SickbedId = intakeOutputs.BedNumber,
                                    Name = patientBasic.PatientName,
                                    Sex = patientBasic.Gender,
                                    Age = inpatient.Age.ToString(),
                                    MonthAge = "",
                                    DiagnoseCode = inpatient.ICDCode,
                                    DiagnoseName = inpatient.Diagnosis,
                                    NursingLevel = intakeOutputs.NursingLevel,
                                    NursingTypeCode = "",
                                    RecordDateTime = intakeOutputs.IODate.Add(intakeOutputs.IOTime),
                                    UrinationDifficultySign = "",
                                    NurseSign = employee.Name,
                                    SignDateTime = intakeOutputs.ModifyDate.Value,
                                    TimeStamp = intakeOutputs.ModifyDate.Value,
                                    DeptCode = departmnt.DepartmentCode,
                                    WardAreaCode = station.StationCode,
                                    IntakeOutputSettingID = intakeOutputs.IntakeOutputSettingID,
                                    IntakeOutputKind = intakeOutputs.IntakeOutputKind,
                                    IntakeOuputNote = intakeOutputs.IntakeOuputNote,
                                    IntakeOutputVolume = intakeOutputs.IntakeOutputVolume,
                                    Color = intakeOutputs.Color,
                                    Characteristic = intakeOutputs.Characteristic,
                                    Smell = intakeOutputs.Smell,
                                    IntakeOutputTimes = intakeOutputs.IntakeOutputTimes,
                                    InPutType = intakeOutputs.InPutType,
                                    InPutContent = intakeOutputs.InPutContent,
                                    DeleteFlag = intakeOutputs.DeleteFlag,
                                }).ToListAsync();

            return result;
        }
        public async Task<List<PatientOutputData>> GetPatientOutputDatas(List<string> inpatientIDs, DateTime shiftDate)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => inpatientIDs.Contains(m.InpatientID) &&
                                 m.ShiftDate == shiftDate && m.DeleteFlag != "*")
                                 .Select(m => new PatientOutputData
                                 {
                                     PatientIntakeOutputID = m.ID,
                                     PatientTubeCareMainID = m.PatientTubeCareMainID,
                                     PatientTubeRecordID = m.PatientTubeRecordID,
                                     AddDate = m.AddDate,
                                     SmellID = m.SmellID,
                                     CharacteristicID = m.CharacteristicID,
                                     Color = m.Color,
                                     IntakeOutputVolume = m.IntakeOutputVolume,
                                     PatientScheduleMainID = m.PatientScheduleMainID,
                                     IoDate = m.IODate,
                                     IoTime = m.IOTime,
                                     IntakeOutputNote = m.IntakeOuputNote.Trim(),
                                     BringToNursingRecords = m.BringToNursingRecords,
                                     InpatientID = m.InpatientID,
                                 }).ToListAsync();
        }
        /// <summary>
        /// 根据sourceID获取数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="sourceID"></param>
        /// <returns></returns>
        public async Task<List<PatientIntakeOutputInfo>> GetDataBySourceID(string inpatientID, string sourceID)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.SourceID == sourceID && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据sourceID获取数据是否存在
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="sourceID"></param>
        /// <returns></returns>
        public async Task<int> GetCountBySourceID(string inpatientID, string sourceID)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.SourceID == sourceID && m.DeleteFlag != "*").CountAsync();
        }
        /// <summary>
        /// 根据IntakeOutputSettingID获取病人所有IO数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="intakeOutputID"></param>
        /// <returns></returns>
        public async Task<List<PatientIntakeOutputInfo>> GetDataByIntakeOutputID(string inpatientID, int[] intakeOutputID)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && intakeOutputID.Contains(m.IntakeOutputSettingID) && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientIntakeOutputInfo>> GetRecordsBySourceID(string sourceID, string sourceType)
        {
            var data = await _dbContext.PatientIntakeOutputs.Where(m => m.SourceID == sourceID && m.DeleteFlag != "*").ToListAsync();
            if (!string.IsNullOrEmpty(sourceType))
            {
                data = data.Where(m => !string.IsNullOrEmpty(m.SourceType) && m.SourceType.Trim() == sourceType.Trim()).ToList();
            }
            return data;
        }

        /// <summary>
        /// 根据开始结束时间获取不同settingID的出入量
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="inpatientID"></param>
        /// <param name="settingID"></param>
        /// <returns></returns>
        public async Task<List<PatientIntakeOutputView>> GetIntakeOutputViewBySettingID(DateTime startDate, DateTime endDate, string inpatientID, int settingID)
        {
            var query = await (from a in _dbContext.PatientIntakeOutputs
                               where a.InpatientID == inpatientID && a.IODate >= startDate && a.IODate <= endDate && a.IntakeOutputVolume != null
                                            && a.DeleteFlag != "*" && a.IntakeOutputSettingID == settingID
                               select new PatientIntakeOutputView
                               {
                                   InpatientID = a.InpatientID,
                                   IODate = a.IODate,
                                   IOTime = a.IOTime,
                                   IntakeOutputVolume = a.IntakeOutputVolume.Value,
                                   IOKind = a.IntakeOutputKind,
                               }).ToListAsync();
            return query;
        }

        public async Task<string> GetIDByTubeCareMainID(string inpatientID, string tubeCareMainID)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.PatientTubeCareMainID == tubeCareMainID && m.DeleteFlag != "*")
                .Select(m => m.ID)
                .FirstOrDefaultAsync();
        }

        public async Task<List<PatientIntakeOutputInfo>> GetViewByInpatientID(string inpatientID)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID
            && m.DeleteFlag != "*")
                .Select(m => new PatientIntakeOutputInfo
                {
                    InpatientID = m.InpatientID,
                    StationID = m.StationID,
                    IODate = m.IODate,
                    IOTime = m.IOTime,
                    IntakeOutputKind = m.IntakeOutputKind,
                    IntakeOuputNote = m.IntakeOuputNote,
                    Color = m.Color,
                    Characteristic = m.Characteristic,
                    Smell = m.Smell,
                    PatientTubeRecordID = m.PatientTubeRecordID,
                    PatientTubeCareMainID = m.PatientTubeCareMainID,
                    IntakeOutputVolume = m.IntakeOutputVolume,
                    IntakeOutputSettingID = m.IntakeOutputSettingID,
                    BedNumber = m.BedNumber
                }
                ).ToListAsync();
        }
        /// <summary>
        /// 根据导管主键获取出入量
        /// </summary>
        /// <param name="tubeRecordIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientIntakeOutputInfo>> GetIntakeOutputByTubeRecordIDsAsync(List<string> tubeRecordIDs)
        {
            if (tubeRecordIDs.Count <= 0)
            {
                return new List<PatientIntakeOutputInfo>();
            }
            return await _dbContext.PatientIntakeOutputs.AsNoTracking().Where(m => tubeRecordIDs.Contains(m.PatientTubeRecordID) && m.DeleteFlag != "*")
                .Select(m => new PatientIntakeOutputInfo
                {
                    IODate = m.IODate,
                    IOTime = m.IOTime,
                    IntakeOutputVolume = m.IntakeOutputVolume,
                    IntakeOuputNote = m.IntakeOuputNote
                }).ToListAsync();
        }
        /// <summary>
        /// 根据病人ID以及source获取病人出入量
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="sourceID"></param>
        /// <param name="sourceType"></param>
        /// <returns></returns>
        public async Task<List<PatientIntakeOutputInfo>> GetRecordBySourceAndInpatientIDAsync(string inpatientID, string sourceType)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m =>
                m.InpatientID == inpatientID && m.DeleteFlag != "*" && m.SourceType == sourceType).ToListAsync();
        }
        /// <summary>
        /// 根据班别取不同settingID的量数据
        /// </summary>
        /// <param name="shiftDate">班别日期</param>
        /// <param name="shift">班别</param>
        /// <param name="inpatientID"></param>
        /// <param name="settingID"></param>
        /// <returns></returns>
        public async Task<List<decimal>> GetIntakeOutputViewBySettingID(DateTime shiftDate, string shift, string inpatientID, int settingID)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.IntakeOutputSettingID == settingID && m.ShiftDate == shiftDate &&
            m.Shift == shift && m.DeleteFlag != "*")
                .Select(m => m.IntakeOutputVolume ?? 0).ToListAsync();
        }

        /// <summary>
        /// 获取病人班内指定IO的次数
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="shiftDate">班别日期</param>
        /// <param name="shift">班别</param>
        /// <param name="kinds">类别</param>
        /// <returns></returns>
        public async Task<List<PatientIntakeOutputView>> GetShiftTimesByKinds(string inpatientID, DateTime shiftDate, string shift, string[] kinds)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.ShiftDate == shiftDate && m.Shift == shift
            && kinds.Contains(m.IntakeOutputKind) && m.DeleteFlag != "*").Select(m => new PatientIntakeOutputView
            {
                IOKind = m.IntakeOutputKind,
                Times = m.IntakeOutputTimes ?? 0
            }).ToListAsync();
        }

        /// <summary>
        /// 获取病人一段时间内某类别的出入量数据
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="kinds">出入量类别</param>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <returns></returns>
        public async Task<List<PatientIntakeOutputView>> GetIOViewByKindAndTime(string inpatientID, string[] kinds, DateTime startDateTime, DateTime endDateTime)
        {
            var ioDatas = await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" && m.IODate >= startDateTime.Date &&
            m.IODate <= endDateTime.Date && kinds.Contains(m.IntakeOutputKind)).Select(m => new PatientIntakeOutputView
            {
                IODate = m.IODate,
                IOTime = m.IOTime,
                IntakeOutputVolume = m.IntakeOutputVolume ?? 0,
                Times = m.IntakeOutputTimes ?? 0,
                IOKind = m.IntakeOutputKind,
                SourceID = m.SourceID,
                IntakeOutputSettingID = m.IntakeOutputSettingID,
                PatientTubeRecordID = m.PatientTubeRecordID
            }).ToListAsync();
            return ioDatas.Where(m => m.IODate.Add(m.IOTime) >= startDateTime && m.IODate.Add(m.IOTime) <= endDateTime).ToList();
        }
        public async Task<List<PatientIntakeOutputInfo>> GetDataByIODate(string inpatientID, DateTime ioDate)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" & m.IODate == ioDate).ToListAsync();
        }

        public async Task<List<PatientIntakeOutputInfo>> GetIntakeOutDataById(string inpatientID, DateTime startTime, DateTime endTime, List<string> kindList)
        {
            var result = await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" && kindList.Contains(m.IntakeOutputSettingID.ToString()))
                .Select(m => new PatientIntakeOutputInfo
                {
                    IntakeOutputSettingID = m.IntakeOutputSettingID,
                    IntakeOutputVolume = m.IntakeOutputVolume,
                    IntakeOutputKind = m.IntakeOutputKind
                }).ToListAsync();

            return result.Where(m => m.IODate.Date.Add(m.IOTime) >= startTime && m.IODate.Date.Add(m.IOTime) <= endTime).ToList();
        }
        /// <summary>
        /// 获取导管本班的引流总量
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="tubeRecordID">导管主记录ID</param>
        /// <param name="shiftDate">班别日期</param>
        /// <param name="shift">班别:如果为null,获取班别日期内的总量</param>
        /// <returns></returns>
        public async Task<decimal> GetShiftVolumeByTubeRecordID(string inpatientID, string tubeRecordID, DateTime shiftDate, string shift)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.PatientTubeRecordID == tubeRecordID
            && m.ShiftDate == shiftDate && (shift == null || m.Shift == shift) && m.DeleteFlag != "*")
                .SumAsync(m => m.IntakeOutputVolume.HasValue ? m.IntakeOutputVolume.Value : 0);
        }
        /// <summary>
        /// 获取导管本班的引流总量
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="tubeRecordID">导管主记录ID</param>
        /// <param name="shiftDate">班别日期</param>
        /// <param name="shift">班别:如果为null,获取班别日期内的总量</param>
        /// <returns></returns>
        public async Task<List<PatientIntakeOutputInfo>> GetPartViewByTubeRecordID(string inpatientID, string tubeRecordID, DateTime shiftDate, string shift)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.PatientTubeRecordID == tubeRecordID
            && m.ShiftDate == shiftDate && (shift == null || m.Shift == shift) && m.DeleteFlag != "*")
                .Select(m => new PatientIntakeOutputInfo
                {
                    IntakeOutputVolume = m.IntakeOutputVolume,
                    Characteristic = m.Characteristic,
                    CharacteristicID = m.CharacteristicID,
                    Color = m.Color,
                    SmellID = m.SmellID,
                    Smell = m.Smell,
                    Shift = m.Shift,
                    IntakeOutputSettingID = m.IntakeOutputSettingID
                })
                .ToListAsync();
        }

        public async Task<List<PatientIntakeOutputInfo>> GetPartViewByTimeRange(string inpatientID, string tubeRecordID, DateTime startDateTime, DateTime endDateTime)
        {
            var data = await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.PatientTubeRecordID == tubeRecordID
            && m.IODate >= startDateTime.Date && m.IODate <= endDateTime.Date && m.DeleteFlag != "*")
                .Select(m => new PatientIntakeOutputInfo
                {
                    IntakeOutputVolume = m.IntakeOutputVolume,
                    Characteristic = m.Characteristic,
                    CharacteristicID = m.CharacteristicID,
                    Color = m.Color,
                    SmellID = m.SmellID,
                    Smell = m.Smell,
                    Shift = m.Shift,
                    IntakeOutputSettingID = m.IntakeOutputSettingID,
                    IODate = m.IODate,
                    IOTime = m.IOTime,
                }).ToListAsync();
            return data.Where(m => m.IODate.Add(m.IOTime) >= startDateTime && m.IODate.Add(m.IOTime) <= endDateTime).ToList();
        }
        public async Task<List<PatientIntakeOutputInfo>> GetIntakeOutDataByIDs(List<string> PatientIntakeOutPutIDs)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => PatientIntakeOutPutIDs.Contains(m.ID)).ToListAsync();
        }
        /// <summary>
        /// 根据主键ID返回输入输出类型
        /// </summary>
        /// <param name="patientIntakeOutputID"></param>
        /// <returns></returns>
        public async Task<Dictionary<string, string>> GetIoTypeByPatientIntakeOutputID(string patientIntakeOutputID)
        {
            var patientIOData = await _dbContext.PatientIntakeOutputs.Where(m => m.ID == patientIntakeOutputID).FirstOrDefaultAsync();
            if (patientIOData == null)
            {
                return null;
            }
            var ioSetting = await _intakeOutputSettingRepository.GetAsync(patientIOData.IntakeOutputSettingID);
            if (ioSetting == null)
            {
                return null;
            }
            return new Dictionary<string, string>()
            {
                   {"ioType", ioSetting.IOType=="I"?"InputEMRSource":"OutputEMRSource"},
                   {"name",ioSetting.IntakeOutput },
            };
        }

        /// <summary>
        /// 根据sourceID获取关联的出入量记录
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="sourceID"></param>
        /// <returns></returns>
        public async Task<PatientIntakeOutputInfo> GetRecordBySourceIDAndInpatientIDAsync(string inpatientID, string sourceID)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.SourceID == sourceID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 查询其他专项数据关联的出入量数据
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="sourceID">来源序号</param>
        /// <param name="iOkind">IO种类</param>
        /// <returns></returns>
        public async Task<List<PatientIntakeOutputInfo>> GetViewBySourceIDAndKind(string inpatientID, string sourceID, int? iOkind)
        {
            // 情况一：跳转窗口录入，有SourceID，可能有ioKind
            var bindingIO = await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.SourceID == sourceID && m.DeleteFlag != "*").ToListAsync();
            bindingIO = bindingIO.Where(iOkind.HasValue, m => m.IntakeOutputKind == iOkind.Value.ToString()).ToList();
            // 情况二：出入量专项录入引流液，无SourceID，有TubeCareMainID
            if (bindingIO.Count == 0)
            {
                bindingIO = await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.PatientTubeCareMainID == sourceID && m.DeleteFlag != "*").ToListAsync();
            }

            return bindingIO;
        }
        /// <summary>
        /// 根据来源获取IO量
        /// </summary>
        /// <param name="sourceID">来源序号</param>
        /// <param name="sourceType">来源类别</param>
        /// <returns></returns>
        public async Task<string> GetVloumeBySourceID(string sourceID, string sourceType)
        {
            return await _dbContext.PatientIntakeOutputs
                .Where(m => m.SourceID == sourceID && m.SourceType == sourceType && m.DeleteFlag != "*")
                .Select(m => m.IntakeOutputVolume.HasValue ? m.IntakeOutputVolume.Value.ToString() : null)
                .FirstOrDefaultAsync();
        }
        public async Task<List<PatientIntakeOutputInfo>> GetSiglePeronIODataByKind(string inpatientID, DateTime startTime, DateTime endTime)
        {
            var query = await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();
            return query.Where(m => m.IODate.Date.Add(m.IOTime) >= startTime && m.IODate.Date.Add(m.IOTime) <= endTime).ToList();
        }
        public async Task<List<PatientIntakeOutputInfo>> GetByGroupID(string GroupID)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.GroupID == GroupID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<PatientIntakeOutputInfo> GetPatientIntakeOutputData(string inpatientID, string patientScheduleMainID, int settingID)
        {
            var query = await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*"
                        && m.PatientScheduleMainID == patientScheduleMainID && m.IntakeOutputSettingID == settingID).FirstOrDefaultAsync();
            return query;
        }
        /// <summary>
        /// 获取患者一段时间内的IO数据
        /// </summary>
        /// <param name="inpatientIDs">患者序号</param>
        /// <param name="stationID">病区ID</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns></returns>
        public async Task<List<PatientIntakeOutputView>> GetIOByStartEndDateTime(string[] inpatientIDs, int stationID, DateTime startDate, DateTime endDate)
        {
            var start = startDate.Date;
            var end = endDate.Date;
            var data = await _dbContext.PatientIntakeOutputs.Where(m => inpatientIDs.Contains(m.InpatientID) && m.IODate >= start && m.IODate <= end
                              && m.StationID == stationID && m.DeleteFlag != "*").Select(m => new PatientIntakeOutputView
                              {
                                  InpatientID = m.InpatientID,
                                  IODate = m.IODate,
                                  IOTime = m.IOTime,
                                  IntakeOutputVolume = m.IntakeOutputVolume.HasValue ? m.IntakeOutputVolume.Value : 0,
                                  IntakeOutputSettingID = m.IntakeOutputSettingID,
                                  IOKind = m.IntakeOutputKind,
                              }).ToListAsync();
            return data.Where(m => m.IODate.Date.Add(m.IOTime) >= startDate && m.IODate.Date.Add(m.IOTime) <= endDate).ToList();
        }
        /// <summary>
        /// 获取病人一段时间内某病区的出入量数据，不跟踪
        /// </summary>
        /// <param name="inpatientID">病人住院序号</param>
        /// <param name="stationID">病区ID</param>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <returns></returns>
        public async Task<List<PatientIntakeOutputInfo>> GetViewsAsNoTracking(string inpatientID, int? stationID, DateTime startDateTime, DateTime endDateTime)
        {
            var query = await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID
                          && m.IODate >= startDateTime.Date && m.IODate <= endDateTime.Date
                          && m.DeleteFlag != "*").Where(stationID.HasValue, t => t.StationID == stationID.Value)
                          .Select(m => new PatientIntakeOutputInfo
                          {
                              ID = m.ID,
                              IntakeOutputSettingID = m.IntakeOutputSettingID,
                              IntakeOutputKind = m.IntakeOutputKind,
                              IntakeOutputVolume = m.IntakeOutputVolume,
                              IODate = m.IODate,
                              IOTime = m.IOTime,
                              Characteristic = m.Characteristic,
                              SourceType = m.SourceType,
                              GroupID = m.GroupID,
                              InPutType = m.InPutType,
                              ModifyPersonID = m.ModifyPersonID,
                          }).AsNoTracking().ToListAsync();

            var data = query.Where(m => m.IODate.Add(m.IOTime) >= startDateTime && m.IODate.Add(m.IOTime) <= endDateTime).ToList();
            return data;
        }
        /// <summary>
        /// 根据班别日期获取出入量数据
        /// </summary>
        /// <param name="inpatientID">病人住院序号</param>
        /// <param name="kinds">输入输出类别</param>
        /// <param name="startDateTime">开始日期时间</param>
        /// <param name="endDateTime">结束日期时间</param>
        /// <returns></returns>
        public async Task<List<PatientIntakeOutputView>> GetViewByKindAndShiftDateTime(string inpatientID, string[] kinds, DateTime startDateTime, DateTime endDateTime)
        {
            var ioDatas = await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" && m.ShiftDate.HasValue && m.ShiftDate >= startDateTime.Date &&
            m.ShiftDate <= endDateTime.Date && kinds.Contains(m.IntakeOutputKind)).Select(m => new PatientIntakeOutputView
            {
                IODate = m.IODate,
                IOTime = m.IOTime,
                IntakeOutputVolume = m.IntakeOutputVolume ?? 0,
                Times = m.IntakeOutputTimes ?? 0,
                IOKind = m.IntakeOutputKind,
                SourceID = m.SourceID,
                IntakeOutputSettingID = m.IntakeOutputSettingID,
                PatientTubeRecordID = m.PatientTubeRecordID,
                ShiftDate = m.ShiftDate,
            }).ToListAsync();
            return ioDatas.Where(m => m.ShiftDate.HasValue && (m.ShiftDate.Value.Add(m.IOTime) >= startDateTime || m.ShiftDate.Value.Add(m.IOTime) <= endDateTime)).ToList();
        }
        /// <summary>
        /// 按类别获取病人某类别的总量
        /// </summary>
        /// <param name="inpatientIDs">病人住院序号</param>
        /// <param name="kind">类别</param>
        /// <returns></returns>
        public async Task<Dictionary<string, decimal>> GetInpatientsIOsByKind(IEnumerable<string> inpatientIDs, string kind)
        {
            return await _dbContext.PatientIntakeOutputs
                .Where(m => inpatientIDs.Contains(m.InpatientID) && m.IntakeOutputKind == kind && m.DeleteFlag != "*")
                .GroupBy(m => m.InpatientID)
                .Select(g => new { InpatientID = g.Key, TotalVolume = g.Sum(m => m.IntakeOutputVolume) })
                .ToDictionaryAsync(g => g.InpatientID, g => g.TotalVolume ?? 0);
        }
        /// <summary>
        /// 获取部分导管线管的IO信息
        /// </summary>
        /// <param name="inpatientID">病人住院唯一ID</param>
        /// <param name="patientTubeCareMainID">导管维护记录ID</param>
        /// <returns></returns>
        public async Task<PatientIntakeOutputInfo> GetPartViewByCareMainID(string inpatientID, string patientTubeCareMainID)
        {
            return await _dbContext.PatientIntakeOutputs.Where(m => m.InpatientID == inpatientID && m.PatientTubeCareMainID == patientTubeCareMainID && m.DeleteFlag != "*")
                .Select(m => new PatientIntakeOutputInfo
                {
                    IntakeOutputVolume = m.IntakeOutputVolume,
                    Characteristic = m.Characteristic,
                    CharacteristicID = m.CharacteristicID,
                    Color = m.Color,
                    SmellID = m.SmellID,
                    Smell = m.Smell,
                    Shift = m.Shift,
                    IntakeOutputSettingID = m.IntakeOutputSettingID,
                    IODate = m.IODate,
                    IOTime = m.IOTime,
                }).FirstOrDefaultAsync();
        }

    }
}