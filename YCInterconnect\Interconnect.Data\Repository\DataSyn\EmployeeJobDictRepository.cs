﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Interconnect.Data
{
   public class EmployeeJobDictRepository
    {
        private DataOutConnection _DataOutConnection = null;

        public EmployeeJobDictRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }

        public  List<EmployeeJobDictInfo> GetAllAsync()
        {
            try
            {
                return  _DataOutConnection.EmployeeJobDictInfos.ToList();
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}
