﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("NursingRecord_Barthel")]
    public class NRBarthelInfo : ModifyReturnInfo
    {
        /// <summary>
        ///自增长编号
        ///</summary>
        [Key]
        [Column("SN")]
        public int SN { get; set; }
        /// <summary>
        ///	就诊序号	
        ///</summary>
        public string CureNo { get; set; }
      
        /// <summary>
        ///	评估日期	
        ///</summary>
        public DateTime? ReportDate { get; set; }
        /// <summary>
        ///	进食：0 需极大帮助,5需部分帮
        ///</summary>
        public short? Feeding { get; set; }
        /// <summary>
        ///	洗澡：0需部分帮助,5完全独立	
        ///</summary>
        public short? Bathing { get; set; }
        /// <summary>
        ///	修饰：0需部分帮助,5完全独立	
        ///</summary>
        public short? Grooming { get; set; }
        /// <summary>
        ///	穿衣：0需极大帮助,5需部分帮助
        ///</summary>
        public short? Dressing { get; set; }
        /// <summary>
        ///	控制大便：0完全失控,5偶尔失控
        ///</summary>
        public short? Bowels { get; set; }
        /// <summary>
        ///	控制小便：0完全失控,5偶尔失控
        ///</summary>
        public short? Bladder { get; set; }
        /// <summary>
        ///	如厕：0需极大帮助,5需部分帮助
        ///</summary>
        public short? ToiletUse { get; set; }
        /// <summary>
        ///	床椅转移：0完全依赖,5需极大帮
        ///</summary>
        public short? Transfers { get; set; }
        /// <summary>
        ///	平地行走：0完全依赖,5需极大帮
        ///</summary>
        public short? Mobility { get; set; }
        /// <summary>
        ///	上下楼梯：0需极大帮助,5需部分
        ///</summary>
        public short? Stairs { get; set; }
        /// <summary>
        ///	录入人工号	
        ///</summary>
        public string InputerCode { get; set; }
        /// <summary>
        ///	录入人姓名	
        ///</summary>
        public string InputerName { get; set; }
        /// <summary>
        ///	录入时间	
        ///</summary>
        public DateTime? InputTime { get; set; }
        /// <summary>
        ///	病区代码	
        ///</summary>
        public string WardCode { get; set; }
        /// <summary>
        ///	科室代码	
        ///</summary>
        public string DeptCode { get; set; }
        /// <summary>
        ///	床号	
        ///</summary>
        public string BedNo { get; set; }
        /// <summary>
        ///	护士长审核人工号	
        ///</summary>
        public string HeadNurseCode { get; set; }
        /// <summary>
        ///	护士长审时间	
        ///</summary>
        public DateTime? HeadNurseTime { get; set; }
        /// <summary>
        ///	科护士长审核人工号	
        ///</summary>
        public string DeptHeadNurseCode { get; set; }
        /// <summary>
        ///	科护士长审时间	
        ///</summary>
        public DateTime? DeptHeadNurseTime { get; set; }
        /// <summary>
        ///	删除人工号	
        ///</summary>
        public string DelOpCode1 { get; set; }
        /// <summary>
        ///	删除时间	
        ///</summary>
        public DateTime? DelTime { get; set; }
        /// <summary>
        ///	状态：0 待审核，1护士长已审核
        ///</summary>
        public int? Status { get; set; }
        /// <summary>
        ///	1 已关联护理级别医嘱	
        ///</summary>
        public int? UseStatus { get; set; }
        /// <summary>
        ///	关联护理级别医嘱编号	
        ///</summary>
        public int? OrderID { get; set; }
        /// <summary>
        ///	最后更新时间	
        ///</summary>
        public DateTime? LastUpdateTime { get; set; }
        /// <summary>
        ///	审核时间	
        ///</summary>
        public DateTime? AuditTime { get; set; }
        /// <summary>
        ///	审核人工号	
        ///</summary>
        public string AuditOpCode { get; set; }
        /// <summary>
        ///	删除人工号	
        ///</summary>
        public string DelOpCode { get; set; }
    }
}