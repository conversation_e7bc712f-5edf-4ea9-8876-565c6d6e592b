﻿using DataStatistics.Models;
using Medical.Models;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public partial class MedicalDbContext
    {
        //岗位字典
        public DbSet<JobArchitectureInfo> JobArchitectureInfos { get; set; }

        public DbSet<AdministrationSettingInfo> AdministrationSettingInfos { get; set; }

        public DbSet<EmployeeBasicInfo> EmployeeBasicInfos { get; set; }
        public DbSet<EmployeeContactInfo> EmployeeContactInfos { get; set; }
        public DbSet<EmployeeJobInfo> EmployeeJobInfos { get; set; }
        public DbSet<EmployeeSpecilistInfo> EmployeeSpecilistInfos { get; set; }
        public DbSet<EducationDegreeInfo> EducationDegreeInfos { get; set; }
        public DbSet<PersonalLanguageInfo> PersonalLanguageInfos { get; set; }
        public DbSet<EmployeeJobPersonSet> EmployeeJobPersonSets { get; set; }
        /// <summary>
        /// 科室岗位
        /// </summary>
        public DbSet<DepartmentJobInfo> DepartmentJobInfos { get; set; }
        /// <summary>
        /// 人员基本信息
        /// </summary>
        public DbSet<EmployeeDataInfo> EmployeeDataInfos { get; set; }
        /// <summary>
        /// 职务职称信息
        /// </summary>
        public DbSet<PostTitleInfo> PostTitleInfos { get; set; }
        /// <summary>
        /// 亲属关系信息
        /// </summary>
        public DbSet<RelativesInfo> RelativesInfos { get; set; }
        /// <summary>
        /// 民族字典
        /// </summary>
        public DbSet<RaceInfo> RaceInfos { get; set; }
        /// <summary>
        /// 级别字典
        /// </summary>
        public DbSet<RankListInfo> RankListInfos { get; set; }
        /// <summary>
        /// 专业字典
        /// </summary>
        public DbSet<SpecialFieldListInfo> SpecialFieldListInfos { get; set; }
        /// <summary>
        /// 职称字典
        /// </summary>
        public DbSet<ProfessionalTitleListInfo> ProfessionalTitleListInfos { get; set; }
        /// <summary>
        /// 部门字典
        /// </summary>
        public DbSet<OrganizationDepartmentInfo> OrganizationDepartmentInfos { get; set; }
        /// <summary>
        /// 职务字典
        /// </summary>
        public DbSet<DepartmentPostInfo> DepartmentPostInfos { get; set; }

        public DbSet<FilePathInfo> FilePathInfos { get; set; }

        public DbSet<UserInfo> EmployeelDataInfos { get; set; }
        /// <summary>
        /// 日带教表
        /// </summary>

        public DbSet<DailyTeachingInfo> DailyTeachingInfos { get; set; }

    }
}
