﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class EmployeeCADataRepository : IEmployeeCADataRepository
    {
        private MedicalDbContext _dbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public EmployeeCADataRepository(MedicalDbContext dbContext, IMemoryCache memoryCache, SessionCommonServer sessionCommonServer, GetCacheService getCacheService)
        {
            _dbContext = dbContext;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<List<EmployeeCADataInfo>> GetAllEmployeeCADataInfoAsync()
        {
            string key = GetCacheType();

            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);

            return await _dbContext.EmployeeCADataInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<bool> UpdateAsync(EmployeeCADataInfo t)
        {
            _dbContext.EmployeeCADataInfos.Update(t);
            return await _dbContext.SaveChangesAsync() > 0;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<EmployeeCADataInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            return await _dbContext.EmployeeCADataInfos.Where(m => m.HospitalID == hospitalID.ToString()&& m.DeleteFlag != "*").ToListAsync();
        }


        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.EmployeeCAData.GetKey(_sessionCommonServer);
        }

        /// <summary>
        /// 根据用户ID获取用户信息（有缓存取缓存，没缓存取数据库）
        /// </summary>
        /// <param name="caUserID"></param>
        /// <returns></returns>
        public async Task<EmployeeCADataInfo> GetEmployeeCADataByCaUserIDAsync(string caUserID)
        {
            //先从缓存获取
            var employeeCADataList = await GetCacheAsync() as List<EmployeeCADataInfo>;

            var employeeCADataInfo = employeeCADataList.Where(m => m.CAUserID == caUserID).FirstOrDefault();
            if (employeeCADataInfo != null)
            {
                return employeeCADataInfo;
            }
            //没有从数据库取

            return await this.GetEmployeeIDByEmployeeIDAsync(caUserID);
        }
        /// <summary>
        /// 根据用户ID获取用户信息（有缓存取缓存，没缓存取数据库）
        /// </summary>
        /// <param name="modifyPersonID"></param>
        /// <returns></returns>
        public async Task<EmployeeCADataInfo> GetModifyPersonIDByEmployeeIDAsync(string modifyPersonID)
        {
            //先从缓存获取
            var employeeCADataList = await GetCacheAsync() as List<EmployeeCADataInfo>;
            var employeeCADataInfo = employeeCADataList.Where(m => m.UserID == modifyPersonID).FirstOrDefault();
            if (employeeCADataInfo != null)
            {
                return employeeCADataInfo;
            }
            //没有从数据库取

            return await this.GetEmployeeIDByEmployeeIDAsync(modifyPersonID);
        }
        /// <summary>
        /// 根据用户employeeID查找到用户
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<EmployeeCADataInfo> GetEmployeeIDByEmployeeIDAsync(string employeeID)
        {
            string key = GetCacheType();

            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);

            var employeeCADataInfo = await _dbContext.EmployeeCADataInfos.Where(m => m.HospitalID == hospitalID && m.UserID == employeeID && m.DeleteFlag != "*").FirstOrDefaultAsync();
            if (employeeCADataInfo != null)
            {
                return employeeCADataInfo;
            }
            return null;
        }
        /// <summary>
        /// 获取最新可以使用的主键ID
        /// </summary>
        /// <returns></returns>
        public int GetNextCAIDAsync()
        {
            string key = GetCacheType();

            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);

            int lastID = _dbContext.EmployeeCADataInfos.Where(m => m.HospitalID == hospitalID).Max(m => m.EmployeeCADataID);

            return lastID + 1;
        }
        /// <summary>
        ///从数据库中获取所有的CA数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<EmployeeCADataInfo>> GetEmployeeCANoCacheAsync()
        {
            string key = GetCacheType();

            var (hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);

            return await _dbContext.EmployeeCADataInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();

        }

        /// <summary>
        /// 根据用户CA签名专用CAUserID获取用户签名信息（取数据库）
        /// </summary>
        /// <param name="caUserID"></param>
        /// <returns></returns>
        public async Task<EmployeeCADataInfo> GetEmployeeCADataByCaUserIDNoCacheAsync(string caUserID)
        {
            string key = GetCacheType();
            var (hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            return await _dbContext.EmployeeCADataInfos.Where(m => m.HospitalID == hospitalID && m.CAUserID == caUserID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<List<EmployeeCADataInfo>> GetEmployeeCADataByIDsAsync(List<string> ids)
        {
            var employeeCADataList = await GetCacheAsync() as List<EmployeeCADataInfo>;
            return employeeCADataList.Where(m => ids.Contains(m.UserID)).ToList();
        }
    }
}
