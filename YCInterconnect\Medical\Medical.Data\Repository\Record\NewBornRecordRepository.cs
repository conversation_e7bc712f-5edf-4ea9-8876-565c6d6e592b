﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class NewBornRecordRepository : INewBornRecordRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;
        private readonly IInpatientDataRepository _inpatientDataRepository;

        public NewBornRecordRepository(MedicalDbContext db, IInpatientDataRepository inpatientDataRepository)
        {
            _medicalDbContext = db;
            _inpatientDataRepository = inpatientDataRepository;
        }

        public async Task<NewBornRecordInfo> GetDataByCasenumber(string casenumber)
        {
            return await _medicalDbContext.NewBornRecordInfos.Where(m => m.Casenumber == casenumber && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据chartNo获取记录
        /// </summary>
        /// <param name="chartNo"></param>
        /// <returns></returns>
        public async Task<NewBornRecordInfo> GetDataByChartNo(string chartNo)
        {
            return await _medicalDbContext.NewBornRecordInfos.Where(m => m.ChartNO == chartNo && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据母亲CaseNumber 获取新生儿数据
        /// </summary>
        /// <param name="parentCasenumber"></param>
        /// <returns></returns>
        public async Task<List<NewBornRecordInfo>> GetDataByParentCasenumber(string parentCasenumber)
        {
            return await _medicalDbContext.NewBornRecordInfos.Where(m => m.ParentCasenumber == parentCasenumber && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据母亲CaseNumber 获取有住院信息的新生儿数据
        /// </summary>
        /// <param name="apiNo"></param>
        /// <returns></returns>
        public async Task<List<NewBornRecordInfo>> GetInpatientNewBornByParentCasenumber(string parentCasenumber)
        {
            return await _medicalDbContext.NewBornRecordInfos.Where(m => m.ParentCasenumber == parentCasenumber && m.Casenumber != null && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据母亲CaseNumber 获取有住院信息的新生儿数据
        /// </summary>
        /// <param name="apiNo"></param>
        /// <returns></returns>
        public async Task<List<NewBornRecordInfo>> GetNewBornViewByParentCasenumber(string parentCasenumber)
        {
            return await _medicalDbContext.NewBornRecordInfos.Where(m => m.ParentCasenumber == parentCasenumber && m.Casenumber != null && m.DeleteFlag != "*")
                .Select(
               m => new NewBornRecordInfo
               {
                   Casenumber = m.Casenumber,
                   ChartNO = m.ChartNO,
                   AddDate = m.AddDate
               }).ToListAsync();
        }

        /// <summary>
        /// 根据母亲CaseNumber 获取新生儿在院数据
        /// </summary>
        /// <param name="parentCasenumber"></param>
        /// <returns></returns>
        public async Task<List<NewBornRecordInfo>> GetByParentCasenumber(string parentCasenumber)
        {
            return await (from a in _medicalDbContext.NewBornRecordInfos
                          join b in _medicalDbContext.InpatientDatas
                          on a.Casenumber equals b.CaseNumber
                          where a.ParentCasenumber == parentCasenumber
                          && InHospitalStatus.INHOSPITALLIST.Contains(b.InHospitalStatus ?? -1)
                          && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                          select a).ToListAsync();
        }

        public async Task<List<NewBornRecordInfo>> GetDataByParentChartNo(string parentChartNo)
        {
            return await _medicalDbContext.NewBornRecordInfos.Where(m => m.ParentChartNO == parentChartNo && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据母亲inpatientID获取数据
        /// </summary>
        /// <param name="parentID"></param>
        /// <returns></returns>
        public async Task<List<NewbornView>> GetDataByParentID(string parentID)
        {
            var newbornViews = await _medicalDbContext.NewBornRecordInfos.Where(m => m.ParentID == parentID &&
            m.DeleteFlag != "*").Select(m => new NewbornView
            {
                PatientDeliveryCareMainID = m.PatientDeliveryCareMainID,
                AssessDate = m.DeliveryTime ?? null,
                AssessTime = m.DeliveryTime.HasValue ? m.DeliveryTime.Value.TimeOfDay : TimeSpan.Zero,
                NewBornNum = m.NewBornNum,
                Gender = m.Gender,
                Length = m.Length,
                Weight = m.Weight,
                DeliveryMode = m.DeliveryMode,
                UserID = m.AddEmployeeID,
                ParentID = m.ParentID
            }).ToListAsync();
            return newbornViews;
        }
        /// <summary>
        /// 根据维护记录ID集合获取新生儿信息集合
        /// </summary>
        /// <param name="careMainIDs"></param>
        /// <returns></returns>
        public async Task<List<NewBornRecordInfo>> GetRecordsByCareMainIDs(params string[] careMainIDs)
        {
            return await _medicalDbContext.NewBornRecordInfos.Where(m => careMainIDs.Contains(m.PatientDeliveryCareMainID) && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<NewBornRecordInfo>> GetByDeliveryRecordID(string patientDeliveryRecordID)
        {
            return await _medicalDbContext.NewBornRecordInfos.Where(m => m.PatientDeliveryRecordID == patientDeliveryRecordID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据主键获取数据
        /// </summary>
        /// <param name="newBornID"></param>
        /// <returns></returns>
        public async Task<NewBornRecordInfo> GetByID(string newBornID)
        {
            return await _medicalDbContext.NewBornRecordInfos.Where(m => m.NewBornID == newBornID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据CaseNumber 判断是否有记录
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        public async Task<NewBornRecordInfo> GetRecordByCaseNumber(string caseNumber)
        {
            var babyRecord = await _medicalDbContext.NewBornRecordInfos.Where(m => m.Casenumber == caseNumber).FirstOrDefaultAsync();
            if (babyRecord != null)
            {
                return babyRecord;
            }
            return null;
        }

        /// <summary>
        /// 根据母亲的住院号获取新生儿的住院号
        /// </summary>
        /// <param name="parentCasenumber">母亲的住院号</param>
        /// <returns></returns>
        public async Task<List<string>> GetNewBornCaseNumberByParentCaseNumber(string parentCasenumber)
        {
            return await _medicalDbContext.NewBornRecordInfos.Where(m => m.ParentCasenumber == parentCasenumber
            && m.DeleteFlag != "*")
                .Select(m => m.Casenumber).ToListAsync();
        }

        /// <summary>
        /// 根据维护记录ID集合获取新生儿评估数据
        /// </summary>
        /// <param name="careMainInfos">维护记录主表集合</param>
        /// <returns></returns>
        public async Task<List<dynamic>> GetViewsByCareMainIDs(List<PatientDeliveryCareMainInfo> careMainInfos)
        {
            var careMainIDs = careMainInfos.Select(m => m.PatientDeliveryCareMainID);
            var newBornRecordInfos = await _medicalDbContext.NewBornRecordInfos.Where(m => careMainIDs.Contains(m.PatientDeliveryCareMainID) && m.DeleteFlag != "*").ToListAsync();
            var patientDatas = await _inpatientDataRepository.GetPatientDataByCaseNumbersContainsDischarge(newBornRecordInfos.Select(m => m.Casenumber));
            var newbornViews = (from m in newBornRecordInfos
                                join n in careMainInfos
                                on m.PatientDeliveryCareMainID equals n.PatientDeliveryCareMainID
                                join q in patientDatas
                                on m.Casenumber equals q.CaseNumber
                                select (dynamic)new NewbornView
                                {
                                    PatientDeliveryCareMainID = m.PatientDeliveryCareMainID,
                                    InpatientID = q.InpatientID,
                                    PatientID = q.PatientID,
                                    ChartNo = m.ChartNO,
                                    CaseNumber = m.Casenumber,
                                    Age = q.Age,
                                    DateOfBirth = q.DateOfBirth,
                                    NusingLevel = q.NursingLevel,
                                    StationID = n.StationID,
                                    DepartmentListID = n.DepartmentListID,
                                    BedID = q.BedID,
                                    BedNumber = q.BedNumber,
                                    NewBornID = m.NewBornID,
                                    AssessDate = n.AssessDate,
                                    AssessTime = n.AssessTime,
                                    NewBornNum = m.NewBornNum,
                                    Gender = m.Gender,
                                    Length = m.Length,
                                    Weight = m.Weight,
                                    DeliveryMode = m.DeliveryMode,
                                    UserID = n.AddEmployeeID,
                                    BringToShift = n.BringToShift,
                                    InformPhysician = n.InformPhysician,
                                    BringToNursingRecord = n.BringToNursingRecord,
                                }).ToList();
            return newbornViews;
        }
        /// <summary>
        /// 根据维护记录ID获取副表数据
        /// </summary>
        /// <param name="careMainID">维护记录ID</param>
        /// <returns></returns>
        public async Task<dynamic> GetByCareMainID(string careMainID)
        {
            return await _medicalDbContext.NewBornRecordInfos.Where(m => careMainID == m.PatientDeliveryCareMainID && m.DeleteFlag != "*")
                .Select(m => (dynamic)m)
                .FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据主键获取新生儿的CaseNumber
        /// </summary>
        /// <param name="newBornID">主键</param>
        /// <returns></returns>
        public async Task<string> GetNewBornCaseNumberByID(string newBornID)
        {
            return await _medicalDbContext.NewBornRecordInfos.Where(m => m.NewBornID == newBornID)
                .Select(m => m.Casenumber).FirstOrDefaultAsync();
        }

        public async Task<bool> GetNewBornExistByCaseNumber(string caseNumber)
        {
            return await _medicalDbContext.NewBornRecordInfos.AnyAsync(m => m.Casenumber == caseNumber && m.DeleteFlag != "*");
        }
    }
}