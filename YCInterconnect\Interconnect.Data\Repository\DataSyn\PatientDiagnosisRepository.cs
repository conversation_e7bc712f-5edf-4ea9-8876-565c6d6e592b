﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Interconnect.Data.Repository
{
    public class PatientDiagnosisRepository : IPatientDiagnosisRepository
    {
        private DataOutConnection _DataOutConnection = null;

        public PatientDiagnosisRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }
        /// <summary>
        /// 获取所有没有抽取的病区数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<InterconnectPatientDiagnosisInfo>> GetAsync(int tongbuCount, int takeRows)
        {
            return await _DataOutConnection.PatientDiagnosis.Where(m => m.DataPumpFlag != "*"
            && ((m.Counts ?? 0) < tongbuCount)).Take(takeRows).ToListAsync();
        }
    }
}