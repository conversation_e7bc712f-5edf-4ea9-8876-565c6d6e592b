﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientRestraintRecordRepository : IPatientRestraintRecordRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientRestraintRecordRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 根据主表ID获取对应数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<PatientRestraintRecordInfo> GetByID(string id)
        {
            return await _medicalDbContext.PatientRestraintRecordInfos.Where(t => t.PatientRestraintRecordID == id && t.DeleteFlag != "*").SingleOrDefaultAsync();
        }

        /// <summary>
        /// 获取主记录数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<List<PatientRestraintRecord>> GetByInpatientID(string inpatientID, string recordID)
        {
            var list = await _medicalDbContext.PatientRestraintRecordInfos.Where(t => t.InpatientID == inpatientID && t.DeleteFlag != "*")
                .Select(m => new PatientRestraintRecord
                {
                    PatientRestraintRecordID = m.PatientRestraintRecordID,
                    StationID = m.StationID,
                    DepartmentListID = m.DepartmentListID,
                    AssessFrequency = m.AssessFrequency,
                    StartDate = m.StartDate,
                    StartTime = m.StartTime,
                    EndDate = m.EndDate,
                    EndTime = m.EndTime,
                    ConsentFlag = m.ConsentFlag,
                    OrderPhysicianID = m.OrderPhysicianID,
                    AddEmployeeID = m.AddEmployeeID,
                    EMRDocumentID = m.EMRDocumentID,
                    AddDate = m.AddDate
                }).ToListAsync();
            if (!string.IsNullOrEmpty(recordID))
            {
                list = list.Where(m => m.PatientRestraintRecordID == recordID).ToList();
            }
            return list;
        }

        public async Task<List<PatientRestraintRecordInfo>> GetByTimeIDAsync(DateTime endTime)
        {
            return await _medicalDbContext.PatientRestraintRecordInfos.Where(m => (m.EndDate >= endTime || m.EndDate == null) && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<HandoverRestraintCareIntervention>> GetPatientRestraintCareIntervention(string inpatientID, DateTime startDate, TimeSpan startTime, DateTime endDate, TimeSpan endTime)
        {
            var datas = await (from a in _medicalDbContext.PatientRestraintCareMainInfos
                               join b in _medicalDbContext.PatientRestraintRecordInfos on a.PatientRestraintRecordID equals b.PatientRestraintRecordID
                               where a.InpatientID == inpatientID && a.AssessDate >= startDate && a.AssessDate <= endDate && a.BringToShift == true && a.BringToShift == true && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                               select new HandoverRestraintCareIntervention
                               {
                                   InpatientID = a.InpatientID,
                                   AssessDate = a.AssessDate,
                                   AssessTime = a.AssessTime,
                                   CareIntervention = a.CareIntervention,
                                   Sort = 0,
                                   BodyPartName = "",
                                   RestraintTool = a.RestraintTool,
                                   RestraintLocation = a.RestraintLocation,
                                   RestraintReason = a.RestraintReason,
                                   EndDate = b.EndDate
                               }).ToListAsync();

            if (datas.Count == 0)
            {
                return datas;
            }

            datas = datas.Where(m => m.AssessDate.Value.Add(m.AssessTime.Value) >= startDate.Date.Add(startTime) && m.AssessDate.Value.Add(m.AssessTime.Value) <= endDate.Date.Add(endTime))
                .GroupBy(m => m.PatientRestraintRecordID)
                .Select(m => m.OrderByDescending(n => n.AssessDate.Value.Date.Add(n.AssessTime.Value)).FirstOrDefault()).ToList();

            return datas;
        }

        /// <summary>
        /// 取得当前病人最新约束的添加时间
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <returns></returns>
        public async Task<DateTime> GetLastRecordByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientRestraintRecordInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*")
                .Select(m => m.AddDate).MaxAsync();
        }

        /// <summary>
        /// 通过inpatientID获取未结束的约束
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<PatientRestraintRecordInfo> GetNotFinishedRestraintByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientRestraintRecordInfos.Where(m => m.InpatientID == inpatientID && !m.EndDate.HasValue && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<int> GetRestraintCountByInpatientID(string inpatientID)
        {
            var query = await _medicalDbContext.PatientRestraintRecordInfos.Where(m => m.InpatientID == inpatientID && !m.EndDate.HasValue && m.DeleteFlag != "*").ToListAsync();
            return query.Count();
        }
        /// <summary>
        /// 获取某个病区在院患者的约束记录
        /// </summary>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<List<PatientRestraintRecordInfo>> GetRestraintLitsByStationID(int stationID)
        {
            return await (from a in _medicalDbContext.PatientRestraintRecordInfos
                          join b in _medicalDbContext.InpatientDatas
                          on a.InpatientID equals b.ID
                          where a.StationID == stationID
                          && InHospitalStatus.INHOSPITALLIST.Contains(b.InHospitalStatus ?? -1)
                          && b.StationID == stationID
                          && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                          select a).ToListAsync();
        }
    }
}