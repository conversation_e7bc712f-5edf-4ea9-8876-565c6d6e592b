﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    /// <summary>
    /// 设定档
    /// </summary>
    [Serializable]
    [Table("SettingDescription")]
  public  class SettingDescriptionInfo
    {
        /// <summary>
        /// 类别
        /// </summary>      
        public int SettingType { get; set; }
        /// <summary>
        ///类别码
        /// </summary>        
        public string SettingTypeName { get; set; }

        /// <summary>
        /// 类型编码
        /// </summary>        
        public string TypeCode { get; set; }

        /// <summary>
        /// 类别值
        /// </summary>        
        public string TypeValue { get; set; }
        /// <summary>
        /// 说明
        /// </summary> 
        public string Description { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>      
        public DateTime? ModifyDate { get; set; }
        /// <summary>
        /// 删除标志 *表示删除
        /// </summary>       
        public string DeleteFlag { get; set; }
        /// <summary>
        /// 修改人员
        /// </summary>     
        public string ModifyPersonID { get; set; }
    }
}
