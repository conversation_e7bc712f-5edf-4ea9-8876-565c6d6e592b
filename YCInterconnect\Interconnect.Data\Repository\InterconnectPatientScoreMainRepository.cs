﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Interconnect.Data.Interface;

namespace Interconnect.Data
{
   public class InterconnectPatientScoreMainRepository: IInterconnectPatientScoreMainRepository
    {
        private DataOutConnection _DataOutConnection = null;

        public InterconnectPatientScoreMainRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }
        public async Task<List<InterconnectPatientScoreMainInfo>> GetPatientScoreMainNotSync()
        {
            return await _DataOutConnection.InterconnectPatientScoreMainInfos.Where(m =>m.DataPumpFlag!="*").ToListAsync();
        }
    
    }
}
