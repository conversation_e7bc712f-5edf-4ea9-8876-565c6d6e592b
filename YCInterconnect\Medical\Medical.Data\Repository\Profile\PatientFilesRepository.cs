﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientFilesRepository : IPatientFilesRepository
    {
       // private readonly IAssessMainRepository _assessMainRepository;
        private MedicalDbContext _medicalDbContext = null;
        public PatientFilesRepository(MedicalDbContext medicalDbContext) {
            _medicalDbContext = medicalDbContext;
        }
        public Task<List<PatientFilesInfo>> GetAdmissionAssessFile(string inpatientID, string recordID)
        {
            return _medicalDbContext.PatientFiles.Where(m=>m.Inpatientid == inpatientID && m.RecordID == recordID).ToListAsync();
        }

        public Task<List<PatientFilesInfo>> GetAsync(string inpatientID)
        {
            throw new NotImplementedException();
        }

        public Task<List<PatientFilesInfo>> GetBloodFile(string inpatientID)
        {
            throw new NotImplementedException();
        }

        public Task<List<PatientFilesInfo>> GetByTypeAndRecordIDAsync(string inpatientID, string type, string recordID)
        {
            throw new NotImplementedException();
        }

        public Task<List<PatientFilesInfo>> GetByTypeAsync(string inpatientID, string type)
        {
            return _medicalDbContext.PatientFiles.Where(m => m.Inpatientid == inpatientID && m.FileType == type).ToListAsync();
        }

        public Task<List<PatientFilesInfo>> GetByTypeAsync(string inpatientID, string type, int version)
        {
            throw new NotImplementedException();
        }

        public async Task<int> GetVersionByRecordID(string recordID)
        {
            List<PatientFilesInfo> list = await _medicalDbContext.PatientFiles.Where(m=>m.RecordID == recordID).OrderByDescending(m=>m.Version).ToListAsync();
            return list.Count;
        }

        public async Task<int> GetVersionByInpatientIDAndType(string inpatientID, string type)
        {
            List<PatientFilesInfo> list = await _medicalDbContext.PatientFiles.Where(m => m.Inpatientid == inpatientID && m.FileType == type).ToListAsync();
            return list.Count;
        }

        public Task<List<PatientFilesInfo>> GetHandoverDetailFile(string inpatientID,string recordID)
        {
            return _medicalDbContext.PatientFiles.Where(m => m.Inpatientid == inpatientID && m.RecordID == recordID).ToListAsync();
        }

        public Task<List<PatientFilesInfo>> GetHandoverListFile(string inpatientID)
        {
            throw new NotImplementedException();
        }

        public Task<List<PatientFilesInfo>> GetHistoryAssessFile(string inpatientID,string recordID)
        {
            throw new NotImplementedException();
        }

        public Task<List<PatientFilesInfo>> GetLastAllAsync(string inpatientID)
        {
            throw new NotImplementedException();
        }

        public Task<List<PatientFilesInfo>> GetNursingPlanDetailFile(string inpatientID,string recordID)
        {
            throw new NotImplementedException();
        }

        public Task<List<PatientFilesInfo>> GetNursingPlanListFile(string inpatientID)
        {
            throw new NotImplementedException();
        }

        public Task<List<PatientFilesInfo>> GetNursingProblemFile(string inpatientID)
        {
            throw new NotImplementedException();
        }

        public Task<List<PatientFilesInfo>> GetRiskAssessFile(string inpatientID,string recordID)
        {
            throw new NotImplementedException();
        }

        public async Task<int> GetVersionByInpatientIDAndRecordID(string inpatientID, string recordID)
        {
            List<PatientFilesInfo> list = await _medicalDbContext.PatientFiles.Where(m=>m.Inpatientid == inpatientID && m.RecordID == recordID).ToListAsync();
            return list.Count;
        }

        public Task<List<PatientFilesInfo>> GetAdmissionAssessLastVersionFile(string inpatientID,string recordID)
        {
            return null;
        }

        public Task<List<PatientFilesInfo>> GetHistoryAssessLastVersionFile(string inpatientID,string recordID)
        {
            throw new NotImplementedException();
        }

        public Task<List<PatientFilesInfo>> GetNursingProblemLastVersionFile(string inpatientID)
        {
            throw new NotImplementedException();
        }

        public Task<List<PatientFilesInfo>> GetNursingPlanListLastVersionFile(string inpatientID)
        {
            throw new NotImplementedException();
        }

        public Task<List<PatientFilesInfo>> GetNursingPlanDetailLastVersionFile(string inpatientID,string recordID)
        {
            throw new NotImplementedException();
        }

        public Task<List<PatientFilesInfo>> GetHandoverListLastVersionFile(string inpatientID)
        {
            throw new NotImplementedException();
        }

        public Task<List<PatientFilesInfo>> GetHandoverDetailLastVersionFile(string inpatientID,string recordID)
        {
            throw new NotImplementedException();
        }

        public Task<List<PatientFilesInfo>> GetRiskAssessLastVersionFile(string inpatientID,string recordID)
        {
            throw new NotImplementedException();
        }

        public Task<List<PatientFilesInfo>> GetBloodLastVersionFile(string inpatientID)
        {
            throw new NotImplementedException();
        }
    }
}
