﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;


namespace Medical.Data.Repository
{
    public class AssessInteractionRepository : IAssessInteractionRepository
    {
        private MedicalDbContext _dbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly GetCacheService _getCacheService;
        private readonly SessionCommonServer _sessionCommonServer;

        public AssessInteractionRepository(
            MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            ,GetCacheService getCacheService

        )
        {
            _dbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<List<AssessInteractionInfo>> GetAsync()
        {
            var datas = (List<AssessInteractionInfo>)await GetCacheAsync();
            return datas;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<AssessInteractionInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            return await _dbContext.AssessInteractions.Where(m => m.HospitalID == hospitalID.ToString() && m.DeleteFlag != "*").ToListAsync();
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.AssessInterActon.GetKey(_sessionCommonServer);
        }

        public async Task<int?> GetInteractionAssessListIDAsync(int assessID1)
        {
            var list = await GetAsync();
            return list.Find(m => m.AssessID1 == assessID1)?.AssessID2;
        }
    }
}





