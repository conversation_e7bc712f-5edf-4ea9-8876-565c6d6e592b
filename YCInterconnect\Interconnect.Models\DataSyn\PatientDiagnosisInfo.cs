﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("PatientDiagnosis")]
    public class InterconnectPatientDiagnosisInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///住院号
        ///</summary>
        public string CaseNumber { get; set; }
        /// <summary>
        ///病案号
        ///</summary>
        public string ChartNo { get; set; }
        /// <summary>
        ///身分证号
        ///</summary>
        public string IdentityID { get; set; }
        /// <summary>
        ///诊断码
        ///</summary>
        public string ICDCode { get; set; }
        /// <summary>
        ///诊断
        ///</summary>
        public string Diagnosis { get; set; }
        /// <summary>
        ///诊断类别 A入院/P住院中/D出院
        ///</summary>
        public string DiagnosisType { get; set; }
    }
}