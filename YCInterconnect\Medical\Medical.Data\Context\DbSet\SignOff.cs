﻿using Medical.Models;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public partial class MedicalDbContext
    {
        /// <summary>
        /// 签核项目
        /// </summary>
        public DbSet<DocumentListInfo> DocumentLists { get; set; }

        public DbSet<SignOffDetailInfo> SignOffDetails { get; set; }
        public DbSet<SignOffMainInfo> SignOffMains { get; set; }

        /// <summary>
        /// 审核记录表
        /// </summary>
        public DbSet<SignOffLogInfo> SignOffLogInfos { get; set; }

        /// <summary>
        /// 审批流程表
        /// </summary>
        public DbSet<ScheduleedWorkflowInfo> ScheduleedWorkflowInfos { get; set; }
    }
}