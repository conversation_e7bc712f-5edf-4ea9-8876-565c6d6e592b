﻿using System;
using System.Globalization;
using System.Text.RegularExpressions;

namespace Medical.Common
{
    /// <summary>
    /// 字符形态判断 xml 20191231
    /// </summary>
    public static class StringCheck
    {
        /// <summary>
        /// 判断是否是数值
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static bool IsNumeric(string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                return false;
            }
            if (value.Contains("+") || value.Contains("-"))
            {
                return false;
            }
            return Regex.IsMatch(value, @"^[+-]?\d*[.]?\d*$");
        }
        /// <summary>
        /// 判断是否是int
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static bool IsInt(string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                return false;
            }
            return Regex.IsMatch(value, @"^[+-]?\d*$");
        }
        /// <summary>
        /// 判断是否是无符号数
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static bool IsUnsign(string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                return false;
            }
            return Regex.IsMatch(value, @"^\d*[.]?\d*$");
        }
        /// <summary>
        /// 判断是否是电话号码
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static bool IsTel(string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                return false;
            }
            return Regex.IsMatch(value, @"\d{3}-\d{8}|\d{4}-\d{7}");
        }

        /// <summary>
        /// 判断是否是日期
        /// </summary>
        /// <param name="value">值</param>
        /// <returns></returns>
        public static bool IsDate(string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                return false;
            }

            return DateTime.TryParse(value, out var _);
        }

        /// <summary>
        /// 判断是否是时间
        /// </summary>
        /// <param name="value">值</param>
        /// <returns></returns>
        public static bool IsTime(string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                return false;
            }
            var dateTimeStyles = DateTimeStyles.AllowLeadingWhite | DateTimeStyles.AllowTrailingWhite | DateTimeStyles.AllowInnerWhite;
            return DateTime.TryParseExact(value, "HH:mm", CultureInfo.InvariantCulture, dateTimeStyles, out var _);
        }

        public static string ReplaceNullToEmpty(string str)
        {
            if (str == null)
            {
                return "";
            }
            return str;
        }

        /// <summary>
        /// 判断是否为中文
        /// </summary>
        /// <param name="text"></param>
        /// <returns></returns>
        public static bool CheckChinese(string text)
        {
            if (text == null)
            {
                return true;
            }
            for (int i = 0; i < text.Length; i++)
            {
                // 判断中文
                if (Regex.IsMatch(text[i].ToString(), @"^[\u4e00-\u9fa5]+$")) // 如果是中文
                {
                    return true;
                }
            }
            return false;
        }
        /// <summary>
        /// 判断韩语
        /// </summary>
        /// <param name="text"></param>
        /// <returns></returns>
        public static bool CheckKorean(string text)
        {
            if (text == null)
            {
                return true;
            }
            for (int i = 0; i < text.Length; i++)
            {
                if (Regex.IsMatch(text[i].ToString(), @"^[\uac00-\ud7ff]+$"))
                {
                    return true;
                }
            }
            return false;
        }
    }
}


