using System;
using System.Collections.Generic;

namespace Interconnect.ViewModels
{
    /// <summary>
    /// 输血数据API响应模型
    /// </summary>
    public class BloodTransfusionApiResponse
    {
        /// <summary>
        /// 响应状态码
        /// </summary>
        public int Code { get; set; }
        /// <summary>
        /// 响应消息
        /// </summary>
        public string Message { get; set; }
        /// <summary>
        /// 响应数据
        /// </summary>
        public List<BloodTransfusionSyncView> Data { get; set; }
        /// <summary>
        /// 数据总数
        /// </summary>
        public int Total { get; set; }
        /// <summary>
        /// 响应时间
        /// </summary>
        public DateTime ResponseTime { get; set; }
        public BloodTransfusionApiResponse()
        {
            Data = new List<BloodTransfusionSyncView>();
            ResponseTime = DateTime.Now;
        }
    }
    /// <summary>
    /// 输血数据同步结果响应模型
    /// </summary>
    public class BloodTransfusionSyncResponse
    {
        /// <summary>
        /// 响应状态码
        /// </summary>
        public int Code { get; set; }
        /// <summary>
        /// 响应消息
        /// </summary>
        public string Message { get; set; }
        /// <summary>
        /// 同步成功数量
        /// </summary>
        public int SuccessCount { get; set; }
        /// <summary>
        /// 同步失败数量
        /// </summary>
        public int FailCount { get; set; }
        /// <summary>
        /// 新增数量
        /// </summary>
        public int AddCount { get; set; }
        /// <summary>
        /// 更新数量
        /// </summary>
        public int UpdateCount { get; set; }
        /// <summary>
        /// 响应时间
        /// </summary>
        public DateTime ResponseTime { get; set; }
        public BloodTransfusionSyncResponse()
        {
            ResponseTime = DateTime.Now;
        }
    }
}
