﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PhysicianOrderRepository : IPhysicianOrderRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public PhysicianOrderRepository(
            MedicalDbContext db,
            IMemoryCache memoryCache,
            SessionCommonServer sessionCommonServer,
            GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        /// <summary>
        /// 透过医嘱码取得医嘱
        /// </summary>
        /// <param name="orderCode">医嘱码</param>
        /// <returns></returns>
        public async Task<List<PhysicianOrderInfo>> GetOrderByCode(string[] orderCode)
        {
            var datas = await this.GetAllAsync<PhysicianOrderInfo>();
            return datas.Where(m => orderCode.Contains(m.OrderCode) && m.DeleteFlag != "*").ToList();
        }

        /// <summary>
        /// 获取最大ID
        /// </summary>
        /// <returns></returns>
        public async Task<int> GetMaxAsync()
        {
            try
            {
                var user = await _medicalDbContext.PhysicianOrders.OrderByDescending(m => m.ID).FirstOrDefaultAsync();
                if (user == null)
                {
                    return 1;
                }
                else
                {
                    return user.ID + 1;
                }
            }
            catch { }
            return 1;
        }

        /// <summary>
        /// 根据orderType获取OrderCode,OrderName
        /// </summary>
        /// <param name="orderType">医嘱类型</param>
        /// <returns></returns>
        public async Task<List<KeyValueString>> GetOrderCodeByType(string orderType)
        {
            var datas = await this.GetAllAsync<PhysicianOrderInfo>();
            return datas.Where(m => m.OrderType == orderType).Select(m => new KeyValueString
            {
                Key = m.OrderCode,
                Value = m.OrderName
            }).ToList();
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<PhysicianOrderInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            return await _medicalDbContext.PhysicianOrders.Where(m => m.HospitalID == hospitalID.ToString()&& m.DeleteFlag != "*").ToListAsync();
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.PhysicianOrder.GetKey(_sessionCommonServer);
        }
    }
}
