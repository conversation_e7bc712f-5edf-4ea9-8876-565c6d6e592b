﻿/*
 2022年6月16日15:20:12   添加药物查询    <PERSON><PERSON><PERSON>
 */
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.Data;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientMedicineScheduleRepository : IPatientMedicineScheduleRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;
        /// <summary>
        /// 给药未执行状态
        /// </summary>
        private static List<int> UN_PERFORM_TYPE = new List<int> { 20, 30, 33, 35, 40, 45, 50, 55 };
        /// <summary>
        /// 给药执行状态
        /// </summary>
        private static List<int> PERFORM_TYPE = new List<int> { 60, 65, 70, 75 };
        /// <summary>
        /// 给药已执行状态
        /// </summary>
        private static List<int> PERFORM_END_TYPE = new List<int> { 80, 85, 90 };

        public PatientMedicineScheduleRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<List<PatientMedicineScheduleInfo>> GetPatientMedicineSchedule(string inpatientID, string hospitalID, DateTime startDate, DateTime endDate)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m =>
             m.InpatientID == inpatientID &&
             m.HospitalID == hospitalID &&
             m.ScheduleDate.Date >= startDate.Date &&
             m.ScheduleDate <= endDate.Date &&
             m.DeleteFlag != "*")
            .OrderBy(m => m.ScheduleDate).ThenBy(m => m.ScheduleTime)
            .ThenBy(m => m.GroupID).ThenBy(m => m.PatientOrderDetailID)
            .ToListAsync();
        }

        public async Task<List<PatientMedicineScheduleInfo>> GetPatientMedicineSchedule(string inpatientID, string hospitalID, DateTime startDate, TimeSpan startTime, DateTime endDate, TimeSpan endTime)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m =>
                (
                    (m.ScheduleDate == startDate.Date && m.ScheduleTime >= startTime) || m.ScheduleDate > startDate.Date)
                    && (m.ScheduleDate < endDate.Date || (m.ScheduleDate == endDate.Date && m.ScheduleTime <= endTime)
                )
                && m.InpatientID == inpatientID && m.HospitalID == hospitalID
                && m.DeleteFlag != "*")
                .OrderBy(m => m.ScheduleDate).ThenBy(m => m.ScheduleTime)
                .ThenBy(m => m.GroupID).ThenBy(m => m.HISOrderSort)
                .ToListAsync();
        }

        public async Task<List<PatientMedicineScheduleInfo>> GetPatientMedicineByShift(string inpatientID, string hospitalID, int stationID, DateTime shiftDate, int shiftID)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m =>
                 m.InpatientID == inpatientID && m.HospitalID == hospitalID && m.StationID == stationID && m.ShiftDate == shiftDate && m.StationShiftID == shiftID)
                .OrderBy(m => m.ScheduleDate).ThenBy(m => m.ScheduleTime)
                .ThenBy(m => m.GroupID).ThenBy(m => m.HISOrderSort)
                .ToListAsync();
        }

        public async Task<List<PatientMedicineScheduleInfo>> GetStationMedicineSchedule(int stationID, string hospitalID, DateTime startDate, DateTime endDate)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m =>
                 m.ScheduleDate >= startDate.Date && m.ScheduleDate <= endDate.Date &&
                 (m.StationID == stationID || stationID == 999999) && m.DeleteFlag != "*" &&
                 m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
        }

        // xml2019-09-19
        public async Task<List<PatientMedicineScheduleInfo>> GetInPatientStationMedicineSchedule(int stationID, string inPatientID, string hospitalID, DateTime startDate, TimeSpan startTime, DateTime endDate, TimeSpan endTime)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m =>
               ((m.ScheduleDate == startDate.Date && m.ScheduleTime >= startTime) || m.ScheduleDate > startDate.Date)
                && (m.ScheduleDate < endDate.Date || (m.ScheduleDate == startDate.Date && m.ScheduleTime <= endTime))
                && m.InpatientID == inPatientID && m.StationID == stationID && m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientMedicineScheduleInfo>> GetStationMedicineSchedule(int stationID, string hospitalID, DateTime startDate, TimeSpan startTime, DateTime endDate, TimeSpan endTime)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m =>
                ((m.ScheduleDate == startDate.Date && m.ScheduleTime >= startTime) || m.ScheduleDate > startDate.Date)
                 && (m.ScheduleDate < endDate.Date || (m.ScheduleDate == startDate.Date && m.ScheduleTime <= endTime))
                 && (m.StationID == stationID || stationID == 999999) && m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<PatientMedicineScheduleInfo> GetByID(string patientMedicineScheduleID)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => m.PatientMedicineScheduleID == patientMedicineScheduleID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<List<PatientMedicineScheduleInfo>> GetByGroupID(string groupID)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => m.GroupID == groupID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientMedicineScheduleInfo>> GetByGroupAsync(string[] groupIDs)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => groupIDs.Contains(m.GroupID) && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        ///
        /// </summary>
        /// <returns></returns>
        public async Task<List<PatientMedicineScheduleInfo>> GetUnSyncAsync()
        {
            //获取未同步、未删除、且已经执行的数据
            return await _medicalDbContext.PatientMedicineScheduleInfos
                .Where(m => m.DataPumpFlag != "*" && m.DeleteFlag != "*"
                && (m.PerformDate != null || m.CancelDate != null || m.StopDate != null)).ToListAsync();
        }

        public async Task<List<PatientMedicineScheduleInfo>> GetAsync(string mainID, DateTime startDate)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos
                .Where(m => m.PatientOrderMainID == mainID && m.DeleteFlag != "*"
                && ((m.ScheduleDate == startDate.Date && m.ScheduleTime > startDate.TimeOfDay) || m.ScheduleDate > startDate.Date)).ToListAsync();
        }

        public async Task<List<PatientMedicineScheduleInfo>> GetAsync(DateTime startDateTime, string inpatientID)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos
                .Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*"
                && ((m.ScheduleDate == startDateTime.Date && m.ScheduleTime > startDateTime.TimeOfDay) || m.ScheduleDate > startDateTime.Date)).ToListAsync();
        }

        public async Task<List<PatientMedicineScheduleInfo>> GetUnPerformByMainAsync(string[] mainIDs)
        {
            //var patientMedicineScheduleList = new List<PatientMedicineScheduleInfo>();
            //for (int i = 0; i < mainIDs.Length; i++)
            //{
            //    var tempList = await _medicalDbContext.PatientMedicineScheduleInfos
            //        .Where(m => m.PatientOrderMainID == mainIDs[i] && m.DeleteFlag != "*").ToListAsync();
            //    patientMedicineScheduleList = patientMedicineScheduleList.Union(tempList).ToList();
            //}

            mainIDs = mainIDs.Distinct().ToArray();

            var hashSet = new HashSet<string>(mainIDs);
            //Todo给药闭环未执行
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => hashSet.Contains(m.PatientOrderMainID) && UN_PERFORM_TYPE.Contains(m.Status)).ToListAsync();
        }

        /// <summary>
        /// 获取时间段内拆分的数据
        /// </summary>
        /// <param name="mainIDs"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public async Task<List<PatientMedicineScheduleInfo>> GetByMainAsync(string[] mainIDs, DateTime startTime, DateTime endTime)
        {
            var patientMedicineScheduleList = new List<PatientMedicineScheduleInfo>();
            for (int i = 0; i < mainIDs.Length; i++)
            {
                var tempList = await _medicalDbContext.PatientMedicineScheduleInfos
                .Where(m => m.PatientOrderMainID == mainIDs[i]
                //&& m.DeleteFlag != "*"  //无需过滤已删除内容，防止已删除内容重复展出
                ).ToListAsync();
                patientMedicineScheduleList = patientMedicineScheduleList.Union(tempList).ToList();
            }

            return patientMedicineScheduleList.Where(m =>
            ((m.ScheduleDate == startTime.Date && m.ScheduleTime >= startTime.TimeOfDay) || m.ScheduleDate > startTime.Date) &&
            ((m.ScheduleDate < endTime.Date) || (m.ScheduleDate == endTime.Date && m.ScheduleTime <= endTime.TimeOfDay))
            ).ToList();
        }

        public async Task<List<PatientOrdersTaskSplit>> GetMedicineByMainAsync(string[] mainIDs, DateTime startTime, DateTime endTime)
        {
            var startDate = startTime.Date;
            var endDate = endTime.Date;
            //无需过滤已删除内容，防止已删除内容重复展出
            var patientMedicineScheduleList = await _medicalDbContext.PatientMedicineScheduleInfos.AsNoTracking().Where(m => mainIDs.Contains(m.PatientOrderMainID)
                 && m.ScheduleDate >= startDate && m.ScheduleDate <= endDate)
               .Select(m => new PatientOrdersTaskSplit
               {
                   InPatientID = m.InpatientID,
                   OrderMainID = m.PatientOrderMainID,
                   OrderDetailID = m.PatientOrderDetailID,
                   GroupID = m.GroupID,
                   ScheduleDate = m.ScheduleDate,
                   ScheduleTime = m.ScheduleTime
               }).ToListAsync();

            return patientMedicineScheduleList.Where(m => m.ScheduleDate.Add(m.ScheduleTime) >= startTime
                && m.ScheduleDate.Add(m.ScheduleTime) <= endTime).ToList();
        }

        public async Task<List<PatientMedicineScheduleInfo>> GetByShiftDateAndShiftID(DateTime shiftDate, int stationShiftID)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos
                .Where(m => m.ShiftDate.Value == shiftDate && m.StationShiftID.Value == stationShiftID)
                .OrderBy(m => m.ScheduleTime).ThenBy(m => m.GroupID).ToListAsync();
        }

        public async Task<List<PatientMedicineScheduleInfo>> GetStationMedicineScheduleAndDelete(int stationID, string hospitalID, DateTime startDate, DateTime endDate)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m =>
                m.ScheduleDate >= startDate.Date && m.ScheduleDate <= endDate.Date &&
                m.StationID == stationID && m.HospitalID == hospitalID).ToListAsync();
        }

        public async Task<List<HandoverSchedule>> GetOnePatientSchedule(HandoverQueryView query)
        {
            //todo给药取已执行
            var datas = await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => m.InpatientID == query.InpatientID
                          && m.ScheduleDate >= query.StartDate && m.ScheduleDate <= query.EndDate
                          && m.StationID == query.StationID && PERFORM_END_TYPE.Contains(m.Status) && m.BringToShift == "1" && m.DeleteFlag != "*")
                            .Select(n => new HandoverSchedule
                            {
                                ScheduleDate = n.ScheduleDate,
                                ScheduleTime = n.ScheduleTime,
                                ActionType = "4",
                                PerformDate = n.PerformDate,
                                PerformTime = n.PerformTime,
                                Content = n.Content,
                            }).ToListAsync();

            datas = datas.Where(m => m.ScheduleDate.Add(m.ScheduleTime) >= query.StartDate.Add(query.StartTime)
                 && m.ScheduleDate.Add(m.ScheduleTime) < query.EndDate.Add(query.EndTime)).ToList();

            return datas;
        }

        public async Task<PatientMedicineScheduleInfo> GetDateByPatientOrderMainIDAsync(string patientOrderMainID)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => m.PatientOrderMainID == patientOrderMainID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<PatientMedicineScheduleInfo> GetDateByPatientOrderDetailIDAsync(string patientOrderDetailID)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => m.PatientOrderDetailID == patientOrderDetailID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<List<string>> GetMedicineRecordByCaseNumberAsync(string caseNumber)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => m.CaseNumber == caseNumber).Select(m => m.PatientOrderDetailID).ToListAsync();
        }

        /// <summary>
        /// 特殊需求，4张表join ，如果没有特殊需求，不要调用
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="shiftDate"></param>
        /// <param name="shiftID"></param>
        /// <param name="assessListIDs"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<List<MedicineTORecord>> GetMedicineRecordByemployeeID(int stationID, DateTime? shiftDate, int? shiftID
            , string[] medicineTypes, string employeeID)
        {
            var query = from a in _medicalDbContext.PatientMedicineScheduleInfos
                        join b in _medicalDbContext.Attendance on
                        new
                        {
                            a.InpatientID,
                            a.StationID,
                            a.ShiftDate,
                            a.StationShiftID,
                            NurseEmployeeID = employeeID,
                            AttendanceDate = Convert.ToDateTime(shiftDate)
                        }
                        equals new
                        {
                            b.InpatientID,
                            StationID = stationID,
                            ShiftDate = shiftDate,
                            StationShiftID = shiftID,
                            b.NurseEmployeeID,
                            b.AttendanceDate
                        }
                        join c in _medicalDbContext.PatientBasicDatas on
                        new { a.PatientID }
                        equals new
                        { c.PatientID }
                        join d in _medicalDbContext.InpatientDatas on
                        new { a.InpatientID } equals new { InpatientID = d.ID }
                        where medicineTypes.Contains(a.MedicineType) && a.DeleteFlag != "*"
                        && InHospitalStatus.INHOSPITALLIST.Contains(d.InHospitalStatus ?? -1) && a.PerformDate.HasValue && a.PerformTime.HasValue
                        select new MedicineTORecord
                        {
                            PatientMedicineScheduleID = a.PatientMedicineScheduleID,
                            PatientName = c.PatientName,
                            BedNumber = a.BedNumber,
                            InpatientID = a.InpatientID,
                            OrderType = a.OrderType,
                            MedicineType = a.MedicineType,
                            LocalCaseNumber = d.LocalCaseNumber,
                            OrderDescription = a.OrderDescription,
                            Frequency = a.Frequency,
                            OrderRule = a.OrderRule,
                            OrderContent = a.OrderContent,
                            PerformDate = a.PerformDate.Value.Add(a.PerformTime.Value),
                            BringToNursingRecords = a.BringToNursingRecords
                        };
            return await query.ToListAsync();
        }

        public async Task<List<PatientMedicineScheduleInfo>> GetMedicineRecordByIDs(List<string> patientMedicineScheduleIDList, string hospitalID)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => patientMedicineScheduleIDList.Contains(m.PatientMedicineScheduleID) && m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取未删除、未取消、未执行的信息，修改orderRule医嘱途径
        /// </summary>
        /// <param name="patientOrderMainID"></param>
        /// <returns></returns>
        public async Task<List<PatientMedicineScheduleInfo>> GetByPatientMedicineScheduleByID(string patientOrderMainID)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => m.PatientOrderMainID == patientOrderMainID
             && m.PerformDate == null && m.CancelDate == null && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<PatientMedicineScheduleInfo> GetPatientMedicineScheduleAsync(string inpatientID, int stationID, string hospitalID, string patientProblemID)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m =>
               m.InpatientID == inpatientID && m.StationID == stationID && m.HospitalID == hospitalID && m.PatientOrderDetailID == patientProblemID).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据orderDetailID获取数据（包括DeleteFlag=="*"）
        /// </summary>
        /// <param name="orderDetailID"></param>
        /// <returns></returns>
        public PatientMedicineScheduleInfo GetByOrderDetailID(string orderDetailID)
        {
            return _medicalDbContext.PatientMedicineScheduleInfos.Where(m => m.PatientOrderDetailID == orderDetailID).FirstOrDefault();
        }

        /// <summary>
        /// 根据orderDetailID获取数据（包括DeleteFlag=="*"）
        /// </summary>
        /// <param name="orderDetailID"></param>
        /// <returns></returns>
        public int GetByCaserNumberAndOrderNO(string orderID, string orderCode, string casenumber, DateTime performDate, TimeSpan performTime)
        {
            return _medicalDbContext.PatientMedicineScheduleInfos.Where(m => m.CaseNumber == casenumber && m.OrderCode == orderCode && m.PatientOrderMainID == orderID
           && m.PerformDate == performDate.Date && m.PerformTime == performTime).Count();
        }

        public async Task<int> GetByCaserNumberAndGroupID(string casenumber, string orderID, string orderCode, string groupID)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => m.CaseNumber == casenumber && m.PatientOrderMainID == orderID && m.OrderCode == orderCode
           && m.GroupID == groupID && m.DeleteFlag != "*").CountAsync();
        }

        /// <summary>
        /// 根据orderDetailID获取数据（包括DeleteFlag=="*"）
        /// </summary>
        /// <param name="orderDetailID"></param>
        /// <returns></returns>
        public async Task<PatientMedicineScheduleInfo> GetByOrderDetailIDAsync(string orderDetailID)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => m.PatientOrderDetailID == orderDetailID).FirstOrDefaultAsync();
        }

        public async Task<List<PatientMedicineScheduleInfo>> GetByCasenumberAndGroupIDAsync(string casenumber, string groupID)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => m.CaseNumber == casenumber && m.GroupID == groupID).AsNoTracking().ToListAsync();
        }

        public async Task<List<MedicineView>> GetNeedPerformSchedule(string inpatientID, DateTime shiftDate, int shiftID)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos
                 .Where(m => m.InpatientID == inpatientID
                 && m.ShiftDate.Value == shiftDate
                 && m.StationShiftID.Value == shiftID
                 && !m.StopDate.HasValue && m.DeleteFlag != "*" && !m.CancelDate.HasValue)
                 .Select(m => new MedicineView
                 {
                     GroupID = m.GroupID,
                     ScheduleDate = m.ScheduleDate,
                     ScheduleTime = m.ScheduleTime,
                     PerformDate = m.PerformDate,
                     PerformTime = m.PerformTime,
                     PerformEmployeeID = m.PerformEmployeeID,
                     BringToNursingRecords = m.BringToNursingRecords,
                     BringToShift = m.BringToShift,
                     Status = m.Status,
                     PerformComment = m.PerformComment,
                     PDAFlag = m.PDAFlag,
                     OrderCode = m.OrderCode,
                     OrderContent = m.OrderContent,
                     OrderDescription = m.OrderDescription,
                     Frequency = m.Frequency,
                     OrderDose = m.OrderDose,
                     Unit = m.Unit,
                     DelayPerformReason = m.DelayPerformReason,
                     NotPerformReason = m.NotPerformReason,
                     OrderStatus = m.OrderStatus,
                     StopDate = m.StopDate,
                     DelayPerformComment = m.DelayPerformComment,
                     NotPerformComment = m.NotPerformComment,
                     StopTime = m.StopTime
                 })
                 .OrderBy(m => m.ScheduleTime).ThenBy(m => m.GroupID).ToListAsync();
        }
        public async Task<List<PatientMedicineScheduleInfo>> GetMedicineSchedule(string inpatientID, int stationID)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => m.StationID == stationID && m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<List<PatientMedicineScheduleInfo>> GetMedicineScheduleUnStation(string inpatientID, int stationID)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => m.InpatientID == inpatientID
                     && !m.PerformDate.HasValue && m.StationID == stationID && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据医嘱码获取触发的皮试
        /// </summary>
        /// <param name="drugSkinTest"></param>
        /// <returns></returns>
        public async Task<List<PatientMedicineScheduleInfo>> GetTriggerSkinTestByOrderCode(string[] skinTestOrderCode)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => skinTestOrderCode.Contains(m.OrderCode)
            && m.DeleteFlag != "*" && m.DataPumpFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据patientOrderMainID获取最近一次液体医嘱滴速
        /// </summary>
        /// <param name="orderDetailID"></param>
        /// <returns></returns>
        public async Task<PatientPatrolRecordInfo> GetByCaserNumberAndPatientOrderMainID(string patientOrderMainID, string caseNumber)
        {
            return await (from a in _medicalDbContext.PatientMedicineScheduleInfos
                          join b in _medicalDbContext.PatrolRecordInfos on
                          a.GroupID equals b.GroupID
                          where a.PatientOrderMainID == patientOrderMainID &&
                          a.CaseNumber == caseNumber &&
                          a.DeleteFlag != "*" &&
                          b.RecordsCode == "InfusionStart" &&
                          b.Speed.HasValue
                          orderby b.PerformData, b.PerformTime
                          select new PatientPatrolRecordInfo()
                          {
                              Speed = b.Speed,
                              PatrolType = b.PatrolType
                          }).LastOrDefaultAsync();
        }

        public async Task<PatientMedicineScheduleInfo> GetRecentPerformDateTimeByOrderCodeAsync(string inpatientID, List<string> orderCodes)
        {
            return await (from m in _medicalDbContext.PatientMedicineScheduleInfos
                          where m.InpatientID == inpatientID && orderCodes.Contains(m.OrderCode) && m.DeleteFlag != "*"
                          orderby m.PerformDate, m.PerformTime
                          select new PatientMedicineScheduleInfo
                          {
                              PerformDate = m.PerformDate,
                              PerformTime = m.PerformTime
                          }).LastOrDefaultAsync();
        }
        /// <summary>
        /// 判断是否有特定orderRule的药嘱
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="stationID"></param>
        /// <param name="orderRuleList"></param>
        /// <returns></returns>
        public async Task<bool> GetMedicineScheduleByOrderRule(string inpatientID, int stationID, List<string> orderRuleList)
        {
            //todo给药正在执行
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => m.StationID == stationID && m.InpatientID == inpatientID && m.DeleteFlag != "*"
            && PERFORM_TYPE.Contains(m.Status) && m.Status < (int)OrderTasksType.EndPerformMedication && orderRuleList.Contains(m.OrderRule)).CountAsync() > 0;
        }
        /// <summary>
        /// 根据orderRule获取已执行的药嘱
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="orderRule">途径</param>
        /// <returns></returns>
        public async Task<List<MedicineView>> GetByOrderRule(string inpatientID, string orderRule)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => m.InpatientID == inpatientID && m.OrderRule.Trim() == orderRule &&
            m.PerformDate.HasValue && m.DeleteFlag != "*").Select(m => new MedicineView
            {
                StationID = m.StationID,
                PerformDate = m.PerformDate,
                PerformTime = m.PerformTime,
                PerformEmployeeID = m.PerformEmployeeID
            }).ToListAsync();
        }
        /// <summary>
        /// 获取某组药品的名称集合
        /// </summary>
        /// <param name="groupID"></param>
        /// <returns></returns>
        public async Task<List<string>> GetOrderContentByGroupID(string groupID)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => m.GroupID == groupID && m.DeleteFlag != "*" && string.IsNullOrEmpty(m.OrderCode)).OrderBy(m => m.HISOrderSort).Select(m => m.OrderContent).ToListAsync();
        }
        /// <summary>
        /// 根据给药日期和药品类别取数据
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="stationID">病区ID</param>
        /// <param name="datetime">给药日期</param>
        /// <param name="MedicineType">给药类别</param>
        /// <returns></returns>
        public async Task<List<PatientMedicineScheduleInfo>> GetMedicineByDateAndMedicineType(string inpatientID, int stationID, DateTime datetime, string MedicineType)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => m.StationID == stationID && m.InpatientID == inpatientID && m.ScheduleDate == datetime.Date && m.MedicineType == MedicineType && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据医院和日期查询数据(在院患者)
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<List<MedicineScheduleCountView>> GetMedicineByDate(string hospitalID, DateTime startDate, DateTime endDate, int stationID)
        {
            var datas = await (from a in _medicalDbContext.PatientMedicineScheduleInfos.AsNoTracking()
                               join b in _medicalDbContext.InpatientDatas.AsNoTracking() on
                               a.InpatientID equals b.ID
                               where InHospitalStatus.INHOSPITALLIST.Contains(b.InHospitalStatus ?? -1) && b.HospitalID == hospitalID &&
                               a.HospitalID == hospitalID && a.ScheduleDate >= startDate && a.ScheduleDate <= endDate && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                               && a.Status != (int)OrderTasksType.Cancel && a.OrderStatus != 4 && (stationID != 999999 ? a.StationID == stationID : a.StationID == a.StationID)
                               select new MedicineScheduleCountView
                               {
                                   InpatientID = a.InpatientID,
                                   StationID = a.StationID,
                                   Status = a.Status,
                                   OrderRule = a.OrderRule,
                                   BedNumber = a.BedNumber,
                                   StationShiftID = a.StationShiftID,
                                   ScheduleDate = a.ScheduleDate,
                                   ShiftDate = a.ShiftDate,
                                   ScanFlag = a.ScanFlag,
                                   PDAFlag = a.PDAFlag,
                                   GroupID = a.GroupID,
                                   CancelFlag = a.CancelDate.HasValue
                               }
                           ).ToListAsync();
            return datas.GroupBy(m => m.GroupID).Select(m => m.FirstOrDefault()).ToList();
        }

        public async Task<List<string>> GetInpatientIDByOrderCodes(List<string> inpatientIDs, List<string> orderCodes, string frequency)
        {
            var datas = await _medicalDbContext.PatientMedicineScheduleInfos
                        .Where(m => inpatientIDs.Contains(m.InpatientID) && orderCodes.Contains(m.OrderCode)
                                && m.ScheduleDate == DateTime.Now.Date && m.DeleteFlag != "*" && m.CancelDate == null)
                        .ToListAsync();
            if (!string.IsNullOrWhiteSpace(frequency))
            {
                datas = datas.Where(m => m.Frequency == frequency).ToList();
            }
            return datas.Select(m => m.InpatientID).ToList();
        }

        public async Task<PatientMedicineScheduleInfo> GetScheduleByOrderCodeAndDateTimeAsync(string inpatientID, List<string> orderCodes, DateTime performDate, TimeSpan performTime, List<string> orderRule)
        {
            var query = _medicalDbContext.PatientMedicineScheduleInfos.Where(m => m.InpatientID == inpatientID && orderCodes.Contains(m.OrderCode)
                && m.PerformDate.HasValue && (m.PerformDate.Value < performDate || (m.PerformDate.Value == performDate && m.PerformTime <= performTime))
                && orderRule.Contains(m.OrderRule))
                .OrderByDescending(m => m.PerformDate).ThenByDescending(m => m.PerformTime);

            return await query.FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据主键获取给药的部分信息（OrderType Frequency）
        /// </summary>
        /// <param name="medicineScheduleMainID"></param>
        /// <returns></returns>
        public async Task<PatientMedicineScheduleInfo> GetMedicineScheduleViewByIDAsync(string medicineScheduleMainID)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => m.PatientMedicineScheduleID == medicineScheduleMainID
              && m.CancelDate == null && m.DeleteFlag != "*").Select
              (m => new PatientMedicineScheduleInfo
              {
                  OrderType = m.OrderType,
                  Frequency = m.Frequency,
                  PatientMedicineScheduleID = m.PatientMedicineScheduleID
              }).FirstOrDefaultAsync();
        }
        public async Task<List<string>> GetExistsGroupIDs(string[] groupIDs)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.AsNoTracking().Where(m => groupIDs.Contains(m.GroupID) && m.DeleteFlag != "*").Select(m => m.GroupID).ToListAsync();
        }
        /// <summary>
        /// 获取患者的给药数据
        /// </summary>
        /// <param name="patientOrderMainIds"></param>
        /// <returns></returns>
        public async Task<List<PatientMedicineScheduleInfo>> GetMedicineScheduleVAsync(List<string> patientOrderMainIds)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => patientOrderMainIds.Contains(m.PatientOrderMainID)).ToListAsync();
        }

        public async Task<List<PatientMedicineScheduleInfo>> GetByMainIDsAsync(List<string> mainIDs, DateTime startDate, DateTime endDate)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => mainIDs.Contains(m.PatientOrderMainID)
                 && m.ScheduleDate >= startDate && m.ScheduleDate <= endDate && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientMedicineView>> GetPatientMedicineScheduleByStatus(int status, int stationID, DateTime startDate, DateTime endDate, string hospitalID)
        {
            return await (from a in _medicalDbContext.PatientMedicineScheduleInfos.AsNoTracking()
                          join b in _medicalDbContext.InpatientDatas.AsNoTracking() on a.InpatientID equals b.ID
                          join c in _medicalDbContext.PatientBasicDatas.AsNoTracking() on b.PatientID equals c.PatientID
                          where a.DeleteFlag != "*" && b.DeleteFlag != "*" && c.DeleteFlag != "*"
                          && a.HospitalID == hospitalID && b.HospitalID == hospitalID && c.HospitalID == hospitalID
                          && b.StationID == stationID && a.ScheduleDate >= startDate && a.ScheduleDate <= endDate && a.Status == status
                          && InHospitalStatus.INHOSPITALLIST.Contains(b.InHospitalStatus ?? -1)
                          select new PatientMedicineView
                          {
                              PatientMedicineScheduleID = a.PatientMedicineScheduleID,
                              GroupID = a.GroupID,
                              InpatientID = b.ID,
                              CaseNumber = b.CaseNumber,
                              ChartNo = b.ChartNo,
                              BedNumber = b.BedNumber,
                              PatientName = c.PatientName,
                              OrderContent = a.OrderContent,
                              OrderDose = a.OrderDose,
                              DrugSpec = a.DrugSpec,
                              ScheduleDate = a.ScheduleDate,
                              ScheduleTime = a.ScheduleTime,
                              OrderRule = a.OrderRule,
                              Status = a.Status,
                          }
               ).ToListAsync();
        }
    }
}