﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    /// <summary>
    /// 科室基本信息
    /// </summary>
    [Serializable]
    [Table("Department")]
    public class DepartmentInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///科别名称
        ///</summary>
        public string Department { get; set; }
        /// <summary>
        ///科别代号
        ///</summary>
        public string DepartmentCode { get; set; }
        /// <summary>
        ///科别类型(内/外/专科)
        ///</summary>
        public string DepartmentPattern { get; set; }
    }
}