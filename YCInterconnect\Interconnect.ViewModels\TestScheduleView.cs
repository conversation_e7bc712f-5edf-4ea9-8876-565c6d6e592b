﻿using System;
using System.Collections.Generic;
using System.Text;


namespace Interconnect.ViewModels
{
    public class TestScheduleView 
    {
        /// <summary>
        ///住院号
        ///</summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public string Gender { get; set; }
        /// <summary>
        /// 年龄
        /// </summary>
        public string Age { get; set; }
        /// <summary>
        /// -病人来源类型： 1-门诊急诊  3-住院  4-体检
        /// </summary>
        public string Source { get; set; }
        /// <summary>
        ///检验申请号
        ///</summary>
        public string TestNO { get; set; }
        /// <summary>
        ///检验日期
        ///</summary>
        public DateTime TestDate { get; set; }
        /// <summary>
        ///检验项目
        ///</summary>
        public string TestItem { get; set; }
        /// <summary>
        ///检验项目码
        ///</summary>
        public string TestItemCode { get; set; }
        /// <summary>
        ///检验值
        ///</summary>
        public string TestValue { get; set; }
        /// <summary>
        ///单位
        ///</summary>
        public string Unit { get; set; }
        /// <summary>
        ///正异常范围
        ///</summary>
        public string NormalRange { get; set; }
        /// <summary>
        /// NormalAbnormal 结果标志：L-低 H-高 M-正常 N-阴性 Q-弱阳性 P-阳性
        /// </summary>
        public string NormalAbnormal { get; set; }
        /// <summary>
        ///说明
        ///</summary>
        public string Description { get; set; }
    }
}
