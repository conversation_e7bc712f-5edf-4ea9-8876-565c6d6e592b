﻿using Interconnect.Models;
using Interconnect.Services.Interface;
using Medical.Common;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using NLog;
using System;
using System.Threading.Tasks;

namespace Interconnect.API.Controllers
{
    /// <summary>
    /// 对外部开发的各种接口
    /// </summary>
    [Produces("application/json")]
    [Route("api/OpenExternal")]
    [EnableCors("any")]
    public class OpenExternalController : ControllerBase
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IOpenExternalService _openExternalService;
        private readonly IOptions<SystemConfig> _options;

        /// <summary>
        /// 构造器
        /// </summary>
        public OpenExternalController(IOpenExternalService openExternalService
            , IOptions<SystemConfig> options)
        {
            _openExternalService = openExternalService;
            _options = options;
        }

        /// <summary>
        /// 根据条件获取派班数据
        /// </summary>
        /// <param name="stationCode">必填，病区码</param>
        /// <param name="nurseEmployeeID">选填，护士工号</param>
        /// <param name="queryDateTime">选填,查询的日期时间</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetNowAttendanceByParam")]
        public async Task<IActionResult> GetNowAttendanceByParam(string stationCode, string nurseEmployeeID, DateTime? queryDateTime)
        {
            var result = new ResponseResult();

            var hospitalID = _options.Value.HospitalID;
            result.Data = await _openExternalService.GetNowAttendanceByParamAsync(hospitalID, stationCode, nurseEmployeeID, queryDateTime);
            result.Sucess();
            return result.ToJson();
        }
    }
}