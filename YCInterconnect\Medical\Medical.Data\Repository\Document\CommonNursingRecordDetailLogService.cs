﻿
using Arch.EntityFrameworkCore.UnitOfWork;
using Medical.Common;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Services.Document
{
    /// <summary>
    /// DetailLog写入公共类
    /// </summary>
    /// <see cref="https://docs.qq.com/doc/DTVF4UGZsY0RhcU5I"/>
    public class CommonNursingRecordDetailLogService : ICommonNursingRecordDetailLogService
    {
        private readonly IEMRSourceListRepository _eMRSourceListRepository;
        private readonly IUnitOfWork _unitOfWork;
        public CommonNursingRecordDetailLogService(
                IEMRSourceListRepository eMRSourceListRepository,
                IUnitOfWork unitOfWork)
        {
            _eMRSourceListRepository = eMRSourceListRepository;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// 主记录结构业务写DetailLog
        /// </summary>
        /// <typeparam name="T">专项主记录数据</typeparam>
        /// <param name="record">record</param>
        /// <param name="bringToNRFlag">带入护理记录注记</param>
        /// <param name="mainTableName">主记录表名</param>
        /// <param name="performDateTime">评估时间</param>
        /// <param name="eventType">主表异动类型，1新增、2修改、3删除</param>
        /// <param name="conditionType">配置明细内容的所属类别比如TubeList，IntakeOutputSetting</param>
        /// <param name="conditionValue">具体的类别</param>
        /// <returns></returns>
        public async Task AddDetailLog<T>(T record, bool? bringToNRFlag, string mainTableName, DateTime performDateTime, int eventType
            , bool supplemnentFlag = false, string conditionType = null, string conditionValue = null)
        {
            // TODO：需要本方法改成此SaveView并调整所有引用
            var saveView = new NursingRecordDetailLogSaveViewByMain<T>
            {
                Main = record,
                BringToNR = bringToNRFlag ?? false,
                MainTableName = mainTableName,
                AssessDate = performDateTime,
                SupplemnentFlag = supplemnentFlag,
                ConditionType = conditionType,
                ConditionValue = conditionValue,
                EventType = eventType,
            };
            if (saveView.SupplemnentFlag)
            {
                return;
            }
            #region --数据声明
            var list = new List<PatientNursingRecordDetailLogInfo>();
            var baseDetailLog = new PatientNursingRecordDetailLogInfo();
            #endregion

            #region --数据准备
            var emrSourceListInfos = await _eMRSourceListRepository.GetAllAsync<EMRSourceListInfo>();
            var (inpatient, careMainID) = GetCommonInfo(record, saveView.MainTableName);
            var addDateTime = DateTime.Now;
            var interventionID = ReflexUtil.GetProperty(record, "InterventionID");
            #endregion
            // 开始生成DetailLog前，先把共性字段赋值
            baseDetailLog = baseDetailLog.WithSaveView(saveView)
                .WithInpatientInfo(inpatient);
            #region --处理主表日志
            //取Main中字段值
            var matchedByMainTableName = emrSourceListInfos.Where(m => m.SourceType == saveView.MainTableName)
                .DistinctBy(m => new { m.SourceType, m.SourceTypeColumn, m.SourceID, m.DataType }).ToList();
            list.AddRange(GetLogsByMain(saveView.Main, baseDetailLog, careMainID, addDateTime, matchedByMainTableName));
            #endregion

            if (list.Count > 0)
            {
                await _unitOfWork.GetRepository<PatientNursingRecordDetailLogInfo>().InsertAsync(list);
            }
        }

        /// <summary>
        /// 【主-副】结构业务写DetialLog
        /// </summary>
        /// <typeparam name="T">业务主表类型</typeparam>
        /// <param name="saveView">保存参数</param>
        /// <returns></returns>
        public async Task AddDetailLog<T>(NursingRecordDetailLogSaveViewByMain<T> saveView)
        {
            if (saveView.SupplemnentFlag || (saveView.DetailsView.IsDataUnChanged() && saveView.Main == null))
            {
                return;
            }

            saveView.DetailsView.InitWhichIsNull();
            #region --数据准备
            var addLogs = new List<PatientNursingRecordDetailLogInfo>();
            var baseDetailLog = new PatientNursingRecordDetailLogInfo();
            var emrSourceListInfos = await _eMRSourceListRepository.GetAllAsync<EMRSourceListInfo>();
            var (inpatient, careMainID) = GetCommonInfo<T>(saveView.Main, saveView.MainTableName);
            var addDateTime = DateTime.Now;
            #endregion
            // 开始生成DetailLog前，先把共性字段赋值
            baseDetailLog = baseDetailLog.WithSaveView(saveView)
                .WithInpatientInfo(inpatient);

            #region --处理主表日志
            //取Main中字段值
            var matchedByMainTableName = emrSourceListInfos.Where(m => m.SourceType == saveView.MainTableName)
                .DistinctBy(m => new { m.SourceType, m.SourceTypeColumn, m.SourceID, m.DataType }).ToList();
            addLogs.AddRange(GetLogsByMain(saveView.Main, baseDetailLog, careMainID, addDateTime, matchedByMainTableName));
            #endregion

            #region --处理明细日志
            var compareListMapEventType = new List<(List<Detail>, int)>
            {
                (saveView.DetailsView.AddList, 1),
                (saveView.DetailsView.ModifyList, 2),
                (saveView.DetailsView.DeleteList, 3),
            };
            foreach (var (details, eventType) in compareListMapEventType)
            {
                // 明细触发写Log，调整eventType、来源业务表名
                baseDetailLog.EventType = eventType;
                baseDetailLog.SourceType = saveView.DetailTableName;
                var matchedByDetailTableName = emrSourceListInfos.Where(m => m.SourceType == saveView.DetailTableName).ToList();
                addLogs.AddRange(GetLogsByDetails(baseDetailLog, details, careMainID, matchedByDetailTableName, addDateTime));
            }
            #endregion

            if (addLogs.Count > 0)
            {
                await _unitOfWork.GetRepository<PatientNursingRecordDetailLogInfo>().InsertAsync(addLogs);
            }
        }

        private List<PatientNursingRecordDetailLogInfo> GetLogsByMain<T>(T main, PatientNursingRecordDetailLogInfo baseDetailLog, string careMainID, DateTime addDateTime, List<EMRSourceListInfo> matachedByMainTableName)
        {
            var addLogs = new List<PatientNursingRecordDetailLogInfo>();
            foreach (var sourceListInfo in matachedByMainTableName)
            {
                var value = ReflexUtil.GetProperty(main, sourceListInfo.SourceTypeColumn);
                //sourceID不为空，判断数据相同才写入
                if (!string.IsNullOrEmpty(sourceListInfo.SourceID) && value != sourceListInfo.SourceID)
                {
                    continue;
                }

                var changingProps = new NursingRecordDetailLogChangingProp
                {
                    AddDateTime = addDateTime,
                    SourceTypeColumn = sourceListInfo.SourceTypeColumn,
                    SourceID1 = careMainID,
                    DataType = sourceListInfo.DataType,
                    DataID = sourceListInfo.SourceID ?? "",
                    Value = value
                };
                var log = baseDetailLog.BuildWithChangingProps(changingProps, baseDetailLog.EventType);
                if (log == null)
                {
                    continue;
                }
                addLogs.Add(log);
            }
            return addLogs;
        }

        /// <summary>
        /// 根据反比的明细数据获取Log
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="details"></param>
        /// <param name="careMainID"></param>
        /// <param name="matchedByDetailTableName"></param>
        /// <param name="addDateTime"></param>
        /// <returns></returns>
        private IEnumerable<PatientNursingRecordDetailLogInfo> GetLogsByDetails(PatientNursingRecordDetailLogInfo baseDetailLog, List<Detail> details, string careMainID, List<EMRSourceListInfo> matchedByDetailTableName, DateTime addDateTime)
        {
            foreach (var detail in details)
            {
                var sourceListInfo = matchedByDetailTableName.Find(m => m.SourceID == detail.AssessListID.ToString());
                var noExtractSetting = sourceListInfo == null;
                if (noExtractSetting)
                {
                    continue;
                }
                //明细表数据默认带入护理记录
                var changingProps = new NursingRecordDetailLogChangingProp
                {
                    AddDateTime = addDateTime,
                    SourceTypeColumn = "",
                    SourceID1 = detail.ID,
                    DataID = detail.AssessListID.ToString(),
                    DataType = sourceListInfo.DataType,
                    SourceMainID = careMainID,
                    Value = detail.AssessValue
                };
                var log = baseDetailLog.BuildWithChangingProps(changingProps, baseDetailLog.EventType);
                if (log == null)
                {
                    continue;
                }
                yield return log;
            }
        }
        /// <summary>
        /// 获取公共参数
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="main">主记录</param>
        /// <param name="mainTableName">主表名</param>
        /// <returns></returns>
        private (InpatientDataInfo, string) GetCommonInfo<T>(T main, string mainTableName)
        {
            var inpatientID = ReflexUtil.GetProperty(main, "InpatientID");
            var patientID = ReflexUtil.GetProperty(main, "PatientID");
            var deptIDStr = ReflexUtil.GetProperty(main, "DepartmentListID");
            deptIDStr ??= ReflexUtil.GetProperty(main, "DepartmentID");
            var stationIDStr = ReflexUtil.GetProperty(main, "StationID");
            var bedIDStr = ReflexUtil.GetProperty(main, "BedID");
            var bedNumber = ReflexUtil.GetProperty(main, "BedNumber");
            var userID = ReflexUtil.GetProperty(main, "ModifyPersonID");

            var careMainID = ReflexUtil.GetPrimaryKeyValue(main);
            careMainID ??= ReflexUtil.GetProperty(main, mainTableName + "ID");
            careMainID ??= ReflexUtil.GetProperty(main, "ID");

            int.TryParse(deptIDStr, out int deptID);
            int.TryParse(stationIDStr, out int stationID);
            int.TryParse(bedIDStr, out int bedID);

            var inpatientData = new InpatientDataInfo
            {
                ID = inpatientID,
                PatientID = patientID,
                StationID = stationID,
                DepartmentListID = deptID,
                BedID = bedID,
                BedNumber = bedNumber,
                ModifyPersonID = userID,
                AttendingPhysicianID = careMainID
            };
            return (inpatientData, careMainID);
        }
    }
    internal static class PatientNursingRecordDetailLogInfoExtend
    {
        /// <summary>
        /// 根据病人信息填充部分字段
        /// </summary>
        /// <param name="detailLog"></param>
        /// <param name="inpatientInfo"></param>
        /// <returns></returns>
        internal static PatientNursingRecordDetailLogInfo WithInpatientInfo(this PatientNursingRecordDetailLogInfo detailLog, InpatientDataInfo inpatientInfo)
        {
            detailLog.InpatientID = inpatientInfo.ID;
            detailLog.PatientID = inpatientInfo.PatientID;
            detailLog.DepartmentListID = inpatientInfo.DepartmentListID;
            detailLog.StationID = inpatientInfo.StationID;
            detailLog.BedID = inpatientInfo.BedID;
            detailLog.BedNumber = inpatientInfo.BedNumber;
            detailLog.PerformPersonID = inpatientInfo.ModifyPersonID;
            detailLog.AddPersonID = inpatientInfo.ModifyPersonID;
            return detailLog;
        }
        /// <summary>
        /// 根据公共入参填充部分字段
        /// </summary>
        /// <param name="detailLog"></param>
        /// <param name="saveView"></param>
        /// <returns></returns>
        internal static PatientNursingRecordDetailLogInfo WithSaveView<T>(this PatientNursingRecordDetailLogInfo detailLog, NursingRecordDetailLogSaveViewByMain<T> saveView)
        {
            detailLog.PerformDateTime = saveView.AssessDate;
            detailLog.SourceType = saveView.MainTableName;
            detailLog.EventType = saveView.EventType;
            detailLog.NursingRecordFlag = saveView.BringToNR;
            detailLog.ConditionType = saveView.ConditionType;
            detailLog.ConditionValue = saveView.ConditionValue ?? "";
            detailLog.DataPumpFlag = "";
            return detailLog;
        }
        /// <summary>
        /// 填充剩余字段（常变属性）
        /// </summary>
        /// <param name="detailLog"></param>
        /// <returns></returns>
        internal static PatientNursingRecordDetailLogInfo BuildWithChangingProps(this PatientNursingRecordDetailLogInfo detailLog, NursingRecordDetailLogChangingProp changingProps, int eventType)
        {
            if (string.IsNullOrEmpty(changingProps.DataID) && string.IsNullOrWhiteSpace(changingProps.Value) && eventType == 1)
            {
                return null;
            }
            var cloneDetail = detailLog.CloneObj();
            cloneDetail.DataType = changingProps.DataType;
            cloneDetail.DataID = changingProps.DataID;
            cloneDetail.DataValue = changingProps.Value;
            cloneDetail.SourceTypeColumn = changingProps.SourceTypeColumn;
            cloneDetail.SourceID1 = changingProps.SourceID1;
            cloneDetail.SourceID2 = changingProps.SourceID2;
            cloneDetail.AddDateTime = changingProps.AddDateTime;

            cloneDetail.SourceMainID = changingProps.SourceMainID ?? changingProps.SourceID1;
            return cloneDetail;
        }
    }
}

