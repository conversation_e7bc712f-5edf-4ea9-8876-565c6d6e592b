﻿using Interconnect.Services.Interface;
using Medical.Data.Interface;
using Medical.ViewModels.View;
using MedicalExternalCommon.Service;
using NLog;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Interconnect.Services
{
    public class OpenExternalService : IOpenExternalService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IStationListRepository  _stationListRepository;     
        private readonly IAttendanceRepository _attendanceRepository;
        private readonly IInpatientDataRepository _inpatientDataRepository;
        private readonly IEmployeeJobRepository _employeeJobRepository;
        private readonly StationaShiftCommonService _stationaShiftCommonService;

        public OpenExternalService(IStationListRepository  stationListRepository           
            , IAttendanceRepository attendanceRepository
            , IInpatientDataRepository inpatientDataRepository
            , IEmployeeJobRepository employeeJobRepository
            , StationaShiftCommonService stationaShiftCommonService
            )
        {
            _stationListRepository = stationListRepository;       
            _attendanceRepository = attendanceRepository;
            _inpatientDataRepository = inpatientDataRepository;
            _employeeJobRepository = employeeJobRepository;
            _stationaShiftCommonService = stationaShiftCommonService;
        }

        /// <summary>
        /// 根据参数获取当前病区派班数据（病区码），（病区码+护士工号）
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="stationCode"></param>
        /// <param name="nurseEmployeeID"></param>
        /// <returns></returns>
        public async Task<List<NowAttendanceView>> GetNowAttendanceByParamAsync(string hospitalID, string stationCode, string nurseEmployeeID, DateTime? shiftDate)
        {
            #region 验证数据合理性

            if (string.IsNullOrEmpty(hospitalID) || string.IsNullOrEmpty(stationCode))
            {
                _logger.Error("调用GetNowAttendanceByParamAsync时错误，传入hospitalID或stationCode为空");
                return null;
            }

            var stationInfo =   _stationListRepository.GetStationInfo(stationCode);
            if (stationInfo == null)
            {
                _logger.Error("调用GetNowAttendanceByParamAsync时错误，StationList未匹配到数据StationCode=" + stationCode);
                return null;
            }

            #endregion 验证数据合理性

            #region 声明

            var nowTime = DateTime.Now.TimeOfDay;
            var nowShiftDate = DateTime.Now.Date;
            if (shiftDate != null)
            {
                nowTime = shiftDate.Value.TimeOfDay;
                nowShiftDate = shiftDate.Value.Date;
            }
            var stationID = stationInfo.ID;
            List<NowAttendanceView> nowAttendanceInfos = new List<NowAttendanceView>();

            #endregion 声明

            #region 数据加载

            //获取班别
            var stationShift = await _stationaShiftCommonService.GetShiftAsync(stationID, nowTime);
            if (stationShift == null)
            {
                _logger.Error("调用GetNowAttendanceByParamAsync时错误，StationShift未匹配到数据StationCode=" + stationCode);
                return null;
            }
            //获取班别日期           
            var stationShiftDate =   _stationaShiftCommonService.GetShiftDate(stationShift, nowShiftDate);
            if (stationShiftDate == null)
            {
                _logger.Error("调用GetNowAttendanceByParamAsync时错误,未获取到班别日期");
                return null;
            }

            var attendanceList = await _attendanceRepository.GetNowAttendanceAsync(stationShiftDate, stationID, stationShift.ID, nurseEmployeeID);
            if (attendanceList.Count == 0)
            {
                _logger.Error("调用GetNowAttendanceByParamAsync时错误，Attendance未匹配到数据StationCode=" + stationCode);
                return null;
            }

            #endregion 数据加载

            foreach (var attendanceItem in attendanceList)
            {
                var inPatient = await _inpatientDataRepository.GetInPatientData(attendanceItem.InpatientID);
                if (inPatient == null)
                {
                    continue;
                }
                var aliasNames = "";
                var employeeJobInfos = await _employeeJobRepository.GetAliasNameByNurseID(nowShiftDate, stationID, stationShift.Shift);
                if (employeeJobInfos.Count > 0)
                {
                    foreach (var employeeJobInfo in employeeJobInfos)
                    {
                        if (!string.IsNullOrEmpty(employeeJobInfo.AliasName))
                        {
                            aliasNames = aliasNames + "";
                        }
                    }
                }
                NowAttendanceView nowAttendanceInfo = new NowAttendanceView();
                nowAttendanceInfo.AttendanceDate = attendanceItem.AttendanceDate;
                nowAttendanceInfo.StationCode = stationInfo.StationCode;
                nowAttendanceInfo.NurseEmployeeID = attendanceItem.NurseEmployeeID;
                nowAttendanceInfo.StationShift = stationShift.Shift;
                nowAttendanceInfo.CaseNumber = inPatient.CaseNumber;
                nowAttendanceInfo.CarePriority = attendanceItem.CarePriority;
                nowAttendanceInfo.AliasName = aliasNames;
                nowAttendanceInfos.Add(nowAttendanceInfo);
            }
            return nowAttendanceInfos;
        }
    }
}
