﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using NLog;
using Interconnect.Data.Context;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Services.Interface;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using Arch.EntityFrameworkCore.UnitOfWork;

namespace Interconnect.Services
{
    public class NurseBedDictService : INurseBedDictService
    {
        //Mdeical
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly INurseBedSetRepository _nurseBedSetRepository;
        private readonly IStationListRepository _IStationListRepository; 
        private readonly IBedListRepository _bedListRepository;

        //Interconnect
        private readonly IUnitOfWork<DataOutConnection> _unitOfWorkOut;
        private readonly IOptions<SystemConfig> _config;
        private readonly INurseBedDictRepository _nurseBedDictRepository;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ILogInfoServices _ILogInfoServices;

        public NurseBedDictService(IUnitOfWork<MedicalDbContext> unitOfWork
            , INurseBedSetRepository nurseBedSetRepository
            , IStationListRepository stationListRepository
            , IUnitOfWork<DataOutConnection> unitOfWorkOut
            , INurseBedDictRepository nurseBedDictRepository
            , ILogInfoServices logInfoServices
            , IBedListRepository bedListRepository
            , IOptions<SystemConfig> options
            )
        {
            _unitOfWork = unitOfWork;
            _nurseBedSetRepository = nurseBedSetRepository;
            _IStationListRepository = stationListRepository;
            _unitOfWorkOut = unitOfWorkOut;
            _nurseBedDictRepository = nurseBedDictRepository;
            _ILogInfoServices = logInfoServices;
            _bedListRepository = bedListRepository;
            _config = options;
        }


        /// <summary>
        /// 如果发现有没有同步的数据，则一直获取，直至完全同步
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SynchronizationMain()
        {
            _logger.Info("开始获取未同步权限数据");
            var OriginalList = await _nurseBedDictRepository.GetAsync();
            var nurseBedSetList = await _nurseBedSetRepository.GetAllAsync<NurseBedSetInfo>();
            var stationList = await _IStationListRepository.GetAllAsync<StationListInfo>();
            stationList = stationList.Where(m => m.HospitalID == _config.Value.HospitalID).ToList();
            var bedList = await _bedListRepository.GetAllAsync<BedListInfo>();
            while (OriginalList.Count > 0) //如果没有同步完成，则继续同步
            {
                if (!SynchronizationDetail(OriginalList, nurseBedSetList, bedList, stationList))
                {
                    return false;
                }
                OriginalList = await _nurseBedDictRepository.GetAsync();
            }            
            return true;
        }
        private  bool SynchronizationDetail(List<NurseBedDictInfo> OriginalList
            , List<NurseBedSetInfo> nurseBedSetList,List<BedListInfo> bedLists
            ,List<StationListInfo> stationList)
        {           

            var Insertlist = new List<NurseBedSetInfo>();
            NurseBedSetInfo t = null;
            string Tablename = "EmployeeJobDict";
            List<LogInfo> LogList = new List<LogInfo>();
            LogInfo TempLog = null;
            TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, " 开始进行数据同步，数据条数：" + OriginalList.Count);
            LogList.Add(TempLog);
            int Failcount = 0;
            #region "数据同步"
            foreach (var item in OriginalList)
            {
                item.Counts = item.Counts ?? 0;
                item.Counts = item.Counts + 1;
                try
                {

                    ////获取病区信息做转换                    
                    var TempstationList = stationList.Where(m => m.StationCode == item.WardCode).ToList();
                    if (TempstationList.Count < 1)
                    {
                        TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, "病区信息[" + item.WardCode + "]未找到对应关系！");
                        LogList.Add(TempLog);
                        Failcount++;
                        continue;
                    }
                    //获取床位bedID
                    var tempBedList = bedLists.Where(m => TempstationList[0].ID == m.StationID && m.BedNumber == item.Bednumber).ToList();
                    if (tempBedList.Count < 1)
                    {
                        TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, "病区[" + TempstationList[0].StationCode + "]的床号["+item.Bednumber+"]未找到对应关系！");
                        LogList.Add(TempLog);
                        Failcount++;
                        continue;
                    }
                    // 判断数据是否已经存在(这个判断条件需要修改)
                    var TempNurseBedSetList = nurseBedSetList.Where(m => m.DepartmentJobID.ToString() == item.StationJobID && m.BedNumber==item.Bednumber).ToList();
                   

                    //获取插入的数据,避免数据重复插入
                    var TempInsertlist = Insertlist.Where(m => m.DepartmentJobID.ToString() == item.StationJobID).ToList();

                    //如果不存在进行新增
                    if (TempNurseBedSetList.Count < 1 && TempInsertlist.Count < 1)
                    {
                        t = new NurseBedSetInfo
                        {
                            DepartmentJobID = Convert.ToInt32(item.StationJobID)
                            ,
                            BedNumber = item.Bednumber
                            ,
                            BedID= tempBedList[0].ID                            
                            ,
                            AddEmployeeID = item.ModifyPersonID
                            ,
                            AddDate = item.ModifyDate                             
                            ,
                            DeleteFlag = ""
                        };                        
                        item.DataPumpFlag = "*";
                        item.DataPumpDate = DateTime.Now;
                        Insertlist.Add(t);
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error(Tablename, "岗位ID[" + item.StationJobID + "] BedNumber:[" + item.Bednumber+ "]"+ ex.ToString());
                    return false;
                }
            }
            #endregion

            #region "数据更新"
            if (OriginalList.Count >= 1)
            {
                try
                {
                    _unitOfWork.GetRepository<NurseBedSetInfo>().Insert(Insertlist);
                    _unitOfWork.SaveChanges();
                   // _unitOfWorkOut.GetRepository<NurseBedDictInfo>().Update(OriginalList);
                   // _unitOfWorkOut.SaveChanges();
                }
                catch (Exception ex)
                {
                    _logger.Error(Tablename + "||保存失败||" + ex.ToString());
                    return false;
                }
            }

            TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, " 同步结束 成功：" + OriginalList.Count.ToString() + "条！");
            LogList.Add(TempLog);
            int ItemNo = 0;
            string Guid = "";
            Guid = System.Guid.NewGuid().ToString("N");
            foreach (var item in LogList)
            {
                item.Guid = Guid;
                item.ItemNo = ItemNo;
                ItemNo++;
            }
            try
            {
                _unitOfWorkOut.GetRepository<LogInfo>().Insert(LogList);
                _unitOfWorkOut.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error(Tablename + "同步成功，但写同步日志失败||" + ex.ToString());
            }
            _logger.Info(Tablename + "  同步完成!");
            return true;
            #endregion
        }

        /// <summary>
        /// 数据对比，删除对方不存在的岗位数据
        /// </summary>
        /// <returns></returns>
        public async Task<bool> DataDelete()
        {
            var OriginalList = await _nurseBedDictRepository.GetAsync();
            var nurseBedSetList = await _nurseBedSetRepository.GetAllAsync<NurseBedSetInfo>();
            var stationList = await _IStationListRepository.GetAllAsync<StationListInfo>();


            //对比删除
            foreach (var item in nurseBedSetList)
            {
              
                var tempInterconnectList = OriginalList.Where(m => m.StationJobID == item.BedNumber).ToList();
                if (tempInterconnectList.Count < 0)
                {
                    item.DeleteFlag = "*";
                }
            }
            try
            {
                _unitOfWork.GetRepository<NurseBedSetInfo>().Update(nurseBedSetList);
                _unitOfWork.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error("删除employeeDepartmentSwitch信息失败||" + ex.ToString());
                return false;
            }
            _logger.Info("删除employeeDepartmentSwitch信息成功");
            return true;
        }
    }
}
