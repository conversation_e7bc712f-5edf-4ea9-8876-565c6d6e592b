﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("NursingRecord_Pipes")]
    public class NRPipesInfo : ModifyReturnInfo
    {
        /// <summary>
        ///	自增长编号	
        ///</summary>
        [Key]
        [Column("SN")]
        public int SN { get; set; }
        /// <summary>
        ///	就诊序号	
        ///</summary>
        public int CureNo { get; set; }
        /// <summary>
        ///	评估日期	
        ///</summary>
        public DateTime ReportDate { get; set; }
        /// <summary>
        ///	I类导管，如：胸管,T管,口鼻气
        ///</summary>
        public string Pipe1 { get; set; }
        /// <summary>
        ///	I类导管分数，每个选项分值3	
        ///</summary>
        public int? Pipe1Score { get; set; }
        /// <summary>
        ///	II类导管，如：各类引流管,双套
        ///</summary>
        public string Pipe2 { get; set; }
        /// <summary>
        ///	II类导管分数，每个选项分值2	
        ///</summary>
        public int? Pipe2Score { get; set; }
        /// <summary>
        ///	III类导管，如：导尿管,胃肠减
        ///</summary>
        public string Pipe3 { get; set; }
        /// <summary>
        ///	III类导管分数，每个选项分值2	
        ///</summary>
        public int? Pipe3Score { get; set; }
        /// <summary>
        ///	意识，轻度烦躁,中度烦躁,重度
        ///</summary>
        public string Sense { get; set; }
        /// <summary>
        ///	意识分数，分值分别是2，35	
        ///</summary>
        public int? SenseScore { get; set; }
        /// <summary>
        ///	其他，如：认知障碍	
        ///</summary>
        public string Other { get; set; }
        /// <summary>
        ///	其他分数，如：5	
        ///</summary>
        public int? OtherScore { get; set; }
        /// <summary>
        ///	重要指标总分	
        ///</summary>
        public int? TotalScore { get; set; }
        /// <summary>
        ///	重要指标危险等级值：无危险，
        ///</summary>
        public int? DangerLevelValue { get; set; }
        /// <summary>
        ///	重要指标危险等级	
        ///</summary>
        public string DangerLevel { get; set; }
        /// <summary>
        ///	护理措施	
        ///</summary>
        public string NurseStep { get; set; }
        /// <summary>
        ///	录入人工号	
        ///</summary>
        public string InputerCode { get; set; }
        /// <summary>
        ///	录入人姓名	
        ///</summary>
        public string InputerName { get; set; }
        /// <summary>
        ///	录入时间	
        ///</summary>
        public DateTime? InputTime { get; set; }
        /// <summary>
        ///	病区代码	
        ///</summary>
        public string WardCode { get; set; }
        /// <summary>
        ///	床号	
        ///</summary>
        public string BedNo { get; set; }
        /// <summary>
        ///	护士长审核人工号	
        ///</summary>
        public string HeadNurseCode { get; set; }
        /// <summary>
        ///	护士长审时间	
        ///</summary>
        public DateTime? HeadNurseTime { get; set; }
        /// <summary>
        ///	删除人工号	
        ///</summary>
        public string DelOpCode { get; set; }
        /// <summary>
        ///	删除时间	
        ///</summary>
        public DateTime? DelTime { get; set; }
        /// <summary>
        ///	状态：0 待审核，1护士长已审核
        ///</summary>
        public int? Status { get; set; }
        /// <summary>
        ///	最后更新时间	
        ///</summary>
        public DateTime? LastUpdateTime { get; set; }
    }
}