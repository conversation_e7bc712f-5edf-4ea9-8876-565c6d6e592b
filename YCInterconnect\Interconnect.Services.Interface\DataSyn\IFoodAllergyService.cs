﻿
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Models;

namespace Interconnect.Services.Interface
{
    public interface IFoodAllergyService
    {
        /// <summary>
        /// 获取没有同步的食物过敏信息
        /// </summary>
        /// <returns></returns>
        Task<List<FoodAllergyInfo>> GetAllAsync();
        /// <summary>
        /// 同步食物过敏信息
        /// </summary>
        /// <returns></returns>
        Task<bool> SynchronizationMain();
    }
}