﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientProfileMarkRepository : IPatientProfileMarkRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientProfileMarkRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<PatientProfileMarkInfo> GetByChartNo(string chartNo)
        {
            return await _medicalDbContext.PatientProfileMark.Where(m =>
            m.ChartNo == chartNo && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        public async Task<List<PatientProfileMarkInfo>> GetByDisChargeFlagAsync(bool flag)
        {
            return await _medicalDbContext.PatientProfileMark.Where(m =>
           m.DischargeFlag == flag && m.DeleteFlag != "*").ToListAsync();
        }
        public async Task<List<PatientProfileMarkInfo>> GetInpatientByStationCode(string stationCode)
        {
            return await _medicalDbContext.PatientProfileMark.Where(m =>
            m.StationCode == stationCode && m.DischargeFlag == false && m.DeleteFlag != "*")
            .OrderBy(m => m.BedSort).ToListAsync();
        }

        public async Task<List<PatientProfileMarkView>> GetPatientProfileMarkViewByStationCode(string stationCode)
        {
            var queryStr = (from m in _medicalDbContext.PatientProfileMark
                            where m.StationCode == stationCode && m.DischargeFlag == false && m.DeleteFlag != "*"
                            orderby m.BedSort
                            select new PatientProfileMarkView
                            {
                                ChartNo = m.ChartNo,
                                MarkList = m.MarkList
                            });


            return await queryStr.AsNoTracking().ToListAsync();

        }
    }
}
