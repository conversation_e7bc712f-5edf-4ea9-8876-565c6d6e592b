﻿using Medical.Models;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public class MedicalStatisticsDbContext : DbContext
    {
        #region -- 构造函数
        public MedicalStatisticsDbContext(DbContextOptions<MedicalStatisticsDbContext> options)
           : base(options)
        { }
        #endregion

        #region -- 创建数据库上下文对象时
        protected override void OnModelCreating(ModelBuilder builder)
        {
            builder.Entity<PatientTubeRecordInfo>().<PERSON><PERSON>ey(t => (new { t.PatientTubeRecordID }));
            builder.Entity<PatientTubeCareMainInfo>().<PERSON><PERSON><PERSON>(t => (new { t.PatientTubeCareMainID }));
            base.OnModelCreating(builder);
        }
        #endregion

        public DbSet<PatientAssessMainInfo> PatientAssessMainInfos { get; set; }
        /// <summary>
        /// 病人护理问题数据集
        /// </summary>
        public DbSet<PatientProblemInfo> PatientProblems { get; set; }
        /// <summary>
        /// 病人护理措施数据集
        /// </summary>
        public DbSet<PatientInterventionInfo> PatientInterventions { get; set; }
        /// <summary>
        /// 病人措施数据集
        /// </summary>
        public DbSet<PatientScheduleMainInfo> PatientScheduleMain { get; set; }

        /// <summary>
        /// 风险表主表
        /// </summary>
        public DbSet<PatientScoreMainInfo> PatientScoreMainInfos { get; set; }
        /// <summary>
        /// 风险表主表
        /// </summary>
        public DbSet<PatientScoreDetailInfo> PatientScoreDetailInfos { get; set; }
        /// <summary>
        /// 患者评估历史表
        /// </summary>
        public DbSet<PatientAssessMainHistoryInfo> PatientAssessMainHistoryInfos { get; set; }
        /// <summary>
        /// 患者评估主表
        /// </summary>
        public DbSet<PatientAssessMainInfo> AssessMains { get; set; }

        /// <summary>
        /// 患者评估明细表
        /// </summary>
        public DbSet<PatientAssessDetailInfo> AssessDetails { get; set; }

        /// <summary>
        /// 患者排成明细表
        /// </summary>
        public DbSet<PatientScheduleDetailInfo> PatientScheduleDetail { get; set; }

        /// <summary>
        /// 病人给药排程
        /// </summary>
        public DbSet<PatientMedicineScheduleInfo> PatientMedicineScheduleInfos { get; set; }

        /// <summary>
        /// 患者出入量
        /// </summary>
        public DbSet<PatientIntakeOutputInfo> PatientIntakeOutputInfos { get; set; }
        /// <summary>
        /// 患者ProfileLogs
        /// </summary>
        public DbSet<PatientProfileLogInfo> PatientProfileLogInfos { get; set; }
        /// <summary>
        /// 派班数据
        /// </summary>
        public DbSet<AttendanceInfo> AttendanceInfos { get; set; }
        /// <summary>
        /// 派班明细数据
        /// </summary>
        public DbSet<PatientAttendanceDetailInfo> PatientAttendanceDetailInfos { get; set; }

        /// <summary>
        /// 统计库交班视图
        /// </summary>
        public DbSet<StatisticsHandoverInfo> HandoverInfos { get; set; }
        /// <summary>
        /// 统计库导管记录
        /// </summary>
        public DbSet<PatientTubeRecordInfo> PatientTubeRecordInfos { get; set; }

        /// <summary>
        /// 导管维护主记录表
        /// </summary>
        public DbSet<PatientTubeCareMainInfo> PatientTubeCareMainInfos { get; set; }
        /// <summary>
        /// 导管维护明细表
        /// </summary>
        public DbSet<PatientTubeCareDetailInfo> PatientTubeCareDetailInfos { get; set; }
        /// <summary>
        /// 巡视记录表
        /// </summary>
        public DbSet<PatientPatrolRecordInfo> PatientPatrolRecordInfos { get; set; }
        /// <summary>
        /// 护理明细记录表
        /// </summary>
        public DbSet<PatientNursingRecordDetailInfo> PatientNursingRecordDetailInfos { get; set; }

    }
}
