﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class VirtualBedListRepository : IVirtualBedListRepository
    {
        private MedicalDbContext _MedicalDbContext = null;

        public VirtualBedListRepository(MedicalDbContext db)
        {
            _MedicalDbContext = db;
        }
        public List<VirtualBedListInfo> GetAllVirtualBedList()
        {
            return _MedicalDbContext.VirtualBedListInfos.ToList();
        }
        /// <summary>
        /// 查询单个虚拟病区内的所有床位
        /// </summary>
        /// <param name="VirtualStationID"></param>
        /// <returns></returns>
        public async Task<List<VirtualBedListInfo>> GetVirtualBedListByVirtualStationIDAsync(int VirtualStationID)
        {

            return await _MedicalDbContext.VirtualBedListInfos.Where(m => m.VirtualStationID == VirtualStationID && m.DeleteFlag != "*").ToListAsync();

        }
        /// <summary>
        /// 查询病区内的虚拟床位
        /// </summary>
        /// <param name="HisBedNumber"></param>
        /// <param name="VirtualStationID"></param>
        /// <returns></returns>
        public async Task<VirtualBedListInfo> GetVirtualBedListByHisBedNumberAsync(string HisBedNumber, int VirtualStationID)
        {
            return await _MedicalDbContext.VirtualBedListInfos.Where(m => m.HisBedNumber == HisBedNumber && m.VirtualStationID == VirtualStationID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 查询单个病区内的所有床位(包括删除的床位)
        /// </summary>
        /// <param name="HisBedNumber"></param>
        /// <param name="VirtualStationID"></param>
        /// <returns></returns>
        public async Task<VirtualBedListInfo> GetVirtualBedByHisBedNumberAsync(string HisBedNumber, int VirtualStationID)
        {
            return await _MedicalDbContext.VirtualBedListInfos.Where(m => m.HisBedNumber == HisBedNumber && m.VirtualStationID == VirtualStationID).FirstOrDefaultAsync();
        }
    }
}
