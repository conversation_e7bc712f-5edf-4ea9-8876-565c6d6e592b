﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;
using Microsoft.EntityFrameworkCore; 
namespace Interconnect.Data
{
   public class ExecTableDictRepository: IExecTableDictRepository
    {
        private DataOutConnection _DataOutConnection = null;
       

        public ExecTableDictRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }

        /// <summary>
        /// 获取字典数据
        /// </summary>
        /// <returns></returns>
        public  List<ExecTableDictInfo> GetAsync()
        {
            return _DataOutConnection.ExecTableDictInfos.ToList();
        }
    }
}
