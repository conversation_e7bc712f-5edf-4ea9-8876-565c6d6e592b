﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    /// <summary>
    /// 病人基本信息
    /// </summary>
    [Serializable]
    [Table("PatientBasic")]
    public class PatientBasicInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///病案号
        ///</summary>
        public string ChartNo { get; set; }
        /// <summary>
        ///身分证号
        ///</summary>
        public string IdentityID { get; set; }
        /// <summary>
        ///病人姓名
        ///</summary>
        public string PatientName { get; set; }
        /// <summary>
        ///性别

        ///</summary>
        public string Gender { get; set; }
        /// <summary>
        ///出生日期
        ///</summary>
        public DateTime DateOfBirth { get; set; }
        /// <summary>
        ///出生时间
        ///</summary>
        public TimeSpan? TimeOfBirth { get; set; }
        /// <summary>
        ///血型

        ///</summary>
        public string BloodType { get; set; }
        /// <summary>
        ///籍贯

        ///</summary>
        public string NativePlace { get; set; }
        /// <summary>
        ///籍贯码
        ///</summary>
        public string NativePlaceCode { get; set; }
    }
}