﻿
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Interconnect.Models;

namespace Interconnect.Data.Interface
{
    public interface ILogInfoRepository
    {
        /// <summary>
        /// 日志删除
        /// </summary>
        /// <returns></returns>
         bool DelAsync(int logSaveDays);

        /// <summary>
        /// 获取日志
        /// </summary>
        /// <returns></returns>
        List<LogInfo> GetLog(string guid);

    }
}

