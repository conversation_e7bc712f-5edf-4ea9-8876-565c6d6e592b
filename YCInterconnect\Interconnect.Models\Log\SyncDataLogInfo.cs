﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("SyncDatasLog")]
    public class SyncDataLogInfo : ModifyInfo
    {
        /// <summary>
        /// 主键
        /// </summary>
        [Key]
        public int ID { get; set; }
        /// <summary>
        /// 医院标识
        /// </summary>
        [Column(TypeName = "varchar(2)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 数据同步的名称，如bedList,StationList 等
        /// </summary>
        [Column(TypeName = "varchar(30)")]
        public string SyncDataType { get; set; }
        /// <summary>
        /// 需要同步的数据
        /// </summary>      
        [Column(TypeName = "nvarchar(Max)")]
        public string SyncData { get; set; }

        /// <summary>
        /// 住院唯一号
        /// </summary>
        [Column(TypeName = "varchar(30)")]
        public string CaseNumber { get; set; }

        /// <summary>
        /// 住院唯一号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string OrderID { get; set; }


        /// <summary>
        /// 新增人员
        /// </summary>
        public string AddPersonID { get; set; }
        /// <summary>
        /// 新增时间
        /// </summary>
        public DateTime AddDate { get; set; }
        /// <summary>
        /// 医嘱执行分组编码
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string BarCode { get; set; }
    }
}
