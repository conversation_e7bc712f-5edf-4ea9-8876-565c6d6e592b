﻿using System;
using System.Collections.Generic;
using System.Text;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Medical.Common;
using Medical.Data.Interface;
using Microsoft.Extensions.Options;
using System.Threading.Tasks;
using NLog;
using Interconnect.ViewModels;

namespace Interconnect.Services
{
    public class InterconnectPatientScoreMainService: IInterconnectPatientScoreMainService
    {
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly IInterconnectPatientScoreMainRepository _interconnectPatientScoreMainRepository;
        private readonly IOptions<SystemConfig> _systemConfig;
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ICommonHelper _commonHelper;

        public InterconnectPatientScoreMainService(IAppConfigSettingRepository appConfigSettingRepository
            , IInterconnectPatientScoreMainRepository interconnectPatientScoreMainRepository
            , IOptions<SystemConfig> options
            , ICommonHelper commonHelper
            )
        {
            _appConfigSettingRepository = appConfigSettingRepository;
            _interconnectPatientScoreMainRepository = interconnectPatientScoreMainRepository;
            _systemConfig = options;
            _commonHelper = commonHelper;
        }

        public async Task<bool> SendPatientScoreToHIS()
        {
            var api = await _appConfigSettingRepository.GetConfigSettingValue( "", "");
            if (string.IsNullOrEmpty(api))
            {
                _logger.Error("获取风险返回API错误");
                return false;
            }
            var patientScoreMain = await _interconnectPatientScoreMainRepository.GetPatientScoreMainNotSync();
            if (patientScoreMain.Count <= 0)
            {
                return true;
            }
            foreach (var item in patientScoreMain)
            {
                var messageJson = ListToJson.ToJson(CreatePatientScoreMainView(item));
                var resultFlag = _commonHelper.CallPostAPI(api, item.PatientScoreMainID, messageJson);
                if (!resultFlag)
                {
                    
                    return false;
                }
            }
            return true;
        }
     
        private InterconnectPatientScoreMainView CreatePatientScoreMainView(InterconnectPatientScoreMainInfo item)
        {
            var t = new InterconnectPatientScoreMainView()
            {
                PatientName = item.PatientName,
                Sex = item.Sex,
                RangeContent = item.RangeContent,
                RecordListID = item.RecordListID,
                RecordName = item.RecordName,
                AssessDateTime = item.AssessDateTime,
                RemindFlag = item.RemindFlag,
                DepartmentCode = item.DepartmentCode,
                CaseNumber = item.CaseNumber,
                StationCode = item.StationCode,
                DepartmentName = item.DepartmentName,
                BedNumber = item.BedNumber,
                StationName = item.StationName,
                DoctorID = item.AttendingPhysicianID,
                DoctorName = item.AttendingPhysicianName,
                ScorePoint = item.ScorePoint,
            };
            return t;
        }

    }
}
