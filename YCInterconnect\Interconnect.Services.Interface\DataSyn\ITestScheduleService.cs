﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Models;

namespace Interconnect.Services.Interface
{
    public interface ITestScheduleService
    {
        /// <summary>
        /// 同步检验信息 
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncTestReportByDateTime();
        /// <summary>
        /// 同步单病人信息
        /// </summary>
        /// <param name="CaseNumber"></param>
        /// <returns></returns>
        Task<bool> SyncTestReportByCaseNumber(string CaseNumber);
        
    }
}