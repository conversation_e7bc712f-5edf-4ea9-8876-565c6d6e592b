﻿using System;
using System.Threading.Tasks;

namespace Interconnect.Services.Interface
{
    public interface ISyncInpatientService
    {


        /// <summary>
        /// 获取新入院病人数据
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncNewInPatient();

        /// <summary>
        /// / 根据病区code 同步在院病人
        /// </summary>
        /// <param name="stationCode"></param>
        /// <returns></returns>
        Task<bool> SyncInPatientByStationCode(string stationCode);

        /// <summary>
        /// 出院病人同步,同步一段时间内的出院病人数据
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncDischargedPatientsByDateTime();

        /// <summary>
        /// 根据时间段同步出院患者（刷新用）
        /// </summary>
        /// <param name="startDateTime"></param>
        /// <param name="endDateTime"></param>
        /// <returns></returns>
        Task<bool> SyncDischargedByDateTime(DateTime? startDateTime, DateTime? endDateTime);
    }
}