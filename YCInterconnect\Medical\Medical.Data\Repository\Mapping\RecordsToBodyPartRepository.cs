﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class RecordsToBodyPartRepository : IRecordsToBodyPartRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public RecordsToBodyPartRepository(MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<RecordsToBodyPartInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            dict.TryGetValue("language", out var language);
            return await _medicalDbContext.RecordsToBodyPartInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.Language == (Int32)language && m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.RecordsToBodyPart.GetKey(_sessionCommonServer);
        }
        public async Task<List<RecordsToBodyPartInfo>> GetAsync(string recordsCode)
        {
            var datas = await GetCacheAsync() as List<RecordsToBodyPartInfo>;
            if (datas != null)
            {
                return datas.Where(t => t.RecordsID.Trim() == recordsCode.Trim()).ToList();
            }
            return null;
        }

        public async Task<string> GetShowNanemByIDAsync(string recordsCode, int bodyPartID)
        {
            var datas = await GetAsync(recordsCode);
            if (datas == null)
            {
                return "";
            }
            var bodyPart = datas.Find(t => t.BodyPartID == bodyPartID);
            if (bodyPart == null)
            {
                return "";
            }
            return bodyPart.ShowName.Trim();
        }
    }
}