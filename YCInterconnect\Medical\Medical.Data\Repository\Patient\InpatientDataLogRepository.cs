﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{

    public class InpatientDataLogRepository : IInpatientDataLogRepository
    {
        private readonly MedicalDbContext _dbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        public InpatientDataLogRepository(
              MedicalDbContext db
            , SessionCommonServer sessionCommonServer
            )
        {
            _dbContext = db;
            _sessionCommonServer = sessionCommonServer;
        }
        /// <summary>
        /// 通过caseNumber获取患者数据（包含出院）
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        public async Task<InpatientDataLogInfo> GetByCaseNumberAsync(string CaseNumber, DateTime StatisticsDate, string shift)
        {
            return await _dbContext.InpatientDataLogInfos.Where(m => m.CaseNumber == CaseNumber && m.StatisticsDate == StatisticsDate
             && m.Shift == shift).FirstOrDefaultAsync();
        }

        public async Task<int> GetByCaseNumberCountAsync(string CaseNumber, DateTime StatisticsDate, string shift)
        {

            return await _dbContext.InpatientDataLogInfos.Where(m => m.CaseNumber == CaseNumber && m.StatisticsDate == StatisticsDate
      && m.Shift == shift).CountAsync();

        }
    }
}
