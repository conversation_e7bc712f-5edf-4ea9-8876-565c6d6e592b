﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using static Medical.Data.GetCacheService;

namespace Medical.Data
{
    public class GetCacheService
    {
        private readonly IMemoryCache _memoryCache;
        private readonly IOptions<SystemConfig> _systemConfig;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        public delegate Task<object> GetDataBaseListData(Dictionary<string, object> dict = null);
        public GetCacheService(
             IMemoryCache memoryCache
            , IOptions<SystemConfig> systemConfig
             , SessionCommonServer sessionCommonServer
            , IRedisService redisService
            )
        {
            _memoryCache = memoryCache;
            _systemConfig = systemConfig;
            _sessionCommonServer = sessionCommonServer;
            _redisService = redisService;
        }
     
        /// <summary>
        /// 获取缓存主入口
        /// </summary>
        public async Task<T> GetCacheMain<T>(string key, GetDataBaseListData getDataBaseListData, Dictionary<string, object> dict = null) where T : class
        {
            object result;
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            if (dict == null)
            {
                dict = new Dictionary<string, object>();
            }
            if (!dict.ContainsKey("hospitalID"))
            {
                dict.Add("hospitalID", hospitalID);
            }
            if (!dict.ContainsKey("language"))
            {
                dict.Add("language", language);
            }
            //获取配置参数，确认使用的缓存
            var useCacheType = _systemConfig.Value.UseCacheType;
            if (useCacheType == "Redis")
            {
                result = await GetRedisCache(key, getDataBaseListData, dict);
 
                try
                {
                    JsonDocument.Parse(result.ToString());
                    return JsonConvert.DeserializeObject<T>(result.ToString());
                }
                catch
                {
                    return (T)result;
                }

            }
            if (useCacheType == "Memory")
            {
                return await GetMemoryCache(key, getDataBaseListData, dict) as T;
            }
            //默认使用内存缓存
            return await GetMemoryCache(key, getDataBaseListData, dict) as T;

        }

        /// <summary>
        /// 获取Redis缓存
        /// </summary>
        /// <param name="key"></param>
        /// <param name="language"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<object> GetRedisCache(string key, GetDataBaseListData getDataBaseListData, Dictionary<string, object> dict = null)
        {

            var datas = await _redisService.GetOrCreateAsync(key, 0, 0, async entry =>
            {
                var result = await getDataBaseListData(dict);
                return result;
            });
            return datas;
        }
        /// <summary>
        /// 获取MemoryCache缓存
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        private async Task<object> GetMemoryCache(string key, GetDataBaseListData getDataBaseListData, Dictionary<string, object> dict = null)
        {
            var datas = await this._memoryCache.GetOrCreateAsync(key, async entry =>
            {
                entry.SetAbsoluteExpiration(TimeSpan.FromSeconds(60000));
                return await getDataBaseListData(dict);
            });
            return datas;
        }

    }
}
