﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("NursingRecord_PhysicalRestraintFirstEvaluation")]
    public class NRPhysicalRestraintFirstEvaluationInfo : ModifyReturnInfo
    {
        /// <summary>
        ///	自增长编号	
        ///</summary>
        [Key]
        [Column("EvaluationID")]
        public int EvaluationID { get; set; }
        /// <summary>
        ///	就诊序号	
        ///</summary>
        public int? CureNo { get; set; }
        /// <summary>
        ///	病区编号	
        ///</summary>
        public string WardCode { get; set; }
        /// <summary>
        ///	床号	
        ///</summary>
        public string BedNo { get; set; }
        /// <summary>
        ///	评估日期	
        ///</summary>
        public DateTime? ReportDate { get; set; }
        /// <summary>
        ///	状态：0 待审核，1护士长已审核
        ///</summary>
        public int? Status { get; set; }
        /// <summary>
        ///	患者是否需要约束：否,是	
        ///</summary>
        public string NeedRestraint { get; set; }
        /// <summary>
        ///	通知医生下达约束医嘱：否,是	
        ///</summary>
        public string InformedDoctor { get; set; }
        /// <summary>
        ///	约束起始时间	
        ///</summary>
        public DateTime? RestraintBeginDate { get; set; }
        /// <summary>
        ///	GCS评分	
        ///</summary>
        public float? GCS { get; set; }
        /// <summary>
        ///	运动反应：6能按吩咐完成动作,5
        ///</summary>
        public float? MovementResponse { get; set; }
        /// <summary>
        ///	语言反应：5能答对，定向正确,4
        ///</summary>
        public float? LanguageResponse { get; set; }
        /// <summary>
        ///	睁眼反应：4自行睁眼,3呼之睁眼
        ///</summary>
        public float? EyeResponse { get; set; }
        /// <summary>
        ///	是否镇静：否,是	
        ///</summary>
        public string IsSedation { get; set; }
        /// <summary>
        ///	RASS评分	
        ///</summary>
        public float? RASSScore { get; set; }
        /// <summary>
        ///	录入时间	
        ///</summary>
        public DateTime? CreateTime { get; set; }
        /// <summary>
        ///	删除人工号	
        ///</summary>
        public string DelOpCode { get; set; }
        /// <summary>
        ///	删除时间	
        ///</summary>
        public DateTime? DelTime { get; set; }
    }
}