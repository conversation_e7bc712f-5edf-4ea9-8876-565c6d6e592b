﻿using Interconnect.Data.Context;
using Interconnect.Data.Interface.Repository.Log;
using Interconnect.Models;
using Microsoft.EntityFrameworkCore;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
namespace Interconnect.Data.Repository
{
    public class SyncDatasLogRepository : ISyncDatasLogRepository
    {
        private DataOutConnection _dataOutConnection = null;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        public SyncDatasLogRepository(
            DataOutConnection db
            )
        {
            _dataOutConnection = db;
        }

        public List<int> GetSyncDataByDataType(string hospitalID, string syncDataType)
        {
            return _dataOutConnection.SyncDatasLogInfos.Where(m => m.HospitalID == hospitalID
           && m.SyncDataType == syncDataType && m.DataPumpFlag != "*" && (!m.Counts.HasValue || m.Counts < 2)).OrderBy(m => m.ID).Select(m => m.ID).ToList();
        }
        public async Task<SyncDataLogInfo> GetSyncDataByIDAsync(int id)
        {
            return await _dataOutConnection.SyncDatasLogInfos.Where(m => m.ID == id && m.DataPumpFlag != "*" && m.Counts < 2).FirstOrDefaultAsync();
        }
    }
}
