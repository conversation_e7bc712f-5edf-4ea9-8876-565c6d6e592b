﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Models;
namespace Interconnect.Services.Interface
{
    public interface  ILogInfoServices
    {
        /// <summary>
        ///  写日志
        /// </summary>
        /// <param name="logInfos"></param>
        /// <returns></returns>
        LogInfo InnsertLogAsync(string tablenames, string logs);

        /// <summary>
        /// 删除日志
        /// </summary>
        /// <returns></returns>
        bool DelLog();

        /// <summary>
        /// 获取数据
        /// </summary>
        /// <returns></returns>
        List<LogInfo> GetLog(string guid);
    }
}