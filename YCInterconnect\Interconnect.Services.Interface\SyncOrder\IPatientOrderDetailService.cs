﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Models;

namespace Interconnect.Services.Interface
{
    public interface IPatientOrderDetailService
    {
        /// <summary>
        /// 获取没有同步的医嘱Detail
        /// </summary>
        /// <returns></returns>
        List<PatientOrderDetailInfo> GetAllAsync();
        /// <summary>
        /// 同步医嘱明细
        /// </summary>
        /// <returns></returns>
        Task<bool> SynchronizationMain();
    }
}