﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class DrugTriggerSettingRepository : IDrugTriggerSettingRepository
    {
        private MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly GetCacheService _getCacheService;

        public DrugTriggerSettingRepository(MedicalDbContext db, IMemoryCache memoryCache, GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _getCacheService = getCacheService;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<DrugTriggerView>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            return await _medicalDbContext.DrugTriggerSettingInfos.Where(m =>  m.DeleteFlag != "*")
                                .Select(m => new DrugTriggerView
                                {
                                    DrugTriggerSettingID = m.DrugTriggerSettingID,
                                    OrderCode = m.OrderCode,
                                    TriggerType = m.TriggerType,
                                    MedicationCategory = m.MedicationCategory,
                                    MedicationRoute = m.MedicationRoute
                                }).ToListAsync(); 
        }

        public string GetCacheType()
        {
            return CacheType.DrugTriggerSetting.ToString();
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public async Task<List<DrugTriggerView>> GetAsync()
        {
            return (List<DrugTriggerView>)await GetCacheAsync();
        }
    }
}
