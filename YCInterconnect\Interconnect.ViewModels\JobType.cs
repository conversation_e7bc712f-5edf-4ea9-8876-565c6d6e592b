﻿namespace Interconnect.ViewModels
{
    /// <summary>
    /// 作业类别
    /// </summary>
    public enum JobType
    {
        /// <summary>
        /// 一个病区数据
        /// </summary>
        OneStationInpatientJob = 1,

        /// <summary>
        /// 一个病们
        /// </summary>
        OneInpatientJob = 2,

        /// <summary>
        /// 一个分组
        /// </summary>
        GroupStationJob = 3,

        /// <summary>
        /// 病区
        /// </summary>
        StationJob = 4,

        /// <summary>
        /// 医嘱字典
        /// </summary>
        OrderDictJob = 5,

        /// <summary>
        /// 医嘱
        /// </summary>
        PatientOrderJob = 6,

        /// <summary>
        /// 给药排成
        /// </summary>
        MedicineScheduleJob = 7,

        /// <summary>
        /// 出院病人
        /// </summary>
        InPatientDischargeJob = 8,

        /// <summary>
        /// 更新病人Mark
        /// </summary>
        UPPatientMarkJob = 9,

        /// <summary>
        /// 病人基本信息
        /// </summary>
        PatientBasicJob = 10,

        /// <summary>
        /// 床位
        /// </summary>
        BedJob = 11,

        /// <summary>
        /// 科室信息
        /// </summary>
        DepartmentJob = 12,

        /// <summary>
        /// 科室与病区对照
        /// </summary>
        StationToDepartment = 13,

        /// <summary>
        /// 检验数据
        /// </summary>
        TestJob = 14,

        /// <summary>
        /// 诊断信息
        /// </summary>
        PatientDiagnosisJob = 15,

        /// <summary>
        /// 手术信息
        /// </summary>
        OperateDataJob = 16,

        /// <summary>
        /// 病人历史记录
        /// </summary>
        InpatientLog = 17,

        /// <summary>
        /// 派班数据
        /// </summary>
        NursingShiftJob = 18,

        /// <summary>
        /// 更新Mark
        /// </summary>
        PatientMarkJob = 19,

        /// <summary>
        /// 发送邮件
        /// </summary>
        SendMailJob = 20,

        /// <summary>
        /// 新病人
        /// </summary>
        NewInpatientJob = 21,

        /// <summary>
        /// 人员病区权限
        /// </summary>
        EmployeeToStation = 22,

        /// <summary>
        /// 病区病人主诉
        /// </summary>
        StationPatientChiefComplaint = 23,

        /// <summary>
        /// 历史医嘱
        /// </summary>
        HistoryPatientOrderJob = 24,

        /// <summary>
        /// 给药写出入量
        /// </summary>
        MedicineIntakeOutPutJob = 25,

        /// <summary>
        /// 同步风险数据给HIS
        /// </summary>
        SendPatientScore = 26,

        /// <summary>
        /// 出院病人补救
        /// </summary>
        DisChargeInPatientJobBuJiu = 26,

        /// <summary>
        /// 刷新出院病人
        /// </summary>
        PatientDischargeJob = 28,

        /// <summary>
        /// 母婴关系
        /// </summary>
        MotherAndChild = 29,
        /// <summary>
        /// 生命体征
        /// </summary>
        ScheduleVitalSign = 30,
        /// <summary>
        /// 生命体征
        /// </summary>
        ScheduleMeasures = 31
    }
}