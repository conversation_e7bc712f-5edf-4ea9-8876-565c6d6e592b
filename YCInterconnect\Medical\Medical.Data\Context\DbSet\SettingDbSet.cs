﻿using Medical.Models;
using Medical.Models.Setting;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public partial class MedicalDbContext
    {
        /// <summary>
        /// 护士派班数据集
        /// </summary>
        public DbSet<AttendanceInfo> Attendance { get; set; }
        /// <summary>
        /// 护士派班数据集
        /// </summary>
        public DbSet<PatientAttendanceDetailInfo> PatientAttendanceDetailInfos { get; set; }
        /// <summary>
        /// 可跨病区
        /// </summary>
        public DbSet<AttendanceCrossInfo> AttendanceCrossInfos { get; set; }
        /// <summary>
        /// 报表样式数据集
        /// </summary>
        public DbSet<SettingDescriptionInfo> SettingDescriptions { get; set; }
        /// <summary>
        /// IO配置
        /// </summary>
        public DbSet<IntakeOutputSettingInfo> IntakeOutputSettings { get; set; }
        /// <summary>
        /// 部门管路配置
        /// </summary>
        public DbSet<StationUseTubeInfo> StationUseTubes { get; set; }
        /// <summary>
        /// 措施触发措施配置
        /// </summary>
        public DbSet<InterventionTriggerInterventionInfo> InterventionTriggerInterventionInfos { get; set; }
        /// <summary>
        /// 措施触发措施维护配置（中山评级使用）
        /// </summary>
        public DbSet<InterventionTriggerInterventionMaintainInfo> InterventionTriggerInterventionMaintainInfos { get; set; }
        /// <summary>
        /// 人员可选择单位
        /// </summary>
        public DbSet<EmployeeDepartmentSwitchInfo> EmployeeDepartmentSwitchInfo { get; set; }

        /// <summary>
        /// 人员可选择单位
        /// </summary>
        public DbSet<HospitalListInfo> HospitalListInfos { get; set; }
        public DbSet<AdminNoticeReadInfo> AdminNoticeReadInfos { get; set; }
        /// <summary>
        /// 用户标记
        /// </summary>
        public DbSet<PatientListIconInfo> PatientListIconInfos { get; set; }
        /// <summary>
        /// 责护岗位配置
        /// </summary>
        public DbSet<NurseBedSetInfo> NurseBedSetInfos { get; set; }
        /// <summary>
        /// 给药途径配置
        /// </summary>
        public DbSet<MedicationRouteInfo> MedicationRouteInfos { get; set; }

        /// <summary>
        /// 计算公式
        /// </summary>
        public DbSet<FormulaListInfo> FormulaListInfos { get; set; }
        /// <summary>
        /// 公式参数
        /// </summary>
        public DbSet<FormulaParamInfo> FormulaParamInfos { get; set; }

        /// <summary>
        /// 临时表
        /// </summary>
        public DbSet<TempData> TempDatas { get; set; }

        /// <summary>
        /// 推送消息设置表
        /// </summary>
        public DbSet<SendMessageSettingInfo> SendMessageSettings { get; set; }

        /// <summary>
        /// 推送消息设置表
        /// </summary>
        public DbSet<TextEntryInfo> TextEntryInfos { get; set; }

        /// <summary>
        /// 临床数据配置
        /// </summary>
        public DbSet<ClinicalSettingInfo> ClinicSettingInfos { get; set; }
        /// <summary>
        /// 测量点
        /// </summary>
        public DbSet<MeasuringPointInfo> MeasuringPointInfos { get; set; }
        /// <summary>
        /// 病人事件配置
        /// </summary>
        public DbSet<EventSettingInfo> EventSettingInfos { get; set; }
        /// <summary>
        /// 会诊目标字典
        /// </summary>
        public DbSet<ConsultGoalInfo> ConsultGoalInfos { get; set; }
        /// <summary>
        /// 会诊目标字典
        /// </summary>
        public DbSet<ConsultGoalToEmployeeInfo> ConsultGoalToEmployeeInfos { get; set; }
        /// <summary>
        /// 交班配置表
        /// </summary>
        public DbSet<HandoverSettingInfo> HandoverSettingInfos { get; set; }
        /// <summary>
        /// 重点交班配置表
        /// </summary>
        public DbSet<HandoverKeySignSettingInfo> HandoverKeySignSettingInfos { get; set; }

        public DbSet<AdministrationTranslationInfo> AdministrationTranslationInfos { get; set; }
        /// <summary>
        /// 排出物属性字典
        /// </summary>
        public DbSet<OutputAttributeInfo> OutputAttributeInfos { get; set; }
        /// <summary>
        /// 排出物对应属性配置
        /// </summary>
        public DbSet<AssessToOutputAttributeInfo> AssessToOutputAttributeIDs { get; set; }

        /// <summary>
        /// HIS.Common呼叫API配置表
        /// </summary>
        public DbSet<APISettingInfo> APISettingInfos { get; set; }

        /// <summary>
        /// AppConfig配置表
        /// </summary>
        public DbSet<AppConfigSettingInfo> AppConfigSettingInfos { get; set; }

        /// <summary>
        /// 汉字拼音首字母
        /// </summary>
        public DbSet<PinyinIndexInfo> PinyinIndexInfos { get; set; }

        /// <summary>
        /// 缓存配置
        /// </summary>
        public DbSet<CacheListInfo> CacheListInfos { get; set; }

        /// <summary>
        /// 系统执行检核日志
        /// </summary>
        public DbSet<PerformReturnLogInfo> PerformReturnLogInfos { get; set; }

        /// <summary>
        /// 体温单数据获取配置表
        /// </summary>
        public DbSet<TPRSettingInfo> TPRSettingInfos { get; set; }

        /// <summary>
        /// 相关因素对措施
        /// </summary>
        public DbSet<EtiologyToInterventionInfo> EtiologyToInterventionInfos { get; set; }

        /// <summary>
        /// 数据交换配置
        /// </summary>
        public DbSet<CDADocumentSettingInfo> CDADocumentSettingInfos { get; set; }

        /// <summary>
        /// 字典映射表
        /// </summary>
        public DbSet<MappingDictionaryInfo> MappingDictionaryInfos { get; set; }
        /// <summary>
        /// 药品清单
        /// </summary>
        public DbSet<DrugListInfo> DrugListInfos { get; set; }

        /// <summary>
        /// 诊断清单
        /// </summary>
        public DbSet<ICDListInfo> ICDListInfos { get; set; }

        /// <summary>
        /// 评估对诊断
        /// </summary>
        public DbSet<ICDToAssessInfo> ICDToAssessInfos { get; set; }

        /// <summary>
        /// 药物触发措施
        /// </summary>
        public DbSet<DrugTriggerSettingInfo> DrugTriggerSettingInfos { get; set; }

        /// <summary>
        /// 语言配置
        /// </summary>
        public DbSet<LanguagePackageInfo> LanguagePackageInfos { get; set; }

        /// <summary>
        /// 级联选单配置表
        /// </summary>
        public DbSet<CascadeSettingInfo> CascadeSettingInfos { get; set; }
        /// <summary>
        /// 工作提醒配置表
        /// </summary>
        public DbSet<JobTipSettingInfo> JobTipSettingInfos { get; set; }

        /// <summary>
        /// 措施明细默认勾选
        /// </summary>
        public DbSet<DetailDefaultSettingInfo> DetailDefaultSettingInfos { get; set; }
        /// <summary>
        /// 看板配置表
        /// </summary>
        public DbSet<NursingBoardSettingInfo> NursingBoardSettingInfos { get; set; }

        /// <summary>
        /// 字段明细对照表
        /// </summary>
        public DbSet<SpecialMappingInfo> SpecialMappingInfos { get; set; }
        /// <summary>
        /// AssessList显示名对照表
        /// </summary>
        public DbSet<LocalShowNameMappingInfo> LocalShowNameMappingInfos { get; set; }
        /// <summary>
        /// 措施是否带护理记录单配置表
        /// </summary>
        public DbSet<InterventionToRecordSettingInfo> InterventionToRecordSettingInfos { get; set; }
        /// <summary>
        /// 生长曲线标准差参数表
        /// </summary>
        public DbSet<SDofGrowthCurveInfo> SDofGrowthCurveInfos { get; set; }

        /// <summary>
        /// 记录单上动态列内容精确显示内容配置
        /// </summary>
        public DbSet<EMRRecordFieldConditionInfo> EMRRecordFieldConditionInfos { get; set; }
        /// <summary>
        ///病区工作提醒
        /// </summary>
        public DbSet<StationToJobTipInfo> StationJobTipInfos { get; set; }
        /// <summary>
        /// 病区患者标识
        /// </summary>
        public DbSet<StationPatientIconInfo> StationPatientIconInfos { get; set; }
        /// <summary>
        /// 动态表格清单（参考配置使用）
        /// </summary>
        public DbSet<DynamicTableListInfo> DynamicTableListInfos { get; set; }
        /// <summary>
        /// 动态表格列配置
        /// </summary>
        public DbSet<DynamicTableSettingInfo> DynamicTableSettingInfos { get; set; }
        /// <summary>
        /// 用户动态表格配置表
        /// </summary>
        public DbSet<DynamicTableUserSettingInfo> DynamicTableUserSettingInfos { get; set; }
        /// <summary>
        /// 动态表格字段配置
        /// </summary>
        public DbSet<DynamicTableColumnInfo> DynamicTableColumnInfos { get; set; }
        /// <summary>
        /// 表格列属性配置
        /// </summary>
        public DbSet<DynamicColumnAttributeInfo> DynamicColumnAttributeInfos { get; set; }
        /// <summary>
        /// 表格列属性字典
        /// </summary>
        public DbSet<DynamicColumnAttributeListInfo> DynamicColumnAttributeListInfos { get; set; }
        /// 患者事件异动表
        /// </summary>
        public DbSet<EventChangeSettingInfo> EventChangeSettingInfos { get; set; }
        /// <summary>
        /// 闭环配置表
        /// </summary>
        public DbSet<ClosingControlListInfo> ClosingControlListInfos { get; set; }
        /// <summary>
        /// 万年历
        /// </summary>
        public DbSet<PerpetualCalendarInfo> PerpetualCalendarInfos { get; set; }
    }
}