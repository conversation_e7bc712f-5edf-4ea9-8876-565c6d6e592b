﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class UserRoleRepository : IUserRoleRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public UserRoleRepository(MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        /// <summary>
        /// 根据用户ID获取角色
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<List<UserRoleInfo>> GetAsync(string employeeID)
        {
            var datas = await GetCacheAsync(null);
            if (datas != null)
            {
                return (datas as List<UserRoleInfo>).Where(t => t.EmployeeID == employeeID && t.DeleteFlag != "*").ToList();
            }
            return null;
        }

        /// <summary>
        /// 根据用户ID获取角色
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<List<UserRoleInfo>> GetUserRoleByEmployeeIDAsync(string employeeID)
        {
            var datas = await GetCacheAsync(null);
            if (datas != null)
            {
                return await _medicalDbContext.UserRoleInfos.Where(m => m.EmployeeID == employeeID).ToListAsync();
            }
            return null;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<UserRoleInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            return await _medicalDbContext.UserRoleInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.UserRole.GetKey(_sessionCommonServer);
        }

        public async Task<List<UserRoleInfo>> GetAsync()
        {
            return await GetCacheAsync() as List<UserRoleInfo>;
        }

        //没有人员信息的权限信息
        public List<UserRoleInfo> GetNoRoleEmployee()
        {
            var result = (from UserRole in _medicalDbContext.UserRoleInfos
                          where !_medicalDbContext.Users.Any(m => m.UserID == UserRole.EmployeeID && m.DeleteFlag != "*")
                          && UserRole.DeleteFlag != "*"
                          select UserRole).ToList();
            return result;
        }

        public async Task<bool> DeleteUserRole(List<UserRoleInfo> userRoles)
        {
            foreach (var userRole in userRoles)
            {
                _medicalDbContext.UserRoleInfos.Remove(userRole);
            }
            return await _medicalDbContext.SaveChangesAsync() > 0;
        }

        public async Task<bool> AddUserRole(List<UserRoleInfo> list)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            if (string.IsNullOrEmpty(hospitalID))
            {
                return false;
            }
            foreach (var user in list)
            {
                if (string.IsNullOrEmpty(user.HospitalID))
                {
                    user.HospitalID = hospitalID;
                }
                _medicalDbContext.UserRoleInfos.Add(user);
            }

            if (await _medicalDbContext.SaveChangesAsync() > 0)
            {
                UpdateAsync();
                return true;
            }
            return false;
        }
        /// <summary>
        /// 不走缓存虎获取数据 同步使用 medical禁止使用
        /// </summary>
        /// <returns></returns>
        public List<UserRoleInfo> NoCacheGetAllRole()
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            return _medicalDbContext.UserRoleInfos.Where(m => m.DeleteFlag != "*" && m.HospitalID == hospitalID).ToList();
        }

        public async Task<List<UserRoleInfo>> GetUserRoleByEmployeeIDs(List<string> employeeIDs)
        {
            var datas = await GetCacheAsync() as List<UserRoleInfo>;
            return datas.Where(t => employeeIDs.Contains(t.EmployeeID)).ToList();
        }
    }
}
