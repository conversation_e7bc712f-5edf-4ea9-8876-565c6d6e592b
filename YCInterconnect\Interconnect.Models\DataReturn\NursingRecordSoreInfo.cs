﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("NursingRecord_Sore")]
    public class NursingRecordSoreInfo :ModifyReturnInfo
    {
        /// <summary>
        ///	自增长	
        ///</summary>
        [Key]
        [Column("SN")]
        public int SN { get; set; }
        /// <summary>
        ///	就诊序号	
        ///</summary>
        public string CureNo { get; set; }
        /// <summary>
        ///	评估时间	
        ///</summary>
        public DateTime? ReportDate { get; set; }
        /// <summary>
        ///	感知能力：0完全受限,1非常受限
        ///</summary>
        public short? Feeling { get; set; }
        /// <summary>
        ///	潮湿度：0持续潮湿,1非常潮湿,2
        ///</summary>
        public short? Wet { get; set; }
        /// <summary>
        ///	活动能力：0限制卧床,1协助坐椅
        ///</summary>
        public short? Action { get; set; }
        /// <summary>
        ///	移动能力：0完全无法移动,1严重
        ///</summary>
        public short? MovingAbility { get; set; }
        /// <summary>
        ///	营养状况：0非常差,1可能不足,
        ///</summary>
        public short? Sustenance { get; set; }
        /// <summary>
        ///	摩擦力和剪切力：0有问题,1有潜
        ///</summary>
        public short? Friction { get; set; }
        /// <summary>
        ///	教育告知（多选）：患者,家属	
        ///</summary>
        public string AirBed { get; set; }
        /// <summary>
        ///	减压设施（多选）：气垫床,减压
        ///</summary>
        public string LessPressure { get; set; }
        /// <summary>
        ///	督促、协助更换体位：0 无，1有
        ///</summary>
        public string Mattress { get; set; }
        /// <summary>
        ///	定时更换体位(见翻身记录)：0 
        ///</summary>
        public string BodyPosition { get; set; }
        /// <summary>
        ///	皮肤护理：清洁,滋润,保护（赛
        ///</summary>
        public string SkinNursing { get; set; }
        /// <summary>
        ///	营养支持：0 无，1有	
        ///</summary>
        public string NailNursing { get; set; }
        /// <summary>
        ///	床单位整洁：0 是，1否	
        ///</summary>
        public string BedUnit { get; set; }
        /// <summary>
        ///	减压设施其他：	
        ///</summary>
        public string Other { get; set; }
        /// <summary>
        ///	输入人工号	
        ///</summary>
        public string InputerCode { get; set; }
        /// <summary>
        ///	审核护士长工号	
        ///</summary>
        public int? HeadNurseCode { get; set; }
        /// <summary>
        ///	护士长审核时间	
        ///</summary>
        public DateTime? HeadNurseTime { get; set; }
        /// <summary>
        ///	科护士长人工号	
        ///</summary>
        public int? DeptHeadNurseCode { get; set; }
        /// <summary>
        ///	科护士长审核时间	
        ///</summary>
        public DateTime? DeptHeadNurseTime { get; set; }
        /// <summary>
        ///	病区代码	
        ///</summary>
        public string WardCode { get; set; }
        /// <summary>
        ///	科室代码	
        ///</summary>
        public string DeptCode { get; set; }
        /// <summary>
        ///	床号	
        ///</summary>
        public string BedNo { get; set; }
        /// <summary>
        ///	状态：0 待审核，1 护士长已审
        ///</summary>
        public int? Status { get; set; }
        /// <summary>
        ///	输入时间	
        ///</summary>
        public DateTime? InputTime { get; set; }
        /// <summary>
        ///	输入人工号	
        ///</summary>
        public string InputerName { get; set; }
        /// <summary>
        ///		
        ///</summary>
        public int? UseStatus { get; set; }
        /// <summary>
        ///	删除人工号	
        ///</summary>
        public string DelOpCode { get; set; }
        /// <summary>
        ///	最后更新时间	
        ///</summary>
        public DateTime? LastUpdateTime { get; set; }
    }
}