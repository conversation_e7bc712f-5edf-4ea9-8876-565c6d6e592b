﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class QCSubjactiveRepository : IQCSubjactiveRepository
    {
        private MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public QCSubjactiveRepository(
            MedicalDbContext db,
            IMemoryCache memoryCache,
            SessionCommonServer sessionCommonServer,
            GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        /// <summary>
        /// 根据id查询数据
        /// </summary>
        /// <param name="qCSubjactiveID">id值</param>
        /// <param name="noCache">不走缓存，默认false</param>
        /// <returns>返回一个数据</returns>
        public async Task<QCCheckSubjectInfo> GetQCSubjactiveByIDAsync(int qCSubjactiveID, bool noCache = false)
        {
            if (noCache)
            {
                return await _medicalDbContext.QCSubjactiveInfos.FirstOrDefaultAsync(m => m.QCCheckSubjectID == qCSubjactiveID && m.DeleteFlag != "*");
            }

            var list = await this.GetAllAsync<QCCheckSubjectInfo>();
            return list.FirstOrDefault(m => m.QCCheckSubjectID == qCSubjactiveID);
        }

        /// <summary>
        /// 获取质控主题数据
        /// </summary>
        /// <param name="versionYear"></param>
        /// <param name="qcCheckLever"></param>
        /// <param name="qcCheckSubjectID"></param>
        /// <returns></returns>
        public async Task<List<QCCheckSubjectInfo>> GetQCSubjactivesAsync(short versionYear, int? qcCheckLever, int? qcCheckSubjectID)
        {
            var list = await this.GetAllAsync<QCCheckSubjectInfo>();
            list = list.Where(m =>
                   m.VersionYear == versionYear
                   && m.VersionSection == "G"
                   && (!qcCheckLever.HasValue || m.QCCheckLever == qcCheckLever)
                   && (!qcCheckSubjectID.HasValue || m.QCCheckSubjectID == qcCheckSubjectID)
            ).OrderBy(m => m.Sort).ToList();
            return list;
        }


        /// <summary>
        /// 获取下一个主键
        /// </summary>
        /// <returns></returns>
        public async Task<int> GetQCSubjactiveCountAsync()
        {
            string key = GetCacheType();
            var (hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            var index = await _medicalDbContext.QCSubjactiveInfos
                .Where(m => m.HospitalID == hospitalID)
                .Select(m => (int?)m.QCCheckSubjectID)
                .DefaultIfEmpty()
                .MaxAsync(m => m) ?? 0;
            return index + 1;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<QCCheckSubjectInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            return await _medicalDbContext.QCSubjactiveInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.QCSubjactive.GetKey(_sessionCommonServer);
        }
    }
}
