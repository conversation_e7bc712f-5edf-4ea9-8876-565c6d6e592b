﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class HandOverSignRepository : IHandOverSignRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public HandOverSignRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 根据HandoverID获取重点标记数据
        /// </summary>
        /// <param name="handOverID">交班序号</param>
        /// <returns></returns>
        public async Task<List<HandOverSignInfo>> GetByIDAsync(string handOverID)
        {
            return await _medicalDbContext.HandoverSignInfos
                .Where(m => m.HandoverID == handOverID && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据HandoverID集合获取一组重点标记数据
        /// </summary>
        /// <param name="handOverIDs">交班序号集合</param>
        /// <returns></returns>
        public async Task<List<HandOverSignInfo>> GetByIDsAsync(string[] handOverIDs)
        {
            return await _medicalDbContext.HandoverSignInfos
                .Where(m => handOverIDs.Contains(m.HandoverID) && m.DeleteFlag != "*").AsNoTracking().ToListAsync();
        }

        /// <summary>
        /// 获取某班指定标记ID的标记数据
        /// </summary>
        /// <param name="dateTime">日期</param>
        /// <param name="shift">班别</param>
        /// <param name="stationID">病区ID</param>
        /// <param name="signID">标记ID</param>
        /// <returns></returns>
        public async Task<List<HandOverSignInfo>> GetSign(DateTime dateTime, string shift, int stationID, int signID)
        {
            return await _medicalDbContext.HandoverSignInfos
                .Where(m => m.ShiftDate == dateTime && m.StationID == stationID && m.SignID == signID && m.HandOverShift == shift && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 获取指定病区某班的标记数据
        /// </summary>
        /// <param name="stationID">病区ID</param>
        /// <param name="date">班别日期</param>
        /// <param name="shiftFlag">班别</param>
        /// <returns></returns>
        public async Task<List<HandOverSignInfo>> GetByStationAndDateAndShift(int stationID, DateTime date, string shiftFlag)
        {
            return await _medicalDbContext.HandoverSignInfos.Where(m => m.StationID == stationID && m.ShiftDate == date && m.HandOverShift == shiftFlag && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 查询病危、病重、重点的标记
        /// </summary>
        /// <param name="handoverIDs">交班序号集合</param>
        /// <param name="hospitalID">医院序号</param>
        /// <param name="language">语言序号</param>
        /// <returns></returns>
        public async Task<List<HandOverSignInfo>> GetByHandoverIDAndDate(List<string> handoverIDs, string hospitalID, int language)
        {
            var handoverIDSet = new HashSet<string>(handoverIDs);

            var typeValues = new List<string>
            {
                "Key",
                "2847",
                "2849"
            };
            var result = await (from m in _medicalDbContext.HandoverSignInfos
                                join n in _medicalDbContext.SettingDescriptions
                                on new { ID = m.SignID, HospitalID = hospitalID, Language = language } equals new { n.ID, n.HospitalID, n.Language }
                                where handoverIDSet.Contains(m.HandoverID) && typeValues.Contains(n.TypeValue)
                                && m.SignID == n.ID && m.DeleteFlag != "*" && n.DeleteFlag != "*"
                                select new HandOverSignInfo
                                {
                                    HandoverID = m.HandoverID,
                                    SignID = m.SignID,
                                    SignName = m.SignName,
                                }).OrderBy(m => m.SignID).ToListAsync();
            return result;
        }

        /// <summary>
        /// 获取交班日期那一天此病区标记数据
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="date"></param>
        /// <returns></returns>
        public async Task<List<HandOverSignInfo>> GetByStationAndShiftDate(int stationID, DateTime date, string hospitalID)
        {
            return await (from m in _medicalDbContext.HandoverSignInfos
                          join n in _medicalDbContext.InpatientDatas on new { ID = m.InpatientID } equals new { n.ID }
                          where m.StationID == stationID && m.ShiftDate == date && m.DeleteFlag != "*" && n.HospitalID == hospitalID
                          && n.DeleteFlag!= "*"
                          select new HandOverSignInfo
                          {
                              InpatientID = m.InpatientID,
                              SignID = m.SignID,
                              HandOverShift = m.HandOverShift,
                              ShiftDate = m.ShiftDate,
                              StationID = m.StationID,
                              DeleteFlag = m.DeleteFlag
                          }).ToListAsync();
        }
        /// <summary>
        /// 获取交班日期那一天班次此病区指定标记数据
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="date"></param>
        /// <param name="hospitalID"></param>
        /// <param name="signIDs"></param>
        /// <returns></returns>
        public async Task<List<HandOverSignInfo>> GetByStationAndShiftDateAndSignIDs(int stationID, DateTime date, string hospitalID, List<int> signIDs,List<string> handOverShifts)
        {
            return await (from m in _medicalDbContext.HandoverSignInfos
                          join n in _medicalDbContext.InpatientDatas on new { ID = m.InpatientID } equals new { n.ID }
                          where m.StationID == stationID && m.ShiftDate == date && m.DeleteFlag != "*" && n.HospitalID == hospitalID
                          && signIDs.Contains(m.SignID) && handOverShifts.Contains(m.HandOverShift)
                          select new HandOverSignInfo
                          {
                              InpatientID = m.InpatientID,
                              SignID = m.SignID,
                              HandOverShift = m.HandOverShift,
                              ShiftDate = m.ShiftDate,
                              StationID = m.StationID,
                              DeleteFlag = m.DeleteFlag
                          }).ToListAsync();
        }
        /// <summary>
        /// 获取某人某班指定标记ID的标记数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="dateTime"></param>
        /// <param name="shift"></param>
        /// <param name="stationID"></param>
        /// <param name="signID"></param>
        /// <returns></returns>
        public async Task<List<HandOverSignInfo>> GetSignByInpatientID(string inpatientID, DateTime dateTime, string shift, int stationID, int signID)
        {
            return await _medicalDbContext.HandoverSignInfos
                .Where(m => m.InpatientID == inpatientID && m.ShiftDate == dateTime && m.StationID == stationID && m.SignID == signID && m.HandOverShift == shift && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据SourceIDSourceType获取数据
        /// </summary>
        /// <param name="sourceID"></param>
        /// <param name="sourceType"></param>
        /// <param name="deleteFlag"></param>
        /// <returns></returns>
        public async Task<HandOverSignInfo> GetSignInfoBySourceIDSourceType(string sourceID, string sourceType, bool deleteFlag)
        {
            if (deleteFlag)
            {
                return await _medicalDbContext.HandoverSignInfos.Where(m => m.SourceID == sourceID && m.SourceType == sourceType).OrderBy(m => m.ModifyDate).LastOrDefaultAsync();
            }
            return await _medicalDbContext.HandoverSignInfos.Where(m => m.SourceID == sourceID && m.SourceType == sourceType && m.DeleteFlag != "*").OrderBy(m => m.ModifyDate).LastOrDefaultAsync();
        }
        public async Task<List<HandOverSignView>> GetSignViewByInpatientIDs(List<string> inpatientIDArr, DateTime dateTime, string shift, int signID)
        {
            return await _medicalDbContext.HandoverSignInfos.Where(m => inpatientIDArr.Contains(m.InpatientID) 
            && m.ShiftDate == dateTime && m.SignID == signID 
            && m.HandOverShift == shift && m.DeleteFlag != "*")
                .Select(m=> new HandOverSignView
                {
                    HandoverID= m.HandoverID,
                    InpatientID= m.InpatientID,
                    StationID=m.StationID,
                    BedNumber=m.BedNumber
                }).ToListAsync();
        }


        /// <summary>
        /// 根据SourceID获取数据
        /// </summary>
        /// <param name="sourceIDArr"></param>
        /// <returns></returns>
        public async Task<List<HandOverSignInfo>> GetSignBysourceIDs(List<string> sourceIDArr)
        {
            return await _medicalDbContext.HandoverSignInfos.Where(m => sourceIDArr.Contains(m.SourceID) && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据SourceID获取数据集合
        /// </summary>
        /// <param name="sourceID"></param>
        /// <param name="sourceType"></param>
        /// <returns></returns>
        public async Task<List<HandOverSignInfo>> GetSignInfoBySourceIDSourceType(string sourceID, string sourceType)
        {
            return await _medicalDbContext.HandoverSignInfos.Where(m => m.SourceID == sourceID && m.SourceType == sourceType && m.DeleteFlag != "*").OrderBy(m => m.ModifyDate).ToListAsync();
        }
        /// <summary>
        /// 根据患者唯一键获取患者数据
        /// </summary>
        /// <param name="inpatientIDs">患者序号集合</param>
        /// <param name="shiftDate">交班日期</param>
        /// <param name="stationID">病区ID</param>
        /// <param name="signID">标志</param>
        /// <returns></returns>
        public async Task<List<HandOverSignInfo>> GetByInpatientAndShiftDate(List<string> inpatientIDs, DateTime shiftDate, int? stationID = null, int? signID = null)
        {
            var query = _medicalDbContext.HandoverSignInfos.Where(m => inpatientIDs.Contains(m.InpatientID) && m.ShiftDate == shiftDate && m.DeleteFlag != "*")
                .Where(signID.HasValue, m => m.SignID == signID.Value)
                .Where(stationID.HasValue, m => m.StationID == stationID.Value);

            return await query.ToListAsync();
        }
    }
}
