﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class SystemVersionRecordRepository : ISystemVersionRecordRepository
    {
        private readonly MedicalDbContext _medicalDbContext;
        private readonly SessionCommonServer _sessionCommonServer;
        public SystemVersionRecordRepository(
            MedicalDbContext db
            , SessionCommonServer sessionCommonServer
            )
        {
            _medicalDbContext = db;
            _sessionCommonServer = sessionCommonServer;
        }

        public async Task<List<SystemVersionRecordInfo>> GetVersionListBySystemCode(string systemCode)
        {
            var datas = await GetAsync();
            return datas.Where(m => m.SystemCode == systemCode).ToList();
        }
        public async Task<SystemVersionRecordInfo> GetLastVersionBySystemCode(string systemCode)
        {
            var versionList = await GetVersionListBySystemCode(systemCode);
            return versionList.Where(m => m.StatusType == 3)
                        .OrderByDescending(m => m.UpdateTime).FirstOrDefault();
        }
        public async Task<SystemVersionRecordInfo> GetVersionByID(string systemVersionRecordID)
        {
            var datas = await GetAsync();
            return datas.Where(m => m.SystemVersionRecordID == systemVersionRecordID).FirstOrDefault();
        }
        public async Task<List<SystemVersionRecordInfo>> GetAsync()
        {
            var session = await _sessionCommonServer.GetSession();
            return await _medicalDbContext.SystemVersionRecordInfos.Where(m => m.HospitalID == session.HospitalID && m.Language == session.Language && m.DeleteFlag != "*").ToListAsync();
        }
    }
}
