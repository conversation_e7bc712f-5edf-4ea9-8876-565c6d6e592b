﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Interconnect.Data.Repository

{
    public class NRPressureScoreInfoRepository: INRPressureScoreInfoRepository
    {
        private DataOutConnection _DataOutConnection = null;

        public NRPressureScoreInfoRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }
        public List<NRPressureScoreInfo> GetPressureScore(string patientScoreMainID)
        {
            return _DataOutConnection.NRPressureScoreInfos.Where(m => m.PatientScoreMainID == patientScoreMainID).ToList();
        }       
    }
}
