﻿/*
 * 2022-01-12 2352 病人信息同步添加转床、转科时写患者事件 -杨欣欣
 * 2022-05-15   新增母婴关系同步，可通过母亲信息补全婴儿信息    ——孟昭永
 */
using Arch.EntityFrameworkCore.UnitOfWork;
using Hangfire;
using Interconnect.Data.Context;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Interconnect.ViewModels;
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.Interface;
using Medical.ViewModels.View;
using MedicalExternalCommon.Service;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ViewModel;

namespace Interconnect.Services
{
    public class InpatientService : IInpatientService
    {
        //Mdeical
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;

        private readonly IStationListRepository _IStationListRepository;
        private readonly IDepartmentListRepository _IDepartmentListRepository;
        private readonly IBedListRepository _IBedListRepository;
        private readonly IInpatientDataRepository _IInpatientDataRepository;
        private readonly IPatientBasicDataRepository _PatientBasicDataRepository;
        private readonly ILogInfoServices _ILogInfoServices;
        private readonly IPatientBasicService _IPatientBasicService;
        private readonly IPatientListIconRepository _patientListIconRepository;
        private readonly IVirtualStationListRepository _virtualStationListRepository;
        private readonly IEventSettingRepository _eventSettingRepository;
        private readonly IUserRepository _userRepository;
        private readonly IHospitalListRepository _hospitalListRepository;
        private readonly IAPISettingRepository _aPISettingRepository;

        //Interconnect
        private readonly IUnitOfWork<DataOutConnection> _unitOfWorkOut;

        private readonly IOptions<SystemConfig> _config;
        private readonly IStationGroupListRepository _stationGroupListRepository;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        public readonly IExecTableDictRepository _execTableDictRepository;
        private readonly IJobLogService _jobLogService;
        private readonly ICommonHelper _commonHelper;
        private readonly ISyncAPIConfigRepository _syncAPIConfigRepository;
        private readonly IPatientEventRepository _patientEventRepository;
        private readonly PatientProfileMarkService _patientProfileMarkService;
        private readonly ExternalCommonService _externalCommonService;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly IInpatientLogRepository _inpatientLogRepository;
        private readonly PatientEventCommonService _patientEventCommonService;
        private readonly ISyncLogService _syncLogService;
        private readonly MQCommonService _mQCommonService;
        private readonly DataTableEditListService _dataTableEditListService;
        private readonly GetBirthDateService _getBirthDateService;
        private readonly INewBornService _newBornService;
        private readonly ExternalProfileCommonService _externalProfileCommonService;

        /// <summary>
        /// 系统默认操作人
        /// </summary>
        private const string MODIFYPERSONID = "TongBu";

        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";

        private List<MarkView> MarkViewList = new List<MarkView>();
        private List<InPatientChangeViewInfo> InPatientChangeList = new List<InPatientChangeViewInfo>();//记录更新或新增的病人数据
        #region --常量配置
        /// <summary>
        /// 转出床事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_TURNOUTBED = 5317;
        /// <summary>
        /// 转入床事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_TURNINBED = 5318;
        /// <summary>
        /// 转出科事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_TRANSOUTDEPT = 5319;
        /// <summary>
        /// 转入科事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_TRANSINDEPT = 5320;
        /// <summary>
        /// 入院事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_ADMISSION = 2872;
        /// <summary>
        /// 入科事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_UNITARRIVALTIME = 4700;
        /// <summary>
        /// 转入病区事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_TRANSIN = 2874;
        /// <summary>
        /// 转病区事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_TRANSOUT = 2875;
        /// <summary>
        /// 出院事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_DISCHARGE = 2873;
        /// <summary>
        /// 死亡
        /// </summary>
        private const int DeathAssessListId = 2876;
        #endregion

        public InpatientService(
              IUnitOfWork<MedicalDbContext> UnitOfWork
            , IUnitOfWork<DataOutConnection> UnitOfWorkOut
            , IStationListRepository StationListRepository
            , IDepartmentListRepository DepartmentListRepository
            , IBedListRepository BedListRepository
            , IInpatientDataRepository inpatientDataRepository
            , IPatientBasicDataRepository PatientBasicDataRepository
            , IOptions<SystemConfig> config
            , ILogInfoServices LogInfoServices
            , IExecTableDictRepository execTableDictRepository
            , IPatientBasicService patientBasicService
            , IJobLogService jobLogService
            , ICommonHelper commonHelper
            , IStationGroupListRepository stationGroupListRepository
            , IPatientListIconRepository patientListIconRepository
            , IVirtualStationListRepository virtualStationListRepository
            , IEventSettingRepository eventSettingRepository
            , ISyncAPIConfigRepository syncAPIConfigRepository
            , IUserRepository userRepository
            , IHospitalListRepository hospitalListRepository
            , IPatientEventRepository patientEventRepository
            , PatientProfileMarkService patientProfileMarkService
            , ExternalCommonService externalCommonService
            , IAppConfigSettingRepository appConfigSettingRepository
            , IInpatientLogRepository inpatientLogRepository
            , PatientEventCommonService patientEventCommonService
            , ISyncLogService syncLogService
            , MQCommonService mQCommonService
            , DataTableEditListService dataTableEditListService
            , GetBirthDateService getBirthDateService
            , IAPISettingRepository aPISettingRepository
            , INewBornService newBornService
            , ExternalProfileCommonService externalProfileCommonService
            )
        {
            _unitOfWork = UnitOfWork;
            _unitOfWorkOut = UnitOfWorkOut;
            _IDepartmentListRepository = DepartmentListRepository;
            _IStationListRepository = StationListRepository;
            _IBedListRepository = BedListRepository;
            _IInpatientDataRepository = inpatientDataRepository;
            _PatientBasicDataRepository = PatientBasicDataRepository;
            _config = config;
            _ILogInfoServices = LogInfoServices;
            _execTableDictRepository = execTableDictRepository;
            _IPatientBasicService = patientBasicService;
            _jobLogService = jobLogService;
            _commonHelper = commonHelper;
            _stationGroupListRepository = stationGroupListRepository;
            _patientListIconRepository = patientListIconRepository;
            _virtualStationListRepository = virtualStationListRepository;
            _eventSettingRepository = eventSettingRepository;
            _syncAPIConfigRepository = syncAPIConfigRepository;
            _userRepository = userRepository;
            _hospitalListRepository = hospitalListRepository;
            _patientEventRepository = patientEventRepository;
            _patientProfileMarkService = patientProfileMarkService;
            _externalCommonService = externalCommonService;
            _appConfigSettingRepository = appConfigSettingRepository;
            _inpatientLogRepository = inpatientLogRepository;
            _patientEventCommonService = patientEventCommonService;
            _syncLogService = syncLogService;
            _mQCommonService = mQCommonService;
            _dataTableEditListService = dataTableEditListService;
            _getBirthDateService = getBirthDateService;
            _aPISettingRepository = aPISettingRepository;
            _newBornService = newBornService;
            _externalProfileCommonService = externalProfileCommonService;
        }

        #region 同步接口

        //按照病区同步在院病人
        public async Task<bool> SyncInPatientByStationGroup(int StationGroup)
        {
            var hospitalInfo = _hospitalListRepository.GetHospitalInfo();
            if (hospitalInfo == null)
            {
                return false;
            }
            //根据病区获取数据
            var stationList = _stationGroupListRepository.GetStationGroupListByGroupID(StationGroup);
            if (stationList.Count <= 0)
            {
                _logger.Info("没有获得分组为[" + StationGroup + "],的病区列表！");
                return false;
            }
            _logger.Info("获得分组信息：" + ListToJson.ToJson(stationList));
            foreach (var item in stationList)
            {
                _logger.Info("同步病区：StationCode" + item.StationCode);
                try
                {
                    var result = await SyncInPatientByStationCode(item.StationCode);
                    if (!result)
                    {
                        _logger.Error("同步病区数据失败,StationCode:" + item.StationCode);
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error("同步病区数据失败,StationCode:" + item.StationCode + ex.ToString());
                    continue;
                }
            }
            return true;
        }

        //同步一个病区的数据
        public async Task<bool> SyncInPatientByStationCode(string statinoCode)
        {
            var hospitalInfo = _hospitalListRepository.GetHospitalInfo();
            if (hospitalInfo == null)
            {
                return false;
            }
            var hospitalBaseDict = await GetHospitalBaseDict(hospitalInfo.HospitalID);
            //获取API接口数据
            var inpatientData = GetInpateintDataByStationCode(statinoCode, hospitalInfo.HospitalID);
            if (inpatientData == null || inpatientData.Count < 1)
            {
                _logger.Error("没有获得住院数据，statinoCode=" + statinoCode);
                return false;
            }
            //将数据进行同步
            var resultFlag = await SyncInPatientData(hospitalBaseDict, inpatientData, hospitalInfo.HospitalID);
            if (!resultFlag)
            {
                return false;
            }
            //本病区没有的病人，把BedNumber改为空，BedID为0，使清单不呈现
            await SyncClearStationInPatient(statinoCode, inpatientData, hospitalInfo.HospitalID);
            return true;
        }

        /// <summary>
        /// 同步时间段内获取的病人数据（入院、出院）
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncNewInPatient()
        {
            var hospitalInfo = _hospitalListRepository.GetHospitalInfo();
            if (hospitalInfo == null)
            {
                return false;
            }
            //获取API接口数据
            var inpatientData = GetInpateintDataByDateTime("N", hospitalInfo.HospitalID);
            if (inpatientData == null || inpatientData.Count < 1)
            {
                return false;
            }
            //获取字典信息
            var hospitalBaseDict = await GetHospitalBaseDict(hospitalInfo.HospitalID);
            //数据同步
            return await SyncInPatientData(hospitalBaseDict, inpatientData, hospitalInfo.HospitalID);
        }

        #endregion 同步接口

        /// <summary>

        /// <summary>
        /// 同步在院数据
        /// </summary>
        /// <param name="hospitalBaseDict"></param>
        /// <param name="inpatientData">从中介库中获取数据</param>
        /// <returns></returns>
        private async Task<bool> SyncInPatientData(HospitalBaseDictView hospitalBaseDict, List<InPatientDataView> inpatientData, string hospitalID)
        {
            #region "初始宣告"
            var language = _config.Value.Language;
            //同步作业名称
            var inpatientJobName = "inpatient";
            //用来判断作业是否可以执行
            var inpatientJobStatus = false;
            //加打日志,但判断执行到第几笔数据
            var count = 0;
            var result = false;

            #endregion "初始宣告"

            #region "数据加载"

            //获得病人标签(护理级别使用)
            var patientListIcon = await _patientListIconRepository.GetAllAsync<PatientListIconInfo>();
            //过滤病人标签
            patientListIcon = patientListIcon.Where(m => m.HospitalID == hospitalID
                                                && m.IdentifyCategory.Contains("NursingLevel")
                                                && m.DeleteFlag != "*").ToList();
            var modifyPersonID = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "ModifyPersonID").Result;
            //取得病人事件
            var eventSettings = await _eventSettingRepository.GetAllAsync<EventSettingInfo>();
            var userList = await _userRepository.GetAllAsync<UserInfo>();

            #endregion "数据加载"

            _logger.Info("同步在院病人[" + inpatientData.Count() + "]人");
            foreach (var item in inpatientData) //记录作业正在执行日志
            {
                count++;
                _logger.Info("开始同步第" + count + "个患者住院信息 CaseNumber:" + item.CaseNumber + " ChartNo:" + item.ChartNo);
                if (item.DischargeDate == null)
                {
                    if (string.IsNullOrEmpty(item.StationCode) || string.IsNullOrEmpty(item.DepartmentCode) || string.IsNullOrEmpty(item.BedNumber))
                    {
                        _logger.Error("住院信息 CaseNumber: " + item.CaseNumber + " ChartNo: " + item.ChartNo + "科室、病区、床位信息个别为空");
                        continue;
                    }
                }

                //记录同步在院病人开始，写入作业
                inpatientJobStatus = _jobLogService.GetJobStatus(item.CaseNumber, inpatientJobName, item.ChartNo);
                //表示不需要同步
                if (!inpatientJobStatus)
                {
                    continue;
                }
                //同步在院病人数据
                try
                {
                    result = await SyncInpatientDetail(item, hospitalBaseDict, patientListIcon, eventSettings, userList, hospitalID, modifyPersonID);
                }
                catch (Exception ex)
                {
                    _logger.Error("病人CaseNumber" + item.CaseNumber + " ChartNo:" + item.ChartNo + "同步失败！" + ex.ToString());
                }
                //记录同步完成，移除作业
                _jobLogService.RemoveJob(item.CaseNumber, inpatientJobName, item.ChartNo);
                _logger.Info("病人CaseNumber" + item.CaseNumber + " ChartNo:" + item.ChartNo + "同步完成，移除作业");
            }
            return result;
        }

        //数据同步明细
        private async Task<bool> SyncInpatientDetail(InPatientDataView hisInpatientData, HospitalBaseDictView hospitalBaseDictView
            , List<PatientListIconInfo> patientListIconList
            , List<EventSettingInfo> eventSettings
            , List<UserInfo> userList
            , string hospitalID, string modifyPersonID)
        {
            #region "初始化"

            //是否需要更新标记
            bool upDataDBFlag = false;
            //写日志用
            string tableName = "Inpatient";
            MarkViewList = new List<MarkView>();
            InPatientChangeList = new List<InPatientChangeViewInfo>();
            var patientProfileList = new List<PatientProfile>();
            var sendMessageList = new List<MessageModel>();
            SyncInpatientDataView syncInpatientDataView = new SyncInpatientDataView();

            #endregion "初始化"

            #region 数据准备

            var patient = " CaseNumber[" + hisInpatientData.CaseNumber + "]  ChartNo [" + hisInpatientData.ChartNo + "]";
            //获取年龄对应的AssessListID
            var ageAssessListID = 0;
            var resultAgeAssessListID = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "AgeAssessListID");
            if (!StringCheck.IsNumeric(resultAgeAssessListID))
            {
                _logger.Error("年龄AssessListID在表appConfigSetting里面配置错误，settingCode(AgeAssessListID)");
            }

            ageAssessListID = int.Parse(resultAgeAssessListID);
            _logger.Info(patient + "获取病区，床位基本数据");
            var hospitalBaseDict = GetinPatientBaseDict(hisInpatientData, hospitalBaseDictView);
            if (!hisInpatientData.DischargeDate.HasValue)
            {
                _logger.Info("同步病人基本信息" + hisInpatientData.CaseNumber + "] ChartNo [" + hisInpatientData.ChartNo + "]");
                if (!await SyncPatientBase(hisInpatientData))
                {
                    _logger.Info("同步病人基本信息" + hisInpatientData.CaseNumber + "] ChartNo [" + hisInpatientData.ChartNo + "]同步失败");
                    return false;
                }
                // 判断获取的病人病区、科室、床位信息是否正常
                if (!CheckPatientBaseDict(hospitalBaseDict))
                {
                    _logger.Info("病人CaseNumber：" + hisInpatientData.CaseNumber + "病人病区、科室、床位信息异常");
                    return true;
                }
            }

            //获取病人基本信息，没有基本信息，不同步数据
            _logger.Info(patient + "获取病人基本信息");
            var patientBasicData = await _PatientBasicDataRepository.GetAsync(hospitalID, hisInpatientData.ChartNo);
            if (patientBasicData == null)
            {
                _logger.Error(tableName + " 表: PatientBasicData 病案号 CaseNumber ["
                + hisInpatientData.CaseNumber + "] ChartNo [" + hisInpatientData.ChartNo + "] 查询病人基本信息错误!");
                return false;
            }

            _logger.Info(tableName + "开始同步 CaseNumber ["
                + hisInpatientData.CaseNumber + "]  ChartNo [" + hisInpatientData.ChartNo + "]");
            //获取病人入院记录
            var tempPatient = _IInpatientDataRepository.GetInpatientIDByCaseNumber(hisInpatientData.CaseNumber);
            if (tempPatient != null && tempPatient.DischargeDate.HasValue && hisInpatientData.DischargeDate.HasValue)
            {
                return true;
            }
            _logger.Info(patient + "判断病区，床位基本数据");
            if (!hisInpatientData.DischargeDate.HasValue)
            {
                if (hospitalBaseDict != null && hospitalBaseDict.BedList != null && hospitalBaseDict.BedList.Count > 0)
                {
                    _logger.Info("床位信息" + ListToJson.ToJson(hospitalBaseDict.BedList));
                    //判断床位上是否有其他在院病人，如果有，先移除
                    var inPatientByBedList = await _IInpatientDataRepository.GetInpatientListByBedIDAsync(hospitalBaseDict.BedList[0].ID, hospitalID);

                    foreach (var item in inPatientByBedList)
                    {
                        if (item.CaseNumber == hisInpatientData.CaseNumber)
                        {
                            continue;
                        }
                        //item.BedID = 0;
                        //item.BedNumber = "";
                        //先让在床病人出科，让新病人入科
                        item.InHospitalStatus = 50;
                    }
                }
            }
            _logger.Info(patient + "医生的工号");
            //转换医生的工号，为登录账号
            var userInfo = userList.Where(m => m.PhysicianID == hisInpatientData.AttendingPhysicianID).FirstOrDefault();
            if (userInfo != null)
            {
                hisInpatientData.AttendingPhysicianID = userInfo.UserID.ToString();
            }

            #endregion 数据准备

            //计算年龄Detail
            hisInpatientData = GetInpatientAgeDetail(hisInpatientData);
            //如果不存在进行新增住院病人
            if (tempPatient == null)
            {
                _logger.Info("新增住院病人:" + hisInpatientData.CaseNumber);
                var cccInpatientData = await NewInpatientData(hisInpatientData, hospitalBaseDict, patientBasicData, eventSettings, patientListIconList, tableName, hospitalID);
                var inpatientLogs = await _inpatientLogRepository.GetByCaseNumberAsync(cccInpatientData.CaseNumber);
                //确认之前是否有PatientProfileMark,如果有，确认病区等基本信息
                await _patientProfileMarkService.UPDataPatientProfileMark(hisInpatientData.ChartNo, cccInpatientData.BedNumber, hospitalBaseDict.StationList[0].StationCode, hospitalBaseDict.DepartmentList[0].DepartmentCode);
                await _externalProfileCommonService.DeletePatientProfileOwnDueDayAsync(cccInpatientData.ChartNo, cccInpatientData.ID);
                //新增住院并人增加Mark异动记录
                var markView = AddInpatientToProfileMark(cccInpatientData, hisInpatientData, patientBasicData);
                MarkViewList.Add(markView);
                var result = await CreateAddPatientProfile(cccInpatientData, patientListIconList, ageAssessListID, modifyPersonID);
                patientProfileList = result.Item1; //profile数据
                sendMessageList = result.Item2;//发送消息通知
                //写入院事件
                await SetInpatientLogAndEvent(inpatientLogs, cccInpatientData, cccInpatientData.StationID, cccInpatientData.DepartmentListID
                   , cccInpatientData.BedID
                  , cccInpatientData.BedNumber, cccInpatientData.AdmissionDate.Add(cccInpatientData.AdmissionTime), EVENTSETTING_ASSESSLISTID_ADMISSION, eventSettings);
                //增加诊断异动的Profile
                upDataDBFlag = true;
                _logger.Info("新增住院病人信息结束 ");
            }

            //如果病人出院，先处理，不再处理其它逻辑
            var dischargeFlag = false;
            if (hisInpatientData.DischargeDate.HasValue && !tempPatient.DischargeDate.HasValue)
            {
                _logger.Info("住院病人出院处理:" + hisInpatientData.CaseNumber);
                syncInpatientDataView = await SyncInpatientDischarge(tempPatient, hisInpatientData, eventSettings, hospitalID);
                upDataDBFlag = syncInpatientDataView.RetureFlag;
                dischargeFlag = true;
                MarkViewList = syncInpatientDataView.MarkViewList;
                InPatientChangeList = syncInpatientDataView.InPatientChangeViewList;
                upDataDBFlag = true;
            }

            //修改住院病人
            if (tempPatient != null && !dischargeFlag)
            {
                _logger.Info("修改住院病人 ,CaseNumber=" + hisInpatientData.CaseNumber);
                var result = await UpdateInpatientData(tempPatient, hisInpatientData, hospitalBaseDict, patientListIconList, patientBasicData.PatientID, tableName, eventSettings, hospitalID, modifyPersonID);
                upDataDBFlag = syncInpatientDataView.RetureFlag;
                upDataDBFlag = result.Item1;
                patientProfileList = result.Item2;
                sendMessageList = result.Item3;
                //确认之前是否有PatientProfileMark,如果有，确认病区等基本信息
                var upProfileResult = await _patientProfileMarkService.UPDataPatientProfileMark(hisInpatientData.ChartNo, hisInpatientData.BedNumber, hospitalBaseDict.StationList[0].StationCode, hospitalBaseDict.DepartmentList[0].DepartmentCode);
                if (upProfileResult)
                {
                    upDataDBFlag = true;
                }
                _logger.Info("修改住院病人结束,CaseNumber=" + hisInpatientData.CaseNumber);
            }
            //不需要更新，返回。
            if (!upDataDBFlag)
            {
                _logger.Info("不需要更新返回,CaseNumber=" + hisInpatientData.CaseNumber);
                return true;
            }

            try
            {
                _unitOfWork.SaveChanges();
                _unitOfWorkOut.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error(tableName + "CareNumber:" + hisInpatientData.CaseNumber + "同步失败||" + ex.ToString());
                return false;
            }

            //异动Mark
            CallMarkAPI(MarkViewList);
            //写PatientProfile
            _logger.Info("写PatientProfile");
            //呼叫Profile
            _commonHelper.AddProfile(patientProfileList);
            _logger.Info("调用病人信息发生变化,调用对应的API");
            //病人信息变化，调用相应的APi进行处理
            CheckInPatient(InPatientChangeList);
            //发送信息
            if (tempPatient != null)
            {
                _logger.Info("发送MQ");
                BackgroundJob.Enqueue(() => _mQCommonService.SendingMessage(sendMessageList, "2", tempPatient.ID, tempPatient.StationID));
            }
            return true;
        }

        private InPatientDataView GetInpatientAgeDetail(InPatientDataView hisInpatientData)
        {
            if (hisInpatientData.DateOfBirth == null)
            {
                return hisInpatientData;
            }
            var birthDate = hisInpatientData.DateOfBirth.Date;
            if (hisInpatientData.TimeOfBirth.HasValue)
            {
                birthDate = birthDate.Add(hisInpatientData.TimeOfBirth.Value);
            }
            var ageDetail = _getBirthDateService.GetAgeDetail(birthDate);
            hisInpatientData.AgeDetail = ageDetail;
            return hisInpatientData;
        }
        /// <summary>
        ///本病区没有的病人，住院状态置为50
        /// </summary>
        /// <param name="statinoCode"></param>
        /// <param name="hisInpatientDataViews"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<bool> SyncClearStationInPatient(string statinoCode, List<InPatientDataView> hisInpatientDataViews, string hospitalID)
        {
            if (hisInpatientDataViews == null || hisInpatientDataViews.Count <= 0)
            {
                return false;
            }
            var stationLists = await _IStationListRepository.GetSimpleList();
            var stationInfo = stationLists.Find(m => m.Code == statinoCode);
            if (stationInfo == null)
            {
                return false;
            }

            var inpatientDatas = await _IInpatientDataRepository.GetInpatientListByStationID(stationInfo.ID, hospitalID);
            if (inpatientDatas == null || inpatientDatas.Count <= 0)
            {
                return false;
            }
            inpatientDatas = inpatientDatas.Where(m => m.BedID != 0 || m.InHospitalStatus < 50).ToList();
            foreach (var item in inpatientDatas)
            {
                var hisInpatient = hisInpatientDataViews.Find(m => m.CaseNumber == item.CaseNumber);
                if (hisInpatient != null)
                {
                    continue;
                }
                //病人清单不呈现-有出院时间调整为出院60
                item.InHospitalStatus = 60;
                item.Modify("TongBu");
                try
                {
                    //A TransactionScope must be disposed on the same thread that it was created. 所以改异步保存为同步
                    _unitOfWork.SaveChanges();
                }
                catch (Exception ex)
                {
                    _logger.Error("保存失败(SyncClearStationInPatient)" + ex.ToString());
                    return false;
                }
            }
            return true;
        }

        //同步病人基本信息
        private async Task<bool> SyncPatientBase(InPatientDataView inPatientData)
        {
            var patientBasicList = new List<PatientBasicInfo>();
            var patientBasic = new PatientBasicInfo
            {
                ChartNo = inPatientData.ChartNo,
                PatientName = inPatientData.PatientName,
                Gender = inPatientData.Gender,
                DateOfBirth = inPatientData.DateOfBirth,
                IdentityID = inPatientData.IdentityID,
                NativePlace = inPatientData.NativePlace,
                NativePlaceCode = inPatientData.NativePlaceCode,
                BloodType = inPatientData.BloodType,
                TimeOfBirth = inPatientData.TimeOfBirth
            };
            patientBasicList.Add(patientBasic);
            var resultFlag = await _IPatientBasicService.SyncPatientBaseDetail(patientBasicList);
            return resultFlag == null ? false : true;
        }

        #region 获得接口数据

        /// <summary>
        /// 根据stationCode获取数据
        /// </summary>
        /// <param name="stationCode"></param>
        /// <returns></returns>
        private List<InPatientDataView> GetInpateintDataByStationCode(string stationCode, string hospitalID)
        {
            //呼叫APi获得数据
            var apiStr = _commonHelper.GetApiStr(1, "17");
            if (string.IsNullOrEmpty(apiStr))
            {
                _logger.Info(",获取API地址失败,接口编号-17");
                return null;
            }
            var data = new Dictionary<string, string>
            {
               { "StationCode",stationCode}
            };
            return GetInpatientApiData(apiStr, data, hospitalID);
        }

        /// <summary>
        /// 获取指定时间的在院病人数据
        /// </summary>
        /// <param name="allDataFlag"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private List<InPatientDataView> GetInpateintDataByDateTime(string allDataFlag, string hospitalID)
        {
            //呼叫APi获得数据
            var syncAPIConfigInfo = _syncAPIConfigRepository.GetSyncAPIConfigByApiID(1);
            var apiStr = syncAPIConfigInfo.APIAddress;
            if (string.IsNullOrEmpty(apiStr))
            {
                _logger.Info(",获取API地址失败,接口编号-1");
                return null;
            }
            //同步指定时间
            var resultSyncHour = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "SyncHour").Result;
            var syncHour = 1;
            if (StringCheck.IsNumeric(resultSyncHour))
            {
                syncHour = int.Parse(resultSyncHour);
            }

            var data = new Dictionary<string, string>
            {
                { "startDataTime", DateTime.Now.AddHours(-syncHour).ToString() },
                { "endDateTime", DateTime.Now.ToString() },
                { "allDataFlag", allDataFlag }
            };
            return GetInpatientApiData(apiStr, data, hospitalID);
        }

        /// <summary>
        /// 获取在院病人
        /// </summary>
        /// <param name="apiStr"></param>
        /// <param name="data"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private List<InPatientDataView> GetInpatientApiData(string apiStr, Dictionary<string, string> data, string hospitalID)
        {
            //创建集合
            var interconnect_Data = new List<InPatientDataView>();

            if (string.IsNullOrEmpty(apiStr))
            {
                _logger.Info("获取API地址失败");
                return interconnect_Data;
            }
            _logger.Info("获取在院病人同步 API:[" + apiStr + "]参数:" + ListToJson.ToJson(data));
            var resultData = "";
            //获取环境 ,1 开发环境
            var systemOperatingEnvironment = _appConfigSettingRepository.GetConfigSettingValue("Configs", "SystemOperatingEnvironment").Result;
            if (systemOperatingEnvironment != "1")
            {
                //呼叫API获得数据
                try
                {
                    resultData = _commonHelper.GetInterconnectData(apiStr, data);
                }
                catch (Exception ex)
                {
                    _logger.Error("呼叫API,失败 ，API:[" + apiStr + "]参数: " + ListToJson.ToJson(data) + ex.ToString());
                    return interconnect_Data;
                }
            }
            else
            {
                //resultData = _ILogInfoServices.GetLog("19")[0].Logs;
                //resultData = ReadFile.ReadTxt(@"E:\MockData\银川在院.json");
            }

            if (string.IsNullOrEmpty(resultData))
            {
                return interconnect_Data;
            }
            //打印接口
            var resultPringData = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "PrintInterfaceData").Result;

            if (resultPringData == "1")
            {
                _logger.Info("获取在院病人数据:" + resultData);
            }

            //将数据装化对象
            var responseResult = new ResponseResult();
            try
            {
                responseResult = JsonConvert.DeserializeObject<ResponseResult>(resultData);
            }
            catch (Exception ex)
            {
                _logger.Info("获取在院病人数据:" + resultData + "转换失败" + ex.ToString());
                return null;
            }

            if (responseResult.Data == null)
            {
                return interconnect_Data;
            }
            var responseResulData = responseResult.Data.ToString();
            if (responseResulData == "[]")
            {
                return interconnect_Data;
            }
            try
            {   //数据转化成InpatientDataView集合
                interconnect_Data = JsonConvert.DeserializeObject<List<InPatientDataView>>(responseResulData);
            }
            catch (Exception ex)
            {
                _logger.Error("Json格式化失败:" + ex.ToString());
                return interconnect_Data;
            }
            _logger.Info("转换Json数据完成，获得在院病人" + interconnect_Data.Count() + "条！");
            //过滤返回数据
            var interconnect_DataTemp = interconnect_Data.Where(m => !string.IsNullOrEmpty(m.DepartmentCode) && !string.IsNullOrEmpty(m.StationCode) && !string.IsNullOrEmpty(m.BedNumber)).ToList();
            _logger.Info("住院病人,有" + (interconnect_Data.Count() - interconnect_DataTemp.Count()).ToString() + "条数据不符合要求！");
            //将数据返回
            return interconnect_Data;
        }

        #endregion 获得接口数据

        #region 获取出院病人

        private List<InpatientInfoView> GetApiDataByDischargedInpateint(string apiStr, Dictionary<string, string> data, string hospitalID)
        {
            _logger.Info("获取出院病人同步 API:[" + apiStr + "]参数:" + ListToJson.ToJson(data));
            //呼叫API获得数据
            var resultData = _commonHelper.GetInterconnectData(apiStr, data);
            //打印接口
            var resultPringData = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "PrintInterfaceData").Result;

            if (resultPringData == "1")
            {
                _logger.Info("获取出院病人数据:" + resultData);
            }

            var result = new ResponseResult();
            //将数据装化对象
            result = JsonConvert.DeserializeObject<ResponseResult>(resultData);
            //var result= _ILogInfoServices.GetLog("10")[0].Logs;
            var Interconnect_Data = new List<InpatientInfoView>();
            try
            {   //数据转化成InpatientDataView集合
                Interconnect_Data = JsonConvert.DeserializeObject<List<InpatientInfoView>>(result.Data.ToString());
            }
            catch (Exception ex)
            {
                _logger.Error("Json格式化失败:" + ex.ToString());
                return Interconnect_Data;
            }
            _logger.Info("转换Json数据完成，获得出院病人" + Interconnect_Data.Count() + "条！");

            return Interconnect_Data;
        }

        #endregion 获取出院病人

        private async Task<HospitalBaseDictView> GetHospitalBaseDict(string hospitalID)
        {
            var hospitalDictView = new HospitalBaseDictView
            {
                StationList = await _IStationListRepository.GetAllAsync<StationListInfo>(),
                BedList = await _IBedListRepository.GetAllAsync<BedListInfo>(),//床位信息
                DepartmentList = await _IDepartmentListRepository.GetAllAsync<DepartmentListInfo>() //科室信息
            };
            return hospitalDictView;
        }

        //获取同步需要的基本字典信息
        private HospitalBaseDictView GetinPatientBaseDict(InPatientDataView inPatientData, HospitalBaseDictView hospitalDictView)
        {
            var hospitalDict = new HospitalBaseDictView();
            var medicalStationList = hospitalDictView.StationList; //病区字典
            var medicalDepartmentList = hospitalDictView.DepartmentList; //科室字典
            var medicalBedList = hospitalDictView.BedList;//床位字典

            //获取Medical中的科室信息
            var tempMedicalDepartmentList = medicalDepartmentList.Where(m => m.DepartmentCode == inPatientData.DepartmentCode).ToList();
            if (tempMedicalDepartmentList.Count != 1)
            {
                return null;
            }

            //获取Medical中的病区信息
            var tempMedicalStationListInfo = medicalStationList.Where(m => m.StationCode == inPatientData.StationCode).ToList();
            if (tempMedicalStationListInfo.Count != 1)
            {
                return null;
            }

            var tempMedicalBedList = new List<BedListInfo>();
            //先在获取Medical中的床位信息
            tempMedicalBedList = medicalBedList.Where(m => m.BedNumber == inPatientData.BedNumber && m.StationID == tempMedicalStationListInfo[0].ID).ToList();
            if (tempMedicalBedList.Count <= 0)
            {
                //确认是否有虚拟病区
                var tempVirtualStationList = _virtualStationListRepository.GetVirtualStationInfoByStationID(tempMedicalStationListInfo[0].ID);
                if (tempVirtualStationList.Count > 0)
                {
                    //获取病区对应虚拟病区列表
                    foreach (var item in tempVirtualStationList)
                    {
                        tempMedicalBedList = medicalBedList.Where(m => m.BedNumber == inPatientData.BedNumber && item.VirtualStationID == m.StationID).ToList();
                        //找到虚拟病区床位
                        if (tempMedicalBedList.Count > 0)
                        {
                            //重新赋值真实病区
                            //获取虚拟病区
                            tempMedicalStationListInfo = medicalStationList.Where(m => m.ID == item.VirtualStationID).ToList();
                            break;
                        }
                    }
                }
            }

            //复制对象
            if (tempMedicalBedList.Count != 1)
            {
                return null;
            }
            hospitalDict.StationList = tempMedicalStationListInfo;
            hospitalDict.DepartmentList = tempMedicalDepartmentList;
            hospitalDict.BedList = tempMedicalBedList;
            return hospitalDict;
        }

        /// <summary>
        /// 判断获取的病人病区、科室、床位信息是否正常
        /// </summary>
        /// <param name="hospitalBaseDict"></param>
        /// <returns></returns>
        private bool CheckPatientBaseDict(HospitalBaseDictView hospitalBaseDict)
        {
            if (hospitalBaseDict == null)
            {
                return false;
            }

            if (hospitalBaseDict.DepartmentList == null || hospitalBaseDict.DepartmentList.Count() <= 0)
            {
                return false;
            }
            if (hospitalBaseDict.StationList == null || hospitalBaseDict.StationList.Count <= 0)
            {
                return false;
            }
            if (hospitalBaseDict.BedList == null || hospitalBaseDict.BedList.Count <= 0)
            {
                return false;
            }
            return true;
        }

        /// <summary>
        /// 新增住院病人逻辑
        /// </summary>
        private async Task<InpatientDataInfo> NewInpatientData(
            InPatientDataView inPatientData, HospitalBaseDictView hospitalBaseDict
             , PatientBasicDataInfo patient, List<EventSettingInfo> eventSettingList
            , List<PatientListIconInfo> patientListIconList
            , string tableName, string hospitalID)
        {
            var inPatientChangeList = new List<InPatientChangeViewInfo>();
            var medicalDepartmentList = hospitalBaseDict.DepartmentList;
            var medicalStationList = hospitalBaseDict.StationList;
            var medicalBedList = hospitalBaseDict.BedList;
            var patientProfileList = new List<PatientProfile>();
            var inpatientChangeList = new List<InPatientChangeViewInfo>();
            var markViewList = new List<MarkView>();

            var t = new InpatientDataInfo();
            t.ID = t.GetId();
            t.PatientID = patient.PatientID;
            t.HospitalID = hospitalID;
            t.CaseNumber = inPatientData.CaseNumber ?? "";
            t.LocalCaseNumber = t.CaseNumber;
            t.ChartNo = inPatientData.ChartNo ?? "";
            t.NumberOfAdmissions = 0; //住院次数
            t.DepartmentListID = medicalDepartmentList[0].ID;
            t.StationID = medicalStationList[0].ID;
            t.BedID = medicalBedList[0].ID;
            t.BedNumber = inPatientData.BedNumber ?? "";
            t.ICUFlag = medicalBedList[0].ICUFlag ?? "";
            t.ICDCode = "";//新增病人数据，不写ICDCode ，为了区分数据是否从诊断接口过来
            t.Diagnosis = inPatientData.Diagnosis ?? "";
            t.AttendingPhysicianID = inPatientData.AttendingPhysicianID ?? "";
            t.Age = inPatientData.Age ?? 0;
            t.AgeDetail = inPatientData.AgeDetail;//不到1岁的显示X月X天-LS
            t.BillingPattern = inPatientData.BillingPattern ?? "";
            t.AdmissionDate = inPatientData.AdmissionDate; //住院日期
            t.AdmissionTime = inPatientData.AdmissionTime; //住院时间
            t.DeleteFlag = "";
            t.DischargeDate = null;
            t.DischargeTime = null;
            if (string.IsNullOrEmpty(inPatientData.NursingLevel))
            {
                t.NursingLevel = "";
            }
            else
            {
                t.NursingLevel = inPatientData.NursingLevel;
            }
            //获取之前的住院次数
            var inpatientDataInfos = await _IInpatientDataRepository.GetListByChartNo(t.ChartNo);
            if (inpatientDataInfos == null || inpatientDataInfos.Count <= 0)
            {
                t.NumberOfAdmissions = 1;
            }
            else
            {
                t.NumberOfAdmissions = inpatientDataInfos.Count + 1;
            }
            //新入院病人-在院状态（30)
            t.InHospitalStatus = 30;
            t.NursingProcedureCode = ""; //病人护理程序码
            t.ModifyPersonID = MODIFYPERSONID;
            t.ModifyDate = DateTime.Now;
            _unitOfWork.GetRepository<InpatientDataInfo>().Insert(t);
            inPatientChangeList.Add(InPatientChangeView(t.ID, t.CaseNumber, "新增", PatientType.PatientInsert));//存储病人变化信息
            return t;
        }

        //修改病人信息
        private async Task<Tuple<bool, List<PatientProfile>, List<MessageModel>>> UpdateInpatientData(InpatientDataInfo medicalInPatientData
            , InPatientDataView inPatientData, HospitalBaseDictView hospitalBaseDict
            , List<PatientListIconInfo> patientListIconList
            , string patientID, string tableName, List<EventSettingInfo> eventSettings
            , string hospitalID, string modifyPersonID)
        {
            var patientProfileList = new List<PatientProfile>();
            var upDataDBFlag = false;
            bool otherFlag = false;
            var messageModelList = new List<MessageModel>();

            if (hospitalBaseDict == null)
            {
                return new Tuple<bool, List<PatientProfile>, List<MessageModel>>(false, null, null);
            }

            //从字典中获取数据,外层已经判断了是否有数据
            var departmentInfo = hospitalBaseDict.DepartmentList[0];
            var stationInfo = hospitalBaseDict.StationList[0];
            var bedInfo = hospitalBaseDict.BedList[0];
            var oldSataionID = medicalInPatientData.StationID;
            var oldDepartmentID = medicalInPatientData.DepartmentListID;
            var oldBedID = medicalInPatientData.BedID;
            var oldBedNumber = medicalInPatientData.BedNumber;
            //换床信息需要，取最近的患者事件的ID和Number
            if (oldBedID == 0 && oldBedNumber == "")
            {
                var eventBedInfo = await _patientEventRepository.GetLastPatientEvent(medicalInPatientData.ID);
                oldBedID = eventBedInfo.BedID;
                oldBedNumber = eventBedInfo.BedNumber;
            }
            var inpatientLog = await _inpatientLogRepository.GetByCaseNumberAsync(medicalInPatientData.CaseNumber);

            _logger.Info(tableName + " 更新住院病人CaseNumber[" + medicalInPatientData.CaseNumber + "]信息!");
            //先进行Mark处理,床位,科室发生变化
            if (medicalInPatientData.StationID != stationInfo.ID
               || medicalInPatientData.BedNumber != inPatientData.BedNumber)
            {
                var markView = MondifyInpateintToProfileMark(medicalInPatientData, inPatientData);
                MarkViewList.Add(markView);

                _logger.Info(medicalInPatientData.CaseNumber + "|病区或床位发生变化，从stationID[" + medicalInPatientData.StationID + "]更新为[" + stationInfo.ID
                    + "],BedNumber[CCC:" + medicalInPatientData.BedNumber + "]->[HIS:" + inPatientData.BedNumber
                    + "],BedID[CCC:" + medicalInPatientData.BedID + "]->[HIS:" + inPatientData.BedCode + "]"
                    , "TongBu", false);

                _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "NursingLevel"
                    , medicalInPatientData.CaseNumber + "|病区或床位发生变化，从stationID[" + medicalInPatientData.StationID + "]更新为[" + stationInfo.ID
                    + "],BedNumber[CCC:" + medicalInPatientData.BedNumber + "]->[HIS:" + inPatientData.BedNumber
                    + "],BedID[CCC:" + medicalInPatientData.BedID + "]->[HIS:" + inPatientData.BedCode + "]"
                    , "TongBu", false);
            }
            if (medicalInPatientData.AdmissionDate != inPatientData.AdmissionDate
             || medicalInPatientData.AdmissionTime != inPatientData.AdmissionTime)
            {
                var tempOldAdmissionDate = medicalInPatientData.AdmissionDate;
                var tempOldAdmissionTime = medicalInPatientData.AdmissionTime;
                _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "AdmissionDate"
                   , medicalInPatientData.CaseNumber + "|入院日期发生改变，从 [" + medicalInPatientData.AdmissionDate.Add(medicalInPatientData.AdmissionTime) +
                   "]更新为[" + inPatientData.AdmissionDate.Add(inPatientData.AdmissionTime)
                   , "TongBu", false);
                medicalInPatientData.AdmissionDate = inPatientData.AdmissionDate;
                medicalInPatientData.AdmissionTime = inPatientData.AdmissionTime;
                //写入院事件
                await _patientEventCommonService.DelInpatientEvent(medicalInPatientData.ID, 2872, tempOldAdmissionDate, tempOldAdmissionTime, "TongBu");

                await SetInpatientLogAndEvent(inpatientLog, medicalInPatientData, medicalInPatientData.StationID, medicalInPatientData.DepartmentListID
                     , medicalInPatientData.BedID, medicalInPatientData.BedNumber, medicalInPatientData.AdmissionDate.Add(medicalInPatientData.AdmissionTime), EVENTSETTING_ASSESSLISTID_ADMISSION, eventSettings);
                upDataDBFlag = true;
            }
            if (medicalInPatientData.StationID != stationInfo.ID)
            {
                _logger.Info("病人CaseNumber[" + medicalInPatientData.CaseNumber + "|病区发生变化" +
                 "，从[" + medicalInPatientData.StationID + "]更新为[" + stationInfo + "]"
                 + "转科，停止护理问题");
                _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "NursingLevel"
                    , medicalInPatientData.CaseNumber + "|病区发生变化，从[" + medicalInPatientData.StationID + "]更新为[" + stationInfo + "]"
                    , "TongBu", false);
                //如果病区发生变化，停止护理问题
                var inPatientChange = InPatientChangeView(medicalInPatientData.ID, medicalInPatientData.CaseNumber, medicalInPatientData.NursingLevel, PatientType.PatientTransfer);
                InPatientChangeList.Add(inPatientChange);//存储病人变化信息
                medicalInPatientData.StationID = stationInfo.ID;
                upDataDBFlag = true;
                //转科 ，护理流程码置空
                medicalInPatientData.NursingProcedureCode = "";

                var apiSettings = await _aPISettingRepository.GetAllAsync<APISettingInfo>();
                var url = apiSettings.Find(m => m.SettingCode == "MQSolutionServer")?.SettingValue;
                var path = apiSettings.Find(m => m.SettingCode == "PushMessage")?.SettingValue;
                // 发送患者转科消息
                var sendMsgView = new SendMessageView
                {
                    MessageType = MessageType.BROADCAST,
                    ExchangeName = "PatientTransfer",
                    Body = medicalInPatientData.ID
                };
                HttpHelper.HttpPostAsync(url + path, ListToJson.ToJson(sendMsgView), "application/json");
            }

            if (medicalInPatientData.BedID != bedInfo.ID)
            {
                _logger.Info("CaseNumber:" + medicalInPatientData.CaseNumber + "BedID变化,CCCbedID:" + medicalInPatientData.BedID + "需要更新的BedID:" + bedInfo.ID);
                _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "NursingLevel"
                    , medicalInPatientData.CaseNumber + "|床位发生变化，BedID从[" + medicalInPatientData.BedID + "]更新为[" + bedInfo.ID + "]", "TongBu", false);
                medicalInPatientData.BedID = bedInfo.ID;
                otherFlag = true;
                upDataDBFlag = true;
            }
            if (medicalInPatientData.BedNumber != (inPatientData.BedNumber ?? ""))
            {
                _logger.Info("CaseNumber:" + medicalInPatientData.CaseNumber + " BedNumber变化,CCCBedNumber:" + medicalInPatientData.BedNumber + "需要更新的BedNumber:" + inPatientData.BedNumber ?? "");
                _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "NursingLevel"
                   , medicalInPatientData.CaseNumber + "|床位发生变化，BedNumber从[" + medicalInPatientData.BedNumber + "]更新为[" + inPatientData.BedNumber ?? "" + "]"
                   , "TongBu", false);
                medicalInPatientData.BedNumber = inPatientData.BedNumber ?? "";
                upDataDBFlag = true;
                otherFlag = true;
            }
            if (medicalInPatientData.DepartmentListID != departmentInfo.ID)
            {
                _logger.Info("CaseNumber:" + medicalInPatientData.CaseNumber + "DepartmentListID变化");
                medicalInPatientData.DepartmentListID = departmentInfo.ID;
                //转科 ，护理流程码置空
                medicalInPatientData.NursingProcedureCode = "";
                otherFlag = true;
                upDataDBFlag = true;
            }

            if (medicalInPatientData.ChartNo != (inPatientData.ChartNo ?? ""))
            {
                _logger.Info("CaseNumber:" + medicalInPatientData.CaseNumber + "ChartNo变化");
                medicalInPatientData.ChartNo = inPatientData.ChartNo ?? "";
                otherFlag = true;
                upDataDBFlag = true;
            }

            if (medicalInPatientData.PatientID != (patientID ?? ""))
            {
                _logger.Info("PatientID变化");
                medicalInPatientData.PatientID = patientID ?? "";
                otherFlag = true;
                upDataDBFlag = true;
            }

            if (medicalInPatientData.ICUFlag != (bedInfo.ICUFlag ?? ""))
            {
                _logger.Info("ICUFlag变化");
                medicalInPatientData.ICUFlag = bedInfo.ICUFlag ?? "";
                otherFlag = true;
                upDataDBFlag = true;
            }
            //病人转科后，会将其状态置成50，转科后更新病人信息会调用该方法，所以在此处将不是“在科”的病人状态重置为30
            if (medicalInPatientData.InHospitalStatus != 30)
            {
                medicalInPatientData.InHospitalStatus = 30;
            }
            var iCDCode = (medicalInPatientData.ICDCode ?? "").Trim();
            //病人入院后，如果诊断编码为空，才进行诊断更新，说明可能没有从诊断接口获取诊断数据
            //如果ICDCode存在，说明已经从诊断接口获得了数据，以诊断接口的数据为准
            if (medicalInPatientData.Diagnosis != (inPatientData.Diagnosis ?? "") && string.IsNullOrEmpty(iCDCode))
            {
                medicalInPatientData.Diagnosis = inPatientData.Diagnosis ?? "";
                otherFlag = true;
                upDataDBFlag = true;
            }

            if (medicalInPatientData.BillingPattern != (inPatientData.BillingPattern ?? ""))
            {
                medicalInPatientData.BillingPattern = inPatientData.BillingPattern ?? "";
                otherFlag = true;
                upDataDBFlag = true;
            }

            if (medicalInPatientData.Age != (inPatientData.Age ?? 0))
            {
                medicalInPatientData.Age = inPatientData.Age ?? 0;
                otherFlag = true;
                upDataDBFlag = true;
            }

            if (medicalInPatientData.AgeDetail != (inPatientData.AgeDetail ?? ""))
            {
                medicalInPatientData.AgeDetail = inPatientData.AgeDetail;
                otherFlag = true;
                upDataDBFlag = true;
            }

            if (!string.IsNullOrEmpty(inPatientData.AttendingPhysicianID) && medicalInPatientData.AttendingPhysicianID != inPatientData.AttendingPhysicianID)
            {
                medicalInPatientData.AttendingPhysicianID = inPatientData.AttendingPhysicianID;
                otherFlag = true;
                upDataDBFlag = true;
            }

            //护理级别进行转换
            if (!string.IsNullOrEmpty(inPatientData.NursingLevel) && medicalInPatientData.NursingLevel.Trim() != inPatientData.NursingLevel.Trim())
            {
                _logger.Info("NursingLevel"
                 , medicalInPatientData.CaseNumber + "|护理级别发生变化，从[" + medicalInPatientData.NursingLevel + "]更新为[" + inPatientData.NursingLevel + "]"
                 , "TongBu", false);
                _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "NursingLevel"
                    , medicalInPatientData.CaseNumber + "|护理级别发生变化，从[" + medicalInPatientData.NursingLevel + "]更新为[" + inPatientData.NursingLevel + "]"
                    , "TongBu", false);
                medicalInPatientData.NursingLevel = inPatientData.NursingLevel;
                //如果护理级别发生变化，停止护理问题
                _logger.Info("病人CaseNumber[" + medicalInPatientData.CaseNumber + "]" + "护理级别发生变化，停止护理问题API");
                var inPatientChange = InPatientChangeView(medicalInPatientData.ID, medicalInPatientData.CaseNumber, medicalInPatientData.NursingLevel, PatientType.PatientLevelChange);
                InPatientChangeList.Add(inPatientChange);//存储病人变化信息
                //增加PatientProfiles数据(护理级别变化)
                patientProfileList.Add(CreateNursingLeveProfile(medicalInPatientData, patientListIconList, hospitalID));
                upDataDBFlag = true;
            }
            //处理，出院后，又不出的问题
            if (!inPatientData.DischargeDate.HasValue && medicalInPatientData.DischargeDate.HasValue)
            {
                medicalInPatientData.DischargeDate = null;
                medicalInPatientData.DischargeTime = null;
                upDataDBFlag = true;
            }

            if (!upDataDBFlag)
            {
                return new Tuple<bool, List<PatientProfile>, List<MessageModel>>(false, null, null);
            }

            medicalInPatientData.ModifyPersonID = MODIFYPERSONID;
            medicalInPatientData.ModifyDate = DateTime.Now;
            _logger.Info("更新在院病人数据:ChartNo" + medicalInPatientData.ChartNo);

            if (otherFlag)
            {
                InPatientChangeList.Add(InPatientChangeView(medicalInPatientData.ID, medicalInPatientData.CaseNumber, medicalInPatientData.NursingLevel, PatientType.PatientOtherChange)); //存储更新的病人信息
            }
            //写病人事件
            await SetInPatientEvent(inpatientLog, medicalInPatientData, oldSataionID, oldDepartmentID, oldBedID, oldBedNumber, eventSettings);
            _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "UpdateInpatientData"
                    , medicalInPatientData.CaseNumber + "|更新病人信息"
                    , "TongBu", false);
            return new Tuple<bool, List<PatientProfile>, List<MessageModel>>(true, patientProfileList, messageModelList);
        }

        /// <summary>
        /// 处理病人正常住院逻辑
        /// </summary>
        /// <param name="medicalInPatientData"></param>
        /// <param name="inPatientData"></param>
        /// <param name="eventSettings"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<SyncInpatientDataView> SyncInpatientDischarge(InpatientDataInfo medicalInPatientData
            , InPatientDataView inPatientData, List<EventSettingInfo> eventSettings, string hospitalID)
        {
            var patientProfileList = new List<PatientProfile>();
            var inpatientChangeList = new List<InPatientChangeViewInfo>();
            var markViewList = new List<MarkView>();
            medicalInPatientData.DischargeDate = inPatientData.DischargeDate;//出院日期
            medicalInPatientData.DischargeTime = inPatientData.DischargeTime;//出院时间
            if (!medicalInPatientData.InHospitalStatus.HasValue || medicalInPatientData.InHospitalStatus.Value < 40)
            {
                medicalInPatientData.InHospitalStatus = 40;
            }
            //病人出院，更新Mark
            var markView = DischargeToProfileMark(medicalInPatientData, hospitalID);
            markViewList.Add(markView);
            _logger.Info("出院病人信息：" + ListToJson.ToJson(medicalInPatientData));
            var dischargeDateTime = CreateDateTime(medicalInPatientData.DischargeDate, medicalInPatientData.DischargeTime);
            //写InpatientLog和PatientEvent
            await InsertInpatientEventInfo(medicalInPatientData, "Discharge", dischargeDateTime, eventSettings);
            //处理病人出院，呼叫信息
            var inPatientChange = InPatientChangeView(medicalInPatientData.ID, medicalInPatientData.CaseNumber, medicalInPatientData.NursingLevel, PatientType.PatientDischarged);
            inpatientChangeList.Add(inPatientChange);
            var syncInpatientDataView = new SyncInpatientDataView
            {
                RetureFlag = true,
                PatientProfileList = patientProfileList,
                InPatientChangeViewList = inpatientChangeList,
                MarkViewList = markViewList
            };
            return syncInpatientDataView;
        }

        private InPatientChangeViewInfo InPatientChangeView(string inPatientId, string caseNumber, string nursingLevel, PatientType patientType)
        {
            var inpatientChangeViewInfo = new InPatientChangeViewInfo()
            {
                InPatientID = inPatientId,
                CaseNumber = caseNumber,
                PatientType = patientType,
                NursingLevel = nursingLevel
            };
            return inpatientChangeViewInfo;
        }

        /// <summary>
        /// 生成DateTime
        /// </summary>
        /// <param name="DischargeDate"></param>
        /// <param name="dateTime"></param>
        /// <returns></returns>
        private DateTime CreateDateTime(DateTime? Dates, TimeSpan? Times)
        {
            var times = new TimeSpan(0, 0, 0);
            var dates = new DateTime(1900, 01, 01);
            if (Times.HasValue)
            {
                _logger.Info("转换时间:" + Times.ToString());
                times = new TimeSpan(Times.Value.Hours, Times.Value.Minutes, Times.Value.Seconds);
            }
            else
            {
                _logger.Info("时间为空");
            }

            if (Dates.HasValue)
            {
                _logger.Info("转换日期" + Dates.ToString());
                dates = dates.Date.Add(times);
            }
            else
            {
                _logger.Info("日期为空");
            }

            return dates;
        }

        private PatientProfile CreateNursingLeveProfile(InpatientDataInfo inPatient, List<PatientListIconInfo> patientListIconList, string hospitalID)
        {
            var nursingLevel = "NursingLevel";
            if (!string.IsNullOrEmpty(inPatient.NursingLevel))
            {
                nursingLevel += inPatient.NursingLevel;
            }
            var patientListIcon = patientListIconList.Where(m => m.IdentifyCategory == nursingLevel).FirstOrDefault();
            if (patientListIcon == null)
            {
                return null;
            }
            return CreateProfile(inPatient, nursingLevel, patientListIcon.IdentifyID.Value, "", hospitalID);
        }

        private PatientProfile CreateProfile(InpatientDataInfo inPatient
            , string subSerialNumber, int assessListID, string assessValue, string hospitalID)
        {
            var patientProfile = new PatientProfile
            {
                HospitalID = hospitalID,
                InpatientID = inPatient.ID,
                CaseNumber = inPatient.CaseNumber,
                ChartNo = inPatient.ChartNo,
                PatientID = inPatient.PatientID,
                ModelName = "HIS",
                Source = "I",
                ProfileDate = inPatient.AdmissionDate,
                ProfileTime = inPatient.AdmissionTime,
                AutoAddFlag = "",
                Note = "",
                ModifyPersonID = MODIFYPERSONID,
                SerialNumber = inPatient.CaseNumber + "_" + subSerialNumber,
                AssessListID = assessListID,
                AssessValue = assessValue
            };
            return patientProfile;
        }

        //新增Mark数据组装
        private MarkView AddInpatientToProfileMark(InpatientDataInfo inpatient
            , InPatientDataView hisInPatient, PatientBasicDataInfo patient
            , string hospitalID)
        {
            Dictionary<string, string> dict = new Dictionary<string, string>
            {
                { "chartNo", inpatient.ChartNo },
                { "patientName", patient.PatientName },
                { "gender", patient.Gender },
                { "nursingLevel", inpatient.NursingLevel },
                { "bedNumber", inpatient.BedNumber },
                { "stationCode", hisInPatient.StationCode },
                { "departmenCode", hisInPatient.DepartmentCode },
                { "hospitalID", hospitalID },
                { "admissionDate", hisInPatient.AdmissionDate.Add(hisInPatient.AdmissionTime).ToString() }
            };

            //从配置当中获取数据 梁宝华 2020-04-29
            var inpatientProfileMarkAPI = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "InpatientProfileMarkAPI").Result;

            var markData = GetKeyValue(dict);
            var markView = new MarkView
            {
                InpatientData = inpatient,
                Url = inpatientProfileMarkAPI,
                MarkDatas = markData,
                DataType = "新增住院病人"
            };
            return markView;
        }

        /// <summary>
        /// 转科\转床，Mark数据组装
        /// </summary>
        /// <param name="inpatient"></param>
        /// <param name="hisInpatient"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private MarkView CreateInpateintMark(InpatientDataInfo inpatient, InPatientDataView hisInpatient, string hospitalID)
        {
            Dictionary<string, string> dict = new Dictionary<string, string>
            {
                { "chartNo", inpatient.ChartNo },
                { "nursingLevel", inpatient.NursingLevel },
                { "bedNumber", hisInpatient.BedNumber },
                { "stationCode", hisInpatient.StationCode },
                { "departmenCode", hisInpatient.DepartmentCode },
                { "hospitalID", hospitalID }
            };

            var transferProfileMarkAPI = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "TransferProfileMarkAPI").Result;

            var markData = GetKeyValue(dict);
            var markView = new MarkView
            {
                InpatientData = inpatient,
                Url = transferProfileMarkAPI,
                MarkDatas = markData,
                DataType = "转科、转床"
            };
            return markView;
        }

        //出院ProfileMark
        private MarkView DischargeToProfileMark(InpatientDataInfo inpatient, string hospitalID)
        {
            Dictionary<string, string> dict = new Dictionary<string, string>
            {
                { "chartNo", inpatient.ChartNo }
            };
            var dischargeProfileMarkAPI = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "DischargeProfileMarkAPI").Result;

            var markData = GetKeyValue(dict);
            var markView = new MarkView
            {
                InpatientData = inpatient,
                Url = dischargeProfileMarkAPI,
                MarkDatas = markData,
                DataType = "出院"
            };
            return markView;
        }

        /// <summary>
        /// Mark数据呼叫API
        /// </summary>
        /// <param name="markViews"></param>
        private void CallMarkAPI(List<MarkView> markViews)
        {
            if (markViews == null || markViews.Count <= 0)
            {
                return;
            }
            foreach (var markView in markViews)
            {
                _logger.Info("异动Mark,CaseNumber=" + markView.InpatientData.CaseNumber + " 异动类型DataType:" + markView.DataType);
                if (markView.Url == "")
                {
                    _logger.Error("呼叫Mark，API为空,数据类型：" + markView.DataType);
                    return;
                }

                var url = markView.Url + "?" + markView.MarkDatas;
                _logger.Info("更新MarkUrl:" + url);
                try
                {
                    //写同步日志
                    var syncLog = _commonHelper.SaveLog(url,
                        "", markView.InpatientData.ID, markView.InpatientData.CaseNumber, _unitOfWork, true, true);
                    WebRequestSugar wrs = new WebRequestSugar();
                    string result = wrs.SendObjectAsJsonInBody(url, null);
                    //更新同步日志状态
                    _commonHelper.GetAPIExecResult(result, syncLog, _unitOfWork);
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "写ProfileMark失败，API" + url);
                    return;
                }
            }
        }

        private string GetKeyValue(Dictionary<string, string> dict)
        {
            string result = "";
            List<string> list = new List<string>();
            foreach (var item in dict)
            {
                list.Add(item.Key + "=" + item.Value);
            }
            result = string.Join("&", list.ToArray());
            return result;
        }
        /// <summary>
        /// 插入患者事件 --写患者事件的同时，会写一笔InpatientLog日志到表中
        /// </summary>
        /// <param name="inpatient"></param>
        /// <param name="logCode"></param>
        /// <param name="logDateTime"></param>
        /// <param name="eventSettings"></param>
        /// <returns></returns>
        private async Task InsertInpatientEventInfo(InpatientDataInfo inpatient, string logCode, DateTime logDateTime
            , List<EventSettingInfo> eventSettings)
        {
            var setting = eventSettings.Find(m => m.LogCode == logCode);
            if (setting == null)
            {
                _logger.Error("病人事件LogCode配置异常,LogCode:" + logCode + "请确认EventSetting是否已经配置");
                return;
            }
            var assessListIDs = new List<int>
            {
                2873,2876,2872
            };
            //判断出院事件是否存在
            if (assessListIDs.Contains(setting.AssessListID))
            {
                var patientEvents = await _patientEventRepository.GetEventByAssessListIDAsync(inpatient.ID, setting.AssessListID);
                foreach (var item in patientEvents)
                {
                    await _patientEventCommonService.DeleteInpatientEventAndLogAsync(item.InpatientID, item.PatientEventID, _config.Value.HospitalID, _config.Value.Language, "TongBu");
                }
            }
            //远程调用MedicalAPI中的保存患者事件的公共方法
            await _patientEventCommonService.CallEventAPI(inpatient, inpatient.StationID, inpatient.DepartmentListID, inpatient.BedID, inpatient.BedNumber
                , logDateTime, setting.AssessListID, logCode, "", inpatient.ID + logCode);
        }

        /// <summary>
        /// 同步出院病人，这个是银川医生下医嘱的出院时间
        /// </summary>
        /// <param name="inpatientData"></param>
        /// <returns></returns>
        private async Task<bool> SyncDischargedPatient(List<InpatientInfoView> inpatientData, string hospitalID)
        {
            _logger.Info("同步出院病人[" + inpatientData.Count() + "]人");
            var eventSettings = await _eventSettingRepository.GetAllAsync<EventSettingInfo>();
            var caseNumberArray = inpatientData.Select(m => m.CaseNumber).ToArray();
            //获取当前同步出院病人的患者事件日志集合
            var inpatientLogs = await _inpatientLogRepository.GetByCaseNumbersAsync(caseNumberArray);
            var dischargeFiag = false;
            //获得病人标签
            foreach (var item in inpatientData) //记录作业正在执行日志
            {
                _logger.Info("开始同步住院号为:" + item.CaseNumber + "的患者出院信息");
                var inpatientDataInfo = _IInpatientDataRepository.GetInpatientDataByCaseNumber(item.CaseNumber);
                if (inpatientDataInfo == null)
                {
                    continue;
                }
                //获取病人在院病人数据
                var inpatientCount = await _IInpatientDataRepository.GetInpatientCountByStationID(inpatientDataInfo.StationID);
                //如果只有一个病人，则判断这个病人是否在院
                if (inpatientCount == 1)
                {
                    //获取病区在院病人
                    var hisInpatientData = GetInpateintDataByStationCode(item.StationCode, hospitalID);
                    if (inpatientData == null || inpatientData.Count < 1)
                    {
                        dischargeFiag = true;
                    }
                }
                if (!inpatientDataInfo.DischargeDate.HasValue)
                {
                    dischargeFiag = true;
                }
                if (dischargeFiag)
                {
                    var hisView = new InPatientDataView
                    {
                        CaseNumber = item.CaseNumber,
                        ChartNo = item.ChartNo,
                        DischargeDate = item.DischargeDateTime.Date,
                        DischargeTime = item.DischargeDateTime.TimeOfDay
                    };
                    var syncInpatientDataView = await SyncInpatientDischarge(inpatientDataInfo, hisView, eventSettings, hospitalID);
                    //异动Mark
                    CallMarkAPI(syncInpatientDataView.MarkViewList);
                    CheckInPatient(syncInpatientDataView.InPatientChangeViewList);
                }
                //筛出当前病人的患者事件日志
                var ipLogs = inpatientLogs.Where(m => m.CaseNumber == item.CaseNumber).ToList();
                //开始同步的逻辑，所以需要更改这个循环
                await SyncInpatientDischargeEvent(ipLogs, item, inpatientDataInfo, eventSettings);
                try
                {
                    _unitOfWork.SaveChanges();
                }
                catch (Exception ex)
                {
                    _logger.Error("SyncDischargedPatient病人CaseNumber" + item.CaseNumber + "出院数据同步失败||" + ex.ToString());
                    return false;
                }
            }
            return true;
        }

        //出院,更新病人事件
        private async Task<bool> SyncInpatientDischargeEvent(List<InpatientLogInfo> inpatientLogs, InpatientInfoView item, InpatientDataInfo inpatientDataInfo, List<EventSettingInfo> eventSettings)
        {
            _logger.Info("同步出院病人 CaseNumber:" + item.CaseNumber + " ChartNo:" + item.ChartNo);

            //如果有出院事件，或者死亡事件不再增加
            var assessListIds = new List<int>
            {
                EVENTSETTING_ASSESSLISTID_DISCHARGE,
                DeathAssessListId
            };
            var patientEvent = await _patientEventRepository.GetEventByAssessListIDs(inpatientDataInfo.ID, assessListIds);

            if (patientEvent != null && patientEvent.Count > 0)
            {
                var oldEvent = patientEvent.Find(m => m.OccurDate == item.DischargeDateTime.Date && m.OccurTime == item.DischargeDateTime.TimeOfDay);
                if (oldEvent != null)
                {
                    return true;
                }
                patientEvent.ForEach(m =>
                {
                    if (m.AssessListID == EVENTSETTING_ASSESSLISTID_DISCHARGE)
                    {
                        m.DeleteFlag = "*";
                        m.ModifyDate = DateTime.Now;
                    }
                });
            }

            //处理病人事件
            await SetInpatientLogAndEvent(inpatientLogs, inpatientDataInfo, inpatientDataInfo.StationID, inpatientDataInfo.DepartmentListID
                       , inpatientDataInfo.BedID
                      , inpatientDataInfo.BedNumber, item.DischargeDateTime, EVENTSETTING_ASSESSLISTID_DISCHARGE, eventSettings);

            //添加体温单异动
            await _dataTableEditListService.AddEditLog(inpatientDataInfo.ID, 0, "PatientEvent", 12, "", null);
            return true;
        }

        //同步病人主诉
        public async Task<bool> SyncPatientChiefComplaintAsync(int? stationID)
        {
            var hospitaiInfo = _hospitalListRepository.GetHospitalInfo();
            if (hospitaiInfo == null)
            {
                return false;
            }

            var hospitalBaseDict = await GetHospitalBaseDict(hospitaiInfo.HospitalID);
            var stationList = hospitalBaseDict.StationList;
            if (stationID == null || stationID == 0)
            {
                _logger.Info("同步所有病区主诉数据");
                return await SyncPatientChiefComplaint(stationList, hospitaiInfo.HospitalID);
            }
            else
            {
                var station = stationList.Where(m => m.ID == stationID).ToList();
                return await SyncPatientChiefComplaint(station, hospitaiInfo.HospitalID);
            }
        }

        /// <summary>
        /// 同步病人主诉
        /// </summary>
        /// <param name="stationLists"></param>
        /// <returns></returns>
        private async Task<bool> SyncPatientChiefComplaint(List<StationListInfo> stationLists, string hospitalID)
        {
            var inpatientJobName = "PatientsChiefComplaint";
            var inpatientJobStatus = false;
            var complaintList = new List<StationChifeComplaintView>();
            if (stationLists.Count <= 0)
            {
                _logger.Error("主诉同步失败,没有获取到病区信息");
                return false;
            }
            foreach (var item in stationLists)
            {
                complaintList = GetPatientChiefComplaintByStationName(item.StationName, hospitalID);
                if (complaintList == null || complaintList.Count <= 0)
                {
                    _logger.Info(item.StationName + "没有需要同步的病人主诉数据");
                    continue;
                }
                _logger.Info("同步" + item.StationName + complaintList.Count + "条数据");
                foreach (var complaint in complaintList)
                {
                    if (string.IsNullOrEmpty(complaint.Symptom))
                    {
                        continue;
                    }

                    inpatientJobStatus = _jobLogService.GetJobStatus(complaint.CaseNumber, inpatientJobName, complaint.ChartNo);
                    if (!inpatientJobStatus)
                    {
                        continue;
                    }
                    await SyncOnePatientsChiefComplaintAsync(complaint, item);
                    _jobLogService.RemoveJob(complaint.CaseNumber, inpatientJobName, complaint.ChartNo);
                }
            }
            return true;
        }

        private List<StationChifeComplaintView> GetPatientChiefComplaintByStationName(string stationName, string hospitalID)
        {
            var complaintList = new List<StationChifeComplaintView>();
            ///在该行中确定要请求的api，从settingdescription读取
            var apiStr = _commonHelper.GetApiStr(1, "23");
            if (string.IsNullOrEmpty(apiStr))
            {
                _logger.Info(",获取API地址失败,接口编号-23");
                return null;
            }
            var data = new Dictionary<string, string>
            {
                { "stationName",  stationName},
            };
            _logger.Info("获取病区病人主诉同步 API:[" + apiStr + "]参数:" + ListToJson.ToJson(data));
            //呼叫API获得数据
            var resultData = _commonHelper.GetInterconnectData(apiStr, data);
            //打印接口
            var resultPringData = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "PrintInterfaceData").Result;
            if (resultPringData == "1")
            {
                _logger.Info("获取病区病人主诉信息:" + resultData);
            }
            var result = new ResponseResult();
            //将数据装化对象
            result = JsonConvert.DeserializeObject<ResponseResult>(resultData);
            try
            {   //数据转化成InpatientDataView集合
                complaintList = JsonConvert.DeserializeObject<List<StationChifeComplaintView>>(result.Data.ToString());
            }
            catch (Exception ex)
            {
                _logger.Error("Json格式化失败:" + ex.ToString());
                return complaintList;
            }
            return complaintList;
        }

        /// <summary>
        /// 同步病人主诉
        /// </summary>
        /// <param name="complaint"></param>
        /// <param name="station"></param>
        /// <returns></returns>
        private async Task SyncOnePatientsChiefComplaintAsync(StationChifeComplaintView complaint, StationListInfo station)
        {
            if (string.IsNullOrEmpty(complaint.Symptom))
            {
                return;
            }
            var inpatient = await _IInpatientDataRepository.GetAsyncByCaseNumber(complaint.CaseNumber, _config.Value.HospitalID);
            if (inpatient == null)
            {
                return;
            }
            if (inpatient.ChiefComplaint == complaint.Symptom)
            {
                return;
            }
            inpatient.ChiefComplaint = complaint.Symptom;
            try
            {
                _unitOfWork.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error("病人CaseNumber" + inpatient.CaseNumber + "主诉数据同步失败||" + ex.ToString());
                return;
            }
        }

        /// <summary>
        /// 出院病人事件同步
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncDischargedPatientsEventByDateTime()
        {
            //获取最后的同步时间
            var hospitalID = _config.Value.HospitalID;
            var nowDateTime = DateTime.Now;
            var hisEndDateTime = DateTime.Now;
            var intervalminutes = 30;
            //获取出院数据API
            var syncAPIConfigInfo = _syncAPIConfigRepository.GetSyncAPIConfigByApiID(18);
            if (syncAPIConfigInfo == null)
            {
                _logger.Error("没有找到ApiID为18(出院数据API)的数据");
                return false;
            }
            var lastDateTime = syncAPIConfigInfo.LastSyncDateTime;
            intervalminutes = syncAPIConfigInfo.IntervalMinutes;
            if (intervalminutes == 0)
            {
                intervalminutes = 30;
            }
            //获取时间重叠分钟，没有值默认60分钟
            var overlapMinutes = syncAPIConfigInfo.OverlapMinutes;
            if (overlapMinutes == null)
            {
                overlapMinutes = 60;
            }

            //如果间隔时间小于5分钟，不启用同步，避免接口服务器阻塞
            if (lastDateTime.AddMinutes(5) > nowDateTime)
            {
                return false;
            }
            //设置数据同步，时间重叠分钟，避免获取的数据不完整
            var startDateTime = lastDateTime.AddMinutes(-overlapMinutes.Value);

            //每次获取30分钟的数据同步频率
            while (startDateTime < nowDateTime)
            {
                //获取数据
                hisEndDateTime = startDateTime.AddMinutes(intervalminutes);
                var dictionary = new Dictionary<string, string>
                 {
                    { "StartDateTime", startDateTime.ToString()},
                    { "EndDateTime", hisEndDateTime.ToString()},
                 };
                startDateTime = hisEndDateTime;
                var hisData = new List<InpatientInfoView>();
                try
                {
                    hisData = await GetDischargedPatientsApiData(syncAPIConfigInfo.APIAddress, dictionary, hospitalID);
                }
                catch (Exception ex)
                {
                    _logger.Error("获取出院病人数据失败" + ex.ToString());
                    return false;
                }
                //同步成功，更新最后同步时间
                syncAPIConfigInfo.LastSyncDateTime = startDateTime;
                //所以在时间段内如果没有数据，则继续循环，直到获取数据。
                if (hisData != null && hisData.Count() > 0)
                {
                    if (!await SyncDischargedPatient(hisData, hospitalID))
                    {
                        return false;
                    }
                }
                _logger.Info("<问题验证>[最后更新时间]" + syncAPIConfigInfo.LastSyncDateTime);
                try
                {
                    _unitOfWorkOut.SaveChanges();
                }
                catch (Exception ex)
                {
                    _logger.Error("更新最后同步时间失败" + ex.ToString());
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 获取出院数据后，json转model
        /// </summary>
        /// <param name="aPIAddress"></param>
        /// <param name="dictionary"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<List<InpatientInfoView>> GetDischargedPatientsApiData(string api, Dictionary<string, string> dictionary, string hospitalID)
        {
            _logger.Info("呼叫API获取出院数据");
            var interconnect_Data = new List<InpatientInfoView>();
            var hisData = await GetDischargeApiData(api, dictionary, hospitalID);
            _logger.Info("<问题验证>[出院数据时间段]" + JsonConvert.SerializeObject(dictionary) + "<问题验证>[出院数据内容]" + hisData);
            if (string.IsNullOrEmpty(hisData))
            {
                return interconnect_Data;
            }

            try
            {
                interconnect_Data = JsonConvert.DeserializeObject<List<InpatientInfoView>>(hisData);
            }
            catch (Exception ex)
            {
                _logger.Error("Json格式化失败:" + ex.ToString());
                return interconnect_Data;
            }
            _logger.Info("转换Json数据完成，获得出院数据" + interconnect_Data.Count() + "条！");
            return interconnect_Data;
        }

        /// <summary>
        /// 根据API获取出院数据
        /// </summary>
        /// <param name="api"></param>
        /// <param name="dictionary"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<string> GetDischargeApiData(string api, Dictionary<string, string> dictionarys, string hospitalID)
        {
            if (string.IsNullOrEmpty(api))
            {
                return "";
            }
            //获取环境 ,1 开发环境
            var systemOperatingEnvironment = await _appConfigSettingRepository.GetConfigSettingValue("Configs", "SystemOperatingEnvironment");
            var resultData = "";
            if (systemOperatingEnvironment != "1")
            {
                //呼叫API获得数据
                resultData = _commonHelper.GetInterconnectData(api, dictionarys);
            }
            else
            {
                //resultData = _ILogInfoServices.GetLog("30")[0].Logs;
                resultData = ReadFile.ReadTxt(@"E:\MockData\银川出院.json");
            }

            var printInterfaceData = 0;
            //获取打印接口配置
            var resultPrintDate = await _appConfigSettingRepository.GetConfigSettingValue("Configs", "PrintInterfaceData");
            if (StringCheck.IsNumeric(resultPrintDate))
            {
                printInterfaceData = int.Parse(resultPrintDate);
            }

            if (printInterfaceData == 1)
            {
                _logger.Info("Api:" + api + "获取的出院数据" + ListToJson.ToJson(dictionarys) + "出院数据：" + resultData);
            }

            try
            {
                var result = JsonConvert.DeserializeObject<ResponseResult>(resultData);
                var resultDataStr = result.Data.ToString();
                if (resultDataStr == "" || resultDataStr == "[]" || resultDataStr == "[{}]" || resultDataStr == "{}")
                {
                    return "";
                }
                return resultDataStr;
            }
            catch (Exception ex)
            {
                _logger.Error("Api: " + api + "获取的出院数据ResponseResult" + ex.ToString());
                return "";
            }
        }

        /// <summary>
        /// 病人出院病人数据补救
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncDisChargeInPatientByDatetimeSupplement()
        {
            var hospitalInfo = _hospitalListRepository.GetHospitalInfo();
            if (hospitalInfo == null)
            {
                _logger.Error("获取医院ID失败！");
                return false;
            }
            var hospitalID = hospitalInfo.HospitalID;
            //获取字典信息
            var hospitalBaseDict = await GetHospitalBaseDict(hospitalInfo.HospitalID);
            var syncAPIConfigInfo = _syncAPIConfigRepository.GetSyncAPIConfigByApiID(27);
            if (syncAPIConfigInfo == null)
            {
                _logger.Error("没有找到ApiID为27(医嘱数据API)的数据");
                return false;
            }
            var apiStr = syncAPIConfigInfo.APIAddress;
            if (string.IsNullOrEmpty(apiStr))
            {
                _logger.Info(",获取API地址失败,接口编号-1");
                return false;
            }

            //获取同步时间
            var nowDateTime = DateTime.Now;
            var lastDateTime = syncAPIConfigInfo.LastSyncDateTime;
            var intervalminutes = syncAPIConfigInfo.IntervalMinutes;
            if (intervalminutes == 0)
            {
                intervalminutes = 30;
            }
            //如果间隔时间小于5分钟，不启用同步，避免接口服务器阻塞
            if (lastDateTime.AddMinutes(5) > nowDateTime)
            {
                return false;
            }
            //设置数据同步，时间重叠分钟，避免获取的数据不完整
            var overlapMinutes = syncAPIConfigInfo.OverlapMinutes;
            if (overlapMinutes == null || overlapMinutes <= 1)
            {
                overlapMinutes = 30;
            }
            var minutes = Convert.ToInt32(overlapMinutes);
            var startDateTime = lastDateTime.AddMinutes(-minutes);
            _logger.Info("同步开始时间：" + startDateTime.ToString() + "结束时间：" + nowDateTime.ToString() + "出院病人数据");
            var resultFlag = await SyncDisChargeInPatientByDateTime(startDateTime, nowDateTime, intervalminutes, hospitalID, syncAPIConfigInfo, hospitalBaseDict, apiStr);
            _logger.Info("开始时间：" + startDateTime.ToString() + "结束时间：" + nowDateTime.ToString() + "的出院同步结束");
            return resultFlag;
        }

        /// <summary>
        /// 循环同步出院病人补救
        /// </summary>
        /// <param name="startDateTime"></param>
        /// <param name="nowDateTime"></param>
        /// <param name="intervalminutes"></param>
        /// <param name="hospitalID"></param>
        /// <param name="syncAPIConfigInfo"></param>
        /// <returns></returns>
        private async Task<bool> SyncDisChargeInPatientByDateTime(DateTime startDateTime, DateTime nowDateTime
            , int intervalminutes, string hospitalID, SyncAPIConfigInfo syncAPIConfigInfo, HospitalBaseDictView hospitalBaseDictView, string apiStr)
        {
            //记录连续没有病人数据次数
            while (startDateTime < nowDateTime)
            {
                var endDateTime = startDateTime.AddMinutes(intervalminutes);
                var data = new Dictionary<string, string>
               {
                { "startDataTime", startDateTime.ToString() },
                { "endDateTime", endDateTime.ToString()},
                { "allDataFlag", "N" }
               };
                var inpatientData = GetInpatientApiData(apiStr, data, hospitalID);
                //同步成功，更新最后同步时间
                syncAPIConfigInfo.LastSyncDateTime = endDateTime;
                inpatientData = inpatientData.Where(m => m.DischargeDate.HasValue).ToList();
                if (inpatientData != null && inpatientData.Count > 0)
                {
                    //数据同步
                    await SyncInPatientData(hospitalBaseDictView, inpatientData, hospitalID);
                    startDateTime = endDateTime;
                }
                try
                {
                    _unitOfWorkOut.SaveChanges();
                }
                catch (Exception ex)
                {
                    _logger.Error("更新最后同步时间失败" + ex.ToString());
                    return false;
                }
                startDateTime = endDateTime;
            }
            return true;
        }

        //同步病人信息，呼叫Proflie
        private async Task<Tuple<List<PatientProfile>, List<MessageModel>>> CreateAddPatientProfile(InpatientDataInfo inpatientData
            , List<PatientListIconInfo> patientListIconList
            , int ageAssessListID
            , string modifyPersonID)
        {
            var hospitalID = _config.Value.HospitalID;
            var patientProfiles = new List<PatientProfile>();
            var sendMessageList = new List<MessageModel>();
            //增加PatientProfiles数据(年龄)
            if (inpatientData.Age != null)
            {
                patientProfiles.Add(_externalCommonService.CreateProfile(inpatientData, "Age", ageAssessListID
                    , inpatientData.Age.ToString(), hospitalID, modifyPersonID));
            }
            //增加PatientProfiles数据(护理级别)
            patientProfiles.Add(CreateNursingLeveProfile(inpatientData, patientListIconList, hospitalID));

            //增加主诊断集束护理
            if (!string.IsNullOrEmpty(inpatientData.ICDCode))
            {
                var ids = await _externalCommonService.GetAssessListIDByICDCode(inpatientData.ICDCode, inpatientData.DepartmentListID);

                foreach (var item in ids)
                {
                    var profile = _externalCommonService.CreateProfile(inpatientData, "Diagnosis", item, "", hospitalID, modifyPersonID);
                    patientProfiles.Add(profile);
                }
                if (patientProfiles.Count > 0)
                {
                    sendMessageList.Add(_externalCommonService.CreateDiagnosisMessage(inpatientData));
                }
            }
            return new Tuple<List<PatientProfile>, List<MessageModel>>(patientProfiles, sendMessageList);
        }

        //新增Mark数据组装
        private MarkView AddInpatientToProfileMark(InpatientDataInfo inpatient
            , InPatientDataView hisInPatient, PatientBasicDataInfo patient)
        {
            var hospitalID = _config.Value.HospitalID;
            Dictionary<string, string> dict = new Dictionary<string, string>
            {
                { "chartNo", inpatient.ChartNo },
                { "patientName", patient.PatientName },
                { "gender", patient.Gender },
                { "nursingLevel", inpatient.NursingLevel },
                { "bedNumber", inpatient.BedNumber },
                { "stationCode", hisInPatient.StationCode },
                { "departmenCode", hisInPatient.DepartmentCode },
                { "hospitalID", hospitalID },
                { "admissionDate", hisInPatient.AdmissionDate.Add(hisInPatient.AdmissionTime).ToString() }
            };
            var inpatientProfileMarkAPI = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "InpatientProfileMarkAPI").Result;
            var markData = GetKeyValue(dict);
            var markView = new MarkView
            {
                InpatientData = inpatient,
                Url = inpatientProfileMarkAPI,
                MarkDatas = markData,
                DataType = "新增住院病人"
            };
            return markView;
        }

        /// <summary>
        /// 写同步日志,及病人事件
        /// </summary>
        /// <param name="inpatientLogs">患者日志档</param>
        /// <param name="inpatientDataInfo">患者信息</param>
        /// <param name="stationID">病区ID</param>
        /// <param name="DepartmentID">科室ID</param>
        /// <param name="bedID">床位ID</param>
        /// <param name="bedNumber">床位号</param>
        /// <param name="eventDateTime">事件发生时间</param>
        /// <param name="assessListID">事件对应ID</param>
        /// <returns></returns>
        private async Task SetInpatientLogAndEvent(List<InpatientLogInfo> inpatientLogs, InpatientDataInfo inpatient
            , int stationID, int DepartmentID, int bedID, string bedNumber, DateTime eventDateTime
            , int assessListID, List<EventSettingInfo> eventSettings)
        {
            var eventSetting = eventSettings.Where(m => m.AssessListID == assessListID)
                                           .Select(t => new { t.ShowName, t.LogCode }).FirstOrDefault();
            if (eventSetting == null)
            {
                _logger.Warn("未能获取到EventSetting中AssessListID={0}的配置！", assessListID);
                return;
            }
            //判断记录是否存在
            inpatientLogs = inpatientLogs.Where(m => m.StationID == stationID && m.LogDateTime == eventDateTime
            && m.LogCode == eventSetting.LogCode).ToList();

            if (inpatientLogs.Count >= 1)
            {
                return;
            }
            //远程调用MedicalAPI中的保存患者事件的公共方法
            await _patientEventCommonService.CallEventAPI(inpatient, stationID, DepartmentID, bedID, bedNumber
                , eventDateTime, assessListID, eventSetting.LogCode, "", inpatient.ID + eventSetting.LogCode);
        }

        /// <summary>
        /// 写转病区，转科，转床事件
        /// </summary>
        /// <param name="medicalInPatientData">患者信息</param>
        /// <param name="oldSataionID">病区ID</param>
        /// <param name="oldDepartmentID">科室ID</param>
        /// <param name="oldBedID">床位ID</param>
        /// <param name="oldBedNumber">床位号</param>
        /// <param name="eventSettings">事件配置</param>
        /// <returns></returns>
        private async Task<bool> SetInPatientEvent(List<InpatientLogInfo> inpatientLogs, InpatientDataInfo medicalInPatientData, int oldSataionID, int oldDepartmentID, int oldBedID, string oldBedNumber, List<EventSettingInfo> eventSettings)
        {
            //写转科事件
            if (oldDepartmentID != medicalInPatientData.DepartmentListID)
            {
                await SetInpatientLogAndEvent(inpatientLogs, medicalInPatientData, oldSataionID, oldDepartmentID, oldBedID
                    , oldBedNumber, DateTime.Now, EVENTSETTING_ASSESSLISTID_TRANSOUTDEPT, eventSettings);

                await SetInpatientLogAndEvent(inpatientLogs, medicalInPatientData, medicalInPatientData.StationID, medicalInPatientData.DepartmentListID
                    , medicalInPatientData.BedID, medicalInPatientData.BedNumber, DateTime.Now, EVENTSETTING_ASSESSLISTID_TRANSINDEPT, eventSettings);
            }
            //写转病区事件
            if (oldSataionID != medicalInPatientData.StationID)
            {
                await SetInpatientLogAndEvent(inpatientLogs, medicalInPatientData, oldSataionID, oldDepartmentID, oldBedID
                    , oldBedNumber, DateTime.Now, EVENTSETTING_ASSESSLISTID_TRANSOUT, eventSettings);

                await SetInpatientLogAndEvent(inpatientLogs, medicalInPatientData, medicalInPatientData.StationID, medicalInPatientData.DepartmentListID
                     , medicalInPatientData.BedID, medicalInPatientData.BedNumber, DateTime.Now, EVENTSETTING_ASSESSLISTID_TRANSIN, eventSettings);
            }
            //写转床事件
            if (oldSataionID == medicalInPatientData.StationID && (oldBedNumber != medicalInPatientData.BedNumber || oldBedID != medicalInPatientData.BedID))
            {
                await SetInpatientLogAndEvent(inpatientLogs, medicalInPatientData, oldSataionID, oldDepartmentID
                       , oldBedID, oldBedNumber
                       , DateTime.Now, EVENTSETTING_ASSESSLISTID_TURNOUTBED, eventSettings);

                await SetInpatientLogAndEvent(inpatientLogs, medicalInPatientData, medicalInPatientData.StationID, medicalInPatientData.DepartmentListID
                , medicalInPatientData.BedID, medicalInPatientData.BedNumber, DateTime.Now, EVENTSETTING_ASSESSLISTID_TURNINBED, eventSettings);
            }
            return true;
        }

        //转科转床,Mark数据组装
        private MarkView MondifyInpateintToProfileMark(InpatientDataInfo inpatient
            , InPatientDataView hisInpatient)
        {
            var hospitalID = _config.Value.HospitalID;
            Dictionary<string, string> dict = new Dictionary<string, string>
            {
                { "chartNo", inpatient.ChartNo },
                { "nursingLevel", inpatient.NursingLevel },
                { "bedNumber", hisInpatient.BedNumber },
                { "stationCode", hisInpatient.StationCode },
                { "departmenCode", hisInpatient.DepartmentCode },
                { "hospitalID", hospitalID }
            };

            //从配置当中获取数据 梁宝华 2020-04-29
            var transferProfileMarkAPI = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "TransferProfileMarkAPI").Result;

            var markData = GetKeyValue(dict);
            var markView = new MarkView
            {
                InpatientData = inpatient,
                Url = transferProfileMarkAPI,
                MarkDatas = markData,
                DataType = "转科、转床、护理级别发生变化"
            };
            return markView;
        }

        //病人信息变化，调用相应的APi进行处理
        ////出院，或则护理级别发生变化，停止问题
        private void CheckInPatient(List<InPatientChangeViewInfo> inPatientChangeList)
        {
            foreach (var item in inPatientChangeList)
            {
                switch (item.PatientType)
                {
                    case PatientType.PatientInsert:
                    case PatientType.PatientOtherChange:
                        _commonHelper.CheckSchedulePatientInfo(item.InPatientID, item.CaseNumber, false); //病人信息发生变化，验证排程中的病人信息（延迟5分钟执行）
                        break;

                    case PatientType.PatientLevelChange:
                        _commonHelper.StopProblem(item.InPatientID, item.NursingLevel, true, MedicalEnumUtility.StopProblemType.NursingLevelChange);
                        break;

                    case PatientType.PatientTransfer:
                        _commonHelper.StopProblem(item.InPatientID, item.NursingLevel, true, MedicalEnumUtility.StopProblemType.Transfer);
                        _commonHelper.CheckSchedulePatientInfo(item.InPatientID, item.CaseNumber, true);
                        break;

                    case PatientType.PatientDischarged:
                        _commonHelper.StopProblem(item.InPatientID, item.NursingLevel, true, MedicalEnumUtility.StopProblemType.Discharge);
                        break;

                    default:
                        break;
                }
            }
        }

        /// <summary>
        /// 根据时间段同步出院患者（刷新用）
        /// </summary>
        /// <param name="startDateTime"></param>
        /// <param name="endDateTime"></param>
        /// <returns></returns>
        public async Task<bool> SyncDischargedByDateTime(DateTime? startDateTime, DateTime? endDateTime)
        {
            var hospitalID = _config.Value.HospitalID;
            var syncAPIConfigInfo = _syncAPIConfigRepository.GetSyncAPIConfigByApiID(18);
            if (syncAPIConfigInfo == null || string.IsNullOrEmpty(syncAPIConfigInfo.APIAddress))
            {
                return false;
            }
            var dictionary = new Dictionary<string, string>
                 {
                    { "StartDateTime", startDateTime.Value.ToString("yyyy-MM-dd HH:mm:ss")},
                    { "EndDateTime", endDateTime.Value.ToString("yyyy-MM-dd HH:mm:ss")},
                 };
            var hisData = new List<InpatientInfoView>();
            try
            {
                hisData = await GetDischargedPatientsApiData(syncAPIConfigInfo.APIAddress, dictionary, hospitalID);
            }
            catch (Exception ex)
            {
                _logger.Error("刷新出院病人数据失败" + ex.ToString());
                return false;
            }
            if (hisData == null || hisData.Count() == 0)
            {
                return false;
            }
            return SyncDischargedPatient(hisData, hospitalID).Result;
        }

        /// <summary>
        /// 同步母婴关系
        /// </summary>
        /// <param name="stationCode">有母婴关系的病区码</param>
        /// <returns></returns>
        public async Task<bool> SyncMotherAndChildList(string stationCode)
        {
            //获取HIS的Json数据
            var hisData = GetHisMotherAndChildAsync(_config.Value.HospitalID, stationCode);
            if (string.IsNullOrEmpty(hisData))
            {
                _logger.Error("<母婴关系同步失败>[获取HIS数据失败]");
                return false;
            }
            //清洗HIS的Json数据为需要的List
            var cleanData = CleanHisMotherAndChildData(hisData);
            if (cleanData == null || cleanData.Count == 0)
            {
                return false;
            }
            //同步HIS数据
            return await _newBornService.SyncNewBorn(cleanData);
        }

        /// <summary>
        /// 清洗格式话HIS数据
        /// </summary>
        /// <param name="hisData"></param>
        /// <returns></returns>
        private List<NewBornView> CleanHisMotherAndChildData(string hisData)
        {
            var hisInpatients = new List<HISInpatient>();
            var newbornList = new List<NewBornView>();
            try
            {
                hisInpatients = JsonConvert.DeserializeObject<List<HISInpatient>>(hisData);
            }
            catch (Exception ex)
            {
                _logger.Error("母婴关系Json转List失败:" + ex.ToString());
                return newbornList;
            }
            //待转换数据
            var newbornTemp = hisInpatients.Where(m => !string.IsNullOrEmpty(m.NewbornNmae)
            && m.Gender == "女" && m.BedNumber == m.NewbornBedNumber
            && m.NewbornBedNumber.Contains("-")).ToList();
            foreach (var item in newbornTemp)
            {
                var gender = "";
                if (!string.IsNullOrEmpty(item.NewbornGender))
                {
                    gender = item.NewbornGender == "女" ? "2" : "1";
                }
                var newborn = new NewBornView
                {
                    MotherCaseNumber = item.CaseNumber,
                    MotherChartNO = item.ChartNo,
                    CaseNumber = item.NewbornNumber,
                    ChartNO = item.NewbornNumber,
                    NewbornName = item.NewbornNmae,
                    Gender = gender,
                    NewbornBirthday = item.NewbornBirthday
                };
                newbornList.Add(newborn);
            }
            return newbornList;
        }

        /// <summary>
        /// 获取HIS的母婴关系数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="stationCode"></param>
        /// <returns></returns>
        private string GetHisMotherAndChildAsync(string hospitalID, string stationCode)
        {
            var resultData = "";
            //获取API
            var apiStr = _commonHelper.GetApiStr(1, "24");
            if (string.IsNullOrEmpty(apiStr))
            {
                _logger.Info("获取母婴关系API地址失败,接口编号-24");
                return null;
            }
            //获取参数
            var data = new Dictionary<string, string>
            {
               { "StationCode",stationCode}
            };
            _logger.Info("获取母婴关系API:[" + apiStr + "]参数:" + ListToJson.ToJson(data));

            var systemOperatingEnvironment = _appConfigSettingRepository.GetConfigSettingValue("Configs", "SystemOperatingEnvironment").Result;
            if (systemOperatingEnvironment != "1")
            {
                //呼叫API获得数据
                try
                {
                    resultData = _commonHelper.GetInterconnectData(apiStr, data);
                }
                catch (Exception ex)
                {
                    _logger.Error("获取母婴关系失败，API:[" + apiStr + "]参数: " + ListToJson.ToJson(data) + ex.ToString());
                    return resultData;
                }
            }
            else
            {
                //配置是1，获取本地测试数据
                resultData = ReadFile.ReadTxt(@"D:\SyncInpatient.json");
            }
            if (string.IsNullOrEmpty(resultData))
            {
                return resultData;
            }
            //打印接口
            var resultPringData = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "PrintInterfaceData").Result;
            if (resultPringData == "1")
            {
                _logger.Info("获取母婴关系数据:" + resultData);
            }
            //获取接口内有效数据
            var responseResult = new ResponseResult();
            try
            {
                responseResult = JsonConvert.DeserializeObject<ResponseResult>(resultData);
            }
            catch (Exception ex)
            {
                _logger.Error("获取在院病人数据:" + resultData + "转换失败" + ex.ToString());
                return resultData;
            }
            if (responseResult != null && responseResult.Data != null && responseResult.Data.ToString() != "[]")
            {
                resultData = responseResult.Data.ToString();
            }
            return resultData;
        }
    }
}