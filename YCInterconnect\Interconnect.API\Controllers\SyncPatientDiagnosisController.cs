﻿using Interconnect.Services.Interface;
using Medical.Common;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using System.Threading.Tasks;

namespace Interconnect.API.Controllers
{
    /// <summary>
    /// 信息同步
    /// </summary>
    [Produces("application/json")]
    [Route("api/PatientDiagnosis")]
    [EnableCors("any")]
    public class SyncPatientDiagnosisController : Controller
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IPatientDiagnosisService _patientDiagnosisService;

        /// <summary>
        /// 病人诊断同步
        /// </summary>
        /// <param name="patientDiagnosisService"></param>
        public SyncPatientDiagnosisController(
             IPatientDiagnosisService patientDiagnosisService
            )
        {
            _patientDiagnosisService = patientDiagnosisService;
        }
        /// <summary>
        /// 同步病人诊断信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncPatientDiagnosisByCaseNumber")]
        public async Task<IActionResult> SyncPatientDiagnosisByCaseNumberAsync(string CaseNumber)
        {
            var result = new ResponseResult
            {
                Data = await _patientDiagnosisService.SyncPatientDiagnosisByCaseNumber(CaseNumber)
            };
            return result.ToJson();
        }
        /// <summary>
        /// 根据病区Code 同步在院患者诊断 
        /// </summary>
        /// <param name="stationCode">不传为同步全院在院数据</param>
        /// <param name="saveCount">每次入数据库的数量 不传默认为1</param>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncPatientDiagnosisByStationCode")]
        public async Task<IActionResult> SyncPatientDiagnosisByStationCode(string stationCode, int? saveCount)
        {
            var result = new ResponseResult
            {
                Data = await _patientDiagnosisService.SyncPatientDiagnosisByStationCode(stationCode, saveCount)
            };
            return result.ToJson();
        }


        /// <summary>
        /// 打印his诊断数据
        /// </summary>
        /// <param name="stationCode"></param>
        /// <param name="caseNumber"></param>
        /// <param name="inHosipitalStatus">默认全部 在院：I </param>
        /// <returns></returns>
        [HttpGet]
        [Route("PrintHisDiagnosis")]
        public async Task<IActionResult> PrintHisDiagnosis(string stationCode, string caseNumber, string inHosipitalStatus)
        {
            var result = new ResponseResult
            {
                Data = await _patientDiagnosisService.PrintHisDiagnosis(stationCode, caseNumber, inHosipitalStatus),
                Code = 1
            };
            return result.ToJson();
        }
    }
}