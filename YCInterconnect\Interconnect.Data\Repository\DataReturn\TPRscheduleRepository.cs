﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;
using Microsoft.EntityFrameworkCore;

namespace Interconnect.Data.Repository

{
    public class TPRscheduleRepository : ITPRscheduleRepository
    {
        private DataOutConnection _DataOutConnection = null;

        public TPRscheduleRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }
        public async Task<List<TPRscheduleInfo>> GetTPRscheduleListAsync(DateTime StartDate, DateTime EndDate)
        {
            return await _DataOutConnection.TPRscheduleInfos.Where(m => m.ModifyDate >= StartDate && m.ModifyDate < EndDate&&!string.IsNullOrEmpty(m.EMRField)).ToListAsync();
        }
    }
}
