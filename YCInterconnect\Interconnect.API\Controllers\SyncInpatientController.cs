﻿using Interconnect.Services.Interface;
using Medical.Common;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using System;
using System.Threading.Tasks;

namespace Interconnect.API.Controllers
{
    /// <summary>
    /// 信息同步
    /// </summary>
    [Produces("application/json")]
    [Route("api/InPatient")]
    [EnableCors("any")]
    public class SyncInpatientController : Controller
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISyncInpatientService _syncInpatientService;
        private readonly IInpatientDealWithService _inpatientDealWithService;

        /// <summary>
        /// 病人基本信息同步
        /// </summary>
        /// <param name="syncInpatientService"></param>
        public SyncInpatientController(
             ISyncInpatientService syncInpatientService
            , IInpatientDealWithService inpatientDealWithService
            )
        {
            _syncInpatientService = syncInpatientService;
            _inpatientDealWithService = inpatientDealWithService;
        }

        /// <summary>
        /// 同步新入院病人数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncNewInPatient")]
        public async Task<IActionResult> SyncNewInPatient()
        {
            var resultSrt = await _syncInpatientService.SyncNewInPatient();
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }


        /// <summary>
        /// 根据病区Code同步在院病人数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncInPatientByStatioCode")]
        public async Task<IActionResult> SyncInPatientByStatioCode(string stationCode)
        {
            var resultSrt = false;

            resultSrt = await _syncInpatientService.SyncInPatientByStationCode(stationCode);

            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }



        /// <summary>
        /// 出院病人同步,同步一段时间内的出院病人数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncDischargedPatientsByDateTime")]
        public async Task<IActionResult> SyncDischargedPatientsByDateTime()
        {
            var result = new ResponseResult();

            result.Data = await _syncInpatientService.SyncDischargedPatientsByDateTime();
            result.Code = 1;
            return result.ToJson();
        }
        /// <summary>
        /// 根据时间段同步出院患者（刷新用）
        /// </summary>
        /// <param name="startDateTime"></param>
        /// <param name="endDateTime"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncDischargedByDateTime")]
        public async Task<IActionResult> SyncDischargedByDateTime(DateTime? startDateTime, DateTime? endDateTime)
        {
            var result = new ResponseResult();

            result.Data = await _syncInpatientService.SyncDischargedByDateTime(startDateTime, endDateTime);
            result.Code = 1;
            return result.ToJson();
        }
        /// <summary>
        /// 根据CaseNumber进行预出院
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncDischargedByCaseNumber")]
        public async Task<IActionResult> SyncDischargedByCaseNumber(string caseNumber)
        {
            var result = new ResponseResult();

            result.Data = await _inpatientDealWithService.SyncDischangeByCaseNumber(caseNumber);
            if ((bool)result.Data)
            {
                result.Code = 1;
            }
            else
            {
                result.Code = 0;
            }
            return result.ToJson();
        }
    }
}