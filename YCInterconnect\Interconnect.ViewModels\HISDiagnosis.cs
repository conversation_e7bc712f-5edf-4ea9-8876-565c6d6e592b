﻿namespace Interconnect.ViewModels
{
    /// <summary>
    /// HIS诊断数据
    /// </summary>
    public class HISDiagnosis
    {
        /// <summary>
        /// 住院号														
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 入院诊断
        /// </summary>
        public string InhospDiagnosis { get; set; }
        /// <summary>
        /// 入院诊断明细
        /// </summary>
        public string InhospDiagnosisDesc { get; set; }
        /// <summary>
        /// 入院诊断码
        /// </summary>
        public string InhospDiagnosisCode { get; set; }
        /// <summary>
        /// 出院诊断
        /// </summary>
        public string OuthospDiagnosis { get; set; }
        /// <summary>
        /// 出院诊断明细
        /// </summary>
        public string OuthospDiagnosisDesc { get; set; }
        /// <summary>
        /// 出院诊断码
        /// </summary>
        public string OuthospDiagnosisCode { get; set; }
        /// <summary>
        /// 修改诊断
        /// </summary>
        public string ModifyDiagnosis { get; set; }
        /// <summary>
        /// 修改诊断码
        /// </summary>
        public string ModifyDiagnosisCode { get; set; }
    }
}

