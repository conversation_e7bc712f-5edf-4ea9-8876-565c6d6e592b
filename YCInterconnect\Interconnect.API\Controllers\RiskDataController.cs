﻿using Medical.Common;
using Interconnect.Services.Interface;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Interconnect.Models;
using Interconnect.Data.Context;

namespace Interconnect.API.Controllers
{
    /// <summary>
    /// 获取风险数据
    /// </summary>
    [Produces("application/json")]
    [Route("api/Focus")]
    [EnableCors("any")]
    public class RiskDataController : Controller
    {
        private IRiskDataService _riskDataService;
        private DataOutConnection _DataOutConnection = null;

        /// <summary>
        /// 获取在院病人风险数据
        /// </summary>
        /// <param name="riskDataService"></param>
        public RiskDataController(
            IRiskDataService riskDataService
            , DataOutConnection dataOutConnection
            )
        {
            _riskDataService = riskDataService;
            _DataOutConnection = dataOutConnection;
        }

        /// <summary>
        /// 获取在医院病人风险数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetRiskDataList")]
        public IActionResult GetRiskDataList()
        {
            var resultSrt = _riskDataService.GetRiskDataList();
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 获取在医院病人风险数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("InsertDiagnosis")]
        public IActionResult InsertDiagnosis()
        {

            var jsonstr = ReadFile.ReadTxt(@"D:\SyncData\yc_zd.json");
            var patientDiagnosisList = ListToJson.ToList<InterconnectPatientDiagnosisInfo>(jsonstr);
            _DataOutConnection.Update(patientDiagnosisList);
            _DataOutConnection.SaveChanges();

            var result = new ResponseResult
            {
                Data = "",
                Code = 1
            };
            return result.ToJson();
        }
    }
}