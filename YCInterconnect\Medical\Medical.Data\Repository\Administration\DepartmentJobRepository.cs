﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class DepartmentJobRepository : IDepartmentJobRepository
    {
        private readonly MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly GetCacheService _getCacheService;

        public DepartmentJobRepository(MedicalDbContext db, IMemoryCache memoryCache, GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _getCacheService = getCacheService;
        }

        public async Task<List<DepartmentJobInfo>> GetAsync(int deptmentID)
        {
            var datas = await this.GetAllAsync<DepartmentJobInfo>();
            if (datas == null)
            {
                return new List<DepartmentJobInfo>();
            }
            return datas.Where(t => t.DepartmentID == deptmentID && t.DeleteFlag != "*").OrderBy(t => t.JobArchitectureID).ToList();
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<DepartmentJobInfo>>(key, GetDataBaseListData);
        }
        /// <summary>
        /// 获取数据库数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            var data = await _medicalDbContext.DepartmentJobInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
            return data;
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.DeptmentJob.ToString();
        }

        public async Task<DepartmentJobInfo> GetByIDAsync(int id)
        {
            var datas = await this.GetAllAsync<DepartmentJobInfo>();
            if (datas != null)
            {
                return datas.Find(t => t.ID == id);

            }
            return null;
        }
        public async Task<List<DepartmentJobInfo>> GetAsyncByJobGroupID(int deptmentID, int jobGroupID)
        {
            var datas = await this.GetAllAsync<DepartmentJobInfo>();
            if (datas == null)
            {
                return new List<DepartmentJobInfo>();
            }
            return datas.Where(t => t.DepartmentID == deptmentID && t.JobGroupID == jobGroupID && t.DeleteFlag != "*").OrderBy(t => t.JobArchitectureID).ToList();
        }

        public async Task<List<DepartmentJobInfo>> GetByIDsAsync(List<int> deptmentIDs)
        {
            return await _medicalDbContext.DepartmentJobInfos.Where(m => deptmentIDs.Contains(m.DepartmentID) && m.DeleteFlag != "*").ToListAsync();
        }
    }
}