﻿namespace ViewModel
{
    /// <summary>
    /// 医嘱字典
    /// </summary>
    public class HISOrderView
    {
        /// <summary>
        /// 患者每次就诊唯一标识
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 病案号
        /// </summary>
        public string ChartNo { get; set; }
        /// <summary>
        /// 床位号码
        /// </summary>
        public string BedNumber { get; set; }
        /// <summary>
        /// 床位代码
        /// </summary>
        public string BedCode { get; set; }
        /// <summary>
        /// 医嘱序号
        /// </summary>
        public string OrderID { get; set; }
        /// <summary>
        /// 医嘱子序号
        /// </summary>
        public string OrderSubID { get; set; }
        /// <summary>
        /// 医嘱代码
        /// </summary>
        public string OrderCode { get; set; }       
        /// <summary>
        /// 医嘱类别(治疗、药品、护理、等)
        /// </summary>
        public string OrderPattern { get; set; }
        /// <summary>
        /// 医嘱类别(长期,临时)
        /// </summary>
        public string OrderType { get; set; }
        /// <summary>
        /// 医嘱内容
        /// </summary>
        public string OrderContent { get; set; }       
        /// <summary>
        /// 频次
        /// </summary>
        public string Frequency { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 一次剂量
        /// </summary>
        public string OrderDose { get; set; }
        /// <summary>
        /// 总剂量
        /// </summary>
        public string TotalVolume { get; set; }
        /// <summary>
        /// 包装量
        /// </summary>
        public string Package { get; set; }
        /// <summary>
        /// 包装单位
        /// </summary>
        public string PackageUnit { get; set; }
        /// <summary>
        /// 服法/途径/姿势
        /// </summary>
        public string OrderRule { get; set; }
        /// <summary>
        /// 部位
        /// </summary>
        public string Location { get; set; }
        /// <summary>
        /// 检验类别
        /// </summary>
        public string MethodCategory { get; set; }
        /// <summary>
        /// 检体
        /// </summary>
        public string SampleName { get; set; }
        /// <summary>
        /// 执行次数
        /// </summary>
        public string NumberOfExecution { get; set; }
        /// <summary>
        /// 一日几次
        /// </summary>
        public string TimesOfDay { get; set; }
        /// <summary>
        /// 首日次数
        /// </summary>
        public string FirstDayTimes { get; set; }
        /// <summary>
        /// 首日首次执行时间
        /// </summary>
        public string FirstDayStartTime { get; set; }
        /// <summary>
        /// 医嘱状态
        /// </summary>
        public string OredrStatus { get; set; }
        /// <summary>
        /// 开始日期时间
        /// </summary>
        public string StartDate { get; set; }
        /// <summary>
        /// 结束日期时间
        /// </summary>
        public string EndDate { get; set; }
        /// <summary>
        /// 开立人员
        /// </summary>
        public string AddEmployeeID { get; set; }
        /// <summary>
        /// 开立时间
        /// </summary>
        public string AddDate { get; set; }
        /// <summary>
        /// 确认人员
        /// </summary>
        public string ConfirmPersonID { get; set; }
        /// <summary>
        /// 确认时间
        /// </summary>
        public string ConfirmDate { get; set; }
        /// <summary>
        /// 取消人员
        /// </summary>
        public string CancalPersonID { get; set; }
        /// <summary>
        /// 取消时间
        /// </summary>
        public string CancalDate { get; set; }
        /// <summary>
        /// 皮试标志
        /// </summary>
        public string SkinTestFlag { get; set; }
        /// <summary>
        /// 皮试结果
        /// </summary>
        public string SkinTestResult { get; set; }
        /// <summary>
        /// 病区码
        /// </summary>
        public string WardCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string MethodVategory { get; set; }
    }
}
