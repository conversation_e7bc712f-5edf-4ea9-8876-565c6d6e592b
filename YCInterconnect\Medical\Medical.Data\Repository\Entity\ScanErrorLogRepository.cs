﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class ScanErrorLogRepository : IScanErrorLogRepostory
    {
        private MedicalDbContext _medicalDbContext;

        public ScanErrorLogRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 查询所有数据
        /// </summary>
        public async Task<List<ScanErrorLogInfo>> GetScanErrorLog()
        {
            return await _medicalDbContext.ScanErrorLogInfos.ToListAsync();
        }
    }
}
