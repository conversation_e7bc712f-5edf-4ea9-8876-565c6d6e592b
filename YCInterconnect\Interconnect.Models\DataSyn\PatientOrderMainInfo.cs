﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    /// <summary>
    /// 医嘱主记录
    /// </summary>
    [Serializable]
    [Table("PatientOrderMain")]
    public class PatientOrderMainInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///医嘱KEY
        ///</summary>
        public string OrderID { get; set; }
        /// <summary>
        ///住院号
        ///</summary>
        public string CaseNumber { get; set; }
        /// <summary>
        ///病案号
        ///</summary>
        public string ChartNo { get; set; }
        /// <summary>
        ///床位号码
        ///</summary>
        public string BedNumber { get; set; }
        /// <summary>
        ///床位代码
        ///</summary>
        public string BedCode { get; set; }
        /// <summary>
        ///开始日期

        ///</summary>
        public DateTime StartDate { get; set; }
        /// <summary>
        ///开始时间

        ///</summary>
        public TimeSpan StartTime { get; set; }
        /// <summary>
        ///结束日期

        ///</summary>
        public DateTime EndDate { get; set; }
        /// <summary>
        ///结束时间

        ///</summary>
        public TimeSpan? EndTime { get; set; }
        /// <summary>
        ///新增人员工号

        ///</summary>
        public string AddEmployeeID { get; set; }
        /// <summary>
        ///新增日期
        ///</summary>
        public DateTime AddDate { get; set; }
        /// <summary>
        ///确认医嘱人员工号
        ///</summary>
        public string ConfirmPersonID { get; set; }
        /// <summary>
        ///确认医嘱日期
        ///</summary>
        public DateTime ConfirmDate { get; set; }
        /// <summary>
        ///作废医嘱人员工号

        ///</summary>
        public string CancalPersonID { get; set; }
        /// <summary>
        ///作废日期
        ///</summary>
        public DateTime CancalDate { get; set; }
        /// <summary>
        ///医嘱已执行标记
        ///</summary>
        public string PerformedFlag { get; set; }
        /// <summary>
        ///医嘱已执行人员工号 

        ///</summary>
        public string PerformedPersonID { get; set; }
        /// <summary>
        ///医嘱已执行日期时间
        ///</summary>
        public DateTime PerformedDate { get; set; }
        /// <summary>
        /// 医嘱类别 1长期，0临时
        /// </summary>
        public string TypeCode { get; set; }
        /// <summary>
        /// 类别名称
        /// </summary>
        public string TypeName { get; set; }    
        /// <summary>
        /// 医嘱状态
        /// </summary>
        public string StatusCode { get; set; }
        /// <summary>
        /// 状态名称
        /// </summary>
        public string StatusName { get; set; }
        /// <summary>
        /// 医嘱类别
        /// </summary>
        public string CategoryCode { get; set; }
        /// <summary>
        /// 医嘱类别名称
        /// </summary>
        public string CategoryName { get; set; }

    }
}