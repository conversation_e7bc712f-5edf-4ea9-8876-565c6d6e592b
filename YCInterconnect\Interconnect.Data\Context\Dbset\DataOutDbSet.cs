﻿using Microsoft.EntityFrameworkCore;
using Interconnect.Models;


namespace Interconnect.Data.Context
{
    /// <summary>
    /// 访问接口的具体实现
    /// </summary>
    public partial class DataOutConnection
    {
        /// <summary>
        /// 床位信息
        /// </summary>
        public DbSet<BedInfo> Beds { get; set; }
        /// <summary>
        /// 护士站(病区)信息
        /// </summary>
        public DbSet<StationInfo> Stations { get; set; }

        /// <summary>
        /// 科室信息
        /// </summary>
        public DbSet<DepartmentInfo> Departments { get; set; }

        /// <summary>
        /// 人员信息
        /// </summary>
        public DbSet<EmployeeInfo> Employees { get; set; }

        /// <summary>
        /// 病人住院记录
        /// </summary>
        public DbSet<InpatientInfo> Inpatients { get; set; }

        /// 病人基本信息
        /// </summary>
        public DbSet<PatientBasicInfo> PatientBasics { get; set; }

        /// <summary>
        /// 病人医嘱明细
        /// </summary>
        public DbSet<PatientOrderDetailInfo> PatientOrderDetails { get; set; }

        /// <summary>
        /// 病人医嘱主记录
        /// </summary>
        public DbSet<PatientOrderMainInfo> PatientOrderMains { get; set; }
        /// <summary>
        /// 排班信息
        /// </summary>
        public DbSet<StationShiftInfo> StationShifts { get; set; }
        /// <summary>
        /// 医嘱字典
        /// </summary>
        public DbSet<OrderInfo> Orders { get; set; }

        /// <summary>
        /// 药物过敏
        /// </summary>
        public DbSet<DrugAllergyInfo> DrugAllergys { get; set; }

        /// <summary>
        /// 食物过敏
        /// </summary>
        public DbSet<FoodAllergyInfo> FoodAllergys { get; set; }

        /// <summary>
        /// 检验信息
        /// </summary>
        public DbSet<TestScheduleInfo> TestSchedules { get; set; }

        /// <summary>
        ///  病人诊断
        /// </summary>
        public DbSet<InterconnectPatientDiagnosisInfo> PatientDiagnosis { get; set; }

        /// <summary>
        ///  日志记录
        /// </summary>
        public DbSet<LogInfo> LogInfos { get; set; }

        /// <summary>
        ///  日志记录
        /// </summary>
        public DbSet<WardDeptInfo> WardDeptInfos { get; set; }

        //数据同步回写
        /// <summary>
        /// 护理分级评估记录
        /// </summary>
        public DbSet<NRBarthelInfo> WNRBarthelInfos { get; set; }
        /// <summary>
        ///  跌倒风险评估记录
        /// </summary>
        public DbSet<NRFallInfo> NRFallInfos { get; set; }
        /// <summary>
        ///  导管滑脱风险评估记录
        /// </summary>
        public DbSet<NRPipesInfo> NRPipesInfos { get; set; }
        /// <summary>
        /// 压疮处理基本记录
        /// </summary>
        public DbSet<NRPressureScoreInfo> NRPressureScoreInfos { get; set; }
        /// <summary>
        /// 压疮处理护理记录
        /// </summary>
        public DbSet<NRPressureScoreNursingEvaluationInfo> NRPressureScoreNursingEvaluationInfos { get; set; }
        /// <summary>
        /// 约束评估基本记录
        /// </summary>
        public DbSet<NRPhysicalRestraintFirstEvaluationInfo> NRPhysicalRestraintFirstEvaluationInfos { get; set; }
        /// <summary>
        /// 约束再评估记录
        /// </summary>
        public DbSet<NRPhysicalRestraintReEvaluationInfo> NRPhysicalRestraintReEvaluationInfos { get; set; }
        /// <summary>
        /// 护理记录
        /// </summary>
        public DbSet<NRNormalNewInfo> NRNormalNewInfos { get; set; }
        /// <summary>
        /// 病人导管基本信息
        /// </summary>
        public DbSet<NRPatientPipeInfo> NRPatientPipeInfos { get; set; }
        /// <summary>
        /// 护理记录中的导管护理
        /// </summary>
        public DbSet<NRNormalPipeInfo> NRNormalPipeInfos { get; set; }
        /// <summary>
        /// 护理记录中的专科观察
        /// </summary>
        public DbSet<NRSpecialtyObservationInfo> NRSpecialtyObservationInfos { get; set; }
        /// <summary>
        /// 病人出水量
        /// </summary>
        public DbSet<NROutflowWater> NROutflowWaters { get; set; }
        /// <summary>
        /// 病人出入水量明细
        /// </summary>
        public DbSet<NRWaterStatisticsInfo> NRWaterStatisticsInfos { get; set; }
        /// <summary>
        /// 病人出入水量小计
        /// </summary>
        public DbSet<NRWaterSummaryInfo> NRWaterSummaryInfos { get; set; }

        public DbSet<ICInpatientLogInfo> ICInpatientLogInfos { get; set; }

        //记录作业状态
        public DbSet<JobLogInfo> JobLogInfos { get; set; }

        //3.1.	体温单-病人体温记录：Nurse_Heat_Record
        public DbSet<NurseHeatRecordInfo> NurseHeatRecordInfos { get; set; }

        //3.1.	 压疮风险评估记录NursingRecord_Sore
        public DbSet<NursingRecordSoreInfo> NursingRecordSoreInfos { get; set; }

        //错误日志
        public DbSet<ErrorLogInfo> ErrorLogInfos { get; set; }
        public DbSet<MailUserDictInfo> MailUserDictInfos { get; set; }

        //存储过程调用记录
        public DbSet<ExecStoredProcedureInfo> ExecStoredProcedureInfos { get; set; }

        //存储过程字典
        public DbSet<ExecTableDictInfo> ExecTableDictInfos { get; set; }

        //人员与病区对应
        public DbSet<EmployeeStationSwitchInfo> EmployeeStationSwitchInfos { get; set; }

        //岗位与床位对应
        public DbSet<NurseBedDictInfo>  NurseBedDictInfos { get; set; }

        //人员与岗位对应
        public DbSet<EmployeeJobDictInfo>  EmployeeJobDictInfos { get; set; }

        public DbSet<NurseShiftInfo>  nurseShiftInfos { get; set; }

        public DbSet<SettingDescriptionInfo>  SettingDescriptionInfos { get; set; }

        //病区分组字典，数据同步使用
        public DbSet<StationGroupListInfo> StationGroupListInfos { get; set; }

        //同步API管理
        public DbSet<SyncAPIConfigInfo>  SyncAPIConfigInfos { get; set; }

        //传出生命体征数据
        public DbSet<TPRscheduleInfo> TPRscheduleInfos { get; set; }
        /// <summary>
        /// 返回HIS风险数据
        /// </summary>
        public DbSet<InterconnectPatientScoreMainInfo>  InterconnectPatientScoreMainInfos { get; set; }
        /// <summary>
        /// 日志
        /// </summary>
        public DbSet<SyncLogInfo>  SyncLogInfos { get; set; }
        /// <summary>
        /// log数据同步
        /// </summary>
        public DbSet<SyncDataLogInfo> SyncDatasLogInfos { get; set; }
    }
}
