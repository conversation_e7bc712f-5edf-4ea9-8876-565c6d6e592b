﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("StationShift")]
    public class StationShiftInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///护士站代号

        ///</summary>
        public string StationCode { get; set; }
        /// <summary>
        ///班别
        ///</summary>
        public string Shift { get; set; }
        /// <summary>
        ///班别开始时间

        ///</summary>
        public TimeSpan ShiftStartTime { get; set; }
        /// <summary>
        ///班别结束时间

        ///</summary>
        public TimeSpan ShiftEndTime { get; set; }
    }
}