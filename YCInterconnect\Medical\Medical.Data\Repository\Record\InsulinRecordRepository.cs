﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class InsulinRecordRepository : IInsulinRecordRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public InsulinRecordRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        ///  获取病人所有胰岛素注射记录
        /// </summary>
        /// <param name="inpatientID">病人在院号</param>
        /// <returns></returns>
        public async Task<List<InsulinRecordInfo>> GetAsync(string inpatientID)
        {
            return await _medicalDbContext.InsulinRecordInfos.Where(m =>
            m.InpatientID == inpatientID &&
            m.DeleteFlag != "*").OrderByDescending(m => m.InsulinDate).ThenByDescending(m => m.InsulinTime).ToListAsync();
        }
        /// <summary>
        ///  获取病人最后一条胰岛素注射记录
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<InsulinRecordInfo> GetSingleAsync(string inpatientID)
        {
            return await _medicalDbContext.InsulinRecordInfos.Where(m =>
            m.InpatientID == inpatientID && m.Drug == "1" && m.Glucose.HasValue &&
            m.DeleteFlag != "*").OrderByDescending(m => m.InsulinDate).ThenByDescending(m => m.InsulinTime).FirstOrDefaultAsync();
        }

        /// <summary>
        ///  获取病人所有胰岛素注射记录
        /// </summary>
        /// <param name="stationID">单位代码</param>
        /// <param name="inpatientID">病人在院号</param>
        /// <returns></returns>
        public async Task<List<InsulinRecordInfo>> GetAsync(int stationID, string inpatientID)
        {
            var data = await GetAsync(inpatientID);

            return data.Where(m => m.StationID == stationID).ToList();
        }

        /// <summary>
        /// 获取病人某日胰岛素注射记录(不进行上下文追踪)
        /// </summary>
        /// <param name="stationID">单位代码</param>
        /// <param name="inpatientID">病人在院号</param>
        /// <param name="insulinDate">注射日期</param>
        /// <returns></returns>
        public async Task<List<InsulinRecordInfo>> GetPatientRecordAsNoTrack(int stationID, string inpatientID, DateTime insulinDate)
        {
            return await _medicalDbContext.InsulinRecordInfos.Where(m =>
            m.InpatientID == inpatientID && m.DeleteFlag != "*" && m.InsulinDate == insulinDate).AsNoTracking().ToListAsync();
        }

        /// <summary>
        /// 透过ID获取数据
        /// </summary>
        /// <param name="insulinRecordID">胰岛素注射序号</param>
        /// <returns></returns>
        public async Task<InsulinRecordInfo> GetByID(string insulinRecordID)
        {
            return await _medicalDbContext.InsulinRecordInfos.Where(m => m.ID == insulinRecordID &&
            m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取时间范围内注射记录
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="inpatientID"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public async Task<List<InsulinRecordInfo>> GetAsync(int stationID, string inpatientID, DateTime startTime, DateTime endTime)
        {
            var data = await GetAsync(stationID, inpatientID);

            return data.Where(m => m.InsulinDate.Add(m.InsulinTime) > startTime && m.InsulinDate.Add(m.InsulinTime) < endTime).ToList();
        }

        public async Task<List<InsulinRecordInfo>> GetAsync(int stationID, string inpatientID, DateTime date, string shift, bool? bringToShift)
        {
            var result = await _medicalDbContext.InsulinRecordInfos.Where(m =>
            m.InpatientID == inpatientID && m.ShiftDate == date.Date && m.Shift == shift &&
            m.DeleteFlag != "*").OrderByDescending(m => m.InsulinDate).OrderByDescending(m => m.InsulinTime).ToListAsync();

            if (bringToShift.HasValue)
            {
                if (bringToShift.Value)
                {
                    result = result.Where(m => m.BringToShift == "1").ToList();
                }
                else
                {
                    result = result.Where(m => m.BringToShift != "1").ToList();
                }
            }
            return result.ToList();
        }

        //透过评估序号取得内容
        public async Task<List<InsulinRecordInfo>> GetByAssessMainID(string assessMainID)
        {
            return await _medicalDbContext.InsulinRecordInfos.Where(m => m.PatientAssessMainID == assessMainID
           && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<InsulinRecordInfo>> GetNoAssessMainIDData(string inpatientID)
        {
            return await _medicalDbContext.InsulinRecordInfos.Where(m => m.InpatientID == inpatientID
                            && (m.PatientAssessMainID == null || m.PatientAssessMainID == "") && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<InsulinRecordInfo> GetByPatientScheduleMainIDAsync(string inpatientID, string patientScheduleMainID)
        {
            return await _medicalDbContext.InsulinRecordInfos.Where(m => m.DeleteFlag != "*"
            && m.InpatientID == inpatientID && m.PatientScheduleMainID == patientScheduleMainID).FirstOrDefaultAsync();
        }

        public async Task<List<PatientGlucoseView>> GetLineChartData(string inpatientID)
        {
            return await _medicalDbContext.InsulinRecordInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*")
                .Select(m => new PatientGlucoseView
                {
                    InsulinDate = m.InsulinDate,
                    InsulinTime = m.InsulinTime,
                    ACorPCGlucose = m.ACorPCGlucose,
                    Glucose = m.Glucose,
                    GlucoseInUrine = m.GlucoseInUrine
                }).OrderBy(m => m.InsulinDate).ThenBy(m => m.InsulinTime).ToListAsync();
        }

        /// <summary>
        /// 根据inpatientID获取胰岛素电子病历需要的记录内容
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<InsulinRecordPdfView>> GetPDFUsedInsulinRecordByInpatientIDAsync(string inpatientID, int language)
        {
            var a = await _medicalDbContext.InsulinRecordInfos.Where(m => (m.Dose.HasValue || m.Drug == "4") && m.InpatientID == inpatientID && m.DeleteFlag != "*").GroupJoin(
                _medicalDbContext.ClinicSettingInfos.Where(n => n.DeleteFlag != "*" && n.SettingTypeCode == "InsulinTreatmentFrequency" && n.Language == language)
                , m => m.ACorPCGlucose, n => n.TypeValue,
                (m, n) => new InsulinRecordPdfView
                {
                    InsulinDate = m.InsulinDate,
                    InsulinTime = m.InsulinTime,
                    RecordFrequency = n.Select(s => s.Description).FirstOrDefault(),
                    Dosege = m.Dose,
                    InsulinNote = m.InsulinNote,
                    NurseEmployee = m.AddEmployeeID,
                    BodyPartID = m.BodyPartID ?? 0,
                }).GroupJoin(_medicalDbContext.ClinicSettingInfos.Where(n => n.DeleteFlag != "*" && n.SettingTypeCode == "InsulinBodyPart" && n.Language == language),
                    b => b.BodyPartID.ToString(), c => c.TypeValue, (b, c) => new InsulinRecordPdfView
                    {
                        InsulinDate = b.InsulinDate,
                        InsulinTime = b.InsulinTime,
                        RecordFrequency = b.RecordFrequency,
                        Dosege = b.Dosege,
                        InsulinNote = b.InsulinNote,
                        NurseEmployee = b.NurseEmployee,
                        BodyPartID = b.BodyPartID,
                        BodyPartName = c.Select(m => m.Description).FirstOrDefault(),
                    }
                ).GroupJoin(_medicalDbContext.userInfos.Where(s => s.DeleteFlag != "*"),
                    u => u.NurseEmployee, d => d.UserID, (u, d) => new InsulinRecordPdfView
                    {
                        InsulinDate = u.InsulinDate,
                        InsulinTime = u.InsulinTime,
                        RecordFrequency = u.RecordFrequency,
                        Dosege = u.Dosege,
                        InsulinNote = u.InsulinNote,
                        NurseEmployee = d.Select(m => m.Name).FirstOrDefault(),
                        BodyPartID = u.BodyPartID,
                        BodyPartName = u.BodyPartName,
                    }).OrderBy(m => m.InsulinDate).ThenBy(m => m.InsulinTime).ToListAsync();
            return a;
        }

        public async Task<List<PatientGlucoseView>> GetPatientDaySugar(string inpatientID, int stationID, DateTime shiftDate)
        {
            return await _medicalDbContext.InsulinRecordInfos.Where(m => m.InpatientID == inpatientID
                 && m.StationID == stationID && m.ShiftDate == shiftDate
                 && m.DeleteFlag != "*")
                .Select(m => new PatientGlucoseView
                {
                    InsulinDate = m.InsulinDate,
                    InsulinTime = m.InsulinTime,
                    ACorPCGlucose = m.ACorPCGlucose,
                    Glucose = m.Glucose,
                    GlucoseInUrine = m.GlucoseInUrine
                }).OrderBy(m => m.InsulinDate).ThenBy(m => m.InsulinTime).ToListAsync();
        }

        /// <summary>
        /// 获取未同步的血糖数据
        /// </summary>
        /// <param name="syncDateTime">当前同步时间点</param>
        /// <returns></returns>
        public async Task<List<InsulinRecordInfo>> GetUnSyncInsulinInfos(DateTime syncDateTime)
        {
            return await _medicalDbContext.InsulinRecordInfos.Where(t => t.ModifyDate >= syncDateTime).ToListAsync();
        }

        public async Task<List<InsulinRecordInfo>> GetRecordsBySourceID(string sourceID, string sourceType)
        {
            var data = await _medicalDbContext.InsulinRecordInfos.Where(m => m.SourceID == sourceID && m.DeleteFlag != "*").ToListAsync();
            if (!string.IsNullOrEmpty(sourceType))
            {
                data = data.Where(m => !string.IsNullOrEmpty(m.SourceType) && m.SourceType.Trim() == sourceType.Trim()).ToList();
            }
            return data;
        }
        /// <summary>
        /// 根据患者ID获取胰岛素数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<InsulinRecordInfo>> GetInsulinByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.InsulinRecordInfos.Where(m => (m.Dose.HasValue || m.Drug == "4") && m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 获取病人血糖数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public async Task<List<InsulinView>> GetInpatientInsulinView(string inpatientID, DateTime startDate, DateTime endDate)
        {
            return await _medicalDbContext.InsulinRecordInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" && m.InsulinDate >= startDate
            && m.InsulinDate <= endDate)
                .Select(m => new InsulinView
                {
                    ID = m.ID,
                    InsulinDate = m.InsulinDate,
                    InsulinTime = m.InsulinTime,
                    ACorPCGlucose = m.ACorPCGlucose,
                    Route = m.Route,
                    Glucose = m.Glucose,
                    GlucoseInUrine = m.GlucoseInUrine,
                    Dose = m.Dose,
                    Drug = m.Drug,
                    BodyPartID = m.BodyPartID.HasValue ? m.BodyPartID.Value.ToString() : "",
                    InsulinNote = m.InsulinNote,
                    InformPhysician = m.InformPhysician,
                    PatientScheduleMainID = m.PatientScheduleMainID,
                    ModifyPersonID = m.ModifyPersonID,
                    AddEmployeeID = m.AddEmployeeID,
                    BringToNursingRecord = m.BringToNursingRecord,
                    InsulinDateTime = m.InsulinDate.Add(m.InsulinTime),
                    Disabled = m.SourceType == "PatientMedicineSchedule",
                }).ToListAsync();
        }
        /// <summary>
        /// 根据sourceID获取血糖监测值
        /// </summary>
        /// <param name="sourceID">来源ID</param>
        /// <param name="sourceType">来源类别</param>
        /// <returns></returns>
        public async Task<string> GetGlucoseValuesBySourceID(string sourceID, string sourceType)
        {
            return await _medicalDbContext.InsulinRecordInfos.Where(m => m.SourceID == sourceID && m.SourceType == sourceType && m.DeleteFlag != "*")
                .Select(m => m.Glucose.HasValue ? m.Glucose.Value.ToString() : "").FirstOrDefaultAsync();
        }

        public async Task<List<InsulinRecordInfo>> GetDataByPatientScheduleMainIDAsync(string inpatientID, string patientScheduleMainID)
        {
            return await _medicalDbContext.InsulinRecordInfos.Where(m => m.DeleteFlag != "*"
            && m.InpatientID == inpatientID && m.PatientScheduleMainID == patientScheduleMainID).ToListAsync();
        }

        public async Task<List<InsulinView>> GetBatchGlucose(List<string> inpatientIDs, List<string> patientScheduleMainIDs)
        {
            return await _medicalDbContext.InsulinRecordInfos.Where(m => m.DeleteFlag != "*"
            && inpatientIDs.Contains(m.InpatientID) && patientScheduleMainIDs.Contains(m.PatientScheduleMainID))
            .Select(m => new InsulinView
            {
                ID = m.ID,
                InsulinDate = m.InsulinDate,
                InsulinTime = m.InsulinTime,
                ACorPCGlucose = m.ACorPCGlucose,
                Route = m.Route,
                Glucose = m.Glucose,
                GlucoseInUrine = m.GlucoseInUrine,
                Dose = m.Dose,
                Drug = m.Drug,
                BodyPartID = m.BodyPartID.HasValue ? m.BodyPartID.Value.ToString() : "",
                InsulinNote = m.InsulinNote,
                InformPhysician = m.InformPhysician,
                PatientScheduleMainID = m.PatientScheduleMainID,
                ModifyPersonID = m.ModifyPersonID,
                AddEmployeeID = m.AddEmployeeID,
                InpatientID = m.InpatientID,
                StationID = m.StationID,
                BringToNursingRecord = m.BringToNursingRecord
            }).ToListAsync();
        }
        /// <summary>
        /// 根据来源ID获取血糖主键
        /// </summary>
        /// <returns></returns>
        public async Task<string> GetIDBySourceID(string sourceID)
        {
            return await _medicalDbContext.InsulinRecordInfos
                .Where(m => m.SourceID == sourceID && m.DeleteFlag != "*").Select(m => m.ID).FirstOrDefaultAsync();
        }

        public async Task<List<InsulinView>> GetInpatientInsulinView(string inpatientID)
        {
            return await _medicalDbContext.InsulinRecordInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*")
                .Select(m => new InsulinView
                {
                    ID = m.ID,
                    InsulinDate = m.InsulinDate,
                    InsulinTime = m.InsulinTime,
                    ACorPCGlucose = m.ACorPCGlucose,
                    Route = m.Route,
                    Glucose = m.Glucose,
                    GlucoseInUrine = m.GlucoseInUrine,
                    Dose = m.Dose,
                    Drug = m.Drug,
                    BodyPartID = m.BodyPartID.HasValue ? m.BodyPartID.Value.ToString() : "",
                    InsulinNote = m.InsulinNote,
                    InformPhysician = m.InformPhysician,
                    PatientScheduleMainID = m.PatientScheduleMainID,
                    ModifyPersonID = m.ModifyPersonID,
                    AddEmployeeID = m.AddEmployeeID,
                    BringToNursingRecord = m.BringToNursingRecord,
                    InsulinDateTime = m.InsulinDate.Add(m.InsulinTime),
                    Disabled = m.SourceType == "PatientMedicineSchedule",
                }).ToListAsync();
        }
        /// <summary>
        /// 根据病人数据和his键值查询数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="hisKey"></param>
        /// <returns></returns>
        public async Task<List<InsulinRecordInfo>> GetPatientRecordByHisKey(string inpatientID, string hisKey)
        {
            return await _medicalDbContext.InsulinRecordInfos.Where(m => m.InpatientID == inpatientID && m.HISKey == hisKey).ToListAsync();
        }
        /// <summary>
        /// 获取某类数据的范围
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public async Task<List<InsulinRecordInfo>> GetPatientByDrugRange(string inpatientID, DateTime startDate, DateTime endDate, string drug)
        {
            // 最小值
            return await _medicalDbContext.InsulinRecordInfos
                                .Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*"
                                            && m.Drug.Trim() == drug.Trim() && m.Glucose.HasValue).ToListAsync();
        }
    }
}