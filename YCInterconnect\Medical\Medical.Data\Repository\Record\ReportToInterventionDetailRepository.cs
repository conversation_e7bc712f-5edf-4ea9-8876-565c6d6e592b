﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class ReportToInterventionDetailRepository : IReportToInterventionDetailRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly GetCacheService _getCacheService;

        public ReportToInterventionDetailRepository(MedicalDbContext db, IMemoryCache memoryCache, GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _getCacheService = getCacheService;
        }
        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<ReportToInterventionDetail>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            return await _medicalDbContext.ReportToInterventionDetails.Where(m => m.DeleteFlag != "*").ToListAsync();
        }
        public string GetCacheType()
        {
            return CacheType.ReportToInterventionDetail.ToString();
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        /// <summary>
        /// 获取明细表数据
        /// </summary>
        /// <param name="RecordsListID"></param>
        /// <param name="InterventionMainID"></param>
        /// <returns></returns>
        public async Task<List<ReportToInterventionDetail>> GetDetailByMain(int RecordsListID, int InterventionMainID)
        {
            var datas = await GetCacheAsync() as List<ReportToInterventionDetail>;
            return datas.Where(t => t.RecordsListID == RecordsListID && t.InterventionMainID == InterventionMainID && t.DeleteFlag != "*").ToList();
        }


    }
}
