﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class AssessScoreRangeRepository : IAssessScoreRangeRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;


        public AssessScoreRangeRepository(MedicalDbContext db, IMemoryCache memoryCache
             , SessionCommonServer sessionCommonServer, GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<AssessScoreRangeInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            dict.TryGetValue("language", out var language);
            return await _medicalDbContext.AssessScoreRangeInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.Language == (Int32)language && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<AssessScoreRangeInfo>> GetAsync()
        {
            var data = (List<AssessScoreRangeInfo>)await GetCacheAsync();
            if (data != null)
            {
                return data;
            }
            return new List<AssessScoreRangeInfo>();
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.AssessScoreRange.GetKey(_sessionCommonServer);
        }
    }
}
