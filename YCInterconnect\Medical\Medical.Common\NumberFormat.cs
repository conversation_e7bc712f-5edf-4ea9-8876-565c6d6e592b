﻿namespace Medical.Common
{
    public static class NumberFormat
    {

        /// <summary>
        /// 数值转换字符，并保留小数位数
        /// </summary>
        /// <param name="dataValue"></param>
        /// <param name="decimalPlaces">小数为数，0 没有小数</param>
        /// <returns></returns>
        public static string NumberToString(decimal dataValue, int decimalPlaces)
        {
            if (decimalPlaces == 0)
            {
                return string.Format("{0:0}", dataValue);
            }
            if (decimalPlaces == 1)
            {
                return string.Format("{0:0.#}", dataValue);
            }
            if (decimalPlaces == 2)
            {
                return string.Format("{0:0.##}", dataValue);
            }
            if (decimalPlaces == 2)
            {
                return string.Format("{0:0.###}", dataValue);
            }
            return string.Format("{0:0}", dataValue);
        }
    }
}
