﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    /// <summary>
    /// 护士站信息
    /// </summary>
    [Serializable]
    [Table("Station")]
    public class StationInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///护士站名称
        ///</summary>
        public string StationName { get; set; }
        /// <summary>
        ///护士站代号
        ///</summary>
        public string StationCode { get; set; }
        /// <summary>
        ///排序
        ///</summary>
        public short Sort { get; set; }
        /// <summary>
        ///护理问题更新频率
        ///</summary>
        public short ProblemRenewFrequency { get; set; }
        /// <summary>
        ///ICU注记
        ///</summary>
        public string ICUFlag { get; set; }
    }
}