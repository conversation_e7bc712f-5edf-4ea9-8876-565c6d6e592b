﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class MeasuringPointRepository : IMeasuringPointRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;

        public MeasuringPointRepository(MedicalDbContext medicalDbContext, IMemoryCache memoryCache, SessionCommonServer sessionCommonServer)
        {
            _medicalDbContext = medicalDbContext;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();

            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);

            var datas = await this._memoryCache.GetOrCreateAsync(key, async entry =>
            {
                entry.SetAbsoluteExpiration(TimeSpan.FromSeconds(600000));
                return await _medicalDbContext.MeasuringPointInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
            });
            return datas;
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.MeasuringPoint.GetKey(_sessionCommonServer);
        }

        public async Task<List<MeasuringPointInfo>> GetAsync(string typeCode)
        {
            string key = GetCacheType();

            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);

            //20190917 LX 体温单获取配置只获取通用配置
            var query = _medicalDbContext.MeasuringPointInfos.Where(
                t => t.TypeCode == typeCode && t.HospitalID == hospitalID
                && t.DeptmentListID == 999999 && t.DeleteFlag != "*");

            return await query.ToListAsync();
        }
    }
}