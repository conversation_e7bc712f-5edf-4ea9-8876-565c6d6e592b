﻿using System;
using System.Collections.Generic;
using System.Text;

namespace ViewModel
{
   public class HISInpatientLogView
    {
        /// <summary>
        /// 住院号
        /// </summary>
        public string CaseNumber { get; set; }      
        /// <summary>
        /// 病区(护理单元)代码
        /// </summary>
        public string StationCode { get; set; }
        /// <summary>
        /// 科室代码
        /// </summary>
        public string DepartmentCode { get; set; }
        /// <summary>
        /// 床位号码
        /// </summary>
        public string BedCode { get; set; }
        /// <summary>
        /// 床位代码
        /// </summary>
        public string BedNumber { get; set; }
        /// <summary>
        /// 新病区码
        /// </summary>
        public string NewStationCode { get; set; }
        /// <summary>
        /// 新科室代码
        /// </summary>
        public string NewDepartmentCode { get; set; }
        /// <summary>
        /// 新床位编号
        /// </summary>
        public string NewBedCode { get; set; }
        /// <summary>
        /// 新床位号
        /// </summary>
        public string NewBedNumber { get; set; }
        /// <summary>
        /// 异动时间
        /// </summary>
        public string ModifyDate { get; set; }

    }
}
