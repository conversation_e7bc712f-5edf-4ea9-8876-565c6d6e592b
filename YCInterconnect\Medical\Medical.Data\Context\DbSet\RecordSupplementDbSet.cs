﻿using Medical.Models.RecordSupplement;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public partial class MedicalDbContext
    {
        /// <summary>
        /// 根据HandoverRecordID查询交接班补录信息
        /// </summary>
        public DbSet<HandoverSupplementInfo> HandoverSupplementInfos { get; set; }
        /// <summary>
        /// 补录日志表
        /// </summary>
        public DbSet<SupplementRecordLogInfo> SupplementRecordLogInfos { get; set; }

    }
}
