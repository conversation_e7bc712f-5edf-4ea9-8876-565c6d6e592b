﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientOperationRepository : IPatientOperationRepository
    {
        private readonly MedicalDbContext _dbContext = null;
        /// <summary>
        /// 在院清单呈现状态
        /// </summary>
        private readonly List<int> INHOSPITALLIST = new List<int> { 30, 40 };

        public PatientOperationRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }

        public async Task<List<PatientOperationInfo>> GetPatientOperateList(string inPatientID)
        {
            return await _dbContext.PatientOperationInfos.Where(m =>
           m.InpatientID == inPatientID &&
           m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据InpatientID获取最近一次手术的手术持续时间
        /// </summary>
        /// <param name="inPatientID"></param>
        /// <returns></returns>
        public async Task<PatientOperationInfo> GetRecentOperationDateTimeByInpatientID(string inPatientID)
        {
            return await _dbContext.PatientOperationInfos.AsNoTracking().Where(m =>
              m.InpatientID == inPatientID && m.DeleteFlag != "*" && m.OperationEndDateTime.HasValue
                ).OrderByDescending(m => m.OperationEndDateTime).FirstOrDefaultAsync();
        }

        public PatientOperationInfo GetPatientOperateListByCondition(string caseNumber, string chartNO, DateTime operateDate, string OperateName)
        {
            return _dbContext.PatientOperationInfos.Where(m =>
           m.CaseNumber == caseNumber && m.ChartNo == chartNO && m.OperationDate == operateDate
           && m.OperationName == OperateName
           && m.OperationName != ""
           ).FirstOrDefault();
        }

        /// <summary>
        /// 根据hisOperationNo取数据记录
        /// </summary>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public async Task<PatientOperationInfo> GetPatientOperateByCaseNumberAndOperationNo(string caseNumber, string operationNo)
        {
            return await _dbContext.PatientOperationInfos.Where(m => m.CaseNumber == caseNumber && m.HISOperationNo == operationNo
            && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据hisOperationNo取数据记录
        /// </summary>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public async Task<PatientOperationInfo> GetPatientOperateByOperationNo(string operationNo)
        {
            return await _dbContext.PatientOperationInfos.Where(m => m.HISOperationNo == operationNo
            && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据hisOperationNo取数据记录
        /// </summary>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public async Task<PatientOperationInfo> GetPatientOperateByInpatientIDAndOperationNo(string InpatientID, string operationNo)
        {
            return await _dbContext.PatientOperationInfos.Where(m => m.InpatientID == InpatientID && m.HISOperationNo == operationNo
            && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据hisOperationNo取数据记录
        /// </summary>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public async Task<PatientOperationInfo> GetPatientOperationViewByOperationNo(string operationNo)
        {
            return await _dbContext.PatientOperationInfos.Where(m => m.HISOperationNo == operationNo
            && m.DeleteFlag != "*").Select(m => new PatientOperationInfo
            {
                InpatientID = m.InpatientID,
                OperationName = m.OperationName,
                AnesthesiaMethod = m.AnesthesiaMethod,
                HISOperationNo = m.HISOperationNo,
                ScheduledDatetime = m.ScheduledDatetime,
            }).FirstOrDefaultAsync();
        }

        public async Task<PatientOperationInfo> GetPatientOperationViewByinpatientId(string inpatientid)
        {
            return await _dbContext.PatientOperationInfos.Where(m => m.InpatientID == inpatientid
            && m.DeleteFlag != "*" && m.ScheduledDatetime != null).OrderByDescending(m => m.ScheduledDatetime).Select(m => new PatientOperationInfo
            {
                InpatientID = m.InpatientID,
                OperationName = m.OperationName,
                AnesthesiaMethod = m.AnesthesiaMethod,
                HISOperationNo = m.HISOperationNo,
                ScheduledDatetime = m.ScheduledDatetime,
            }).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据hisOperationNo取数据记录(同步获取)
        /// </summary>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public PatientOperationInfo GetPatientOperateByOperationNoSync(string operationNo)
        {
            return _dbContext.PatientOperationInfos.Where(m => m.HISOperationNo == operationNo
           && m.DeleteFlag != "*").FirstOrDefault();
        }
        /// <summary>
        /// 中山手术同步使用,如有修改，请一并修改同步程序
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        public async Task<int> GetPatientOperateListCountByCondition(string caseNumber)
        {
            return await _dbContext.PatientOperationInfos.Where(m => m.CaseNumber == caseNumber).CountAsync();
        }

        /// <summary>
        /// 获取取得病区手术数据
        /// </summary>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<List<StationOperationView>> GetStationPatientOperation(int stationID)
        {
            var query = await (from a in _dbContext.InpatientDatas
                               join b in _dbContext.PatientOperationInfos on a.ID equals b.InpatientID
                               join c in _dbContext.PatientBasicDatas on a.PatientID equals c.PatientID
                               where a.StationID == stationID && InHospitalStatus.INHOSPITALLIST.Contains(a.InHospitalStatus ?? -1)
                               && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                               select new StationOperationView
                               {
                                   InpatientID = a.ID,
                                   BedNumber = a.BedNumber,
                                   PatientName = c.PatientName,
                                   OperateIDListID = b.PatientOperationID,
                                   OperateDate = b.OperationDate,
                                   OperateName = b.OperationName,
                                   AnesthesiaMethod = b.AnesthesiaMethod
                               }).ToListAsync();
            return query;
        }

        /// <summary>
        /// 获取取得病区手术数据
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="shiftDate"></param>
        /// <param name="shiftID"></param>
        /// <param name="userID"></param>
        /// <returns></returns>
        public async Task<List<StationOperationView>> GetMainCarePatientOperationAsync(int stationID, DateTime shiftDate, int shiftID, string userID)
        {
            var query = await (from a in _dbContext.InpatientDatas
                               join b in _dbContext.Attendance on new { InpatientID = a.ID, a.StationID, AttendanceDate = shiftDate, StationShiftID = shiftID, NurseEmployeeID = userID }
                               equals new { b.InpatientID, b.StationID, b.AttendanceDate, b.StationShiftID, b.NurseEmployeeID }
                               join c in _dbContext.PatientOperationInfos on a.ID equals c.InpatientID
                               join d in _dbContext.PatientBasicDatas on a.PatientID equals d.PatientID
                               where a.StationID == stationID && InHospitalStatus.INHOSPITALLIST.Contains(a.InHospitalStatus ?? -1)
                               && a.DeleteFlag != "*" && b.DeleteFlag != "*" && c.DeleteFlag != "*"
                               select new StationOperationView
                               {
                                   InpatientID = a.ID,
                                   BedNumber = a.BedNumber,
                                   PatientName = d.PatientName,
                                   OperateIDListID = c.PatientOperationID,
                                   OperateDate = c.OperationDate,
                                   OperateName = c.OperationName,
                                   AnesthesiaMethod = c.AnesthesiaMethod
                               }).ToListAsync();
            return query;
        }

        /// <summary>
        /// 根据ID获取信息
        /// </summary>
        /// <param name="ID"></param>
        /// <returns></returns>
        public async Task<PatientOperationInfo> GetMainByID(string ID)
        {
            return await _dbContext.PatientOperationInfos.Where(m => m.PatientOperationID == ID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<PatientOperationInfo> GetCareByOperationCodeAsync(string patientOperationID, string operationCode)
        {
            return await _dbContext.PatientOperationInfos.Where(m => m.PatientOperationID == patientOperationID && m.OperationCode == operationCode && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<List<PatientOperationInfo>> GetByOperationCodeAsync(string caseNumber, List<string> operationCodes)
        {
            return await _dbContext.PatientOperationInfos.Where(m => m.CaseNumber == caseNumber && operationCodes.Contains(m.OperationCode)
            && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientOperationInfo>> GetBycaseNumberAsync(string caseNumber)
        {
            return await _dbContext.PatientOperationInfos.Where(m => m.CaseNumber == caseNumber
            && m.DeleteFlag != "*").ToListAsync();
        }

        public PatientOperationInfo GetPatientOperateByOperationNo(string caseNumber, string operationNo)
        {
            return _dbContext.PatientOperationInfos.Where(m => m.CaseNumber == caseNumber && m.HISOperationNo == operationNo).FirstOrDefault();
        }

        public async Task<List<PatientOperationInfo>> GetOperationByDateAsync(DateTime date)
        {
            return await _dbContext.PatientOperationInfos.Where(m => m.ScheduledDatetime >= date).ToListAsync();
        }

        public async Task<List<string>> GetPatientOperation(string inpatientID, DateTime dateTime)
        {
            return await _dbContext.PatientOperationInfos.Where(
                           m => m.InpatientID == inpatientID && m.OperationDate <= dateTime
                        && m.DeleteFlag != "*")
                   .Select(m => m.OperationName).ToListAsync();
        }

        public async Task<List<PatientOperationInfo>> GetByDateAsync(DateTime date)
        {
            return await _dbContext.PatientOperationInfos.Where(m => m.ScheduledDatetime != null && m.ScheduledDatetime.Value.Date == date.Date && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 获取一段时间内未结束的手术预约唯一码
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <returns></returns>
        public async Task<List<string>> GetInpatientUnEndOperationNoByDate(string inpatientID, DateTime startDateTime, DateTime endDateTime)
        {
            return await _dbContext.PatientOperationInfos.Where(m => m.InpatientID == inpatientID && m.ScheduledDatetime >= startDateTime && m.ScheduledDatetime <= endDateTime &&
            !m.OperationEndDateTime.HasValue && m.DeleteFlag != "*").Select(m => m.HISOperationNo).ToListAsync();
        }

        /// <summary>
        /// 获取病人一段时间内的手术预约信息
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <returns></returns>
        public async Task<List<PatientOperationInfo>> GetPatientOperationsByDate(string inpatientID, DateTime startDateTime, DateTime endDateTime)
        {
            return await _dbContext.PatientOperationInfos.Where(m => m.InpatientID == inpatientID && m.ScheduledDatetime >= startDateTime && m.ScheduledDatetime <= endDateTime &&
            m.DeleteFlag != "*" && m.ScheduledDatetime.HasValue).OrderBy(m => m.ScheduledDatetime).ToListAsync();
        }

        /// <summary>
        /// 根据hisOperationNo查找手术是否存在
        /// </summary>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public async Task<bool> CheckExistByNO(string operationNo)
        {
            return await _dbContext.PatientOperationInfos.AsNoTracking().AnyAsync(m => m.HISOperationNo == operationNo && m.DeleteFlag != "*");
        }

        public async Task<List<PatientOperationInfo>> GetOperationListByDateAsync(DateTime startDate, DateTime endDate)
        {
            return await _dbContext.PatientOperationInfos.Where(m => m.ScheduledDatetime >= startDate && m.ScheduledDatetime <= endDate &&
            m.DeleteFlag != "*" && m.ScheduledDatetime.HasValue).OrderBy(m => m.ScheduledDatetime).ToListAsync();
        }

        public async Task<List<PatientOperationInfo>> GetOperationListByInpatientIDAsync(List<string> inpatisntIDs, DateTime startDate, DateTime endDate)
        {
            return await _dbContext.PatientOperationInfos.Where(m => m.ScheduledDatetime >= startDate && m.ScheduledDatetime <= endDate &&
            m.DeleteFlag != "*" && m.ScheduledDatetime.HasValue).OrderBy(m => m.ScheduledDatetime).Select(m => new PatientOperationInfo
            {
                InpatientID = m.InpatientID,
                OperationName = m.OperationName,
                AnesthesiaMethod = m.AnesthesiaMethod,
                HISOperationNo = m.HISOperationNo,
                ScheduledDatetime = m.ScheduledDatetime,
                OperationDate = m.OperationDate
            }).ToListAsync();
        }

        public async Task<List<PatientOperationInfo>> GetByInpatientIDList(List<string> inpatientIDs)
        {
            return await _dbContext.PatientOperationInfos.Where(m => inpatientIDs.Contains(m.InpatientID) && m.DeleteFlag != "*").ToListAsync();
        }
    }
}