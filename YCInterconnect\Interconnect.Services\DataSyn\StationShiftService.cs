﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Interconnect.Data.Interface;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using NLog;
using System.Linq;
using Microsoft.Extensions.Options;
using Medical.Data.Context;
using Interconnect.Data.Context;
using Medical.Common;
using Arch.EntityFrameworkCore.UnitOfWork;

namespace Interconnect.Services
{
    public class StationShiftService : IStationShiftService
    {
        //Mdeical
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IStationListRepository _IStationListRepository;
        private readonly Medical.Data.Interface.IStationShiftRepository _IMedicalStationShiftRepository;
        private readonly ILogInfoServices _ILogInfoServices;
        private readonly IAppConfigSettingRepository  _appConfigSettingRepository;

        //Interconnect
        private readonly IUnitOfWork<DataOutConnection> _unitOfWorkOut;
        private readonly IOptions<SystemConfig> _config;
        private readonly Data.Interface.IStationShiftRepository _IShzsyyStationShiftRepository;
        private static Logger _logger = LogManager.GetCurrentClassLogger();  
        
        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";
        /// <summary>
        /// 系统默认操作人
        /// </summary>
        private string MODIFYPERSONID = "TongBu";

        public StationShiftService(
              IUnitOfWork<MedicalDbContext> UnitOfWork
            , IUnitOfWork<DataOutConnection> UnitOfWorkOut
            , IStationListRepository StationListRepository
            , Medical.Data.Interface.IStationShiftRepository MedicalStationShiftRepository
            , Data.Interface.IStationShiftRepository ShzsyyStationShiftRepository
            , IOptions<SystemConfig> config
             , ILogInfoServices LogInfoServices
            , IAppConfigSettingRepository  appConfigSettingRepository
            )
        {
            _unitOfWork = UnitOfWork;
            _unitOfWorkOut = UnitOfWorkOut; 
            _IStationListRepository = StationListRepository;
            _IMedicalStationShiftRepository = MedicalStationShiftRepository;
            _IShzsyyStationShiftRepository = ShzsyyStationShiftRepository;
            _config = config;
            _ILogInfoServices = LogInfoServices;
            _appConfigSettingRepository = appConfigSettingRepository;
           
        }

      
        /// <summary>
        /// 输出没有同步的排班数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<Models.StationShiftInfo>> GetAllAsync()
        {
            //从配置档中获取数据 梁宝华 2020-04-29
            var resultTongbuCount = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "TongbuCount");
            var tongbuCount = 0;
            if (StringCheck.IsNumeric(resultTongbuCount))
            {
                tongbuCount = int.Parse(resultTongbuCount);
            }
            var resultTakeRows = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "TakeRows");
            var takeRows = 0;
            if (StringCheck.IsNumeric(resultTakeRows))
            {
                takeRows = int.Parse(resultTakeRows);
            }
            List<Models.StationShiftInfo> BedList = 
                await _IShzsyyStationShiftRepository.GetAsync(tongbuCount, takeRows);
            return BedList;
        }

        /// <summary>
        /// 获取Medical排班数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<Medical.Models.StationShiftInfo>> GetMedicalStationShiftAll()
        {
            List<Medical.Models.StationShiftInfo> StationShift = await _IMedicalStationShiftRepository.GetShiftByStationAllAsync();
            return StationShift;
        }
        /// <summary>
        /// 获取Medical病区对照信息
        /// </summary>
        /// <returns></returns>
        public async Task<List<StationListInfo>> GetStationListAsync()
        {
            List<StationListInfo> BedList = await _IStationListRepository.GetAllAsync();
            return BedList;
        }

        /// <summary>
        /// 如果发现有没有同步的数据，则一直获取，直至完全同步
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SynchronizationMain()
        { 
            var OriginalList = await GetAllAsync();
            var MedicalStationList = await GetStationListAsync();
            while (OriginalList.Count > 0) //如果没有同步完成，则继续同步
            {
                if (!await SynchronizationDetail(OriginalList, MedicalStationList))
                {
                    return false;
                }
                OriginalList = await GetAllAsync();
            }
            return true;
        }

        private async Task<bool> SynchronizationDetail(List<Models.StationShiftInfo> OriginalList
            ,List<StationListInfo> MedicalStationList)
        {
            var MedicalList = await GetMedicalStationShiftAll();         
            //xml20181218 屏蔽，测试是否需要
           // int MaxID = await _IMedicalStationShiftRepository.GetMaxAsync();
            List<Medical.Models.StationShiftInfo> Insertlist = new List<Medical.Models.StationShiftInfo>();
            Medical.Models.StationShiftInfo t = null;

            string Tablename = "StationShift";
            List<LogInfo> LogList = new List<LogInfo>();
            LogInfo TempLog = null;
            int Failcount = 0;
            TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, " 开始进行数据同步，数据条数：" + OriginalList.Count);
            LogList.Add(TempLog); 

            #region "数据同步"
            foreach (var item in OriginalList)
            {
                item.Counts = item.Counts ?? 0;
                item.Counts = item.Counts + 1;
                try
                {
                    //获取病区对照信息
                    var TempStation = MedicalStationList.Where(m => m.StationCode.Trim() == item.StationCode.Trim()).ToList();
                    if (TempStation.Count != 1)
                    {

                        TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, "["+item.StationCode + "]查询床位所属病区信息错误!");
                        LogList.Add(TempLog);
                        Failcount++;
                        continue;
                    }

                    //获取Medical中的排班信息(根据病区和班别)
                    var TempMedicalList = MedicalList.Where(m => m.StationID == TempStation[0].ID && m.Shift == item.Shift).ToList();

                    //获取插入的数据
                    var TempInsertlist = Insertlist.Where(m => m.Shift == item.Shift && m.StationID== TempStation[0].ID).ToList();
                     

                    //如果不存在进行新增
                    if (TempMedicalList.Count < 1 && TempInsertlist.Count<1)
                    {
                        t = new Medical.Models.StationShiftInfo
                        {
                           // ID = MaxID,
                            HospitalID = _config.Value.HospitalID,
                            StationID = TempStation[0].ID,
                            Shift = item.Shift,
                            ShiftStartTime = item.ShiftStartTime,
                            ShiftEndTime = item.ShiftEndTime,
                            CrossDayFlag = "",
                            ModifyPersonID = MODIFYPERSONID,                            
                            ModifyDate = DateTime.Now,
                            DeleteFlag = ""
                        };
                        item.DataPumpFlag = "*";
                        item.DataPumpDate = DateTime.Now;
                        Insertlist.Add(t);
                       // MaxID++;
                    }
                    //如果根据根据病区和班别查询到这条记录，则判断其他的记录是否发生了改变
                    if (TempMedicalList.Count == 1)
                    {
                        if (TempMedicalList[0].ShiftStartTime!=item.ShiftStartTime)
                        {
                            TempMedicalList[0].ShiftEndTime = item.ShiftEndTime;
                        }

                        if (TempMedicalList[0].ShiftEndTime != item.ShiftEndTime)
                        {
                            TempMedicalList[0].ShiftEndTime = item.ShiftEndTime;
                        }

                        if (item.DeleteFlag == "*")
                        {
                            TempMedicalList[0].DeleteFlag = "*";
                        }
                        TempMedicalList[0].ModifyPersonID = MODIFYPERSONID;
                        TempMedicalList[0].ModifyDate = DateTime.Now;
                        item.DataPumpFlag = "*";
                        item.DataPumpDate = DateTime.Now;
                    }
                }

                catch (Exception ex)
                {
                    _logger.Error(Tablename+"||同步错误：" + item.StationCode + "||" + item.Shift + ":" + ex.ToString());
                    return false;
                }
            }
            #endregion
            #region "数据更新"
            if (OriginalList.Count >= 1)
            {
                try
                {
                    _unitOfWork.GetRepository<Medical.Models.StationShiftInfo>().Insert(Insertlist);
                    _unitOfWork.GetRepository<Medical.Models.StationShiftInfo>().Update(MedicalList);
                    _unitOfWorkOut.GetRepository<Models.StationShiftInfo>().Update(OriginalList);
                    //_unitOfWorkOut.GetRepository<LogInfo>().Insert(LogList);
                     _unitOfWork.SaveChanges();
                   // await _unitOfWorkOut.SaveChangesAsync();
                    _unitOfWorkOut.SaveChanges();
                }
                catch (Exception ex)
                {
                    _logger.Error(Tablename + "||同步失败||" + ex.ToString());
                    return false;
                } 
            }

            TempLog = _ILogInfoServices.InnsertLogAsync(Tablename, " 同步结束 成功：" + (OriginalList.Count - Failcount).ToString() + "条！ 失败：" + Failcount.ToString() + "条！");

            LogList.Add(TempLog);
            int ItemNo = 0;
            string Guid = "";
            Guid = System.Guid.NewGuid().ToString("N");
            // item.Guid = Guid;
            foreach (var item in LogList)
            {
                item.Guid = Guid;
                item.ItemNo = ItemNo;
                ItemNo++;
            }
            try
            {
                _unitOfWorkOut.GetRepository<LogInfo>().Insert(LogList);
               // await _unitOfWorkOut.SaveChangesAsync();
                _unitOfWorkOut.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error(Tablename + "同步成功，但写同步日志失败||" + ex.ToString());
            }

            _logger.Info(Tablename + "    同步完成!" + DateTime.Now);
            return true;
            #endregion
        }

    }
}

