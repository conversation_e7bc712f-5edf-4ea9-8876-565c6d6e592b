﻿using Interconnect.Services.Interface;
using Medical.Common;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using System;
using System.Threading.Tasks;

namespace Interconnect.API.Controllers
{
    /// <summary>
    /// 信息同步
    /// </summary>
    [Produces("application/json")]
    [Route("api/HisOrder")]
    [EnableCors("any")]
    public class SyncOrderController : Controller
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IGetHisJsonService _getHisJsonService;
        private readonly ISyncOrderService _syncOrderService;
        private readonly ICommonHelper _commonHelper;

        /// <summary>
        /// 医嘱同步
        /// </summary>
        /// <param name="syncInpatientService"></param>
        public SyncOrderController(
             IGetHisJsonService getHisJsonService
            , ISyncOrderService syncOrderService
            , ICommonHelper commonHelper
            )
        {
            _getHisJsonService = getHisJsonService;
            _syncOrderService = syncOrderService;
            _commonHelper = commonHelper;
        }

        /// <summary>
        /// 获取平台单病人医嘱JSON(打印)
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetOrderHisJson")]
        public async Task<IActionResult> SyncNewInPatient(string caseNumber)
        {
            var resultSrt = await _getHisJsonService.GetOrderHisJsonByCaseNumber(caseNumber);
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }
        /// <summary>
        /// 同步单病人医嘱
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncHisOrderByCaseNumber")]
        public async Task<IActionResult> SyncHisOrderByCaseNumber(string caseNumber)
        {
            var resultSrt = await _syncOrderService.SyncHisOrderByCaseNumber(caseNumber);
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }
        /// <summary>
        /// 医嘱停止本班别措施补处理
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="patientOrderMainID"></param>
        /// <param name="endDateTime"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("StopSchduleByOrder")]
        public async Task<IActionResult> SyncHisOrderByCaseNumber(string inpatientID, string patientOrderMainID, DateTime endDateTime)
        {
            await _commonHelper.StopSchduleByOrder(inpatientID, patientOrderMainID, endDateTime);
            var result = new ResponseResult
            {
                Data = "",
                Code = 1
            };
            return result.ToJson();
        }
        /// <summary>
        /// 根据病区码同步医嘱
        /// </summary>
        /// <param name="stationCode"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncHisOrderByStationCode")]
        public async Task<IActionResult> SyncHisOrderByStationCode(string stationCode)
        {
            var resultSrt = await _syncOrderService.SyncHisOrderByStationCode(stationCode);
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }
    }
}