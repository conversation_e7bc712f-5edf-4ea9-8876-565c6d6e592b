﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Interconnect.Data.Context;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Services.Interface;
using Interconnect.ViewModels;
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using MedicalExternalCommon.Service;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Interconnect.Services
{
    public class MedicineRecordService : IMedicineRecordService
    {
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";

        #region 全局变量

        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IUnitOfWork<DataOutConnection> _unitOfWorkOut;
        private readonly IOptions<Models.SystemConfig> _config;
        private readonly ISyncAPIConfigRepository _syncAPIConfigRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly ICommonHelper _commonHelper;
        private readonly ILogInfoServices _logInfoServices;
        private readonly IPatientMedicineScheduleRepository _patientMedicineScheduleRepository;
        private readonly IInpatientDataRepository _inPatientRepository;     
        private readonly IPatientEventRepository _patientEventRepository;
        private readonly StationaShiftCommonService _stationaShiftCommonService;

        #endregion 全局变量

        #region 构造器

        public MedicineRecordService(IUnitOfWork<MedicalDbContext> unitOfWork
            , IUnitOfWork<DataOutConnection> unitOfWorkOut
            , IOptions<Models.SystemConfig> options
            , ISyncAPIConfigRepository syncAPIConfigRepository
            , IAppConfigSettingRepository  appConfigSettingRepository
            , ICommonHelper commonHelper
            , ILogInfoServices logInfoServices
            , IPatientMedicineScheduleRepository patientMedicineScheduleRepository
            , IInpatientDataRepository inPatientRepository        
            , IPatientEventRepository patientEventRepository
            , StationaShiftCommonService stationaShiftCommonService
            )
        {
            _unitOfWork = unitOfWork;
            _unitOfWorkOut = unitOfWorkOut;
            _config = options;
            _syncAPIConfigRepository = syncAPIConfigRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
            _commonHelper = commonHelper;
            _logInfoServices = logInfoServices;
            _patientMedicineScheduleRepository = patientMedicineScheduleRepository;
            _inPatientRepository = inPatientRepository; 
            _patientEventRepository = patientEventRepository;
            _stationaShiftCommonService = stationaShiftCommonService;
        }

        #endregion 构造器

        /// <summary>
        /// 同步一段时间内的给药记录数据
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncMedicineRecordByDateTimeAsync()
        {
            #region 声明

            var hospitalID = _config.Value.HospitalID;
            var nowDateTime = DateTime.Now;
            var lastDateTime = nowDateTime;
            var intervalminutes = 30;

            #endregion 声明

            #region 数据加载

            var syncAPIConfigInfo = _syncAPIConfigRepository.GetSyncAPIConfigByApiID(25);
            if (syncAPIConfigInfo == null)
            {
                _logger.Error("未获取到给药数据同步接口配置，ApiID为25");
                return false;
            }
            if (string.IsNullOrEmpty(syncAPIConfigInfo.APIAddress))
            {
                _logger.Error("未获取到给药数据同步接口配置的同步地址");
                return false;
            }

            lastDateTime = syncAPIConfigInfo.LastSyncDateTime;
            intervalminutes = syncAPIConfigInfo.IntervalMinutes;
            if (intervalminutes == 0)
            {
                intervalminutes = 30;
            }
            //获取时间重叠分钟，没有值默认60分钟
            var overlapMinutes = syncAPIConfigInfo.OverlapMinutes;
            if (overlapMinutes == null)
            {
                overlapMinutes = 60;
            }
            //如果间隔时间小于5分钟，不启用同步，避免接口服务器阻塞
            if (lastDateTime.AddMinutes(5) > nowDateTime)
            {
                return false;
            }
            //设置数据同步，时间重叠分钟，避免获取的数据不完整
            var startDateTime = lastDateTime.AddMinutes(-overlapMinutes.Value);
            var endDateTime = startDateTime;

            #endregion 数据加载

            //每次获取30分钟的数据同步频率
            while (lastDateTime < nowDateTime)
            {
                if (lastDateTime.AddMinutes(intervalminutes) > nowDateTime)
                {
                    endDateTime = nowDateTime;
                    lastDateTime = endDateTime;
                }
                else
                {
                    //获取30分钟内的数据
                    lastDateTime = lastDateTime.AddMinutes(intervalminutes);
                    endDateTime = lastDateTime;
                }
                var dictionary = new Dictionary<string, string>
                 {
                    { "StartDateTime", startDateTime.ToString()},
                    { "EndDateTime", endDateTime.ToString()},
                 };

                //获取HIS中需要同步的数据
                var hisData = GetMedicineRecordApiData(syncAPIConfigInfo.APIAddress, dictionary, hospitalID);
                //// 根据条件过滤同步数据
                //hisData = FilterData(hisData);

                //所以在时间段内如果没有数据，则继续循环，直到获取数据。
                if (hisData == null || hisData.Count <= 0)
                {
                    continue;
                }
                //开始同步
                if (!await SynchronizationDetailAsync(hisData, hospitalID))
                {
                    return false;
                }
                //同步成功，更新最后同步时间
                syncAPIConfigInfo.LastSyncDateTime = lastDateTime;
                try
                {
                    _unitOfWorkOut.SaveChanges();
                }
                catch (Exception ex)
                {
                    _logger.Error("更新最后同步时间失败" + ex.ToString());
                    return false;
                }
            }
            return true;
        }

        #region 根据条件过滤同步数据

        ///// <summary>
        ///// 根据条件过滤同步数据
        ///// </summary>
        ///// <param name="hisData"></param>
        ///// <returns></returns>
        //private List<HISMedicineRecordView> FilterData(List<HISMedicineRecordView> hisData)
        //{
        //    return hisData;
        //}

        #endregion 根据条件过滤同步数据

        /// <summary>
        /// 同步给药记录数据
        /// </summary>
        /// <param name="hisData"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<bool> SynchronizationDetailAsync(List<HISMedicineRecordView> hisData, string hospitalID)
        {
            //单病人循环
            var inPatientGroupList = hisData.GroupBy(m => m.CaseNumber).ToList();
            foreach (var inPatientGroup in inPatientGroupList)
            {
                var inpatient = await _inPatientRepository.GetAsyncByCaseNumber(inPatientGroup.Key,hospitalID);
                if (inpatient == null)
                {
                    _logger.Error("根据caseNumber=" + inPatientGroup.Key + "未获得患者基本信息");
                    continue;
                }

                var patientEventInfos = await _patientEventRepository.GetByInpatientID(inpatient.ID);
                var eventStart = patientEventInfos.Where(m => m.AssessListID == 2870)
                    .OrderByDescending(m => m.OccurDate).OrderByDescending(m => m.OccurTime).FirstOrDefault();
                var eventEnd = patientEventInfos.Where(m => m.AssessListID == 2871)
                    .OrderByDescending(m => m.OccurDate).OrderByDescending(m => m.OccurTime).FirstOrDefault();

                var medicineRecordInfos = await _patientMedicineScheduleRepository.GetMedicineRecordByCaseNumberAsync(inPatientGroup.Key);
                //单医嘱循环
                var orderGroupList = inPatientGroup.GroupBy(m => m.PatientOrderMainID).ToList();
                foreach (var orderGroup in orderGroupList)
                {
                    var content = "";
                    List<PatientMedicineScheduleInfo> medicineList = new List<PatientMedicineScheduleInfo>();
                    foreach (var item in orderGroup)
                    {
                        //判断患者事件是否要写IO
                        var flag = GetIOEvent(eventStart, eventEnd, item);
                        if (!flag)
                        {
                            continue;
                        }
                        var medicineRecordInfo = medicineRecordInfos.Where(m => m == item.PatientOrderSubID).FirstOrDefault();
                        if (medicineRecordInfo != null)
                        {
                            continue;
                        }
                        if (string.IsNullOrEmpty(item.OrderDose)
                            || string.IsNullOrEmpty(item.Unit)
                            || string.IsNullOrEmpty(item.PatientOrderSubID)
                            || string.IsNullOrEmpty(item.OrderType)
                            || string.IsNullOrEmpty(item.OrderContent)
                            || string.IsNullOrEmpty(item.StartDate)
                            || string.IsNullOrEmpty(item.StartEmployeeID)
                            || string.IsNullOrEmpty(item.OrderRuleCode)
                            )
                        {
                            _logger.Info("给药记录同步，数据格式异常，数据原文" + ListToJson.ToJson(item));
                            continue;
                        }
                        if (!decimal.TryParse(item.OrderDose, out decimal orderDose))
                        {
                            _logger.Info("给药记录同步，剂量转换异常，数据原文" + ListToJson.ToJson(item));
                            continue;
                        }
                        //组装需写入的给药记录
                        var medicineItem = await WriteMedicineRecordAsync(item, inpatient, hospitalID, inPatientGroup.Key);
                        content += CombineOrder(medicineItem) + ";";
                        medicineList.Add(medicineItem);
                    }
                    if (medicineList.Count == 0)
                    {
                        continue;
                    }
                    _unitOfWork.GetRepository<PatientMedicineScheduleInfo>().Insert(medicineList);
                    _unitOfWork.SaveChanges();

                    //取得最大液体准备写出入量
                    var medicineInfo = CloneData.Clone(medicineList).Where(m => m.OrderDose == medicineList.Max(z => z.OrderDose)).FirstOrDefault();
                    medicineInfo.OrderContent = content;
                    //写入出入量                  
                    await SaveIO(medicineList, inpatient, hospitalID, "TongBu");                    
                }
            }
            return true;
        }

        private bool GetIOEvent(PatientEventInfo eventStart, PatientEventInfo eventEnd, HISMedicineRecordView item)
        {
            var flag = false;
            var performDate = DateTime.Now.Date;
            var performTime = DateTime.Now.TimeOfDay;
            if (DateTime.TryParse(item.StartDate, out DateTime parseDateTime))
            {
                performDate = parseDateTime.Date;
                performTime = parseDateTime.TimeOfDay;
            }
            else
            {
                return false;
            }

            //1、有开始事件无结束事件，给药执行时间大于开始事件时间，写出入量
            //2、有开始事件有结束事件，开始事件时间大于结束事件时间，写出入量
            //3、有开始事件有结束事件，结束事件时间大于开始事件时间，给药执行时间在开始和结束之间，写出入量

            if (eventStart != null && eventEnd == null)
            {
                if (eventStart.OccurDate <= performDate && eventStart.OccurTime <= performTime)
                {
                    flag = true;
                }
            }
            else if (eventStart != null && eventEnd != null)
            {
                if (eventStart.OccurDate >= eventEnd.OccurDate && eventStart.OccurTime >= eventEnd.OccurTime)
                {
                    flag = true;
                }
                else if (eventStart.OccurDate <= eventEnd.OccurDate && eventStart.OccurTime <= eventEnd.OccurTime)
                {
                    if ((eventStart.OccurDate <= performDate && eventStart.OccurTime <= performTime)
                        && (eventEnd.OccurDate > performDate && eventEnd.OccurTime > performTime))
                    {
                        flag = true;
                    }
                }
            }

            return flag;
        }

        #region 合并医嘱内容

        /// <summary>
        /// 合并医嘱内容
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        private string CombineOrder(PatientMedicineScheduleInfo data)
        {
            string orderStr = "";
            orderStr = data.OrderContent + " " + data.OrderDescription + " ";
            if (data.OrderDose.Value.ToString("0.####") != "0")
            {
                orderStr += data.OrderDose.Value.ToString("0.####")
                    + data.Unit;
            }
            return orderStr;
        }

        #endregion 合并医嘱内容

        #region 写入新增的给药记录

        /// <summary>
        /// 组装需写入的给药记录
        /// </summary>
        /// <param name="hisDataMedicine"></param>
        /// <param name="inpatient"></param>
        /// <param name="hospitalID"></param>
        /// <param name="medicineRecordInfo"></param>
        private async Task<PatientMedicineScheduleInfo> WriteMedicineRecordAsync(HISMedicineRecordView hisDataMedicine, InpatientDataInfo inpatient
            , string hospitalID, string key)
        {
            var orderDose = decimal.Parse(hisDataMedicine.OrderDose);
            var performDate = Convert.ToDateTime(hisDataMedicine.StartDate).Date;
            var performTime = Convert.ToDateTime(hisDataMedicine.StartDate).TimeOfDay;
            var stationShiftInfo = await _stationaShiftCommonService.GetShiftAsync(inpatient.StationID, performTime);

            var medicineSchedule = new PatientMedicineScheduleInfo();
            medicineSchedule.PatientMedicineScheduleID = medicineSchedule.GetId();
            medicineSchedule.PatientOrderMainID = hisDataMedicine.PatientOrderMainID;
            medicineSchedule.PatientOrderDetailID = hisDataMedicine.PatientOrderSubID;
            medicineSchedule.HospitalID = hospitalID;
            medicineSchedule.OrderType = hisDataMedicine.OrderType;
            medicineSchedule.InpatientID = inpatient.ID;
            medicineSchedule.PatientID = inpatient.PatientID;
            medicineSchedule.CaseNumber = inpatient.CaseNumber;
            medicineSchedule.ChartNo = inpatient.ChartNo;
            medicineSchedule.StationID = inpatient.StationID;
            medicineSchedule.NumberOfAdmissions = inpatient.NumberOfAdmissions;
            medicineSchedule.BedID = inpatient.BedID;
            medicineSchedule.BedNumber = inpatient.BedNumber;
            medicineSchedule.OrderRule = hisDataMedicine.OrderRuleName;
            medicineSchedule.OrderContent = hisDataMedicine.OrderContent;
            medicineSchedule.ScheduleDate = performDate;
            medicineSchedule.ScheduleTime = performTime;
            medicineSchedule.PerformDate = performDate;
            medicineSchedule.PerformTime = performTime;
            medicineSchedule.AddDate = DateTime.Now;
            medicineSchedule.ShiftDate = performDate;
            medicineSchedule.OrderDose = orderDose;
            medicineSchedule.ConvertedVolume = orderDose;
            medicineSchedule.Unit = hisDataMedicine.Unit;
            medicineSchedule.Frequency = hisDataMedicine.Frequency;
            medicineSchedule.GroupID = hisDataMedicine.PatientOrderMainID;
            medicineSchedule.PerformEmployeeID = hisDataMedicine.StartEmployeeID;
            medicineSchedule.MedicineType = hisDataMedicine.OrderRuleCode;
            medicineSchedule.Status = 3;
            medicineSchedule.Content = "依医嘱执行" + hisDataMedicine.OrderContent + orderDose.ToString("0.####") + hisDataMedicine.Unit;
            medicineSchedule.BringToShift = "0";
            medicineSchedule.BringToNursingRecords = "0";
            medicineSchedule.StationShiftID = stationShiftInfo?.ID;
            medicineSchedule.AddEmployeeID = hisDataMedicine.StartEmployeeID;
            medicineSchedule.ClientType = "2";
            medicineSchedule.StationCode = hisDataMedicine.StationCode;

            return medicineSchedule;
        }

        #endregion 写入新增的给药记录

        #region 获取api数据后json转model

        /// <summary>
        /// 获取api数据后json转model
        /// </summary>
        /// <param name="aPIAddress"></param>
        /// <param name="dictionary"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private List<HISMedicineRecordView> GetMedicineRecordApiData(string api, Dictionary<string, string> dictionary, string hospitalID)
        {
            var hisData = GetApiData(api, dictionary, hospitalID);
            if (string.IsNullOrEmpty(hisData))
            {
                return null;
            }
            var interconnect_Data = new List<HISMedicineRecordView>();
            try
            {
                interconnect_Data = JsonConvert.DeserializeObject<List<HISMedicineRecordView>>(hisData);
            }
            catch (Exception ex)
            {
                _logger.Error("Json格式化失败:" + ex.ToString());
                return null;
            }
            _logger.Info("转换Json数据完成，获得给药数据" + interconnect_Data.Count() + "条！");
            return interconnect_Data;
        }

        #endregion 获取api数据后json转model

        #region 获取api数据

        /// <summary>
        /// 获取api数据
        /// </summary>
        /// <param name="api"></param>
        /// <param name="dictionary"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private string GetApiData(string api, Dictionary<string, string> dictionary, string hospitalID)
        {
            var resultData = "";

            //获取环境 ,1 开发环境
            var systemOperatingEnvironment = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "SystemOperatingEnvironment").Result;

            if (systemOperatingEnvironment != "1")
            {
                //呼叫API获得数据
                resultData = _commonHelper.GetInterconnectData(api, dictionary);
            }
            else
            {
                resultData = _logInfoServices.GetLog("SyncMedicineRecord")[0].Logs;
            }

            var printInterfaceData = 0;
            //获取打印接口配置
            var resultPrintDate = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "PrintInterfaceData").Result;
            if (StringCheck.IsNumeric(resultPrintDate))
            {
                printInterfaceData = int.Parse(resultPrintDate);
            }

            if (printInterfaceData == 1)
            {
                _logger.Info("Api:" + api + "获取的给药记录数据" + ListToJson.ToJson(dictionary) + "给药记录数据：" + resultData);
            }

            try
            {
                var result = JsonConvert.DeserializeObject<ResponseResult>(resultData);
                var resultDataStr = result.Data.ToString();
                if (resultDataStr == "" || resultDataStr == "[]" || resultDataStr == "[{}]" || resultDataStr == "{}")
                {
                    return "";
                }
                return resultDataStr;
            }
            catch (Exception ex)
            {
                _logger.Error("Api: " + api + "获取的给药记录数据异常" + ex.ToString());
                return "";
            }
        }

        #endregion 获取api数据

        /// <summary>
        /// 同步有效数据
        /// </summary>
        /// <param name="item"></param>
        /// <param name="medicineScheduleListByUnit"></param>
        /// <param name="tablename"></param>
        /// <param name="inpatientData"></param>
        /// <returns></returns>
        private async Task<bool> SaveIO(
             List<PatientMedicineScheduleInfo> patientMedicineScheduleInfos
           , InpatientDataInfo inpatientData
            , string hospitalID, string modifyPersonID
            )
        {
            var resultLanguage = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "Language");
            var language = StringCheck.IsNumeric(resultLanguage) ? int.Parse(resultLanguage) : 0;
            var caseNumber = patientMedicineScheduleInfos[0].CaseNumber;
            var syncPatientMedicineViewList = new List<SyncPatientMedicineView>();
            _logger.Info("CaseNumber=" + caseNumber + "的患者数据找到，可尝试同步");
            foreach (var medicine in patientMedicineScheduleInfos)
            {
                var syncPatientMedicineView = CreatePatientMedicineView
                    (
                    medicine, modifyPersonID, language
                    );
                syncPatientMedicineViewList.Add(syncPatientMedicineView);
            }
            //呼叫API，写入IO;
            WebRequestSugar wrs = new WebRequestSugar();
            var patientProfileAPI = _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "SaverPatientMedicineIO").Result;
            if (string.IsNullOrEmpty(patientProfileAPI))
            {
                _logger.Error("获取PatientProfileAPI为空");
                return false;
            }

            try
            {
                _logger.Info("开始呼叫PatientProfileAPI");
                var result = wrs.SendObjectAsJsonInBody(patientProfileAPI, syncPatientMedicineViewList);
            }
            catch (Exception ex)
            {
                _logger.Error("呼叫：API写IO失败" + ex.ToString());
                return false;
            }
            return true;
        }
        /// <summary>
        /// 组装需要写IO的样式
        /// </summary>
        /// <param name="patientMedicineScheduleInfo"></param>
        /// <param name="modifyPersonID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        private SyncPatientMedicineView CreatePatientMedicineView(PatientMedicineScheduleInfo patientMedicineScheduleInfo
            , string modifyPersonID, int language)
        {
            var syncPatientMedicineView = new SyncPatientMedicineView
            {
                PatientMedicineSchedule = patientMedicineScheduleInfo,
                Session = new Session()
                {
                    ClientType = "1",
                    UserID = modifyPersonID,
                    Language = language
                }
            };
            return syncPatientMedicineView;
        }
    }
}