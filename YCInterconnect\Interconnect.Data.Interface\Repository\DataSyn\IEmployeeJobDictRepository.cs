﻿using Interconnect.Models;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Interconnect.Data.Interface
{
   public  interface IEmployeeJobDictRepository
    {
        /// <summary>
        /// 获取未同步的信息
        /// </summary>
        /// <returns></returns>
        Task<List<EmployeeJobDictInfo>> GetAsync();

        // <summary>
        /// 获取全部的信息
        /// </summary>
        /// <returns></returns>
        Task<List<EmployeeJobDictInfo>> GetAllAsync();
    }
}
