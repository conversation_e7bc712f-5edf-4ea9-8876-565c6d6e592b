﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientStomaCareMainRepository : IPatientStomaCareMainRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientStomaCareMainRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 根据造口ID取得造口评估列表
        /// </summary>
        /// <param name="StomaID"></param>
        /// <returns></returns>
        public async Task<List<PatientStomaCareMainInfo>> GetStomaCareAsync(string StomaID)
        {
            return await _medicalDbContext.PatientStomaCareMainInfos.Where(t => t.PatientStomaRecordID == StomaID && t.DeleteFlag != "*").OrderBy(t => t.NumberOfAssessment).ToListAsync();
        }

        /// <summary>
        /// 获取按次数排序的最后一次评估主记录
        /// </summary>
        /// <param name="StomaID"></param>
        /// <returns></returns>
        public async Task<PatientStomaCareMainInfo> GetCountLastAssessRecordsAsync(string StomaID)
        {
            var list = await _medicalDbContext.PatientStomaCareMainInfos.Where(t => t.PatientStomaRecordID == StomaID && t.DeleteFlag != "*").OrderByDescending(t => t.NumberOfAssessment).ToListAsync();
            if (list != null && list.Count > 0)
            {
                return list[0];
            }
            return null;
        }

        /// <summary>
        ///  获取按时间排序的最后一次评估主记录
        /// </summary>
        /// <param name="StomaID"></param>
        /// <returns></returns>
        public async Task<PatientStomaCareMainInfo> GetTimeLastAssessRecordsAsync(string StomaID)
        {
            var list = await _medicalDbContext.PatientStomaCareMainInfos
                    .Where(t => t.PatientStomaRecordID == StomaID && t.DeleteFlag != "*").ToListAsync();

            return list.OrderByDescending(t => t.AssessDate.Add(t.AssessTime)).ThenByDescending(t => t.NumberOfAssessment).FirstOrDefault();
        }

        /// <summary>
        /// 根据造口评估主表ID获取数据
        /// </summary>
        /// <param name="stomaCareMainID"></param>
        /// <returns></returns>
        /// FirstOrDefaultAsync
        public async Task<PatientStomaCareMainInfo> GetStomaCareMainByIDAsync(string stomaCareMainID)
        {
            return await _medicalDbContext.PatientStomaCareMainInfos.Where(t => t.PatientStomaCareMainID == stomaCareMainID && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 通过护理评估ID获取数据
        /// </summary>
        /// <param name="assessMainID"></param>
        /// <returns></returns>
        public async Task<List<PatientStomaCareMainInfo>> GetStomaCareMainByAssessMainIDAsync(string assessMainID)
        {
            return await _medicalDbContext.PatientStomaCareMainInfos.Where(t => t.PatientAssessMainID == assessMainID && t.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据RecordsCode获取造口评估数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="recordsCode"></param>
        /// <returns></returns>
        public async Task<PatientStomaCareMainInfo> GetStomaCareMainByRecordsCode(string recordID, string recordsCode)
        {
            return await _medicalDbContext.PatientStomaCareMainInfos
                .Where(t => t.PatientStomaRecordID == recordID && t.RecordsCode == recordsCode && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取最后一次造口维护记录
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<PatientStomaCareMainInfo> GetNumberOfAssessmentMaxStomaCareMain(string recordID)
        {
            return await _medicalDbContext.PatientStomaCareMainInfos
                .Where(t => t.RecordsCode.Contains("Maintain") && t.PatientStomaRecordID == recordID && t.DeleteFlag != "*")
                .OrderByDescending(m => m.NumberOfAssessment).FirstOrDefaultAsync();
        }

        public async Task<List<HandoverStomaCareIntervention>> GetPatientStomaCareIntervention(string inpatientID, string patientAssessMainID)
        {
            var datas = await (from a in _medicalDbContext.PatientStomaCareMainInfos
                               join b in _medicalDbContext.PatientStomaRecordInfos on a.PatientStomaRecordID equals b.PatientStomaRecordID
                               where a.InpatientID == inpatientID && a.PatientAssessMainID == patientAssessMainID && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                               select new HandoverStomaCareIntervention
                               {
                                   InpatientID = a.InpatientID,
                                   AssessDate = a.AssessDate,
                                   AssessTime = a.AssessTime,
                                   StomaKind = b.StomaKind,
                                   BodyPartID = b.BodyPartID,
                                   CareIntervention = a.CareIntervention,
                                   Sort = 0,
                                   BodyPartName = "",
                                   StomaCode = b.StomaCode,
                                   PatientStomaRecordID = a.PatientStomaRecordID,
                                   StomaOpen = a.StomaOpen,
                                   //排气
                                   Exhaust = a.Exhaust,
                                   //排便
                                   Defecation = a.Defecation,
                                   //排泄物性质
                                   FecalProperties = a.FecalProperties,
                                   //用物
                                   Utilities = a.Utilities,
                                   //黏膜颜色
                                   MucosalColor = a.MucosalColor,
                                   //周围皮肤完整性
                                   SurroundSkin = a.SurroundSkin,
                                   AssessMainID = b.AssessMainID
                               }).ToListAsync();
            return datas;
        }

        public async Task<Dictionary<string, string>> GetCareMainID(string[] recordIDs, string recordsCode)
        {
            var query = await _medicalDbContext.PatientStomaCareMainInfos.Where(m => recordIDs.Contains(m.PatientStomaRecordID)
                && m.RecordsCode.Contains(recordsCode) && m.DeleteFlag != "*")
                .Select(m => new KeyValueString
                {
                    Key = m.PatientStomaRecordID,
                    Value = m.PatientStomaCareMainID
                }).ToListAsync();

            var data = query.DistinctBy(m => m.Key).ToList().ToDictionary(m => m.Key, m => m.Value);

            return data;
        }

        /// <summary>
        /// 根据患者住院号获取主列表
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientStomaCareMainInfo>> GetByInpatientIDAsync(string inpatientID, DateTime startDate, TimeSpan startTime, DateTime endDate, TimeSpan endTime)
        {
            var datas = await _medicalDbContext.PatientStomaCareMainInfos
                .Where(t => t.InpatientID == inpatientID && t.AssessDate >= startDate && t.AssessDate <= endDate && t.BringToShift == true && t.DeleteFlag != "*")
                .ToListAsync();
            var dataList = new List<PatientStomaCareMainInfo>();
            if (datas.Count > 0)
            {
                dataList = datas.Where(m => m.AssessDate.Add(m.AssessTime) >= startDate.Date.Add(startTime) && m.AssessDate.Add(m.AssessTime) <= endDate.Date.Add(endTime))
               .OrderBy(n => n.AssessDate).ThenBy(n => n.AssessTime).ToList();
                return dataList;
            }
            return dataList;
        }
        public async Task<List<PatientStomaCareMainInfo>> GetRecordsBySourceID(string sourceID, string sourceType)
        {
            var data = await _medicalDbContext.PatientStomaCareMainInfos.Where(m => m.SourceID == sourceID && m.DeleteFlag != "*").ToListAsync();
            if (!string.IsNullOrEmpty(sourceType))
            {
                data = data.Where(m => !string.IsNullOrEmpty(m.SourceType) && m.SourceType.Trim() == sourceType.Trim()).ToList();
            }
            return data;
        }

        /// <summary>
        /// 获取病人造口维护记录
        /// </summary>
        /// <param name="inpatientID">病人ID</param>
        /// <returns></returns>
        public async Task<List<StomaHandoverView>> GetHandoverViewByInpatientID(string inpatientID)
        {
            var datas = await _medicalDbContext.PatientStomaCareMainInfos
                .Where(t => t.InpatientID == inpatientID && t.DeleteFlag != "*").
                Select(m => new StomaHandoverView
                {
                    PatientStomaCareMainID = m.PatientStomaCareMainID,
                    PatientStomaRecordID = m.PatientStomaRecordID,
                    AssessDate = m.AssessDate,
                    AssessTime = m.AssessTime,
                    RecordsCode = m.RecordsCode,
                    BringToShift = m.BringToShift,
                    NumberOfAssessment = m.NumberOfAssessment,
                    Shift = m.Shift,
                    ShiftDate = m.ShiftDate
                }).ToListAsync();
            return datas;
        }
        public async Task<List<StomaHandoverView>> GetHandoverView(string inpatientID, DateTime startDateTime, DateTime endDateTime)
        {
            return await (from m in _medicalDbContext.PatientStomaCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*")
                          join n in _medicalDbContext.PatientStomaRecordInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*"
                          && (m.StartDate < endDateTime.Date || (m.StartDate == endDateTime.Date && m.StartTime <= endDateTime.TimeOfDay))
                          && (!m.EndDate.HasValue || (m.EndDate.HasValue && m.EndDate.Value > startDateTime.Date || (m.EndDate.Value == startDateTime.Date && m.EndTime >= startDateTime.TimeOfDay))))
                          on m.PatientStomaRecordID equals n.PatientStomaRecordID
                          select new StomaHandoverView
                          {
                              PatientStomaCareMainID = m.PatientStomaCareMainID,
                              PatientStomaRecordID = m.PatientStomaRecordID,
                              AssessDate = m.AssessDate,
                              AssessTime = m.AssessTime,
                              RecordsCode = m.RecordsCode,
                              BringToShift = m.BringToShift,
                              StomaKind = n.StomaKind,
                              BodyPartID = n.BodyPartID,
                          }).OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).ToListAsync();
        }
    }
}