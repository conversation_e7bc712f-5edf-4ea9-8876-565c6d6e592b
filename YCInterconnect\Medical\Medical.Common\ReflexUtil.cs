﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;

namespace Medical.Common
{
    public class ReflexUtil
    {
        static readonly Dictionary<string, Dictionary<string, MethodInfo>> SETTER_MAP = new Dictionary<string, Dictionary<string, MethodInfo>>();
        /// <summary>
        /// 通过属性名获取属性值
        /// </summary>
        /// <param name="o">对象</param>
        /// <param name="field">属性字段名</param>
        /// <returns></returns>
        public static string GetProperty(object o, string field, Type type = null, bool ignoreCaseFlag = false)
        {
            if (type == null)
            {
                type = o.GetType();
            }
            string returnStr = "";

            var value = ignoreCaseFlag
                    ? type.GetProperty(field, BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase)?.GetValue(o)
                    : type.GetProperty(field)?.GetValue(o);

            if (value == null)
            {
                return null;
            }
            if (value.GetType() == typeof(String))
            {
                returnStr = value.ToString();
            }
            else if (value.GetType() == typeof(DateTime))
            {
                returnStr = ((DateTime)value).ToString("yyyy-MM-dd");
            }
            else if (value.GetType() == typeof(DateTime?))
            {
                returnStr = ((DateTime?)value).Value.ToString("yyyy-MM-dd");
            }
            else if (value.GetType() == typeof(int))
            {
                returnStr = value + "";
            }
            else if (value.GetType() == typeof(int?))
            {
                returnStr = ((int?)value).Value + "";
            }
            else if (value.GetType() == typeof(TimeSpan))
            {
                returnStr = ((TimeSpan)value).ToString(@"hh\:mm"); ;
            }
            else if (value.GetType() == typeof(decimal?))
            {
                returnStr = ((decimal?)value).Value + "";
            }
            else if (value.GetType() == typeof(byte?))
            {
                returnStr = ((byte?)value).Value + "";
            }
            else if (value.GetType() == typeof(decimal))
            {
                returnStr = ((decimal)value) + "";
            }
            else if (value.GetType() == typeof(byte))
            {
                returnStr = ((byte)value) + "";
            }
            else if (value is short)
            {
                returnStr = ((short)value) + "";
            }
            else if (value is short?)
            {
                returnStr = ((short?)value).Value + "";
            }
            else
            {
                returnStr = "没有找到反射类:" + value.GetType().ToString();
            }
            return returnStr;
        }

        public static string GetPrimaryKeyValue<T>(T obj)
        {
            PropertyInfo pkProp = typeof(T).GetProperties().Where(p => p.GetCustomAttributes(typeof(KeyAttribute), false).Length > 0).FirstOrDefault();
            if (pkProp == null)
            {
                return null;
            }
            return GetProperty(obj, pkProp.Name);
        }

        /// <summary>
        /// 反射调用方法，支持调用私有方法
        /// </summary>
        /// <param name="instance">方法所在对象实例</param>
        /// <param name="methodName">方法名称</param>
        /// <param name="parameters">参数，多个用逗号隔开</param>
        /// <returns>方法调用结果</returns>
        public static async ValueTask<object> InvokeMethod(object instance, string methodName, params object[] parameters)
        {
            object result;
            Type type = instance.GetType();
            var method = type.GetMethod(methodName, BindingFlags.Instance | BindingFlags.NonPublic);
            if (method == null)
            {
                throw new Exception("未找到方法");
            }
            // 通过检查是否有AsyncStateMachineAttribute来判断方法是否是异步
            var attrib = (AsyncStateMachineAttribute)method.GetCustomAttribute(typeof(AsyncStateMachineAttribute));
            if (attrib is null)
            {
                result = method.Invoke(instance, parameters);
            }
            else
            {
                var task = method.Invoke(instance, parameters) as Task;
                await task;
                result = task.GetType().GetProperty("Result")?.GetValue(task);
            }
            return result;
        }
        /// <summary>
        /// 反射赋值
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="instance"></param>
        /// <param name="propertyName"></param>
        /// <param name="parameters"></param>
        public static void SetProperty<T>(T instance, string propertyName, params object[] parameters)
        {
            Type instanceType = instance.GetType();
            var className = instanceType.Name;
            var setterName = $"set_{propertyName}";
            // 获取类对应的Setter方法字典
            if (!SETTER_MAP.TryGetValue(className, out var setters))
            {
                // 未成功获取到，初始化此类的缓存
                SETTER_MAP.Add(className, new Dictionary<string, MethodInfo>());
                setters = SETTER_MAP[className];
            }
            // 获取对应方法
            if (!setters.TryGetValue(setterName, out var method))
            {
                // 未获取到方法，缓存方法
                method = instanceType.GetMethod(setterName);
                setters.Add(setterName, method);
            }
            method.Invoke(instance, parameters);
        }
    }
}
