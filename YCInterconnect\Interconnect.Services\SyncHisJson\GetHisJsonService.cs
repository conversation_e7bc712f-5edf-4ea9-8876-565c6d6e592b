﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Interconnect.Services.Interface;
using Interconnect.Data.Interface;
using NLog;
using Medical.Common;
using ViewModel;
using Medical.Data.Interface;

namespace Interconnect.Services
{
    public class GetHisJsonService : IGetHisJsonService
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISyncAPIConfigRepository _syncAPIConfigRepository;
        private readonly IAPICommonService _aPICommonService;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        /// <summary>
        /// 请求类型
        /// </summary>
        public readonly string CONTENTTYPE = "API Key";
        /// <summary>
        /// 请求头名称
        /// </summary>
        public readonly string HEADERKEY = "x-api-key";
        /// <summary>
        /// 请求头内容
        /// </summary>
        public readonly string HEADERVALUE = "c5e73a65a138f1a2fed63567b2e2fff2";
        public GetHisJsonService(
            ISyncAPIConfigRepository syncAPIConfigRepository
            , IAPICommonService aPICommonService
            ,IAppConfigSettingRepository appConfigSettingRepository
            )
        {
            _syncAPIConfigRepository = syncAPIConfigRepository;
            _aPICommonService = aPICommonService;
            _appConfigSettingRepository = appConfigSettingRepository;
        }
        /// <summary>
        /// 根据CaseNumber获取医嘱数据
        /// </summary>
        /// <param name="caseNumbe"></param>
        /// <returns></returns>
        public async Task<List<HISOrderView>> GetOrderHisJsonByCaseNumber(string caseNumber)
        {
            var datas = new List<HISOrderView>();
            var logInfo = "单病人医嘱同步||CaseNumber || "+ caseNumber + "|| ";
            _logger.Info(logInfo+"开始进行医嘱数据获取");

            //取得平台接口
            //var api = "http://10.10.61.29:6001/api/V_HIS_CCC_YZXX?filter[order]=CaseNumber&filter[where][CaseNumber][eq]=796661";
            var apiSetting = await _appConfigSettingRepository.GetBySettingCode("SyncOrderByCaseNumber");
            if (apiSetting == null||string.IsNullOrEmpty(apiSetting.SettingValue))
            {
                _logger.Error("获取平台单病人医嘱接口未配置 APIID|| "+28);
                return null;
            }
            //初始化请求信息
            var url = apiSetting.SettingValue + caseNumber;
            _logger.Info(logInfo + "URL||" + url);
            var headers = new Dictionary<string, string>()
            {
                {HEADERKEY,HEADERVALUE},
            };
            //平台数据获取
            var resultData = await _aPICommonService.GetApiDataHttpGet(url, CONTENTTYPE, headers);
            if (resultData == null)
            {
                _logger.Error(logInfo+"获取平台医嘱数据失败");
                return null;
            }
            if (resultData == "")
            {
                _logger.Error(logInfo + "获取平台医嘱数据为空");
                return datas;
            }
            //Json转换为List          
            try
            {
                datas = ListToJson.ToList<List<HISOrderView>>(resultData);
            }
            catch (Exception ex)
            {
                _logger.Error(logInfo+"此病人医嘱数据转换Json失败 Json|| " + resultData + ex.ToString());
                return null;
            }
            _logger.Info(logInfo+ "医嘱数据由JSON转换完成,共" + datas.Count + "笔数据");
            return datas;
        }
        /// <summary>
        /// 根据StationCode获取医嘱数据
        /// </summary>
        /// <param name="stationCode"></param>
        /// <returns></returns>
        public async Task<List<HISOrderView>> GetOrderHisJsonBystationCode(string stationCode)
        {
            var datas = new List<HISOrderView>();
            var logInfo = "病区医嘱同步||stationCode || " + stationCode + "|| ";
            _logger.Info(logInfo + "开始进行医嘱数据获取");

            //取得平台接口
            //var api = "http://10.10.60.42/prod/a109/xhhk/services/ycs1y/v1/IN/GetOrderByWard";
            var apiSetting = await _appConfigSettingRepository.GetBySettingCode("GetPatientOrderByWardCode");
            if (apiSetting == null || string.IsNullOrEmpty(apiSetting.SettingValue))
            {
                _logger.Error("获取平台病区医嘱接口未配置 SettingCode = GetPatientOrderByWardCode");
                return null;
            }
            var headers = new Dictionary<string, string>()
            {
                {HEADERKEY,HEADERVALUE},
            };
            //平台数据获取
            var resultData = await HttpHelper.HttpPostAsync(apiSetting.SettingValue, ListToJson.ToJson(new { WardCode = stationCode }), "application/json", 30,headers);
            //var resultData = ReadFile.ReadTxt(@"C:\Users\<USER>\Desktop\json\StationOrder.txt");
            if (resultData == null)
            {
                _logger.Error(logInfo + "获取平台医嘱数据失败");
                return null;
            }
            if (resultData == "")
            {
                _logger.Error(logInfo + "获取平台医嘱数据为空");
                return datas;
            }
            //Json转换为List          
            try
            {
                datas = ListToJson.ToList<List<HISOrderView>>(resultData);
            }
            catch (Exception ex)
            {
                _logger.Error(logInfo + "此病人医嘱数据转换Json失败 Json|| " + resultData + ex.ToString());
                return null;
            }
            _logger.Info(logInfo + "医嘱数据由JSON转换完成,共" + datas.Count + "笔数据");
            return datas;
        }
    }
}