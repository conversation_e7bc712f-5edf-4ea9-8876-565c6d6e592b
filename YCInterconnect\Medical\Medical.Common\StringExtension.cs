﻿using System.Collections.Generic;

namespace Medical.Common
{
    public static class StringExtension
    {
        public static string[] Split(this string str, string splitStr)
        {
            var list = new List<string>();
            var temp = 0;
            var splitLenth = splitStr.Length;
            var endStr = str.Substring(str.Length - splitLenth);
            if (!string.Equals(endStr, splitStr))
            {
                str += splitStr;
            }
            for (var i = 0; i < str.Length; i++)
            {
                var str1 = string.Empty;
                for (var j = 0; j < splitLenth; j++)
                {
                    if (i + j >= str.Length)
                    {
                        break;
                    }
                    str1 += str[i + j];
                }
                if (string.Equals(str1, splitStr))
                {
                    list.Add(str.Substring(temp, i - temp));
                    temp = i;
                    str = str.Remove(i, splitStr.Length);
                }
            }
            return list.ToArray();
        }
        /// <summary>
        /// 拼接字符串
        /// </summary>
        /// <param name="originalString">原始字符串</param>
        /// <param name="addString">要拼接的字符串</param>
        /// <param name="separator">分隔符</param>
        /// <param name="isAddHead">是否拼接到最前面</param>
        /// <returns></returns>
        public static string ConcatString(string originalString, string addString, string separator, bool isAddHead)
        {
            var ret = "";
            if (string.IsNullOrEmpty(originalString))
            {
                ret = addString;
            }
            else
            {
                if (isAddHead)
                {
                    ret = addString + separator + originalString;
                }
                else
                {
                    ret = originalString + separator + addString;
                }
            }
            return ret;
        }
        /// <summary>
        /// 中文字符转为英文字符
        /// </summary>
        /// <param name="text">转换的英文字符串</param>
        /// <returns></returns>
        public static string ConvertToCN(string text)
        {
            const string en = @":;,?!\""""''";//英文字符
            const string ch = "：；，？！、“”‘’";//中文字符
            char[] e = text.ToCharArray();
            for (int i = 0; i < e.Length; i++)
            {
                int n = en.IndexOf(e[i]);
                if (n != -1)
                {
                    e[i] = ch[n];
                }
                else
                {
                    if (i == e.Length - 1 && e[i] == '.')
                    {
                        e[i] = '。';
                    }
                }
            }
            return new string(e);
        }
    }
}
