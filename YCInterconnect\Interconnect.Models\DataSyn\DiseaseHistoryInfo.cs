﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("DiseaseHistory")]
    public class DiseaseHistoryInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///病案号
        ///</summary>
        public string ChartNo { get; set; }
        /// <summary>
        ///ICDCode
        ///</summary>
        public string ICDCode { get; set; }
        /// <summary>
        ///诊断名称
        ///</summary>
        public string DiagnosticName { get; set; }
    }
}