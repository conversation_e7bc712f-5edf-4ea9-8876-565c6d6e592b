﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("NursingRecord_WaterSummary")]
    public class NRWaterSummaryInfo : ModifyReturnInfo
    {
        /// <summary>
        ///	自增长序号	
        ///</summary>
         [Key]
        [Column("SN")]
        public int SN { get; set; }
        /// <summary>
        ///	1 小计，2 24小时合计	
        ///</summary>
        public int? Category { get; set; }
        /// <summary>
        ///	住院就诊序号	
        ///</summary>
        public int? CureNo { get; set; }
        /// <summary>
        ///	病区代码	
        ///</summary>
        public int? WardCode { get; set; }
        /// <summary>
        ///	时间区间名称	
        ///</summary>
        public string WorkPeriod { get; set; }
        /// <summary>
        ///	日期	
        ///</summary>
        public DateTime? WorkDate { get; set; }
        /// <summary>
        ///	开始时间	
        ///</summary>
        public DateTime? BeginTime { get; set; }
        /// <summary>
        ///	结束时间	
        ///</summary>
        public DateTime? EndTime { get; set; }
        /// <summary>
        ///	入水量	
        ///</summary>
        public float? InWater { get; set; }
        /// <summary>
        ///	出水量	
        ///</summary>
        public float? OutWater { get; set; }
        /// <summary>
        ///	输液余量	
        ///</summary>
        public float? LeftWater { get; set; }
        /// <summary>
        ///	生成时间	
        ///</summary>
        public DateTime? OpTime { get; set; }
        /// <summary>
        ///	操作人工号	
        ///</summary>
        public string OpCode { get; set; }
    }
}