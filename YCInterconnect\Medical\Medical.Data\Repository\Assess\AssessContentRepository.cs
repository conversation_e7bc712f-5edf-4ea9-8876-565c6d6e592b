﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;


namespace Medical.Data.Repository
{
    public class AssessContentRepository : IAssessContentRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;
        public AssessContentRepository(
              MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService
        )
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }


        public async Task<List<AssessContentInfo>> GetByRecordsCode(string recordsCode)
        {
            dynamic cacheQuery = new DynamicDictionary();
            cacheQuery.RecordsCode = recordsCode;
            var result = await GetCacheAsync(cacheQuery) as List<AssessContentInfo>;
            return result;
        }

        public async Task<List<AssessContentInfo>> GetByMultiRecordsCodes(List<string> recordsCodes)
        {
            var datas = new List<AssessContentInfo>();
            if (recordsCodes == null || recordsCodes.Count <= 0)
            {
                return null;
            }
            foreach (var recordsCode in recordsCodes)
            {
                datas.AddRange(await GetByRecordsCode(recordsCode));
            }
            return datas;
        }

        public async Task<List<AssessContentInfo>> GetByRecordsCode(string recordsCode, string controllType)
        {
            var datas = await GetByRecordsCode(recordsCode);
            return datas.Where(t => t.ControlerType == controllType).ToList();
        }
        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            var dict = new Dictionary<string, object>();
            if (Query != null)
            {
                if (Query is DynamicDictionary query && query.HasMember("RecordsCode"))
                {
                    dict = new Dictionary<string, object>
                    {
                        { "RecordsCode",Query.RecordsCode }
                    };
                    key = key + "_" + Query.RecordsCode;                   
                }
                //缓存预加载
                if (Query is DynamicDictionary query1 && query1.HasMember("PreloadingFlag"))
                {
                    return await GetCachePreloading(key, dict);
                }

            }
            return await _getCacheService.GetCacheMain<List<AssessContentInfo>>(key, GetDataBaseListData, dict);
        }

        /// <summary>
        /// 缓存预热
        /// </summary>
        /// <param name="key"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<bool> GetCachePreloading(string key, Dictionary<string, object> dict = null)
        {
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var recordsCodes = await _medicalDbContext.AssessContents.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").Select(m => m.RecordsCode).Distinct().ToListAsync();
            foreach (var item in recordsCodes)
            {
                if (string.IsNullOrEmpty(item))
                {
                    continue;
                }
                dict = new Dictionary<string, object>
                    {
                        { "RecordsCode",item }
                    };
                var keyTemp=key + "_" + item;
                await _getCacheService.GetCacheMain<List<AssessContentInfo>>(keyTemp, GetDataBaseListData, dict);
            }
            return true;
        }

        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            dict.TryGetValue("RecordsCode", out var recordsCode);
            object data = null;
            if (hospitalID == null)
            {
                return data;
            }
            if (recordsCode == null)
            {
                return await _medicalDbContext.AssessContents.Where(m => m.HospitalID == hospitalID.ToString() && m.DeleteFlag != "*").ToListAsync();
            }
            else
            {
                return await _medicalDbContext.AssessContents.Where(m => m.HospitalID == hospitalID.ToString() && m.RecordsCode == recordsCode.ToString() && m.DeleteFlag != "*").ToListAsync();
            }


        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.AssessContent.GetKey(_sessionCommonServer);
        }


        /// <summary>
        /// 评估模板配置使用 其它页面不允许使用
        /// </summary>
        /// <param name="recordsCode"></param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public async Task<List<AssessContentInfo>> GetTestAssessDataByRecordsCode(string recordsCode, string hospitalID, int language)
        {
            if (string.IsNullOrWhiteSpace(recordsCode))
            {
                return [];
            }
            var datas = await GetCacheNotBySessionAsync(hospitalID, language) as List<AssessContentInfo>;
            return datas.Where(t => t.RecordsCode.Trim() == recordsCode).ToList();
        }
        /// <summary>
        /// 评估模板配置使用 其它页面不允许使用
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public async Task<object> GetCacheNotBySessionAsync(string hospitalID, int language)
        {
            string key = GetCacheTypeNotBySession(hospitalID, language);
            var datas = await this._memoryCache.GetOrCreateAsync(key, async entry =>
            {
                entry.SetAbsoluteExpiration(TimeSpan.FromSeconds(600000));
                return await _medicalDbContext.AssessContents.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
            });
            return datas;
        }
        /// <summary>
        /// 评估模板配置使用 其它页面不允许使用
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public string GetCacheTypeNotBySession(string hospitalID, int language)
        {
            return CacheType.AssessContent.GetKeyNotBySession(hospitalID, language);
        }
        /// <summary>
        /// 不使用缓存获取模板数据    开发使用 其它不允许调用
        /// </summary>
        /// <param name="reocrdsCode"></param>
        /// <returns></returns>
        private async Task<List<AssessContentInfo>> GetAssessContentNotByCache(string reocrdsCode)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            return await _medicalDbContext.AssessContents.Where(m => m.HospitalID == hospitalID && m.RecordsCode == reocrdsCode && m.DeleteFlag != "*").ToListAsync();
        }
    }
}