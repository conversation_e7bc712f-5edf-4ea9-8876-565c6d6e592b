﻿/**
 * 2022-05-15   新增母婴关系同步，可通过母亲信息补全婴儿信息  ——孟昭永
 * 
 */
using Interconnect.Services.Interface;
using Interconnect.ViewModels;
using NLog;
using System;
using System.Threading.Tasks;

namespace Interconnect.Services
{
    public class SyncInpatientService : ISyncInpatientService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IInpatientDealWithService _InpatientDealWithService;
        private readonly IJobLogService _jobLogService;

        public SyncInpatientService(
            IInpatientDealWithService InpatientDealWithService
            , IJobLogService jobLogService
            )
        {
            _InpatientDealWithService = InpatientDealWithService;
            _jobLogService = jobLogService;
        }

        /// <summary>
        ///  同步新入院病人数据
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncNewInPatient()
        {
            var syncResult = false;
            //生成一个随机的日志序号
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.NewInpatientJob).ToString();
            var jobName = "新入院病人信息";
            var logMsg = "作业编号:" + guid + "启动同步任务:" + jobName;
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                try
                {
                    syncResult = await _InpatientDealWithService.SyncNewInPatient();
                }
                catch (Exception ex)
                {
                    _logger.Error("新入院病人信息同步失败" + ex.ToString());
                }

                ResultLog(guid, syncResult, "住院病人信息");// 同步住院病人信息
                _jobLogService.RemoveJob(jobId, jobName, "");
                _logger.Info("作业ID：" + jobId.ToString() + "作业编号:" + guid + "**同步住院病人信息作业:本次同步结束*");
            }
            return syncResult;
        }



        /// <summary>
        ///  根据病区同步在院病人数据
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncInPatientByStationCode(string stationCode)
        {
            //生成一个随机的日志序号
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.OneStationInpatientJob).ToString();
            var jobName = "根据病区同步在院病人数据 StationCode||"+ stationCode;
            var logMsg = "作业编号:" + guid + "启动同步任务:" + jobName;
            _logger.Info("启动作业" + logMsg);
            var subJobId = stationCode;
            var jobStatus = false;
            try
            {
                jobStatus = _jobLogService.GetJobStatus(jobId, jobName, subJobId);
            }
            catch (Exception ex)
            {
                _logger.Error(jobName + "||判断作业状态失败 StationCode||"+ stationCode + ex.ToString());
                return false;
            }

            if (jobStatus)
            {
                _logger.Info(logMsg + jobName);
                var resultFlag = false;
                try
                {
                    resultFlag = await _InpatientDealWithService.SyncInPatientByStationCode(stationCode);
                }
                catch (Exception ex)
                {
                    _logger.Error(jobName+"||同步失败" + ex.ToString());
                    resultFlag = false;
                }
                _jobLogService.RemoveJob(jobId, jobName, subJobId);
                _logger.Info("作业ID：" + jobId.ToString() + "作业编号:" + guid + "**同步住院病人信息作业:本次同步结束*");
                if (resultFlag)
                {
                    _logger.Info(jobName + "||同步成功");
                }
                else
                {
                    _logger.Error(jobName + "||同步失败");
                    return false;
                }
            }
            return true;
        }


        /// <summary>
        /// <summary>
        /// 写同步日志
        /// </summary>
        /// <param name="Sign"></param>
        /// <param name="Text"></param>
        private void ResultLog(string guid, bool Sign, string Text)
        {
            string resultStr = "";
            if (Sign)
            {
                resultStr = "本次作业编号[" + guid + "]" + Text + "同步成功";
            }
            else
            {
                resultStr = "本次作业编号[" + guid + "]" + Text + "同步失败";
            }
            _logger.Info(resultStr);
        }




        /// <summary>
        /// 出院病人同步,同步一段时间内的出院病人数据
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SyncDischargedPatientsByDateTime()
        {
            //生成一个随机的日志序号
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.InPatientDischargeJob).ToString();
            var jobName = "出院病人信息";
            var logMsg = "作业编号:" + guid + "启动同步任务:" + jobName;
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            if (jobStatus)
            {
                var resultFlag = false;
                try
                {
                    resultFlag =await _InpatientDealWithService.SyncDischargedPatientsEventByDateTime();
                }
                catch (Exception ex)
                {
                    _logger.Error("出院病人信息同步失败" + ex.ToString());
                    resultFlag = false;
                }
                _logger.Info(logMsg + jobName);
                ResultLog(guid, resultFlag, "出院病人信息");// 同步住院病人信息
                _jobLogService.RemoveJob(jobId, jobName, "");
                _logger.Info("作业ID：" + jobId.ToString() + "作业编号:" + guid + "***同步出院病人信息作业:本次同步结束***");
            }
            return true;
        }

        /// <summary>
        /// 根据时间段同步出院患者（刷新用）
        /// </summary>
        /// <param name="startDateTime"></param>
        /// <param name="endDateTime"></param>
        /// <returns></returns>
        public async Task<bool> SyncDischargedByDateTime(DateTime? startDateTime, DateTime? endDateTime)
        {
            //生成一个随机的日志序号
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.PatientDischargeJob).ToString();
            var jobName = "出院病人信息";
            var logMsg = "作业编号:" + guid + "启动同步任务:" + jobName;
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            if (jobStatus)
            {
                var resultFlag = false;
                try
                {
                    resultFlag =await _InpatientDealWithService.SyncDischargedByDateTime(startDateTime, endDateTime);
                }
                catch (Exception ex)
                {
                    _logger.Error("出院病人信息同步失败" + ex.ToString());
                    resultFlag = false;
                }
                _logger.Info(logMsg + jobName);
                ResultLog(guid, resultFlag, "出院病人信息");// 同步住院病人信息
                _jobLogService.RemoveJob(jobId, jobName, "");
                _logger.Info("作业ID：" + jobId.ToString() + "作业编号:" + guid + "***同步出院病人信息作业:本次同步结束***");
            }
            return true;
        }
    }
}