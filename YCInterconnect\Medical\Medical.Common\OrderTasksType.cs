﻿using System.ComponentModel;

namespace Medical.Common
{
    public enum OrderTasksType
    {
        /// <summary>
        /// 医嘱开立
        /// </summary>
        [Description("医嘱开立")]
        OrderIssuance = 0,
        /// <summary>
        /// 医嘱审核
        /// </summary>
        [Description("医嘱审核")]
        OrderCheck = 10,
        /// <summary>
        /// 医嘱拆分
        /// </summary>
        [Description("医嘱拆分")]
        SplitOrder = 20,
        /// <summary>
        /// 药师核对
        /// </summary>
        [Description("药师核对")]
        PharmacistCheck= 30,
        /// <summary>
        /// 标签打印
        /// </summary>
        [Description("标签打印")]
        LabelPrinting = 33,
        /// <summary>
        /// 药品调配
        /// </summary>
        [Description("药品调配")]
        Prescription = 35,
        /// <summary>
        /// 药品配送
        /// </summary>
        [Description("药品配送")]
        DrugDistribution = 40,
        /// <summary>
        /// 药品签收
        /// </summary>
        [Description("药品签收")]
        SignForMedication = 45,
        /// <summary>
        /// 药品核对
        /// </summary>
        [Description("药品核对")]
        VerifyDrug = 50, 
        /// <summary>
        /// 病房配药
        /// </summary>
        [Description("病房配药")]
        WardDispense = 55,
        /// <summary>
        /// 患者核对
        /// </summary>
        [Description("患者核对")]
        VerifyPatient = 60,
        /// <summary>
        /// 患者给药
        /// </summary>
        [Description("患者给药")]
        StartPerformMedication = 65,
        /// <summary>
        /// 用药监测
        /// </summary>
        [Description("用药监测")]
        MedicationInspection = 70,
        /// <summary>
        /// 不良反应
        /// </summary>
        [Description("不良反应")]
        AdverseReactions = 75,
        /// <summary>
        /// 用药结束
        /// </summary>
        [Description("用药结束")]
        EndPerformMedication = 80,
        /// <summary>
        /// 剩药处理
        /// </summary>
        [Description("剩药处理")]
        DisposalLeftoverMedicine = 85,
        /// <summary>
        /// 出院随访
        /// </summary>
        [Description("出院随访")]
        RandomAccess = 90,
        /// <summary>
        /// 已取消
        /// </summary>
        [Description("已取消")]
        Cancel = 99,
    }
}