﻿using Microsoft.EntityFrameworkCore;
using System;

namespace Medical.Data.Context
{
    public partial class CDADBContext : DbContext
    {
        public CDADBContext(DbContextOptions<CDADBContext> options)
   : base(options)
        { }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            base.OnConfiguring(optionsBuilder);
            //显示sql详细日志
#if DEBUG
            //因为妇幼同步程序不产生sql日志，所以添加此代码，可显示黑色控制台console
            optionsBuilder.LogTo(Console.WriteLine, Microsoft.Extensions.Logging.LogLevel.Information).EnableSensitiveDataLogging();

            optionsBuilder.EnableSensitiveDataLogging();
#endif

        }
    }
}
