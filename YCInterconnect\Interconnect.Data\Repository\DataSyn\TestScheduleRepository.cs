﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Interconnect.Data.Repository
{
    public class TestScheduleRepository : ITestScheduleRepository
    {
        private DataOutConnection _DataOutConnection = null;

        public TestScheduleRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }
        /// <summary>
        /// 获取所有没有抽取的病区数据
        /// </summary>
        /// <param name="tongbuCount"></param>
        /// <param name="takeRows"></param>
        /// <returns></returns>
        public List<TestScheduleInfo> GetAsync(int tongbuCount,int takeRows)
        {
            return  _DataOutConnection.TestSchedules.Where(m => m.DataPumpFlag != "*"
             && ((m.Counts ?? 0) < tongbuCount)).Take(takeRows).ToList();
 
        }
    }
}