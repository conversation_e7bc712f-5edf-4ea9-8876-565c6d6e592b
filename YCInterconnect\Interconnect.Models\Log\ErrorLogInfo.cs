﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("ErrorLog")]
    public class ErrorLogInfo
    {
        /// <summary>
        ///
        ///</summary>
         [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///严重级别，1、2、3、4 越高越严
        ///</summary>
        public int SeverityLevel { get; set; }
         /// <summary>
        /// 日志类别
        /// </summary>
        public string LogType { get; set; }
        /// <summary>
        ///日志内容
        ///</summary>
        public string Logs { get; set; }      
        /// <summary>
        ///产生时间
        ///</summary>
        public DateTime DateTimes { get; set; }
        /// <summary>
        ///推送给管理人员时间
        ///</summary>
        public DateTime? SendDate { get; set; }
        /// <summary>
        ///是否已经推送
        ///</summary>
        public string SendFlag { get; set; }
    }
}