﻿/**
 * 2022-05-15   新增母婴关系同步，可通过母亲信息补全婴儿信息  ——孟昭永
 * 
 */
using Arch.EntityFrameworkCore.UnitOfWork;
using Interconnect.Services.Interface;
using Interconnect.ViewModels;
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using MedicalExternalCommon.Service;
using Microsoft.Extensions.Options;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Interconnect.Services
{
    public class NewBornService : INewBornService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IInpatientDataRepository _IInpatientDataRepository;
        private readonly INewBornRecordRepository _newBornRecordRepository;
        private readonly DataTableEditListService _dataTableEditListService;
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IOptions<SystemConfig> _options;

        public NewBornService(IInpatientDataRepository inPatientRepository
             ,IUnitOfWork<MedicalDbContext> unitOfWork
             ,INewBornRecordRepository newBornRecordRepository
            , DataTableEditListService dataTableEditListService
            , IOptions<SystemConfig> options
            )
        {
            _IInpatientDataRepository = inPatientRepository;
            _unitOfWork = unitOfWork;
            _newBornRecordRepository = newBornRecordRepository;
            _dataTableEditListService = dataTableEditListService;
            _options = options;
        }
        /// <summary>
        /// 同步新生儿数据
        /// </summary>
        /// <param name="newborns"></param>
        /// <returns></returns>
        public async Task<bool> SyncNewBorn(List<NewBornView> newborns)
        {
            if (newborns == null || newborns.Count <= 0)
            {
                return false;
            }
            foreach (var item in newborns)
            {
                if (item.CaseNumber == null)
                {
                    continue;
                }
                //获取病人入院记录
                var saveFlag = false;
                var motherMedicalInPatientInfo = await _IInpatientDataRepository.GetAsyncByCaseNumber(item.MotherCaseNumber, _options.Value.HospitalID);
                var newBornRecord = await _newBornRecordRepository.GetDataByParentCasenumber(item.MotherCaseNumber);
                if (newBornRecord == null || newBornRecord.Count == 0)
                {
                    await InsertNewBornRecordAsync(item, motherMedicalInPatientInfo, 0);
                    saveFlag = true;
                }
                else
                {
                    saveFlag = UPDateNewBornRecord(item, newBornRecord, motherMedicalInPatientInfo);
                }
                try
                {
                    if (saveFlag)
                    {
                        //新增新生儿单子的异动
                        var babyInpatient = await _IInpatientDataRepository.GetAsyncByCaseNumber(item.CaseNumber, _options.Value.HospitalID);
                        if (babyInpatient != null)
                        {
                            await _dataTableEditListService.AddEditLog(babyInpatient.ID, babyInpatient.StationID, "PatientAssessMainHistory", 56, null, null);
                        }
                        _unitOfWork.SaveChanges();
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error("新生儿数据保存失败" + ex.ToString());
                    continue;
                }
            }

            return true;
        }
        /// <summary>
        /// 通过母亲更新新生儿信息
        /// </summary>
        /// <param name="newborn"></param>
        /// <param name="newBornRecordList"></param>
        /// <param name="motherInpatientDataInfo"></param>
        /// <returns></returns>
        private bool UPDateNewBornRecord(NewBornView newborn, List<NewBornRecordInfo> newBornRecordList, InpatientDataInfo motherInpatientDataInfo)
        {
            var upflag = false;
            foreach (var newBornRecord in newBornRecordList)
            {
                if (!string.IsNullOrEmpty(newborn.CaseNumber) && newBornRecord.Casenumber != newborn.CaseNumber)
                {
                    newBornRecord.Casenumber = newborn.CaseNumber;
                    upflag = true;
                }
                if (!string.IsNullOrEmpty(newborn.ChartNO) && newBornRecord.ChartNO != newborn.ChartNO)
                {
                    newBornRecord.ChartNO = newborn.ChartNO;
                    upflag = true;
                }
                if (newBornRecord.Gender != newborn.Gender)
                {
                    newBornRecord.Gender = newborn.Gender;
                    upflag = true;
                }
                if (newBornRecord.DeliveryTime!=newborn.NewbornBirthday)
                {
                    newBornRecord.DeliveryTime = newborn.NewbornBirthday;
                }
                if (newborn.DeleteFlag == "*")
                {
                    newBornRecord.DeleteFlag = "*";
                    upflag = true;
                }
                if (motherInpatientDataInfo != null)
                {
                    if (newBornRecord.ParentID != motherInpatientDataInfo.ID)
                    {
                        newBornRecord.ParentID = motherInpatientDataInfo.ID;
                        upflag = true;
                    }
                    if (newBornRecord.ParentCasenumber != motherInpatientDataInfo.CaseNumber)
                    {
                        newBornRecord.ParentCasenumber = motherInpatientDataInfo.CaseNumber;
                        upflag = true;
                    }
                    if (newBornRecord.ParentChartNO != motherInpatientDataInfo.ChartNo)
                    {
                        newBornRecord.ParentChartNO = motherInpatientDataInfo.ChartNo;
                        upflag = true;
                    }
                }
            }
            return upflag;
        }
        /// <summary>
        /// 没有母婴关系时建立关系，保证婴儿哺喂可用
        /// </summary>
        /// <param name="newborn"></param>
        /// <param name="motherMedicalInPatientInfo"></param>
        /// <param name="newBornNum"></param>
        /// <returns></returns>
        private async Task<bool> InsertNewBornRecordAsync(NewBornView newborn, InpatientDataInfo motherMedicalInPatientInfo, int newBornNum)
        {
            var newBornRecordInfo = new NewBornRecordInfo()
            {
                NewBornID = Guid.NewGuid().ToString("N"),
                Casenumber = newborn.CaseNumber,
                ChartNO = newborn.ChartNO,
                ParentCasenumber = newborn.MotherCaseNumber,
                ParentChartNO = newborn.MotherChartNO,
                NewBornNum = (byte)newBornNum,
                AddDate = DateTime.Now,
                AddEmployeeID = "TongBu",
                ParentID = "",
                DeleteFlag = "",
                Gender = newborn.Gender,
                DeliveryTime = newborn.NewbornBirthday
            };
            if (motherMedicalInPatientInfo != null)
            {
                newBornRecordInfo.ParentID = motherMedicalInPatientInfo.ID;
                newBornRecordInfo.ParentCasenumber = motherMedicalInPatientInfo.CaseNumber;
                newBornRecordInfo.ParentChartNO = motherMedicalInPatientInfo.ChartNo;
            }
            await _unitOfWork.GetRepository<NewBornRecordInfo>().InsertAsync(newBornRecordInfo);
            return true;
        }
    }
}