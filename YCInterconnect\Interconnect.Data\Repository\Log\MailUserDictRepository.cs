﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Interconnect.Data.Interface;
using Interconnect.Models;
using Interconnect.Data.Context;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Interconnect.Data
{
    public class MailUserDictRepository : IMailUserDictRepository
    {
        private DataOutConnection _DataOutConnection = null;

        public MailUserDictRepository(DataOutConnection db)
        {
            _DataOutConnection = db;
        }
        public   List<MailUserDictInfo> GetAsync()
        {
            try
            {
                return _DataOutConnection.MailUserDictInfos.Where(m => m.DeleteFlag != "*").ToList();
            }
            catch (Exception ex)
            {

                throw ex;
            }
           
        }
    }
}
