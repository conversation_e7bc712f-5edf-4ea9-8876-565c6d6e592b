using Interconnect.Services.Interface;
using Interconnect.ViewModels;
using Medical.Common;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using System;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;

namespace Interconnect.API.Controllers
{
    /// <summary>
    /// 输血数据同步到NursingRecordDetail控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/BloodTransfusionSync")]
    [EnableCors("any")]
    public class BloodTransfusionSyncController : Controller
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IBloodTransfusionSyncService _bloodTransfusionSyncService;
        /// <summary>
        /// 输血数据同步到NursingRecordDetail控制器构造函数
        /// </summary>
        /// <param name="bloodTransfusionSyncService">输血数据同步服务</param>
        public BloodTransfusionSyncController(IBloodTransfusionSyncService bloodTransfusionSyncService)
        {
            _bloodTransfusionSyncService = bloodTransfusionSyncService;
        }
        /// <summary>
        /// 同步输血数据到NursingRecordDetail
        /// </summary>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <returns>同步结果</returns>
        [HttpGet]
        [Route("SyncBloodTransfusionToNursingRecordDetail")]
        public async Task<IActionResult> SyncBloodTransfusionToNursingRecordDetail([Required] DateTime startDateTime, [Required] DateTime endDateTime)
        {
            var result = new ResponseResult();
            _logger.Info($"开始同步输血数据到NursingRecordDetail，时间范围：{startDateTime:yyyy-MM-dd HH:mm:ss} - {endDateTime:yyyy-MM-dd HH:mm:ss}");
            if (startDateTime >= endDateTime)
            {
                result.Error("开始时间不能大于或等于结束时间");
                return result.ToJson();
            }
            var syncResult = await _bloodTransfusionSyncService.SyncBloodTransfusionToNursingRecordDetail(startDateTime, endDateTime);
            if (syncResult)
            {
                result.Sucess("输血数据同步到NursingRecordDetail成功");
            }
            else
            {
                result.Error("输血数据同步到NursingRecordDetail失败");
            }
            return result.ToJson();
        }
        /// <summary>
        /// 同步Wiley补录单病人输血数据到NursingRecordDetail
        /// </summary>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <param name="caseNumber">病案号</param>
        /// <returns>同步结果</returns>
        [HttpGet]
        [Route("SyncWileyBloodTransfusionToNursingRecordDetail")]
        public async Task<IActionResult> SyncWileyBloodTransfusionToNursingRecordDetail([Required] DateTime startDateTime, [Required] DateTime endDateTime, [Required] string caseNumber)
        {
            var result = new ResponseResult();
            _logger.Info($"开始同步Wiley补录单病人输血数据到NursingRecordDetail，时间范围：{startDateTime:yyyy-MM-dd HH:mm:ss} - {endDateTime:yyyy-MM-dd HH:mm:ss}，病案号：{caseNumber}");
            if (startDateTime >= endDateTime)
            {
                result.Error("开始时间不能大于或等于结束时间");
                return result.ToJson();
            }
            if (string.IsNullOrWhiteSpace(caseNumber))
            {
                result.Error("病案号不能为空");
                return result.ToJson();
            }
            var syncResult = await _bloodTransfusionSyncService.SyncWileyBloodTransfusionToNursingRecordDetail(startDateTime, endDateTime, caseNumber);
            if (syncResult)
            {
                result.Sucess("Wiley补录单病人输血数据同步到NursingRecordDetail成功");
            }
            else
            {
                result.Error("Wiley补录单病人输血数据同步到NursingRecordDetail失败");
            }
            return result.ToJson();
        }
        /// <summary>
        /// 获取输血数据（仅获取不同步）
        /// </summary>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <returns>输血数据</returns>
        [HttpGet]
        [Route("GetBloodTransfusionData")]
        public async Task<IActionResult> GetBloodTransfusionData([Required] DateTime startDateTime, [Required] DateTime endDateTime)
        {
            var result = new ResponseResult();
            _logger.Info($"获取输血数据，时间范围：{startDateTime:yyyy-MM-dd HH:mm:ss} - {endDateTime:yyyy-MM-dd HH:mm:ss}");
            if (startDateTime >= endDateTime)
            {
                result.Error("开始时间不能大于或等于结束时间");
                return result.ToJson();
            }
            var bloodTransfusionData = await _bloodTransfusionSyncService.GetBloodTransfusionDataFromApi(startDateTime, endDateTime);
            result.Data = bloodTransfusionData;
            result.Sucess($"获取到 {bloodTransfusionData.Count} 条输血数据");
            return result.ToJson();
        }
        /// <summary>
        /// 获取Wiley补录单病人输血数据（仅获取不同步）
        /// </summary>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <param name="caseNumber">病案号</param>
        /// <returns>输血数据</returns>
        [HttpGet]
        [Route("GetWileyBloodTransfusionData")]
        public async Task<IActionResult> GetWileyBloodTransfusionData([Required] DateTime startDateTime, [Required] DateTime endDateTime, [Required] string caseNumber)
        {
            var result = new ResponseResult();
            _logger.Info($"获取Wiley补录单病人输血数据，时间范围：{startDateTime:yyyy-MM-dd HH:mm:ss} - {endDateTime:yyyy-MM-dd HH:mm:ss}，病案号：{caseNumber}");
            if (startDateTime >= endDateTime)
            {
                result.Error("开始时间不能大于或等于结束时间");
                return result.ToJson();
            }
            if (string.IsNullOrWhiteSpace(caseNumber))
            {
                result.Error("病案号不能为空");
                return result.ToJson();
            }
            var bloodTransfusionData = await _bloodTransfusionSyncService.GetWileyBloodTransfusionDataFromApi(startDateTime, endDateTime, caseNumber);
            result.Data = bloodTransfusionData;
            result.Sucess($"获取到 {bloodTransfusionData.Count} 条Wiley补录单病人输血数据");
            return result.ToJson();
        }
    }
}
