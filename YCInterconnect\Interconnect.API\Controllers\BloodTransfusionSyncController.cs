using Interconnect.Services.Interface;
using Interconnect.ViewModels;
using Medical.Common;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using System;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;

namespace Interconnect.API.Controllers
{
    /// <summary>
    /// 输血数据同步到NursingRecordDetail控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/BloodTransfusionSync")]
    [EnableCors("any")]
    public class BloodTransfusionSyncController : Controller
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IBloodTransfusionSyncService _bloodTransfusionSyncService;
        /// <summary>
        /// 输血数据同步到NursingRecordDetail控制器构造函数
        /// </summary>
        /// <param name="bloodTransfusionSyncService">输血数据同步服务</param>
        public BloodTransfusionSyncController(IBloodTransfusionSyncService bloodTransfusionSyncService)
        {
            _bloodTransfusionSyncService = bloodTransfusionSyncService;
        }
        /// <summary>
        /// 同步输血数据到NursingRecordDetail
        /// </summary>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <returns>同步结果</returns>
        [HttpGet]
        [Route("SyncTransfusionToRecordDetail")]
        public async Task<IActionResult> SyncTransfusionToRecordDetail([Required] DateTime startDateTime, [Required] DateTime endDateTime)
        {
            var result = new ResponseResult();
            _logger.Info($"开始同步输血数据到NursingRecordDetail，时间范围：{startDateTime:yyyy-MM-dd HH:mm:ss} - {endDateTime:yyyy-MM-dd HH:mm:ss}");
            var syncResult = await _bloodTransfusionSyncService.SyncTransfusionToRecordDetail(startDateTime, endDateTime);
            if (syncResult)
            {
                result.Sucess("输血数据同步到NursingRecordDetail成功");
            }
            else
            {
                result.Error("输血数据同步到NursingRecordDetail失败");
            }
            return result.ToJson();
        }
        /// <summary>
        /// 同步单个病人输血数据到NursingRecordDetail
        /// </summary>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <param name="caseNumber">病案号</param>
        /// <returns>同步结果</returns>
        [HttpGet]
        [Route("SyncSinglePatientTransfusionToRecordDetail")]
        public async Task<IActionResult> SyncSinglePatientTransfusionToRecordDetail([Required] DateTime startDateTime, [Required] DateTime endDateTime, [Required] string caseNumber)
        {
            var result = new ResponseResult();
            _logger.Info($"开始同步单个病人输血数据到NursingRecordDetail，时间范围：{startDateTime:yyyy-MM-dd HH:mm:ss} - {endDateTime:yyyy-MM-dd HH:mm:ss}，病案号：{caseNumber}");
            var syncResult = await _bloodTransfusionSyncService.SyncSinglePatientTransfusionToRecordDetail(startDateTime, endDateTime, caseNumber);
            if (syncResult)
            {
                result.Sucess("单个病人输血数据同步到NursingRecordDetail成功");
            }
            else
            {
                result.Error("单个病人输血数据同步到NursingRecordDetail失败");
            }
            return result.ToJson();
        }

    }
}
