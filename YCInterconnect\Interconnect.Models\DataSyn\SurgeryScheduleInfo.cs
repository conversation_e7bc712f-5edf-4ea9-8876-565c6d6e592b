﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Interconnect.Models
{
    [Serializable]
    [Table("SurgerySchedule")]
    public class SurgeryScheduleInfo : ModifyInfo
    {
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        ///住院号
        ///</summary>
        public string CaseNumber { get; set; }
        /// <summary>
        ///手术日期
        ///</summary>
        public DateTime OperateDate { get; set; }
        /// <summary>
        ///手术时间
        ///</summary>
        public TimeSpan? OperateTime { get; set; }
        /// <summary>
        ///PCSCode
        ///</summary>
        public string ICDPCS { get; set; }
        /// <summary>
        ///手术名称
        ///</summary>
        public string OperateName { get; set; }
        /// <summary>
        ///注记事项
        ///</summary>
        public string Memo { get; set; }
    }
}