﻿using Medical.Models;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public partial class MedicalDbContext
    {
        /// <summary>
        /// 护理记录
        /// </summary>
        public DbSet<NursingRecordInfo> NursingRecordInfos { get; set; }

        /// <summary>
        /// 记录核实
        /// </summary>
        public DbSet<VerifyRecordInfo> VerifyRecordInfos { get; set; }

        /// <summary>
        /// 记录核实日志
        /// </summary>
        public DbSet<VerifyRecordLogInfo> VerifyRecordLogInfos { get; set; }

        /// <summary>
        /// 病历审核记录
        /// </summary>
        public DbSet<EMRReviewLogInfo> EMRReviewLogInfos { get; set; }

    }
}
