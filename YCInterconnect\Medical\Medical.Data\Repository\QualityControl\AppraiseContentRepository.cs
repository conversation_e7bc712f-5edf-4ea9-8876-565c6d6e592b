﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class QCCheckAppraiseContentRepository : IQCCheckAppraiseContentRepository
    {
        private readonly MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public QCCheckAppraiseContentRepository(MedicalDbContext medicalDbContext, IMemoryCache memoryCache, SessionCommonServer sessionCommonServer, GetCacheService getCacheService)
        {
            _medicalDbContext = medicalDbContext;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }


        /// <summary>
        /// 通过表单编号和医院id查询考核项目配置
        /// </summary>
        /// <param name="recordsCode">表单编号</param>
        /// <param name="noCache">是否不走缓存</param>
        /// <returns></returns>
        public async Task<List<QCCheckAppraiseContentInfo>> GetAppraiseContentInfoByRecordsCodeAsync(string recordsCode, bool noCache = false)
        {
            Expression<Func<QCCheckAppraiseContentInfo, int>> orderFunc = a => a.Sort;
            Expression<Func<QCCheckAppraiseContentInfo, bool>> whereFunc = a => a.RecordsCode == recordsCode && a.DeleteFlag != "*";
            if (noCache)
            {
                string key = GetCacheType();
                var (hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
                return await _medicalDbContext.QCCheckAppraiseContentInfos
                    .Where(whereFunc)
                    .Where(m => m.HospitalID == hospitalID)
                    .OrderBy(orderFunc).ToListAsync();
            }
            return (await this.GetAllAsync<QCCheckAppraiseContentInfo>())
                .Where(m => whereFunc.Compile()(m))
                .OrderBy(m => orderFunc.Compile()(m)).ToList();
        }
        /// <summary>
        /// 获取最大ID
        /// </summary>
        /// <param name="recordsCode"></param>
        /// <returns></returns>
        public async Task<int> GetMaxID(string recordsCode)
        {
            string key = GetCacheType();
            var (hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            return await _medicalDbContext.QCCheckAppraiseContentInfos
                .Where(m => m.RecordsCode == recordsCode && m.HospitalID == hospitalID)
                .Select(m => (int?)m.QCCheckAppraiseContentID)
                .DefaultIfEmpty()
                .MaxAsync(m => m) ?? 0;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<QCCheckAppraiseContentInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            return await _medicalDbContext.QCCheckAppraiseContentInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.QCCheckAppraiseContent.GetKey(_sessionCommonServer);
        }
    }
}
