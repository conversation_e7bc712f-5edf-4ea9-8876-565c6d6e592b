﻿using System;


namespace Interconnect.ViewModels
{
   public class InterconnectPatientScoreMainView
    {
        /// <summary>
        /// 姓名
        /// </summary>
        public string PatientName { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public string Sex { get; set; }
        /// <summary>
        /// 风险说明
        /// </summary>
        public string RangeContent { get; set; }
        /// <summary>
        /// 风险表ID
        /// </summary>
        public int? RecordListID { get; set; }
        /// <summary>
        /// 风险名称
        /// </summary>
        public string RecordName { get; set; }
        /// <summary>
        /// 风险评估时间
        /// </summary>
        public DateTime AssessDateTime { get; set; }
        /// <summary>
        /// 是否需要提醒 0 不提醒 1提醒    
        /// </summary>
        public int? RemindFlag { get; set; }
        /// <summary>
        /// 科室Code
        /// </summary>
        public string DepartmentCode { get; set; }
        /// <summary>
        /// 住院唯一号
        /// </summary>
        public string CaseNumber { get; set; }       

        /// <summary>
        /// 病区Code
        /// </summary>
        public string StationCode { get; set; }

        /// <summary>
        /// 科室名称
        /// </summary>
        public string DepartmentName { get; set; }       
        /// <summary>
        /// 床位号
        /// </summary>
        public string BedNumber { get; set; }
        /// <summary>
        /// 病区名称
        /// </summary>
        public string StationName { get; set; }
        /// <summary>
        /// 主治医生ID
        /// </summary>
        public string DoctorID { get; set; }
       
        /// <summary>
        /// 主治医生
        /// </summary>
        public string DoctorName { get; set; }
        /// <summary>
        /// 分数
        /// </summary>
        public decimal ScorePoint { get; set; }
    }
}
