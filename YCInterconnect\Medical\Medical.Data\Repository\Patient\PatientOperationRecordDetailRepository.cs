﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientOperationRecordDetailRepository : IPatientOperationRecordDetailRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;

        public PatientOperationRecordDetailRepository(MedicalDbContext medicalDbContext)
        {
            _medicalDbContext = medicalDbContext;
        }

        public async Task<List<PatientOperationRecordDetailInfo>> GetByDetailIDAsync(string patientOperationRecordMainID)
        {
            return await _medicalDbContext.PatientOperationRecordDetailInfos.Where(m => m.PatientOperationRecordMainID == patientOperationRecordMainID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientOperationRecordDetailInfo>> GetByDetailInpatientAsync(string patientOperationRecordMainID)
        {
            return await _medicalDbContext.PatientOperationRecordDetailInfos.Where(m => m.PatientOperationRecordMainID == patientOperationRecordMainID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<PatientOperationRecordDetailInfo> GetByOneDetailAssessListIDAsync(int assessListID, string patientOperationRecordDetailID)
        {
            return await _medicalDbContext.PatientOperationRecordDetailInfos.Where(m => m.AssessListID == assessListID && m.PatientOperationRecordDetailID == patientOperationRecordDetailID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<PatientOperationRecordDetailInfo> GetByOneDetailIDAsync(string patientOperationRecordMainID)
        {
            return await _medicalDbContext.PatientOperationRecordDetailInfos.Where(m => m.PatientOperationRecordDetailID == patientOperationRecordMainID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据主表ID获取明细表数据
        /// </summary>
        /// <param name="patientOperationRecordMainID"></param>
        /// <returns></returns>
        public async Task<List<PatientOperationRecordDetailInfo>> GetListByMainIDAsync(string patientOperationRecordMainID)
        {
            return await _medicalDbContext.PatientOperationRecordDetailInfos.Where(m => m.PatientOperationRecordMainID == patientOperationRecordMainID
            && m.DeleteFlag != "*").ToListAsync();
        }
    }
}